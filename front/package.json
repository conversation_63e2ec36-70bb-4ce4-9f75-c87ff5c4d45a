{"name": "meubonsai-frontend", "version": "1.0.0", "private": true, "dependencies": {"@google-cloud/local-auth": "^3.0.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.4.0", "date-fns": "^2.30.0", "exif-js": "^2.3.0", "framer-motion": "^12.19.1", "google-auth-library": "^9.15.1", "react": "^18.2.0", "react-calendar": "^6.0.0", "react-dom": "^18.2.0", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.45.0", "react-icons": "^4.10.1", "react-intersection-observer": "^9.16.0", "react-query": "^3.39.3", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "react-toastify": "^9.1.3", "styled-components": "^5.3.11"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}