import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FaEdit, FaCheck, FaTimes, FaSeedling } from 'react-icons/fa';
import Button from './UI/Button';
import { toast } from 'react-toastify';
import plantsService from '../services/plantsService';

const CultivationSection = styled.div`
  background-color: ${props => props.theme.colors.surface};
  padding: ${props => props.theme.spacing.xl};
  border-radius: ${props => props.theme.borderRadius.lg};
  box-shadow: ${props => props.theme.shadows.sm};
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const SectionTitle = styled.h2`
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing.lg};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  width: 100%;

  @media (max-width: 480px) {
    flex-direction: column;
    align-items: flex-start;
    gap: ${props => props.theme.spacing.xs};
  }
`;

const SectionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${props => props.theme.spacing.lg};

  ${SectionTitle} {
    margin-bottom: 0;
  }

  @media (max-width: 480px) {
    flex-direction: column;
    align-items: stretch;
    gap: ${props => props.theme.spacing.md};

    ${SectionTitle} {
      text-align: center;
    }
  }
`;

const FieldsContainer = styled.div`
  display: grid;
  gap: ${props => props.theme.spacing.lg};
`;

const FieldGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.sm};
`;

const FieldLabel = styled.label`
  font-weight: 600;
  color: ${props => props.theme.colors.text.primary};
  font-size: ${props => props.theme.typography.fontSize.sm};
`;

const FieldValue = styled.div`
  color: ${props => props.theme.colors.text.secondary};
  font-size: ${props => props.theme.typography.fontSize.md};
  min-height: 24px;
  padding: ${props => props.theme.spacing.sm} 0;
`;

const FieldInput = styled.textarea`
  width: 100%;
  padding: ${props => props.theme.spacing.sm};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.md};
  font-family: inherit;
  resize: vertical;
  min-height: 60px;
  
  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}20;
  }
`;

const EditActions = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
  margin-top: ${props => props.theme.spacing.md};
`;

const EmptyState = styled.div`
  color: ${props => props.theme.colors.text.secondary};
  font-style: italic;
  text-align: center;
  padding: ${props => props.theme.spacing.lg};
`;

const CultivationPhaseSection = ({ plant, onUpdate, isOwner = true }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    evolutionary_stage: plant?.evolutionary_stage || '',
    fertilization: plant?.fertilization || '',
    substrate: plant?.substrate || ''
  });

  const hasAnyData = plant?.evolutionary_stage || plant?.fertilization || plant?.substrate;

  const handleEdit = () => {
    setFormData({
      evolutionary_stage: plant.evolutionary_stage || '',
      fertilization: plant.fertilization || '',
      substrate: plant.substrate || ''
    });
    setIsEditing(true);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setFormData({
      evolutionary_stage: plant.evolutionary_stage || '',
      fertilization: plant.fertilization || '',
      substrate: plant.substrate || ''
    });
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      const updatedPlant = await plantsService.updatePlant(plant.id, formData);
      // Mesclar os dados atualizados com os dados existentes da planta
      const mergedPlant = {
        ...plant,
        ...updatedPlant,
        // Preservar arrays e objetos complexos que podem não vir na resposta
        images: plant.images || [],
        cares: plant.cares || [],
        reminders: plant.reminders || []
      };

      // Atualizar o cache do React Query
      onUpdate(mergedPlant);

      // Atualizar o estado local do componente para refletir as mudanças imediatamente
      // Isso garante que os valores exibidos sejam atualizados sem precisar de refresh
      setFormData({
        evolutionary_stage: updatedPlant.evolutionary_stage || '',
        fertilization: updatedPlant.fertilization || '',
        substrate: updatedPlant.substrate || ''
      });

      setIsEditing(false);
      toast.success('Fase de cultivo atualizada com sucesso!');
    } catch (error) {
      console.error('Erro ao atualizar fase de cultivo:', error);
      toast.error('Erro ao atualizar fase de cultivo');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <CultivationSection>
      <SectionHeader>
        <SectionTitle>
          <FaSeedling />
          Fase de Cultivo
        </SectionTitle>
        {isOwner && !isEditing && (
          <Button size="small" onClick={handleEdit}>
            <FaEdit />
            Editar
          </Button>
        )}
      </SectionHeader>

      {isEditing ? (
        <>
          <FieldsContainer>
            <FieldGroup>
              <FieldLabel>Estágio Evolutivo</FieldLabel>
              <FieldInput
                value={formData.evolutionary_stage}
                onChange={(e) => handleInputChange('evolutionary_stage', e.target.value)}
                placeholder="Ex: correção de nebari, engorda, manutenção..."
                rows={2}
              />
            </FieldGroup>

            <FieldGroup>
              <FieldLabel>Adubação</FieldLabel>
              <FieldInput
                value={formData.fertilization}
                onChange={(e) => handleInputChange('fertilization', e.target.value)}
                placeholder="Ex: adubação orgânica, osmocote, adubação rica em nitrogênio..."
                rows={2}
              />
            </FieldGroup>

            <FieldGroup>
              <FieldLabel>Substrato</FieldLabel>
              <FieldInput
                value={formData.substrate}
                onChange={(e) => handleInputChange('substrate', e.target.value)}
                placeholder="Ex: caqueira, terra vegetal, akadama..."
                rows={2}
              />
            </FieldGroup>
          </FieldsContainer>

          <EditActions>
            <Button 
              onClick={handleSave} 
              disabled={loading}
              size="small"
            >
              <FaCheck />
              {loading ? 'Salvando...' : 'Salvar'}
            </Button>
            <Button 
              variant="secondary" 
              onClick={handleCancel}
              disabled={loading}
              size="small"
            >
              <FaTimes />
              Cancelar
            </Button>
          </EditActions>
        </>
      ) : (
        <>
          {hasAnyData ? (
            <FieldsContainer>
              {plant.evolutionary_stage && (
                <FieldGroup>
                  <FieldLabel>Estágio Evolutivo</FieldLabel>
                  <FieldValue>{plant.evolutionary_stage}</FieldValue>
                </FieldGroup>
              )}

              {plant.fertilization && (
                <FieldGroup>
                  <FieldLabel>Adubação</FieldLabel>
                  <FieldValue>{plant.fertilization}</FieldValue>
                </FieldGroup>
              )}

              {plant.substrate && (
                <FieldGroup>
                  <FieldLabel>Substrato</FieldLabel>
                  <FieldValue>{plant.substrate}</FieldValue>
                </FieldGroup>
              )}
            </FieldsContainer>
          ) : (
            isOwner && (
              <EmptyState>
                Nenhuma informação sobre a fase de cultivo foi adicionada ainda.
                <br />
                Clique em "Editar" para adicionar informações.
              </EmptyState>
            )
          )}
        </>
      )}
    </CultivationSection>
  );
};

export default CultivationPhaseSection;
