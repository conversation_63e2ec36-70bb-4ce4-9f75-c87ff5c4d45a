import React from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { <PERSON>a<PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaCalendarAlt } from 'react-icons/fa';
import { getImageUrl } from '../services/api';
import { useImageUrl } from '../hooks/useImageUrl';
import LikeButton from './LikeButton';

const FeedItemContainer = styled.div`
  background: ${props => props.theme.colors.background.primary};
  border: 1px solid ${props => props.theme.colors.border.light};
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.lg};
  margin-bottom: ${props => props.theme.spacing.md};
  transition: all 0.2s ease;

  &:hover {
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  @media (max-width: 768px) {
    padding: ${props => props.theme.spacing.md};
    margin-bottom: ${props => props.theme.spacing.sm};
  }
`;

const FeedHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  margin-bottom: ${props => props.theme.spacing.md};
`;

const UserAvatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: ${props => props.theme.colors.background.secondary};
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  svg {
    color: ${props => props.theme.colors.text.secondary};
    font-size: 1.2rem;
  }
`;

const UserInfo = styled.div`
  flex: 1;
  min-width: 0;
`;

const UserName = styled.div`
  font-weight: 600;
  color: ${props => props.theme.colors.text.primary};
  font-size: 0.9rem;
`;

const ActivityInfo = styled.div`
  color: ${props => props.theme.colors.text.secondary};
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
`;

const TimeStamp = styled.div`
  color: ${props => props.theme.colors.text.secondary};
  font-size: 0.75rem;
  flex-shrink: 0;
`;

const PlantContent = styled(Link)`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  text-decoration: none;
  color: inherit;
  padding: ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.md};
  transition: background-color 0.2s ease;

  &:hover {
    background: ${props => props.theme.colors.background.secondary};
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: ${props => props.theme.spacing.sm};
  }
`;

const PlantImage = styled.div`
  width: 80px;
  height: 80px;
  border-radius: ${props => props.theme.borderRadius.md};
  background: ${props => props.theme.colors.background.secondary};
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  svg {
    color: ${props => props.theme.colors.text.secondary};
    font-size: 1.5rem;
  }

  @media (max-width: 768px) {
    width: 100%;
    height: 120px;
  }
`;

const PlantInfo = styled.div`
  flex: 1;
  min-width: 0;
`;

const PlantName = styled.h4`
  margin: 0 0 ${props => props.theme.spacing.xs} 0;
  color: ${props => props.theme.colors.text.primary};
  font-size: 1rem;
  font-weight: 600;
`;

const ActivityDescription = styled.p`
  margin: 0;
  color: ${props => props.theme.colors.text.secondary};
  font-size: 0.85rem;
  line-height: 1.4;
`;

const FeedActions = styled.div`
  margin-top: ${props => props.theme.spacing.md};
  padding-top: ${props => props.theme.spacing.sm};
  border-top: 1px solid ${props => props.theme.colors.border.light};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.md};
`;

const FeedItem = ({ activity }) => {
  const [imageError, setImageError] = React.useState(false);
  const { imageUrl: plantImageUrl } = useImageUrl(
    activity.plant_primary_image && !imageError ? activity.plant_primary_image : null,
    'medium',
    activity.plant_id
  );
  const { imageUrl: userAvatarUrl } = useImageUrl(
    activity.user_avatar,
    'thumbnail'
  );

  const formatTimeAgo = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now - date) / (1000 * 60));

    if (diffInMinutes < 1) return 'agora';
    if (diffInMinutes < 60) return `${diffInMinutes}m`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`;
    if (diffInMinutes < 10080) return `${Math.floor(diffInMinutes / 1440)}d`;
    return date.toLocaleDateString('pt-BR');
  };

  const getActivityText = () => {
    switch (activity.activity_type) {
      case 'new_plant':
        return 'adicionou uma nova planta';
      case 'plant_care':
        return `fez um cuidado: ${activity.care_type_display || 'Cuidado'}`;
      default:
        return 'fez uma atividade';
    }
  };

  const getUserDisplayName = () => {
    if (activity.user_username) {
      return `@${activity.user_username}`;
    }
    return `${activity.user_first_name} ${activity.user_last_name}`;
  };

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <FeedItemContainer>
      <FeedHeader>
        <UserAvatar>
          {userAvatarUrl ? (
            <img
              src={userAvatarUrl}
              alt={`${activity.user_first_name} ${activity.user_last_name}`}
            />
          ) : (
            <FaUser />
          )}
        </UserAvatar>
        
        <UserInfo>
          <UserName>
            {activity.user_first_name} {activity.user_last_name}
          </UserName>
          <ActivityInfo>
            <FaLeaf size={12} />
            <span>{getActivityText()}</span>
          </ActivityInfo>
        </UserInfo>
        
        <TimeStamp>
          {formatTimeAgo(activity.created_at)}
        </TimeStamp>
      </FeedHeader>

      {activity.plant_id && (
        <PlantContent to={`/plants/${activity.plant_id}`}>
          <PlantImage>
            {plantImageUrl ? (
              <img
                src={plantImageUrl}
                alt={activity.plant_name}
                onError={handleImageError}
              />
            ) : (
              <FaLeaf />
            )}
          </PlantImage>

          <PlantInfo>
            <PlantName>{activity.plant_name}</PlantName>
            {activity.care_description && (
              <ActivityDescription>
                {activity.care_description}
              </ActivityDescription>
            )}
          </PlantInfo>
        </PlantContent>
      )}

      {/* Ações do Feed - Botão de Curtir */}
      {activity.plant_id && (
        <FeedActions>
          <LikeButton
            plantId={activity.plant_id}
            initialLikesCount={activity.likes_count || 0}
            initialIsLiked={activity.is_liked_by_user || false}
            canLike={true}
          />
        </FeedActions>
      )}
    </FeedItemContainer>
  );
};

export default FeedItem;
