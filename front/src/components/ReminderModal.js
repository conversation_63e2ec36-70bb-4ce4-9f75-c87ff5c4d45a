import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FaTimes, FaBell, FaCalendarPlus } from 'react-icons/fa';
import Button from './UI/Button';
import { toast } from 'react-toastify';
import plantsService from '../services/plantsService';
import remindersService from '../services/remindersService';
import { useReminder } from '../contexts/ReminderContext';

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.xl};
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const ModalTitle = styled.h2`
  margin: 0;
  color: ${props => props.theme.colors.text.primary};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: ${props => props.theme.colors.text.secondary};
  padding: ${props => props.theme.spacing.xs};
  
  &:hover {
    color: ${props => props.theme.colors.text.primary};
  }
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.md};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.xs};
`;

const Label = styled.label`
  font-weight: 500;
  color: ${props => props.theme.colors.text.primary};
`;

const Input = styled.input`
  padding: ${props => props.theme.spacing.sm};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const Select = styled.select`
  padding: ${props => props.theme.spacing.sm};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: 1rem;
  background: white;
  
  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const TextArea = styled.textarea`
  padding: ${props => props.theme.spacing.sm};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: 1rem;
  min-height: 80px;
  resize: vertical;
  
  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const IntervalGroup = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${props => props.theme.spacing.sm};
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
  justify-content: flex-end;
  margin-top: ${props => props.theme.spacing.lg};
`;

const ReminderModal = ({
  isOpen,
  onClose,
  plantId,
  careId = null,
  careType = null,
  onSuccess,
  mode = 'fromCare', // 'fromCare', 'manual', ou 'edit'
  reminder = null // Para modo de edição
}) => {
  const { invalidateReminders } = useReminder();
  const [formData, setFormData] = useState({
    interval_amount: 30,
    interval_unit: 'days',
    care_type: careType || 'poda',
    scheduled_date: '',
    description: ''
  });
  const [loading, setLoading] = useState(false);

  // Preencher dados quando for modo de edição
  useEffect(() => {
    if (mode === 'edit' && reminder) {
      setFormData({
        interval_amount: 30, // Não usado em edição
        interval_unit: 'days', // Não usado em edição
        care_type: reminder.care_type || 'poda',
        scheduled_date: reminder.scheduled_date || '',
        description: reminder.description || ''
      });
    }
  }, [mode, reminder]);

  const careTypes = [
    { value: 'poda', label: 'Poda' },
    { value: 'adubacao', label: 'Adubação' },
    { value: 'transplante', label: 'Transplante' },
    { value: 'aramacao', label: 'Aramação' },
    { value: 'limpeza', label: 'Limpeza' },
    { value: 'tratamento', label: 'Tratamento' },
    { value: 'desfolha', label: 'Desfolha' },
    { value: 'outro', label: 'Outro' }
  ];

  const intervalUnits = [
    { value: 'days', label: 'Dias' },
    { value: 'weeks', label: 'Semanas' },
    { value: 'months', label: 'Meses' },
    { value: 'years', label: 'Anos' }
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    // Validar data para lembretes manuais
    if ((mode === 'manual' || mode === 'edit') && formData.scheduled_date) {
      const selectedDate = new Date(formData.scheduled_date);
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Reset time to start of day

      if (selectedDate < today) {
        toast.error('A data do lembrete não pode ser anterior a hoje');
        setLoading(false);
        return;
      }
    }

    try {
      if (mode === 'edit' && reminder) {
        // Editar lembrete existente
        await remindersService.updateReminder(reminder.id, {
          care_type: formData.care_type,
          scheduled_date: formData.scheduled_date,
          description: formData.description
        });
        toast.success('Lembrete atualizado com sucesso!');
      } else if (mode === 'fromCare' && careId) {
        // Criar lembrete baseado em cuidado
        await plantsService.createReminderFromCare(plantId, careId, {
          interval_amount: parseInt(formData.interval_amount),
          interval_unit: formData.interval_unit,
          description: formData.description
        });
        toast.success('Lembrete criado com sucesso!');
      } else {
        // Criar lembrete manual
        await remindersService.createReminder(plantId, {
          care_type: formData.care_type,
          scheduled_date: formData.scheduled_date,
          description: formData.description
        });
        toast.success('Lembrete criado com sucesso!');
      }

      // Invalidate reminder queries to update ReminderBell immediately
      await invalidateReminders();

      onSuccess && onSuccess();
      onClose();
    } catch (error) {
      console.error('Erro ao processar lembrete:', error);
      toast.error(mode === 'edit' ? 'Erro ao atualizar lembrete' : 'Erro ao criar lembrete');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <ModalOverlay>
      <ModalContent>
        <ModalHeader>
          <ModalTitle>
            <FaBell />
            {mode === 'edit' ? 'Editar Lembrete' :
             mode === 'fromCare' ? 'Criar Lembrete Automático' : 'Criar Lembrete Manual'}
          </ModalTitle>
          <CloseButton onClick={onClose}>
            <FaTimes />
          </CloseButton>
        </ModalHeader>

        <Form onSubmit={handleSubmit}>
          {mode === 'fromCare' ? (
            <>
              <FormGroup>
                <Label>Intervalo para próximo cuidado:</Label>
                <IntervalGroup>
                  <Input
                    type="number"
                    name="interval_amount"
                    value={formData.interval_amount}
                    onChange={handleInputChange}
                    min="1"
                    required
                  />
                  <Select
                    name="interval_unit"
                    value={formData.interval_unit}
                    onChange={handleInputChange}
                    required
                  >
                    {intervalUnits.map(unit => (
                      <option key={unit.value} value={unit.value}>
                        {unit.label}
                      </option>
                    ))}
                  </Select>
                </IntervalGroup>
              </FormGroup>
            </>
          ) : (
            <>
              <FormGroup>
                <Label>Tipo de Cuidado:</Label>
                <Select
                  name="care_type"
                  value={formData.care_type}
                  onChange={handleInputChange}
                  required
                >
                  {careTypes.map(type => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </Select>
              </FormGroup>

              <FormGroup>
                <Label>Data do Lembrete:</Label>
                <Input
                  type="date"
                  name="scheduled_date"
                  value={formData.scheduled_date}
                  onChange={handleInputChange}
                  min={new Date().toISOString().split('T')[0]}
                  required
                />
              </FormGroup>
            </>
          )}

          <FormGroup>
            <Label>Descrição (opcional):</Label>
            <TextArea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="Adicione uma descrição para o lembrete..."
            />
          </FormGroup>

          <ButtonGroup>
            <Button
              type="button"
              variant="secondary"
              onClick={onClose}
              disabled={loading}
            >
              {mode === 'fromCare' ? 'Pular' : 'Cancelar'}
            </Button>
            <Button
              type="submit"
              disabled={loading}
            >
              <FaCalendarPlus />
              {loading ?
                (mode === 'edit' ? 'Atualizando...' : 'Criando...') :
                (mode === 'edit' ? 'Atualizar Lembrete' : 'Criar Lembrete')
              }
            </Button>
          </ButtonGroup>
        </Form>
      </ModalContent>
    </ModalOverlay>
  );
};

export default ReminderModal;
