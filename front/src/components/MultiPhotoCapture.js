import React, { useState, useRef } from 'react';
import styled from 'styled-components';
import { FaCamera, FaImage, FaTimes, FaTrash, FaPlay } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { processImageForUpload, extractExifDate } from '../utils/imageUtils';
import Button from './UI/Button';

const CaptureContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.md};
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
  justify-content: center;
  flex-wrap: wrap;
`;

const CaptureButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
  flex: 1;
  min-width: 120px;
`;

const HiddenInput = styled.input`
  display: none;
`;

const PreviewGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: ${props => props.theme.spacing.sm};
  margin-top: ${props => props.theme.spacing.sm};
`;

const PreviewContainer = styled.div`
  position: relative;
  aspect-ratio: 1;
`;

const PreviewImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: ${props => props.theme.borderRadius.md};
  border: 2px solid ${props => props.theme.colors.border};
`;

const PreviewVideo = styled.video`
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: ${props => props.theme.borderRadius.md};
  border: 2px solid ${props => props.theme.colors.border};
`;

const VideoOverlay = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  pointer-events: none;
`;

const RemoveButton = styled.button`
  position: absolute;
  top: -8px;
  right: -8px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 10px;

  &:hover {
    background: #c82333;
  }
`;

const InfoText = styled.p`
  font-size: 12px;
  color: #666;
  text-align: center;
  margin: 0;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
  justify-content: center;
  margin-top: ${props => props.theme.spacing.sm};
`;

const MultiPhotoCapture = ({
  photos = [],
  onPhotosChange,
  maxPhotos = 10,
  disabled = false,
  allowVideos = false,
  userPlan = null
}) => {
  const fileInputRef = useRef(null);
  const cameraInputRef = useRef(null);

  const handleFileSelect = async (event, isCamera = false) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    // Check if adding these files would exceed the limit
    if (photos.length + files.length > maxPhotos) {
      toast.error(`Máximo de ${maxPhotos} fotos permitidas`);
      return;
    }

    const validFiles = [];
    const newPreviews = [];

    // Process files sequentially to avoid overwhelming the system
    for (const file of files) {
      const isImage = file.type.startsWith('image/');
      const isVideo = file.type.startsWith('video/');

      // Validate file type
      if (!isImage && !isVideo) {
        toast.error(`${file.name} não é uma imagem ou vídeo válido`);
        continue;
      }

      // Check if videos are allowed
      if (isVideo && !allowVideos) {
        toast.error(`Upload de vídeos é exclusivo do plano Premium`);
        continue;
      }

      // Check user plan for videos
      if (isVideo && (!userPlan || !userPlan.allows_videos)) {
        toast.error(`Upload de vídeos é exclusivo do plano Premium. Faça upgrade para acessar esta funcionalidade.`);
        continue;
      }

      // Handle video files (keep existing validation)
      if (isVideo) {
        // Validate file size for videos (100MB limit)
        const maxVideoSize = 100 * 1024 * 1024;
        if (file.size > maxVideoSize) {
          toast.error(`${file.name} é muito grande (máximo 100MB)`);
          continue;
        }

        // Validate video formats
        const allowedVideoTypes = [
          'video/mp4', 'video/quicktime', 'video/x-msvideo',
          'video/x-matroska', 'video/webm', 'video/x-m4v'
        ];
        if (!allowedVideoTypes.includes(file.type)) {
          toast.error(`${file.name} - formato de vídeo não suportado. Use MP4, MOV, AVI, MKV, WEBM ou M4V`);
          continue;
        }

        validFiles.push(file);
      } else {
        // Handle image files with automatic compression
        try {
          // Show processing message for large files
          if (file.size > 5 * 1024 * 1024) {
            toast.info(`Processando ${file.name}...`, { autoClose: 2000 });
          }

          // Process and compress image
          const processedFile = await processImageForUpload(file);

          validFiles.push(processedFile);
        } catch (error) {
          console.error(`Error processing image ${file.name}:`, error);
          toast.error(`Erro ao processar ${file.name}. Arquivo ignorado.`);
          continue;
        }
      }
    }

    // Process valid files to create previews
    validFiles.forEach((file) => {
      const isVideo = file.type.startsWith('video/');

      // Create preview
      if (isVideo) {
        // For videos, create object URL for preview
        const mediaData = {
          id: `${Date.now()}_${Math.random()}`,
          file: file,
          preview: URL.createObjectURL(file),
          isCamera: isCamera,
          isVideo: true,
          type: 'video'
        };

        // Auto-fill date/time for camera videos
        if (isCamera) {
          const now = new Date();
          const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000)
            .toISOString()
            .slice(0, 16); // Format: YYYY-MM-DDTHH:MM
          mediaData.photoDate = localDateTime;
        }

        newPreviews.push(mediaData);

        // Check if all previews are ready
        if (newPreviews.length === validFiles.length) {
          const updatedPhotos = [...photos, ...newPreviews];
          onPhotosChange(updatedPhotos);
        }
      } else {
        // For images, use FileReader
        const reader = new FileReader();
        reader.onload = async (e) => {
          const photoData = {
            id: `${Date.now()}_${Math.random()}`,
            file: file,
            preview: e.target.result,
            isCamera: isCamera,
            isVideo: false,
            type: 'image'
          };

          // Auto-fill date/time for camera photos or extract EXIF for gallery photos
          if (isCamera) {
            const now = new Date();
            const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000)
              .toISOString()
              .slice(0, 16); // Format: YYYY-MM-DDTHH:MM
            photoData.photoDate = localDateTime;
          } else {
            // For gallery images, try to extract EXIF date
            try {
              const exifDate = await extractExifDate(file);
              if (exifDate) {
                // Convert to date-only format (YYYY-MM-DD)
                const dateOnly = exifDate.split('T')[0];
                photoData.photoDate = dateOnly;
              }
            } catch (error) {
              console.log('Error extracting EXIF date:', error);
            }
          }

          newPreviews.push(photoData);

          // When all previews are ready, update the state
          if (newPreviews.length === validFiles.length) {
            const updatedPhotos = [...photos, ...newPreviews];
            onPhotosChange(updatedPhotos);
          }
        };
        reader.readAsDataURL(file);
      }
    });

    // Reset input
    event.target.value = '';
  };

  const handleGalleryClick = () => {
    fileInputRef.current?.click();
  };

  const handleCameraClick = async () => {
    // Detecta se é dispositivo móvel
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    if (isMobile && navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      try {
        // Tenta usar a API de câmera diretamente
        const stream = await navigator.mediaDevices.getUserMedia({
          video: { facingMode: 'environment' },
          audio: false
        });

        // Para o stream imediatamente (só queríamos testar se a câmera está disponível)
        stream.getTracks().forEach(track => track.stop());

        // Agora clica no input file com os atributos corretos
        if (cameraInputRef.current) {
          const input = cameraInputRef.current;
          input.setAttribute('capture', 'environment');
          input.setAttribute('accept', allowVideos ? 'image/*,video/*' : 'image/*');
          input.click();
        }
      } catch (error) {
        console.log('Câmera não disponível, usando input file padrão:', error);
        // Fallback para o comportamento padrão
        if (cameraInputRef.current) {
          cameraInputRef.current.click();
        }
      }
    } else {
      // Desktop ou fallback
      if (cameraInputRef.current) {
        cameraInputRef.current.click();
      }
    }
  };

  const handleRemovePhoto = (index) => {
    const updatedPhotos = photos.filter((_, i) => i !== index);
    onPhotosChange(updatedPhotos);
  };

  const handleRemoveAll = () => {
    onPhotosChange([]);
  };

  const canAddMore = photos.length < maxPhotos;

  return (
    <CaptureContainer>
      {/* Hidden inputs */}
      <HiddenInput
        ref={fileInputRef}
        type="file"
        accept={allowVideos ? "image/*,video/*" : "image/*"}
        multiple
        onChange={(e) => handleFileSelect(e, false)}
        disabled={disabled}
      />
      <HiddenInput
        ref={cameraInputRef}
        type="file"
        accept={allowVideos ? "image/*,video/*" : "image/*"}
        capture="environment"
        captureMode="camera"
        onChange={(e) => handleFileSelect(e, true)}
        disabled={disabled}
      />

      {canAddMore && (
        <ButtonGroup>
          <CaptureButton
            type="button"
            variant="outline"
            onClick={handleCameraClick}
            disabled={disabled}
          >
            <FaCamera />
            {allowVideos ? 'Câmera' : 'Tirar Foto'}
          </CaptureButton>
          <CaptureButton
            type="button"
            variant="outline"
            onClick={handleGalleryClick}
            disabled={disabled}
          >
            <FaImage />
            {allowVideos ? 'Galeria' : 'Galeria'}
          </CaptureButton>
        </ButtonGroup>
      )}

      {photos.length > 0 && (
        <>
          <PreviewGrid>
            {photos.map((photo, index) => (
              <PreviewContainer key={photo.id || index}>
                {photo.isVideo ? (
                  <>
                    <PreviewVideo
                      src={photo.preview}
                      muted
                      preload="metadata"
                    />
                    <VideoOverlay>
                      <FaPlay />
                    </VideoOverlay>
                  </>
                ) : (
                  <PreviewImage src={photo.preview} alt={`Preview ${index + 1}`} />
                )}
                <RemoveButton
                  type="button"
                  onClick={() => handleRemovePhoto(index)}
                  disabled={disabled}
                >
                  <FaTimes />
                </RemoveButton>
              </PreviewContainer>
            ))}
          </PreviewGrid>

          <InfoText>
            {photos.length} de {maxPhotos} {allowVideos ? 'itens' : 'fotos'} selecionados
            {allowVideos && (
              <div style={{ fontSize: '0.8em', color: '#666', marginTop: '0.25rem' }}>
                Fotos: {photos.filter(p => !p.isVideo).length} • Vídeos: {photos.filter(p => p.isVideo).length}
              </div>
            )}
          </InfoText>

          <ActionButtons>
            {canAddMore && (
              <>
                <Button
                  type="button"
                  variant="outline"
                  size="small"
                  onClick={handleCameraClick}
                  disabled={disabled}
                >
                  <FaCamera />
                  {allowVideos ? 'Câmera' : 'Mais Fotos'}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="small"
                  onClick={handleGalleryClick}
                  disabled={disabled}
                >
                  <FaImage />
                  Galeria
                </Button>
              </>
            )}
            {photos.length > 1 && (
              <Button
                type="button"
                variant="secondary"
                size="small"
                onClick={handleRemoveAll}
                disabled={disabled}
              >
                <FaTrash />
                Remover Todas
              </Button>
            )}
          </ActionButtons>
        </>
      )}
    </CaptureContainer>
  );
};

export default MultiPhotoCapture;
