import React, { useState, useRef } from 'react';
import styled from 'styled-components';
import { FaCamera, FaImage, FaTimes, FaTrash, FaPlay } from 'react-icons/fa';
import { toast } from 'react-toastify';
import Button from './UI/Button';

const CaptureContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.md};
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
  justify-content: center;
  flex-wrap: wrap;
`;

const CaptureButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
  flex: 1;
  min-width: 120px;
`;

const HiddenInput = styled.input`
  display: none;
`;

const PreviewGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: ${props => props.theme.spacing.sm};
  margin-top: ${props => props.theme.spacing.sm};
`;

const PreviewContainer = styled.div`
  position: relative;
  aspect-ratio: 1;
`;

const PreviewImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: ${props => props.theme.borderRadius.md};
  border: 2px solid ${props => props.theme.colors.border};
`;

const PreviewVideo = styled.video`
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: ${props => props.theme.borderRadius.md};
  border: 2px solid ${props => props.theme.colors.border};
`;

const VideoOverlay = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  pointer-events: none;
`;

const RemoveButton = styled.button`
  position: absolute;
  top: -8px;
  right: -8px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 10px;

  &:hover {
    background: #c82333;
  }
`;

const InfoText = styled.p`
  font-size: 12px;
  color: #666;
  text-align: center;
  margin: 0;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
  justify-content: center;
  margin-top: ${props => props.theme.spacing.sm};
`;

const MultiPhotoCapture = ({
  photos = [],
  onPhotosChange,
  maxPhotos = 10,
  disabled = false,
  allowVideos = false,
  userPlan = null
}) => {
  const fileInputRef = useRef(null);
  const cameraInputRef = useRef(null);

  const handleFileSelect = (event, isCamera = false) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    // Check if adding these files would exceed the limit
    if (photos.length + files.length > maxPhotos) {
      toast.error(`Máximo de ${maxPhotos} fotos permitidas`);
      return;
    }

    const validFiles = [];
    const newPreviews = [];

    files.forEach((file) => {
      const isImage = file.type.startsWith('image/');
      const isVideo = file.type.startsWith('video/');

      // Validate file type
      if (!isImage && !isVideo) {
        toast.error(`${file.name} não é uma imagem ou vídeo válido`);
        return;
      }

      // Check if videos are allowed
      if (isVideo && !allowVideos) {
        toast.error(`Upload de vídeos é exclusivo do plano Premium`);
        return;
      }

      // Check user plan for videos
      if (isVideo && (!userPlan || !userPlan.allows_videos)) {
        toast.error(`Upload de vídeos é exclusivo do plano Premium. Faça upgrade para acessar esta funcionalidade.`);
        return;
      }

      // Validate file size (images: 5MB, videos: 100MB)
      const maxSize = isVideo ? 100 * 1024 * 1024 : 5 * 1024 * 1024;
      const maxSizeText = isVideo ? '100MB' : '5MB';
      if (file.size > maxSize) {
        toast.error(`${file.name} é muito grande (máximo ${maxSizeText})`);
        return;
      }

      // Validate video formats
      if (isVideo) {
        const allowedVideoTypes = [
          'video/mp4', 'video/quicktime', 'video/x-msvideo',
          'video/x-matroska', 'video/webm', 'video/x-m4v'
        ];
        if (!allowedVideoTypes.includes(file.type)) {
          toast.error(`${file.name} - formato de vídeo não suportado. Use MP4, MOV, AVI, MKV, WEBM ou M4V`);
          return;
        }
      }

      validFiles.push(file);

      // Create preview
      if (isVideo) {
        // For videos, create object URL for preview
        const mediaData = {
          id: `${Date.now()}_${Math.random()}`,
          file: file,
          preview: URL.createObjectURL(file),
          isCamera: isCamera,
          isVideo: true,
          type: 'video'
        };

        // Auto-fill date/time for camera videos
        if (isCamera) {
          const now = new Date();
          const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000)
            .toISOString()
            .slice(0, 16); // Format: YYYY-MM-DDTHH:MM
          mediaData.photoDate = localDateTime;
        }

        newPreviews.push(mediaData);

        // Check if all previews are ready
        if (newPreviews.length === validFiles.length) {
          const updatedPhotos = [...photos, ...newPreviews];
          onPhotosChange(updatedPhotos);
        }
      } else {
        // For images, use FileReader
        const reader = new FileReader();
        reader.onload = (e) => {
          const photoData = {
            id: `${Date.now()}_${Math.random()}`,
            file: file,
            preview: e.target.result,
            isCamera: isCamera,
            isVideo: false,
            type: 'image'
          };

          // Auto-fill date/time for camera photos
          if (isCamera) {
            const now = new Date();
            const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000)
              .toISOString()
              .slice(0, 16); // Format: YYYY-MM-DDTHH:MM
            photoData.photoDate = localDateTime;
          }

          newPreviews.push(photoData);

          // When all previews are ready, update the state
          if (newPreviews.length === validFiles.length) {
            const updatedPhotos = [...photos, ...newPreviews];
            onPhotosChange(updatedPhotos);
          }
        };
        reader.readAsDataURL(file);
      }
    });

    // Reset input
    event.target.value = '';
  };

  const handleGalleryClick = () => {
    fileInputRef.current?.click();
  };

  const handleCameraClick = async () => {
    // Detecta se é dispositivo móvel
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    if (isMobile && navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      try {
        // Tenta usar a API de câmera diretamente
        const stream = await navigator.mediaDevices.getUserMedia({
          video: { facingMode: 'environment' },
          audio: false
        });

        // Para o stream imediatamente (só queríamos testar se a câmera está disponível)
        stream.getTracks().forEach(track => track.stop());

        // Agora clica no input file com os atributos corretos
        if (cameraInputRef.current) {
          const input = cameraInputRef.current;
          input.setAttribute('capture', 'environment');
          input.setAttribute('accept', allowVideos ? 'image/*,video/*' : 'image/*');
          input.click();
        }
      } catch (error) {
        console.log('Câmera não disponível, usando input file padrão:', error);
        // Fallback para o comportamento padrão
        if (cameraInputRef.current) {
          cameraInputRef.current.click();
        }
      }
    } else {
      // Desktop ou fallback
      if (cameraInputRef.current) {
        cameraInputRef.current.click();
      }
    }
  };

  const handleRemovePhoto = (index) => {
    const updatedPhotos = photos.filter((_, i) => i !== index);
    onPhotosChange(updatedPhotos);
  };

  const handleRemoveAll = () => {
    onPhotosChange([]);
  };

  const canAddMore = photos.length < maxPhotos;

  return (
    <CaptureContainer>
      {/* Hidden inputs */}
      <HiddenInput
        ref={fileInputRef}
        type="file"
        accept={allowVideos ? "image/*,video/*" : "image/*"}
        multiple
        onChange={(e) => handleFileSelect(e, false)}
        disabled={disabled}
      />
      <HiddenInput
        ref={cameraInputRef}
        type="file"
        accept={allowVideos ? "image/*,video/*" : "image/*"}
        capture="environment"
        captureMode="camera"
        onChange={(e) => handleFileSelect(e, true)}
        disabled={disabled}
      />

      {canAddMore && (
        <ButtonGroup>
          <CaptureButton
            type="button"
            variant="outline"
            onClick={handleCameraClick}
            disabled={disabled}
          >
            <FaCamera />
            {allowVideos ? 'Câmera' : 'Tirar Foto'}
          </CaptureButton>
          <CaptureButton
            type="button"
            variant="outline"
            onClick={handleGalleryClick}
            disabled={disabled}
          >
            <FaImage />
            {allowVideos ? 'Galeria' : 'Galeria'}
          </CaptureButton>
        </ButtonGroup>
      )}

      {photos.length > 0 && (
        <>
          <PreviewGrid>
            {photos.map((photo, index) => (
              <PreviewContainer key={photo.id || index}>
                {photo.isVideo ? (
                  <>
                    <PreviewVideo
                      src={photo.preview}
                      muted
                      preload="metadata"
                    />
                    <VideoOverlay>
                      <FaPlay />
                    </VideoOverlay>
                  </>
                ) : (
                  <PreviewImage src={photo.preview} alt={`Preview ${index + 1}`} />
                )}
                <RemoveButton
                  type="button"
                  onClick={() => handleRemovePhoto(index)}
                  disabled={disabled}
                >
                  <FaTimes />
                </RemoveButton>
              </PreviewContainer>
            ))}
          </PreviewGrid>

          <InfoText>
            {photos.length} de {maxPhotos} {allowVideos ? 'itens' : 'fotos'} selecionados
            {allowVideos && (
              <div style={{ fontSize: '0.8em', color: '#666', marginTop: '0.25rem' }}>
                Fotos: {photos.filter(p => !p.isVideo).length} • Vídeos: {photos.filter(p => p.isVideo).length}
              </div>
            )}
          </InfoText>

          <ActionButtons>
            {canAddMore && (
              <>
                <Button
                  type="button"
                  variant="outline"
                  size="small"
                  onClick={handleCameraClick}
                  disabled={disabled}
                >
                  <FaCamera />
                  {allowVideos ? 'Câmera' : 'Mais Fotos'}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="small"
                  onClick={handleGalleryClick}
                  disabled={disabled}
                >
                  <FaImage />
                  Galeria
                </Button>
              </>
            )}
            {photos.length > 1 && (
              <Button
                type="button"
                variant="secondary"
                size="small"
                onClick={handleRemoveAll}
                disabled={disabled}
              >
                <FaTrash />
                Remover Todas
              </Button>
            )}
          </ActionButtons>
        </>
      )}
    </CaptureContainer>
  );
};

export default MultiPhotoCapture;
