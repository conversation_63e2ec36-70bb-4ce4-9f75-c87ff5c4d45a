import React, { useState } from 'react';
import styled from 'styled-components';
import { <PERSON>a<PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, FaSpinner } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { useAuth } from '../contexts/AuthContext';
import { getImageUrl } from '../services/api';
import { useImageUrl } from '../hooks/useImageUrl';
import socialInteractionsApi from '../services/socialInteractionsApi';

const CommentContainer = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
  padding: ${props => props.theme.spacing.md};
  border-bottom: 1px solid ${props => props.theme.colors.border.light};
  
  &:last-child {
    border-bottom: none;
  }
  
  @media (max-width: 768px) {
    padding: ${props => props.theme.spacing.sm};
    gap: ${props => props.theme.spacing.xs};
  }
`;

const UserAvatar = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: ${props => props.theme.colors.background.secondary};
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  svg {
    color: ${props => props.theme.colors.text.secondary};
    font-size: 0.9rem;
  }
  
  @media (max-width: 768px) {
    width: 28px;
    height: 28px;
    
    svg {
      font-size: 0.8rem;
    }
  }
`;

const CommentContent = styled.div`
  flex: 1;
  min-width: 0;
`;

const CommentHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${props => props.theme.spacing.xs};
  gap: ${props => props.theme.spacing.sm};
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
  flex: 1;
  min-width: 0;
`;

const UserName = styled.span`
  font-weight: 600;
  color: ${props => props.theme.colors.text.primary};
  font-size: 0.85rem;
  
  @media (max-width: 768px) {
    font-size: 0.8rem;
  }
`;

const Username = styled.span`
  color: ${props => props.theme.colors.text.secondary};
  font-size: 0.75rem;
  
  @media (max-width: 768px) {
    font-size: 0.7rem;
  }
`;

const CommentTime = styled.span`
  color: ${props => props.theme.colors.text.secondary};
  font-size: 0.75rem;
  flex-shrink: 0;
  
  @media (max-width: 768px) {
    font-size: 0.7rem;
  }
`;

const CommentText = styled.p`
  margin: 0;
  color: ${props => props.theme.colors.text.primary};
  font-size: 0.85rem;
  line-height: 1.4;
  word-wrap: break-word;
  
  @media (max-width: 768px) {
    font-size: 0.8rem;
  }
`;

const CommentActions = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
  margin-top: ${props => props.theme.spacing.xs};
`;

const DeleteButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
  padding: ${props => props.theme.spacing.xs};
  background: transparent;
  border: none;
  color: ${props => props.theme.colors.text.secondary};
  font-size: 0.75rem;
  cursor: pointer;
  border-radius: ${props => props.theme.borderRadius.sm};
  transition: all 0.2s ease;
  
  &:hover:not(:disabled) {
    color: ${props => props.theme.colors.error};
    background: ${props => props.theme.colors.error}10;
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  svg {
    font-size: 0.7rem;
  }
`;

const CommentItem = ({ comment, plantOwnerId, onCommentDeleted }) => {
  const { user } = useAuth();
  const [deleting, setDeleting] = useState(false);
  const { imageUrl: userAvatarUrl } = useImageUrl(comment.user_avatar, 'thumbnail');

  const formatTimeAgo = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now - date) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'agora';
    if (diffInMinutes < 60) return `${diffInMinutes}m`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`;
    if (diffInMinutes < 10080) return `${Math.floor(diffInMinutes / 1440)}d`;
    return date.toLocaleDateString('pt-BR');
  };

  const canDelete = user && (
    user.id === comment.user_id || // Comment owner
    user.id === plantOwnerId // Plant owner
  );

  const handleDelete = async () => {
    if (!canDelete || deleting) return;

    if (!window.confirm('Tem certeza que deseja excluir este comentário?')) {
      return;
    }

    setDeleting(true);

    try {
      await socialInteractionsApi.deletePlantComment(comment.id);
      toast.success('Comentário excluído com sucesso!');
      
      if (onCommentDeleted) {
        onCommentDeleted(comment.id);
      }
    } catch (error) {
      console.error('Error deleting comment:', error);
      const errorMessage = error.response?.data?.detail || 'Erro ao excluir comentário';
      toast.error(errorMessage);
    } finally {
      setDeleting(false);
    }
  };

  const getUserDisplayName = () => {
    return `${comment.user_first_name} ${comment.user_last_name}`;
  };

  return (
    <CommentContainer>
      <UserAvatar>
        {userAvatarUrl ? (
          <img src={userAvatarUrl} alt={getUserDisplayName()} />
        ) : (
          <FaUser />
        )}
      </UserAvatar>
      
      <CommentContent>
        <CommentHeader>
          <UserInfo>
            <UserName>{getUserDisplayName()}</UserName>
            {comment.user_username && (
              <Username>@{comment.user_username}</Username>
            )}
          </UserInfo>
          <CommentTime>
            {formatTimeAgo(comment.created_at)}
          </CommentTime>
        </CommentHeader>
        
        <CommentText>{comment.content}</CommentText>
        
        {canDelete && (
          <CommentActions>
            <DeleteButton
              onClick={handleDelete}
              disabled={deleting}
              title="Excluir comentário"
            >
              {deleting ? (
                <FaSpinner className="spinning" />
              ) : (
                <FaTrash />
              )}
              {deleting ? 'Excluindo...' : 'Excluir'}
            </DeleteButton>
          </CommentActions>
        )}
      </CommentContent>
    </CommentContainer>
  );
};

export default CommentItem;
