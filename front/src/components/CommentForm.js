import React, { useState } from 'react';
import styled from 'styled-components';
import { <PERSON>aCom<PERSON>, FaSpinner } from 'react-icons/fa';
import { toast } from 'react-toastify';
import socialInteractionsApi from '../services/socialInteractionsApi';

const CommentFormContainer = styled.div`
  background: ${props => props.theme.colors.background.secondary};
  border-radius: ${props => props.theme.borderRadius.md};
  padding: ${props => props.theme.spacing.md};
  margin-bottom: ${props => props.theme.spacing.md};
`;

const CommentTextarea = styled.textarea`
  width: 100%;
  min-height: 80px;
  padding: ${props => props.theme.spacing.sm};
  border: 1px solid ${props => props.theme.colors.border.light};
  border-radius: ${props => props.theme.borderRadius.md};
  font-family: inherit;
  font-size: 0.9rem;
  resize: vertical;
  background: ${props => props.theme.colors.background.primary};
  color: ${props => props.theme.colors.text.primary};
  
  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 2px ${props => props.theme.colors.primary}20;
  }
  
  &::placeholder {
    color: ${props => props.theme.colors.text.secondary};
  }
  
  @media (max-width: 768px) {
    min-height: 60px;
    font-size: 0.85rem;
  }
`;

const CommentActions = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: ${props => props.theme.spacing.sm};
  gap: ${props => props.theme.spacing.sm};
`;

const CharacterCount = styled.div`
  font-size: 0.75rem;
  color: ${props => props.count > 900 ? props.theme.colors.error : props.theme.colors.text.secondary};
`;

const CommentButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  background: ${props => props.theme.colors.primary};
  border: none;
  border-radius: ${props => props.theme.borderRadius.md};
  color: white;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover:not(:disabled) {
    background: ${props => props.theme.colors.primaryDark};
    transform: translateY(-1px);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
  
  svg {
    font-size: 0.8rem;
  }
  
  @media (max-width: 768px) {
    padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
    font-size: 0.8rem;
  }
`;

const CommentForm = ({ plantId, onCommentAdded, canComment = true }) => {
  const [content, setContent] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!content.trim() || loading || !canComment) return;
    
    if (content.length > 1000) {
      toast.error('Comentário muito longo (máximo 1000 caracteres)');
      return;
    }

    setLoading(true);

    try {
      const comment = await socialInteractionsApi.createPlantComment(plantId, content.trim());
      
      setContent('');
      toast.success('Comentário adicionado com sucesso!');
      
      if (onCommentAdded) {
        onCommentAdded(comment);
      }
    } catch (error) {
      console.error('Error creating comment:', error);
      const errorMessage = error.response?.data?.detail || 'Erro ao adicionar comentário';
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      handleSubmit(e);
    }
  };

  if (!canComment) {
    return null;
  }

  return (
    <CommentFormContainer>
      <form onSubmit={handleSubmit}>
        <CommentTextarea
          value={content}
          onChange={(e) => setContent(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Escreva um comentário sobre esta planta..."
          disabled={loading}
          maxLength={1000}
        />
        
        <CommentActions>
          <CharacterCount count={content.length}>
            {content.length}/1000 caracteres
          </CharacterCount>
          
          <CommentButton
            type="submit"
            disabled={!content.trim() || loading || content.length > 1000}
          >
            {loading ? (
              <>
                <FaSpinner className="spinning" />
                Enviando...
              </>
            ) : (
              <>
                <FaComment />
                Comentar
              </>
            )}
          </CommentButton>
        </CommentActions>
      </form>
    </CommentFormContainer>
  );
};

export default CommentForm;
