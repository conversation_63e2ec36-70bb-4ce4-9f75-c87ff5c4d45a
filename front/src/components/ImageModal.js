import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FaTimes, FaChevronLeft, FaChevronRight, FaDownload, FaExpand } from 'react-icons/fa';

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
`;

const ModalContent = styled.div`
  position: relative;
  width: 95vw;
  height: 95vh;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const ModalImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: ${props => props.theme.borderRadius.md};
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
`;

const CloseButton = styled.button`
  position: absolute;
  top: -50px;
  right: 0;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 24px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  @media (max-width: 768px) {
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
  }
`;

const NavigationButton = styled.button`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 24px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  
  ${props => props.direction === 'left' ? 'left: -70px;' : 'right: -70px;'}

  &:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-50%) scale(1.1);
  }

  &:disabled {
    opacity: 0.3;
    cursor: not-allowed;
  }

  @media (max-width: 768px) {
    ${props => props.direction === 'left' ? 'left: 10px;' : 'right: 10px;'}
    background: rgba(0, 0, 0, 0.7);
  }
`;

const ImageInfo = styled.div`
  position: absolute;
  bottom: -60px;
  left: 0;
  right: 0;
  color: white;
  text-align: center;
  background: rgba(0, 0, 0, 0.7);
  padding: 10px;
  border-radius: ${props => props.theme.borderRadius.md};

  @media (max-width: 768px) {
    bottom: 10px;
    left: 10px;
    right: 10px;
  }
`;

const ImageTitle = styled.div`
  font-weight: bold;
  margin-bottom: 4px;
`;

const ImageDescription = styled.div`
  font-size: 14px;
  opacity: 0.8;
`;

const ActionButtons = styled.div`
  position: absolute;
  top: -50px;
  left: 0;
  display: flex;
  gap: 10px;

  @media (max-width: 768px) {
    top: 10px;
    left: 10px;
  }
`;

const ActionButton = styled.button`
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 16px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.3);
  }

  @media (max-width: 768px) {
    background: rgba(0, 0, 0, 0.7);
  }
`;

const ImageModal = ({ 
  isOpen, 
  onClose, 
  images = [], 
  currentIndex = 0, 
  onIndexChange 
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(currentIndex);

  useEffect(() => {
    setCurrentImageIndex(currentIndex);
  }, [currentIndex]);

  useEffect(() => {
    const handleKeyPress = (e) => {
      if (!isOpen) return;
      
      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowLeft':
          handlePrevious();
          break;
        case 'ArrowRight':
          handleNext();
          break;
        default:
          break;
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [isOpen, currentImageIndex]);

  const handlePrevious = () => {
    if (currentImageIndex > 0) {
      const newIndex = currentImageIndex - 1;
      setCurrentImageIndex(newIndex);
      onIndexChange?.(newIndex);
    }
  };

  const handleNext = () => {
    if (currentImageIndex < images.length - 1) {
      const newIndex = currentImageIndex + 1;
      setCurrentImageIndex(newIndex);
      onIndexChange?.(newIndex);
    }
  };

  const handleDownload = () => {
    const currentImage = images[currentImageIndex];
    if (currentImage) {
      const link = document.createElement('a');
      link.href = currentImage.src;
      link.download = currentImage.title || 'image';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleFullscreen = () => {
    const currentImage = images[currentImageIndex];
    if (currentImage) {
      window.open(currentImage.src, '_blank');
    }
  };

  if (!isOpen || !images.length) return null;

  const currentImage = images[currentImageIndex];

  return (
    <ModalOverlay>
      <ModalContent>
        <ModalImage 
          src={currentImage.src} 
          alt={currentImage.title || 'Imagem'} 
        />
        
        <CloseButton onClick={onClose}>
          <FaTimes />
        </CloseButton>

        <ActionButtons>
          <ActionButton onClick={handleDownload} title="Baixar imagem">
            <FaDownload />
          </ActionButton>
          <ActionButton onClick={handleFullscreen} title="Abrir em nova aba">
            <FaExpand />
          </ActionButton>
        </ActionButtons>

        {images.length > 1 && (
          <>
            <NavigationButton 
              direction="left"
              onClick={handlePrevious}
              disabled={currentImageIndex === 0}
              title="Imagem anterior"
            >
              <FaChevronLeft />
            </NavigationButton>
            
            <NavigationButton 
              direction="right"
              onClick={handleNext}
              disabled={currentImageIndex === images.length - 1}
              title="Próxima imagem"
            >
              <FaChevronRight />
            </NavigationButton>
          </>
        )}

        {(currentImage.title || currentImage.description) && (
          <ImageInfo>
            {currentImage.title && (
              <ImageTitle>{currentImage.title}</ImageTitle>
            )}
            {currentImage.description && (
              <ImageDescription>{currentImage.description}</ImageDescription>
            )}
            {images.length > 1 && (
              <div style={{ marginTop: '8px', fontSize: '12px' }}>
                {currentImageIndex + 1} de {images.length}
              </div>
            )}
          </ImageInfo>
        )}
      </ModalContent>
    </ModalOverlay>
  );
};

export default ImageModal;
