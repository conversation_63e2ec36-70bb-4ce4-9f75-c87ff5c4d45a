import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { FaCalendar<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaExclamationTriangle } from 'react-icons/fa';
import { useQuery } from 'react-query';
import remindersService from '../services/remindersService';
import { toast } from 'react-toastify';

const ReminderContainer = styled.div`
  position: relative;
  display: inline-block;
`;

const BellButton = styled.button`
  background: none;
  border: none;
  color: #333;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  position: relative;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f0f0f0;
    transform: scale(1.1);
  }

  @media (max-width: 768px) {
    font-size: 18px;
    padding: 6px;
  }
`;

const Badge = styled.span`
  position: absolute;
  top: 2px;
  right: 2px;
  background-color: #e74c3c;
  color: white;
  border-radius: 50%;
  padding: 2px 6px;
  font-size: 12px;
  font-weight: bold;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;

  @media (max-width: 768px) {
    font-size: 10px;
    min-width: 16px;
    height: 16px;
    padding: 1px 4px;
  }
`;

const Dropdown = styled.div`
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 320px;
  max-height: 400px;
  overflow-y: auto;
  z-index: 1000;

  @media (max-width: 768px) {
    width: 280px;
    right: -20px;
  }
`;

const DropdownHeader = styled.div`
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #f8f9fa;
  border-radius: 8px 8px 0 0;
`;

const ReminderItem = styled.div`
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #f8f9fa;
  }
`;

const ReminderContent = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
`;

const ReminderInfo = styled.div`
  flex: 1;
`;

const PlantName = styled.div`
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
`;

const CareType = styled.div`
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
`;

const DueDate = styled.div`
  font-size: 12px;
  color: ${props => props.isOverdue ? '#e74c3c' : '#666'};
  display: flex;
  align-items: center;
  gap: 4px;
`;

const ActionButton = styled.button`
  background-color: #2ecc71;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #27ae60;
  }
`;

const EmptyState = styled.div`
  padding: 40px 20px;
  text-align: center;
  color: #999;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
`;

const ReminderBell = () => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  const buttonRef = useRef(null);

  // Buscar lembretes próximos e atrasados
  const { data: upcomingReminders = [], refetch: refetchUpcoming } = useQuery(
    'upcomingReminders',
    () => remindersService.getUpcomingReminders(30).then(res => res.data),
    {
      refetchInterval: 5 * 60 * 1000, // Refetch a cada 5 minutos
    }
  );

  const { data: overdueReminders = [], refetch: refetchOverdue } = useQuery(
    'overdueReminders',
    () => remindersService.getOverdueReminders().then(res => res.data),
    {
      refetchInterval: 5 * 60 * 1000, // Refetch a cada 5 minutos
    }
  );

  const allReminders = [...overdueReminders, ...upcomingReminders];
  const totalCount = allReminders.length;

  // Fechar dropdown ao clicar fora
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        dropdownRef.current && 
        !dropdownRef.current.contains(event.target) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleMarkAsCompleted = async (reminderId, e) => {
    e.stopPropagation();
    try {
      await remindersService.completeReminder(reminderId);
      toast.success('Lembrete marcado como concluído!');
      // Refetch all queries to ensure consistency
      await Promise.all([
        refetchUpcoming(),
        refetchOverdue()
      ]);
    } catch (error) {
      console.error('Erro ao marcar lembrete como concluído:', error);
      toast.error('Erro ao marcar lembrete como concluído');
    }
  };

  const formatDueDate = (reminder) => {
    const daysUntilDue = reminder.days_until_due;
    
    if (daysUntilDue < 0) {
      return `${Math.abs(daysUntilDue)} dias atrasado`;
    } else if (daysUntilDue === 0) {
      return 'Hoje';
    } else if (daysUntilDue === 1) {
      return 'Amanhã';
    } else {
      return `Em ${daysUntilDue} dias`;
    }
  };

  return (
    <ReminderContainer>
      <BellButton
        ref={buttonRef}
        onClick={() => setIsOpen(!isOpen)}
        title="Lembretes de cuidados"
      >
        <FaCalendarAlt />
        {totalCount > 0 && <Badge>{totalCount > 99 ? '99+' : totalCount}</Badge>}
      </BellButton>

      {isOpen && (
        <Dropdown ref={dropdownRef}>
          <DropdownHeader>
            <FaCalendarAlt />
            Lembretes de Cuidados ({totalCount})
          </DropdownHeader>

          {allReminders.length === 0 ? (
            <EmptyState>
              <FaCalendarAlt size={32} style={{ marginBottom: '8px', opacity: 0.5 }} />
              <div>Nenhum lembrete pendente</div>
            </EmptyState>
          ) : (
            allReminders.map((reminder) => (
              <ReminderItem key={reminder.id}>
                <ReminderContent>
                  <ReminderInfo>
                    <PlantName>{reminder.plant_name}</PlantName>
                    <CareType>{reminder.care_type_display}</CareType>
                    <DueDate isOverdue={reminder.is_overdue}>
                      {reminder.is_overdue && <FaExclamationTriangle />}
                      {formatDueDate(reminder)}
                    </DueDate>
                  </ReminderInfo>

                  <ActionButton
                    onClick={(e) => handleMarkAsCompleted(reminder.id, e)}
                    title="Marcar como concluído"
                  >
                    <FaCheck />
                    Feito
                  </ActionButton>
                </ReminderContent>
              </ReminderItem>
            ))
          )}
        </Dropdown>
      )}
    </ReminderContainer>
  );
};

export default ReminderBell;
