import React from 'react';
import styled from 'styled-components';

const AlertContainer = styled.div`
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  padding: 16px 20px;
  margin: 16px 0;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
  border-left: 4px solid #ff4757;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }
`;

const AlertHeader = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  position: relative;
  z-index: 1;
`;

const AlertIcon = styled.div`
  font-size: 24px;
  margin-right: 12px;
  animation: pulse 2s infinite;

  @keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
  }
`;

const AlertTitle = styled.h3`
  margin: 0;
  font-size: 18px;
  font-weight: 600;
`;

const AlertContent = styled.div`
  position: relative;
  z-index: 1;
`;

const AlertText = styled.p`
  margin: 0 0 12px 0;
  font-size: 14px;
  line-height: 1.5;
  opacity: 0.95;
`;

const AlertActions = styled.div`
  display: flex;
  gap: 12px;
  margin-top: 16px;
`;

const ActionButton = styled.button`
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
  }

  &.primary {
    background: white;
    color: #ff6b6b;
    border-color: white;

    &:hover {
      background: #f8f9fa;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
`;

const CountdownText = styled.span`
  font-weight: 600;
  font-size: 16px;
  color: #fff;
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 4px;
  margin: 0 4px;
`;

const GracePeriodAlert = ({ gracePeriodInfo, onRenewClick }) => {
  if (!gracePeriodInfo || !gracePeriodInfo.is_in_grace_period) {
    return null;
  }

  const { days_remaining, grace_period_end } = gracePeriodInfo;
  
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getUrgencyMessage = (days) => {
    if (days <= 3) {
      return "⚠️ URGENTE: Sua assinatura será cancelada em breve!";
    } else if (days <= 7) {
      return "⏰ ATENÇÃO: Sua assinatura precisa ser renovada!";
    } else {
      return "📋 AVISO: Sua assinatura expirou";
    }
  };

  return (
    <AlertContainer>
      <AlertHeader>
        <AlertIcon>⚠️</AlertIcon>
        <AlertTitle>{getUrgencyMessage(days_remaining)}</AlertTitle>
      </AlertHeader>
      
      <AlertContent>
        <AlertText>
          Sua assinatura expirou, mas você ainda tem acesso aos recursos premium por mais
          <CountdownText>{days_remaining} {days_remaining === 1 ? 'dia' : 'dias'}</CountdownText>
          para regularizar sua situação.
        </AlertText>
        
        <AlertText>
          <strong>Data limite:</strong> {formatDate(grace_period_end)}
        </AlertText>
        
        <AlertText>
          Após esse período, sua conta será automaticamente convertida para o plano gratuito 
          e você perderá acesso aos recursos premium.
        </AlertText>
        
        {onRenewClick && (
          <AlertActions>
            <ActionButton className="primary" onClick={onRenewClick}>
              🔄 Renovar Assinatura
            </ActionButton>
          </AlertActions>
        )}
      </AlertContent>
    </AlertContainer>
  );
};

export default GracePeriodAlert;
