import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>aLea<PERSON> } from 'react-icons/fa';
import { toast } from 'react-toastify';
import FeedItem from './FeedItem';
import socialInteractionsApi from '../services/socialInteractionsApi';

const FeedContainer = styled.div`
  width: 100%;
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: ${props => props.theme.spacing.xl};
  color: ${props => props.theme.colors.text.secondary};
  
  svg {
    margin-right: ${props => props.theme.spacing.sm};
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${props => props.theme.spacing.xl};
  color: ${props => props.theme.colors.text.secondary};
  
  svg {
    font-size: 3rem;
    margin-bottom: ${props => props.theme.spacing.md};
    color: ${props => props.theme.colors.text.secondary};
  }
  
  h3 {
    margin: 0 0 ${props => props.theme.spacing.sm} 0;
    color: ${props => props.theme.colors.text.primary};
  }
  
  p {
    margin: 0;
    line-height: 1.5;
  }
`;

const LoadMoreButton = styled.button`
  width: 100%;
  padding: ${props => props.theme.spacing.md};
  background: ${props => props.theme.colors.background.secondary};
  border: 1px solid ${props => props.theme.colors.border.light};
  border-radius: ${props => props.theme.borderRadius.md};
  color: ${props => props.theme.colors.text.primary};
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: ${props => props.theme.spacing.md};
  
  &:hover:not(:disabled) {
    background: ${props => props.theme.colors.primary};
    color: white;
    border-color: ${props => props.theme.colors.primary};
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  svg {
    margin-right: ${props => props.theme.spacing.xs};
  }
`;

const ErrorMessage = styled.div`
  background: ${props => props.theme.colors.error}20;
  border: 1px solid ${props => props.theme.colors.error};
  border-radius: ${props => props.theme.borderRadius.md};
  padding: ${props => props.theme.spacing.md};
  color: ${props => props.theme.colors.error};
  text-align: center;
  margin-bottom: ${props => props.theme.spacing.md};
`;

const FeedList = () => {
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [total, setTotal] = useState(0);

  const loadFeed = async (pageNum = 1, append = false) => {
    try {
      if (pageNum === 1) {
        setLoading(true);
        setError(null);
      } else {
        setLoadingMore(true);
      }

      const response = await socialInteractionsApi.getFeed(pageNum, 20);
      
      if (append) {
        setActivities(prev => [...prev, ...response.activities]);
      } else {
        setActivities(response.activities);
      }
      
      setTotal(response.total);
      setHasMore(response.has_next);
      setPage(pageNum);
      
    } catch (error) {
      console.error('Error loading feed:', error);
      const errorMessage = error.response?.data?.detail || 'Erro ao carregar feed';
      setError(errorMessage);
      
      if (pageNum === 1) {
        toast.error(errorMessage);
      }
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const handleLoadMore = () => {
    if (!loadingMore && hasMore) {
      loadFeed(page + 1, true);
    }
  };

  const handleRefresh = () => {
    setPage(1);
    setHasMore(true);
    loadFeed(1, false);
  };

  useEffect(() => {
    loadFeed();
  }, []);

  if (loading) {
    return (
      <FeedContainer>
        <LoadingContainer>
          <FaSpinner />
          Carregando feed...
        </LoadingContainer>
      </FeedContainer>
    );
  }

  if (error && activities.length === 0) {
    return (
      <FeedContainer>
        <ErrorMessage>
          {error}
        </ErrorMessage>
      </FeedContainer>
    );
  }

  if (activities.length === 0) {
    return (
      <FeedContainer>
        <EmptyState>
          <FaUsers />
          <h3>Seu feed está vazio</h3>
          <p>
            Siga outros usuários para ver suas atividades aqui!<br />
            Quando eles adicionarem plantas ou fizerem cuidados,<br />
            você verá as atualizações neste feed.
          </p>
        </EmptyState>
      </FeedContainer>
    );
  }

  return (
    <FeedContainer>
      {activities.map((activity) => (
        <FeedItem key={activity.id} activity={activity} />
      ))}
      
      {hasMore && (
        <LoadMoreButton 
          onClick={handleLoadMore} 
          disabled={loadingMore}
        >
          {loadingMore ? (
            <>
              <FaSpinner />
              Carregando...
            </>
          ) : (
            <>
              <FaLeaf />
              Carregar mais atividades
            </>
          )}
        </LoadMoreButton>
      )}
      
      {!hasMore && activities.length > 0 && (
        <EmptyState>
          <p>Você viu todas as atividades recentes!</p>
        </EmptyState>
      )}
    </FeedContainer>
  );
};

export default FeedList;
