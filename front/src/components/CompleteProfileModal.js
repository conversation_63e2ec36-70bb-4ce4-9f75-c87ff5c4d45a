import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Fa<PERSON>ser, FaCheck, FaTimes, FaSpinner } from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';
import { useSocial } from '../contexts/SocialContext';
import { checkUsernameAvailabilityPublic } from '../services/socialApi';
import Button from './UI/Button';
import Modal from './UI/Modal';

const ModalContent = styled.div`
  padding: ${props => props.theme.spacing.xl};
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const Icon = styled.div`
  color: ${props => props.theme.colors.primary};
  margin-bottom: ${props => props.theme.spacing.lg};
  display: flex;
  justify-content: center;
`;

const Title = styled.h2`
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing.md};
`;

const Description = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  margin-bottom: 0;
  line-height: 1.5;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.lg};
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${props => props.theme.spacing.md};

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.xs};
`;

const Label = styled.label`
  color: ${props => props.theme.colors.text.primary};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  font-size: 0.9rem;
`;

const Input = styled.input`
  width: 100%;
  padding: ${props => props.theme.spacing.md};
  border: 2px solid ${props =>
    props.error ? props.theme.colors.error :
    props.theme.colors.border.light
  };
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: 1rem;
  transition: all 0.2s ease;

  &:focus {
    outline: none;
    border-color: ${props =>
      props.error ? props.theme.colors.error :
      props.theme.colors.primary
    };
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: ${props => props.theme.spacing.md};
  border: 2px solid ${props => props.theme.colors.border.light};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: 1rem;
  min-height: 80px;
  resize: vertical;
  font-family: inherit;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const UsernameContainer = styled.div`
  position: relative;
`;

const UsernameInput = styled(Input)`
  padding-right: 40px;
  border-color: ${props =>
    props.isValid === false ? props.theme.colors.error :
    props.isValid === true ? props.theme.colors.success :
    props.theme.colors.border.light
  };

  &:focus {
    border-color: ${props =>
      props.isValid === false ? props.theme.colors.error :
      props.isValid === true ? props.theme.colors.success :
      props.theme.colors.primary
    };
  }
`;

const ValidationIcon = styled.div`
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: ${props =>
    props.isChecking ? props.theme.colors.primary :
    props.isValid === true ? props.theme.colors.success :
    props.isValid === false ? props.theme.colors.error :
    props.theme.colors.text.secondary
  };
  font-size: 1.2rem;

  ${props => props.isChecking && `
    animation: spin 1s linear infinite;
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
  `}
`;

const ValidationMessage = styled.p`
  color: ${props =>
    props.type === 'error' ? props.theme.colors.error :
    props.type === 'success' ? props.theme.colors.success :
    props.theme.colors.text.secondary
  };
  font-size: 0.85rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 4px;
`;

const UsernameHelp = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  font-size: 0.85rem;
  margin: 0;
`;

const ErrorMessage = styled.span`
  color: ${props => props.theme.colors.error};
  font-size: 0.85rem;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  justify-content: center;
  margin-top: ${props => props.theme.spacing.xl};
`;

const RequiredField = styled.span`
  color: ${props => props.theme.colors.error};
  margin-left: 2px;
`;

const CompleteProfileModal = ({ isOpen, onClose, onSuccess }) => {
  const { user, updateProfile } = useAuth();
  const [formData, setFormData] = useState({
    username: '',
    first_name: '',
    last_name: '',
    bio: '',
    city: '',
    state: '',
    country: ''
  });
  const [usernameValidation, setUsernameValidation] = useState({
    isValid: null,
    message: '',
    isChecking: false
  });
  const [checkTimeout, setCheckTimeout] = useState(null);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // Inicializar dados do usuário
  useEffect(() => {
    if (user && isOpen) {
      const initialData = {
        username: '',
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        bio: user.bio || '',
        city: user.city || '',
        state: user.state || '',
        country: user.country || ''
      };

      setFormData(initialData);

      // Gerar username inicial baseado no nome se disponível
      if (user.first_name && user.last_name) {
        const firstName = user.first_name.toLowerCase().replace(/[^a-z]/g, '');
        const lastName = user.last_name.toLowerCase().replace(/[^a-z]/g, '');

        if (firstName.length >= 2 && lastName.length >= 2) {
          const baseUsername = `${firstName}${lastName}`;
          const truncatedBase = baseUsername.length > 25 ? baseUsername.substring(0, 25) : baseUsername;

          if (truncatedBase.length >= 3) {
            setFormData(prev => ({ ...prev, username: truncatedBase }));
            return;
          }
        }
      }

      // Se não conseguiu gerar do nome, usar aleatório
      const randomUsername = generateRandomUsername();
      setFormData(prev => ({ ...prev, username: randomUsername }));
    }
  }, [user, isOpen]);

  // Função para gerar username baseado no nome do usuário
  const generateRandomUsername = () => {
    // Tentar usar nome e sobrenome primeiro
    if (formData.first_name && formData.last_name) {
      // Limpar e concatenar nome e sobrenome
      const firstName = formData.first_name.toLowerCase().replace(/[^a-z]/g, '');
      const lastName = formData.last_name.toLowerCase().replace(/[^a-z]/g, '');

      if (firstName.length >= 2 && lastName.length >= 2) {
        const baseUsername = `${firstName}${lastName}`;

        // Limitar a 25 caracteres para deixar espaço para números
        const truncatedBase = baseUsername.length > 25 ? baseUsername.substring(0, 25) : baseUsername;

        if (truncatedBase.length >= 3) {
          return truncatedBase;
        }
      }
    }

    // Se nome não disponível ou muito curto, gerar string completamente aleatória
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';

    // Começar sempre com uma letra
    result += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)];

    // Adicionar 7-11 caracteres aleatórios (total 8-12 caracteres)
    const length = Math.floor(Math.random() * 4) + 7; // 7 a 10 caracteres adicionais
    for (let i = 0; i < length; i++) {
      result += chars[Math.floor(Math.random() * chars.length)];
    }

    return result;
  };

  // Função para validar formato do username
  const validateUsernameFormat = (value) => {
    if (!value || value.trim() === '') {
      return { isValid: null, message: '', isChecking: false };
    }

    const cleanValue = value.trim();

    if (cleanValue.length < 3) {
      return { isValid: false, message: 'Username deve ter pelo menos 3 caracteres', isChecking: false };
    }

    if (cleanValue.length > 30) {
      return { isValid: false, message: 'Username deve ter no máximo 30 caracteres', isChecking: false };
    }

    const validPattern = /^[a-zA-Z0-9_]+$/;
    if (!validPattern.test(cleanValue)) {
      return { isValid: false, message: 'Apenas letras, números e _ são permitidos', isChecking: false };
    }

    if (cleanValue.startsWith('_') || cleanValue.endsWith('_')) {
      return { isValid: false, message: 'Username não pode começar ou terminar com _', isChecking: false };
    }

    if (cleanValue.includes('__')) {
      return { isValid: false, message: 'Username não pode ter __ consecutivos', isChecking: false };
    }

    return { isValid: null, message: 'Verificando disponibilidade...', isChecking: true };
  };

  // Função para verificar disponibilidade
  const checkUsernameWithDebounce = async (value) => {
    const cleanValue = value.trim();

    try {
      const result = await checkUsernameAvailabilityPublic(cleanValue);

      if (result.available) {
        return { isValid: true, message: 'Username disponível!', isChecking: false };
      } else {
        const messages = {
          already_taken: 'Username já está em uso',
          invalid_length: 'Comprimento inválido',
          invalid_characters: 'Caracteres inválidos',
          invalid_format: 'Formato inválido'
        };
        return {
          isValid: false,
          message: messages[result.reason] || 'Username não disponível',
          isChecking: false
        };
      }
    } catch (error) {
      console.error('Error checking username:', error);
      return { isValid: false, message: 'Erro ao verificar disponibilidade', isChecking: false };
    }
  };

  // Effect para validar username em tempo real
  useEffect(() => {
    if (!formData.username) {
      setUsernameValidation({ isValid: null, message: '', isChecking: false });
      return;
    }

    const formatValidation = validateUsernameFormat(formData.username);
    setUsernameValidation(formatValidation);

    if (formatValidation.isChecking) {
      if (checkTimeout) {
        clearTimeout(checkTimeout);
      }

      const timeout = setTimeout(async () => {
        const availabilityValidation = await checkUsernameWithDebounce(formData.username);
        setUsernameValidation(availabilityValidation);
      }, 800);

      setCheckTimeout(timeout);
    }

    return () => {
      if (checkTimeout) {
        clearTimeout(checkTimeout);
      }
    };
  }, [formData.username]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    
    if (name === 'username') {
      // Limpar caracteres inválidos automaticamente
      let cleanValue = value.replace(/[^a-zA-Z0-9_]/g, '');
      // Limitar comprimento
      if (cleanValue.length > 30) {
        cleanValue = cleanValue.substring(0, 30);
      }
      setFormData(prev => ({ ...prev, [name]: cleanValue }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
    
    // Limpar erro do campo
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };



  const validateForm = () => {
    const newErrors = {};

    if (!formData.username.trim()) {
      newErrors.username = 'Username é obrigatório';
    } else if (!usernameValidation.isValid) {
      newErrors.username = 'Username inválido ou não disponível';
    }

    if (!formData.first_name.trim()) {
      newErrors.first_name = 'Nome é obrigatório';
    }

    if (!formData.last_name.trim()) {
      newErrors.last_name = 'Sobrenome é obrigatório';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const result = await updateProfile(formData);

      if (result.success) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error updating profile:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} closeOnOverlayClick={false}>
      <ModalContent>
        <Header>
          <Icon>
            <FaUser size={48} />
          </Icon>
          
          <Title>Complete seu Cadastro</Title>
          
          <Description>
            Para finalizar seu cadastro, preencha as informações abaixo. 
            Os campos marcados com * são obrigatórios.
          </Description>
        </Header>

        <Form onSubmit={handleSubmit}>
          <FormRow>
            <FormGroup>
              <Label htmlFor="first_name">
                Nome <RequiredField>*</RequiredField>
              </Label>
              <Input
                id="first_name"
                name="first_name"
                type="text"
                value={formData.first_name}
                onChange={handleInputChange}
                placeholder="Seu nome"
                error={errors.first_name}
              />
              {errors.first_name && <ErrorMessage>{errors.first_name}</ErrorMessage>}
            </FormGroup>

            <FormGroup>
              <Label htmlFor="last_name">
                Sobrenome <RequiredField>*</RequiredField>
              </Label>
              <Input
                id="last_name"
                name="last_name"
                type="text"
                value={formData.last_name}
                onChange={handleInputChange}
                placeholder="Seu sobrenome"
                error={errors.last_name}
              />
              {errors.last_name && <ErrorMessage>{errors.last_name}</ErrorMessage>}
            </FormGroup>
          </FormRow>

          <FormGroup>
            <Label htmlFor="username">
              Nome de Usuário <RequiredField>*</RequiredField>
            </Label>
            <UsernameContainer>
              <UsernameInput
                id="username"
                name="username"
                type="text"
                value={formData.username}
                onChange={handleInputChange}
                placeholder="seuusername"
                isValid={usernameValidation.isValid}
                maxLength={30}
                error={errors.username}
              />
              <ValidationIcon
                isValid={usernameValidation.isValid}
                isChecking={usernameValidation.isChecking}
              >
                {usernameValidation.isChecking && <FaSpinner />}
                {!usernameValidation.isChecking && usernameValidation.isValid === true && <FaCheck />}
                {!usernameValidation.isChecking && usernameValidation.isValid === false && <FaTimes />}
              </ValidationIcon>
            </UsernameContainer>

            <UsernameHelp>
              Seu perfil ficará disponível em: meubonsai.app/profile/{formData.username || 'seuusername'}
            </UsernameHelp>

            {usernameValidation.message && (
              <ValidationMessage type={usernameValidation.isValid ? 'success' : 'error'}>
                {usernameValidation.isValid ? <FaCheck /> : <FaTimes />}
                {usernameValidation.message}
              </ValidationMessage>
            )}
            {errors.username && <ErrorMessage>{errors.username}</ErrorMessage>}
          </FormGroup>

          <FormGroup>
            <Label htmlFor="bio">Bio</Label>
            <TextArea
              id="bio"
              name="bio"
              value={formData.bio}
              onChange={handleInputChange}
              placeholder="Conte um pouco sobre você e sua paixão por bonsais..."
              maxLength={500}
            />
          </FormGroup>

          <FormRow>
            <FormGroup>
              <Label htmlFor="city">Cidade</Label>
              <Input
                id="city"
                name="city"
                type="text"
                value={formData.city}
                onChange={handleInputChange}
                placeholder="Sua cidade"
              />
            </FormGroup>

            <FormGroup>
              <Label htmlFor="state">Estado</Label>
              <Input
                id="state"
                name="state"
                type="text"
                value={formData.state}
                onChange={handleInputChange}
                placeholder="Seu estado"
              />
            </FormGroup>
          </FormRow>

          <FormGroup>
            <Label htmlFor="country">País</Label>
            <Input
              id="country"
              name="country"
              type="text"
              value={formData.country}
              onChange={handleInputChange}
              placeholder="Seu país"
            />
          </FormGroup>

          <ButtonGroup>
            <Button
              type="submit"
              variant="primary"
              disabled={!usernameValidation.isValid || loading}
            >
              {loading ? 'Salvando...' : 'Completar Cadastro'}
            </Button>
          </ButtonGroup>
        </Form>
      </ModalContent>
    </Modal>
  );
};

export default CompleteProfileModal;
