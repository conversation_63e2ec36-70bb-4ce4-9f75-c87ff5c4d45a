import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FaHistory, FaFileInvoice, FaDownload, FaCalendarAlt, FaCheckCircle, FaClock, FaTimesCircle, FaTimes } from 'react-icons/fa';
import subscriptionService from '../services/subscriptionService';
import { getBasePlanDisplayName } from '../utils/planUtils';

// Modal Styles
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
`;

const ModalContent = styled.div`
  background: white;
  border-radius: ${props => props.theme.borderRadius.lg};
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
`;

const ModalHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${props => props.theme.spacing.xl};
  border-bottom: 1px solid #e0e0e0;
  background: white;
  border-radius: ${props => props.theme.borderRadius.lg} ${props => props.theme.borderRadius.lg} 0 0;

  h3 {
    margin: 0;
    display: flex;
    align-items: center;
    color: #333;
    font-size: 1.25rem;
    font-weight: 600;
  }
`;

const CloseButton = styled.button`
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  color: #666;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  transition: all 0.2s ease;

  &:hover {
    background: #e9ecef;
    color: #333;
    border-color: #ccc;
  }
`;

const ModalBody = styled.div`
  padding: ${props => props.theme.spacing.xl};
  background: white;
  border-radius: 0 0 ${props => props.theme.borderRadius.lg} ${props => props.theme.borderRadius.lg};
`;

const HistoryContainer = styled.div`
  background: transparent;
  border-radius: 0;
  padding: 0;
  margin-bottom: 0;
`;

const HistoryHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  margin-bottom: ${props => props.theme.spacing.lg};
  
  h3 {
    margin: 0;
    color: ${props => props.theme.colors.text.primary};
    font-size: ${props => props.theme.typography.fontSize.lg};
  }
`;

const SubscriptionCard = styled.div`
  background: #f8f9fa;
  border-radius: ${props => props.theme.borderRadius.md};
  padding: ${props => props.theme.spacing.lg};
  margin-bottom: ${props => props.theme.spacing.md};
  border: 1px solid #e0e0e0;
  border-left: 4px solid ${props => {
    switch (props.status) {
      case 'active': return '#28a745';
      case 'cancelled': return '#dc3545';
      case 'expired_grace_period': return '#ffc107';
      default: return '#6c757d';
    }
  }};
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const SubscriptionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${props => props.theme.spacing.md};

  @media (max-width: 768px) {
    flex-direction: column;
    gap: ${props => props.theme.spacing.sm};
  }
`;

const SubscriptionInfo = styled.div`
  flex: 1;
`;

const SubscriptionTitle = styled.h4`
  margin: 0 0 ${props => props.theme.spacing.xs} 0;
  color: ${props => props.theme.colors.text.primary};
  font-size: ${props => props.theme.typography.fontSize.md};
`;

const SubscriptionMeta = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${props => props.theme.spacing.md};
  color: ${props => props.theme.colors.text.secondary};
  font-size: ${props => props.theme.typography.fontSize.sm};
`;

const InvoicesButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  background: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: ${props => props.theme.typography.fontSize.sm};
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: ${props => props.theme.spacing.md};

  &:hover {
    background: ${props => props.theme.colors.primaryDark};
  }
`;



const InvoicesTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: ${props => props.theme.borderRadius.md};
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border: 1px solid #e0e0e0;
`;

const TableHeader = styled.thead`
  background: #f8f9fa;
`;

const TableHeaderCell = styled.th`
  padding: ${props => props.theme.spacing.md};
  text-align: left;
  font-weight: 600;
  color: #333;
  font-size: ${props => props.theme.typography.fontSize.sm};
  border-bottom: 1px solid #e0e0e0;
`;

const TableRow = styled.tr`
  &:nth-child(even) {
    background: #f8f9fa;
  }

  &:hover {
    background: #f1f3f4;
  }
`;

const TableCell = styled.td`
  padding: ${props => props.theme.spacing.md};
  color: #333;
  font-size: ${props => props.theme.typography.fontSize.sm};
  border-bottom: 1px solid #e0e0e0;
`;

const StatusBadge = styled.span`
  display: inline-flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.full};
  font-size: ${props => props.theme.typography.fontSize.xs};
  font-weight: 600;
  background: ${props => {
    const status = props.status?.toLowerCase();
    switch (status) {
      case 'active':
      case 'received':
      case 'confirmed':
      case 'pago': return '#d4edda';
      case 'pending':
      case 'pendente': return '#fff3cd';
      case 'cancelled': return '#f8d7da';
      case 'expired_grace_period': return '#fff3cd';
      default: return '#e2e3e5';
    }
  }};
  color: ${props => {
    const status = props.status?.toLowerCase();
    switch (status) {
      case 'active':
      case 'received':
      case 'confirmed':
      case 'pago': return '#155724';
      case 'pending':
      case 'pendente': return '#856404';
      case 'cancelled': return '#721c24';
      case 'expired_grace_period': return '#856404';
      default: return '#383d41';
    }
  }};
`;



const InvoiceButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border: 1px solid ${props => props.theme.colors.primary};
  background: transparent;
  color: ${props => props.theme.colors.primary};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: ${props => props.theme.typography.fontSize.xs};
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: ${props => props.theme.colors.primary};
    color: white;
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${props => props.theme.spacing.xl};
  color: ${props => props.theme.colors.text.secondary};
`;

const LoadingState = styled.div`
  text-align: center;
  padding: ${props => props.theme.spacing.xl};
  color: ${props => props.theme.colors.text.secondary};
`;

const SubscriptionHistory = ({ onClose }) => {
  const [history, setHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showInvoicesModal, setShowInvoicesModal] = useState(false);

  useEffect(() => {
    loadSubscriptionHistory();
  }, []);

  const loadSubscriptionHistory = async () => {
    try {
      setLoading(true);
      const response = await subscriptionService.getSubscriptionHistory();
      setHistory(response.data.subscription_history || []);
    } catch (error) {
      console.error('Error loading subscription history:', error);
      setError('Erro ao carregar histórico de assinaturas');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active': return <FaCheckCircle />;
      case 'cancelled': return <FaTimesCircle />;
      case 'expired_grace_period': return <FaClock />;
      default: return <FaClock />;
    }
  };

  const getStatusText = (status) => {
    switch (status.toLowerCase()) {
      case 'active': return 'Ativo';
      case 'cancelled': return 'Cancelado';
      case 'expired_grace_period': return 'Período de Graça';
      case 'pending': return 'Pendente';
      case 'overdue': return 'Em Atraso';
      case 'confirmed': return 'Pago';
      case 'received': return 'Pago';
      case 'awaiting_payment': return 'Aguardando Pagamento';
      default: return status;
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const formatCurrency = (value) => {
    return `R$ ${value.toFixed(2).replace('.', ',')}`;
  };

  // Função helper para formatar nome do plano do histórico
  const formatPlanName = (planName) => {
    if (!planName) return '';

    // Se contém emojis especiais, retornar como está (cobranças avulsas, etc.)
    if (planName.includes('💰') || planName.includes('📄')) {
      return planName;
    }

    // Para planos normais, remover "Mensal" e "Anual"
    return planName
      .replace(' Mensal', '')
      .replace(' Anual', '')
      .trim();
  };

  const handleDownloadInvoice = (invoiceUrl) => {
    if (invoiceUrl) {
      window.open(invoiceUrl, '_blank');
    }
  };

  if (loading) {
    return (
      <ModalOverlay onClick={onClose}>
        <ModalContent onClick={(e) => e.stopPropagation()}>
          <ModalHeader>
            <h3>
              <FaHistory style={{ marginRight: '8px' }} />
              Histórico de Assinaturas
            </h3>
            <CloseButton onClick={onClose}>
              <FaTimes />
            </CloseButton>
          </ModalHeader>
          <ModalBody>
            <LoadingState>Carregando histórico...</LoadingState>
          </ModalBody>
        </ModalContent>
      </ModalOverlay>
    );
  }

  if (error) {
    return (
      <ModalOverlay onClick={onClose}>
        <ModalContent onClick={(e) => e.stopPropagation()}>
          <ModalHeader>
            <h3>
              <FaHistory style={{ marginRight: '8px' }} />
              Histórico de Assinaturas
            </h3>
            <CloseButton onClick={onClose}>
              <FaTimes />
            </CloseButton>
          </ModalHeader>
          <ModalBody>
            <EmptyState>{error}</EmptyState>
          </ModalBody>
        </ModalContent>
      </ModalOverlay>
    );
  }

  if (history.length === 0) {
    return (
      <ModalOverlay onClick={onClose}>
        <ModalContent onClick={(e) => e.stopPropagation()}>
          <ModalHeader>
            <h3>
              <FaHistory style={{ marginRight: '8px' }} />
              Histórico de Assinaturas
            </h3>
            <CloseButton onClick={onClose}>
              <FaTimes />
            </CloseButton>
          </ModalHeader>
          <ModalBody>
            <EmptyState>Nenhuma assinatura encontrada</EmptyState>
          </ModalBody>
        </ModalContent>
      </ModalOverlay>
    );
  }

  // Separar assinaturas reais das seções especiais
  const subscriptions = history.filter(item =>
    item.id !== 'all_invoices' &&
    item.id !== 'standalone_payments' &&
    !item.plan_name.includes('💰') &&
    !item.plan_name.includes('📄')
  );

  const standaloneSection = history.find(item => item.id === 'standalone_payments');
  const standaloneInvoices = standaloneSection?.invoices || [];

  return (
    <ModalOverlay onClick={onClose}>
      <ModalContent onClick={(e) => e.stopPropagation()}>
        <ModalHeader>
          <h3>
            <FaHistory style={{ marginRight: '8px' }} />
            Histórico de Assinaturas
          </h3>
          <CloseButton onClick={onClose}>
            <FaTimes />
          </CloseButton>
        </ModalHeader>

        <ModalBody>

        {subscriptions.map((subscription) => (
          <SubscriptionCard key={subscription.id} status={subscription.status}>
            <SubscriptionHeader>
              <SubscriptionInfo>
                <SubscriptionTitle>{formatPlanName(subscription.plan_name)}</SubscriptionTitle>
                <SubscriptionMeta>
                  <span>
                    <FaCalendarAlt style={{ marginRight: '4px' }} />
                    {subscription.billing_cycle === 'monthly' ? 'Mensal' : 'Anual'}
                  </span>
                  {subscription.expires_at && (
                    <span>Expira em: {formatDate(subscription.expires_at)}</span>
                  )}
                </SubscriptionMeta>
              </SubscriptionInfo>
              <StatusBadge status={subscription.status}>
                {getStatusIcon(subscription.status)}
                {getStatusText(subscription.status)}
              </StatusBadge>
            </SubscriptionHeader>

            {/* Mostrar faturas desta assinatura */}
            {subscription.invoices && subscription.invoices.length > 0 && (
              <div style={{ marginTop: '16px', paddingTop: '16px', borderTop: '1px solid #e0e0e0' }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  marginBottom: '12px',
                  color: '#666',
                  fontSize: '14px',
                  fontWeight: '600'
                }}>
                  <FaFileInvoice />
                  Faturas ({subscription.invoices.length})
                </div>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                  {subscription.invoices.slice(0, 3).map((invoice) => (
                    <div key={invoice.id} style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      padding: '12px',
                      background: '#f8f9fa',
                      borderRadius: '8px',
                      fontSize: '14px'
                    }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                        <StatusBadge status={invoice.status?.toLowerCase()}>
                          {getStatusIcon(invoice.status?.toLowerCase())}
                          {getStatusText(invoice.status)}
                        </StatusBadge>
                        <span>{formatCurrency(invoice.value)}</span>
                        <span>Vence: {formatDate(invoice.due_date)}</span>
                      </div>
                      {invoice.invoice_url && (
                        <InvoiceButton onClick={() => handleDownloadInvoice(invoice.invoice_url)}>
                          <FaDownload size={12} />
                          Fatura
                        </InvoiceButton>
                      )}
                    </div>
                  ))}

                  {subscription.invoices.length > 3 && (
                    <InvoicesButton onClick={() => setShowInvoicesModal(true)}>
                      <FaFileInvoice />
                      Ver todas as {subscription.invoices.length} faturas
                    </InvoicesButton>
                  )}
                </div>
              </div>
            )}
          </SubscriptionCard>
        ))}

        {/* Seção de cobranças avulsas */}
        {standaloneInvoices.length > 0 && (
          <SubscriptionCard status="active">
            <SubscriptionHeader>
              <SubscriptionInfo>
                <SubscriptionTitle>💰 Cobranças Avulsas</SubscriptionTitle>
                <SubscriptionMeta>
                  <span>Pagamentos únicos não relacionados a assinaturas</span>
                </SubscriptionMeta>
              </SubscriptionInfo>
            </SubscriptionHeader>

            <div style={{ marginTop: '16px', paddingTop: '16px', borderTop: '1px solid #e0e0e0' }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                marginBottom: '12px',
                color: '#666',
                fontSize: '14px',
                fontWeight: '600'
              }}>
                <FaFileInvoice />
                Cobranças ({standaloneInvoices.length})
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                {standaloneInvoices.slice(0, 3).map((invoice) => (
                  <div key={invoice.id} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '12px',
                    background: '#f8f9fa',
                    borderRadius: '8px',
                    fontSize: '14px'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                      <StatusBadge status={invoice.status?.toLowerCase()}>
                        {getStatusIcon(invoice.status?.toLowerCase())}
                        {getStatusText(invoice.status)}
                      </StatusBadge>
                      <span>{formatCurrency(invoice.value)}</span>
                      <span>Vence: {formatDate(invoice.due_date)}</span>
                    </div>
                    {invoice.invoice_url && (
                      <InvoiceButton onClick={() => handleDownloadInvoice(invoice.invoice_url)}>
                        <FaDownload size={12} />
                        Fatura
                      </InvoiceButton>
                    )}
                  </div>
                ))}

                {standaloneInvoices.length > 3 && (
                  <InvoicesButton onClick={() => setShowInvoicesModal(true)}>
                    <FaFileInvoice />
                    Ver todas as {standaloneInvoices.length} cobranças
                  </InvoicesButton>
                )}
              </div>
            </div>
          </SubscriptionCard>
        )}
        </ModalBody>

      {/* Modal de Faturas */}
      {showInvoicesModal && (
        <ModalOverlay onClick={() => setShowInvoicesModal(false)}>
          <ModalContent onClick={(e) => e.stopPropagation()}>
            <ModalHeader>
              <h3>
                <FaFileInvoice style={{ marginRight: '8px' }} />
                Todas as Faturas
              </h3>
              <CloseButton onClick={() => setShowInvoicesModal(false)}>
                <FaTimes />
              </CloseButton>
            </ModalHeader>

            <InvoicesTable>
              <TableHeader>
                <tr>
                  <TableHeaderCell>Status</TableHeaderCell>
                  <TableHeaderCell>Valor</TableHeaderCell>
                  <TableHeaderCell>Vencimento</TableHeaderCell>
                  <TableHeaderCell>Assinatura</TableHeaderCell>
                  <TableHeaderCell>Ações</TableHeaderCell>
                </tr>
              </TableHeader>
              <tbody>
                {subscriptions.map(subscription =>
                  subscription.invoices?.map(invoice => (
                    <TableRow key={invoice.id}>
                      <TableCell>
                        <StatusBadge status={invoice.status?.toLowerCase()}>
                          {getStatusIcon(invoice.status?.toLowerCase())}
                          {getStatusText(invoice.status)}
                        </StatusBadge>
                      </TableCell>
                      <TableCell>{formatCurrency(invoice.value)}</TableCell>
                      <TableCell>{formatDate(invoice.due_date)}</TableCell>
                      <TableCell>{formatPlanName(subscription.plan_name)}</TableCell>
                      <TableCell>
                        {invoice.invoice_url && (
                          <InvoiceButton onClick={() => handleDownloadInvoice(invoice.invoice_url)}>
                            <FaDownload size={12} />
                            Fatura
                          </InvoiceButton>
                        )}
                      </TableCell>
                    </TableRow>
                  ))
                )}
                {standaloneInvoices.map((invoice) => (
                  <TableRow key={invoice.id}>
                    <TableCell>
                      <StatusBadge status={invoice.status?.toLowerCase()}>
                        {getStatusIcon(invoice.status?.toLowerCase())}
                        {getStatusText(invoice.status)}
                      </StatusBadge>
                    </TableCell>
                    <TableCell>{formatCurrency(invoice.value)}</TableCell>
                    <TableCell>{formatDate(invoice.due_date)}</TableCell>
                    <TableCell>Cobrança Avulsa</TableCell>
                    <TableCell>
                      {invoice.invoice_url && (
                        <InvoiceButton onClick={() => handleDownloadInvoice(invoice.invoice_url)}>
                          <FaDownload size={12} />
                          Fatura
                        </InvoiceButton>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </tbody>
            </InvoicesTable>
          </ModalContent>
        </ModalOverlay>
      )}
      </ModalContent>
    </ModalOverlay>
  );
};

export default SubscriptionHistory;
