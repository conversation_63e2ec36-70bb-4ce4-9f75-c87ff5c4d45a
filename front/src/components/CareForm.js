import React, { useState, useRef } from 'react';
import styled from 'styled-components';
import { FaCalendarAlt, FaCheck, FaTimes, FaCamera, FaTrash, FaBell } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { useQuery } from 'react-query';
import Button from './UI/Button';
import ReminderModal from './ReminderModal';
import MultiReminderModal from './MultiReminderModal';
import MultiPhotoCapture from './MultiPhotoCapture';
import subscriptionService from '../services/subscriptionService';

const FormContainer = styled.div`
  background-color: ${props => props.theme.colors.background};
  padding: ${props => props.theme.spacing.lg};
  border-radius: ${props => props.theme.borderRadius.lg};
  border: 2px dashed ${props => props.theme.colors.border};
  margin-bottom: ${props => props.theme.spacing.lg};
  width: 100%;
  box-sizing: border-box;

  @media (max-width: 768px) {
    padding: ${props => props.theme.spacing.md};
  }

  @media (max-width: 480px) {
    padding: ${props => props.theme.spacing.sm};
  }
`;

const FormTitle = styled.h3`
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing.lg};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const FormGroup = styled.div`
  margin-bottom: ${props => props.theme.spacing.md};
`;

const Label = styled.label`
  display: block;
  margin-bottom: ${props => props.theme.spacing.xs};
  color: ${props => props.theme.colors.text.primary};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
`;

const Select = styled.select`
  width: 100%;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.md};
  background-color: white;
  
  &:focus {
    border-color: ${props => props.theme.colors.primary};
    outline: none;
  }
`;

const Input = styled.input`
  width: 100%;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.md};
  box-sizing: border-box;

  &:focus {
    border-color: ${props => props.theme.colors.primary};
    outline: none;
  }

  @media (max-width: 480px) {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: ${props => props.theme.spacing.sm};
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 80px;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.md};
  resize: vertical;
  
  &:focus {
    border-color: ${props => props.theme.colors.primary};
    outline: none;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
  justify-content: flex-end;
  margin-top: ${props => props.theme.spacing.lg};

  @media (max-width: 480px) {
    flex-direction: column;
    gap: ${props => props.theme.spacing.md};
  }
`;

const ImageUploadArea = styled.div`
  border: 2px dashed ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  padding: ${props => props.theme.spacing.lg};
  text-align: center;
  cursor: pointer;
  transition: border-color 0.2s ease;

  &:hover {
    border-color: ${props => props.theme.colors.primary};
  }

  &.dragover {
    border-color: ${props => props.theme.colors.primary};
    background-color: ${props => props.theme.colors.primary}10;
  }
`;

const HiddenInput = styled.input`
  display: none;
`;

const PreviewGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: ${props => props.theme.spacing.sm};
  margin-bottom: ${props => props.theme.spacing.sm};
`;

const PreviewContainer = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
`;

const PreviewImage = styled.img`
  width: 100%;
  height: 100px;
  object-fit: cover;
  border-radius: ${props => props.theme.borderRadius.md};
  border: 2px solid ${props => props.theme.colors.border};
`;

const ImageInfo = styled.div`
  font-size: 10px;
  color: ${props => props.theme.colors.text.secondary};
  text-align: center;
  margin-top: 4px;
  word-break: break-all;
`;

const RemoveImageButton = styled.button`
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: none;
  background-color: #dc3545;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;

  &:hover {
    background-color: #c82333;
  }
`;

const CareForm = ({ plantId, onSuccess, onCancel }) => {
  const [formData, setFormData] = useState({
    care_types: [], // Start empty - user must select types
    description: '',
    date: (() => {
      // Criar data local atual (apenas data, sem hora)
      const now = new Date();
      // Retornar no formato date (YYYY-MM-DD)
      return now.toISOString().split('T')[0];
    })(),
    notes: ''
  });
  const [loading, setLoading] = useState(false);
  const [photos, setPhotos] = useState([]);
  const [showReminderModal, setShowReminderModal] = useState(false);
  const [showMultiReminderModal, setShowMultiReminderModal] = useState(false);
  const [createdCareId, setCreatedCareId] = useState(null);
  const [createdCareTypes, setCreatedCareTypes] = useState([]);
  const maxFiles = 10;

  // Buscar informações do plano do usuário
  const { data: userPlan } = useQuery(
    'user-plan',
    () => subscriptionService.getCurrentSubscription().then(res => res.data?.plan),
    {
      staleTime: 5 * 60 * 1000, // 5 minutos
      retry: false
    }
  );

  const careTypes = [
    { value: 'poda', label: 'Poda' },
    { value: 'adubacao', label: 'Adubação' },
    { value: 'transplante', label: 'Transplante' },
    { value: 'aramacao', label: 'Aramação' },
    { value: 'limpeza', label: 'Limpeza' },
    { value: 'tratamento', label: 'Tratamento' },
    { value: 'desfolha', label: 'Desfolha' },
    { value: 'outro', label: 'Outro' }
  ];

  const handlePhotosChange = (newPhotos) => {
    setPhotos(newPhotos);
  };

  const handleCareTypeToggle = (careType) => {
    setFormData(prev => ({
      ...prev,
      care_types: prev.care_types.includes(careType)
        ? prev.care_types.filter(type => type !== careType)
        : [...prev.care_types, careType]
    }));
  };

  const handleReminderSuccess = () => {
    setShowReminderModal(false);
    onSuccess();
  };

  const handleMultiReminderSuccess = () => {
    setShowMultiReminderModal(false);
    onSuccess();
  };

  const handleReminderCancel = () => {
    setShowReminderModal(false);
    onSuccess(); // Ainda chama onSuccess mesmo sem criar lembrete
  };

  const handleMultiReminderCancel = () => {
    setShowMultiReminderModal(false);
    onSuccess(); // Ainda chama onSuccess mesmo sem criar lembrete
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const photoFiles = photos.filter(p => !p.isVideo);
      const videoFiles = photos.filter(p => p.isVideo);

      console.log('Iniciando submissão do cuidado...', {
        formData,
        plantId,
        photosCount: photoFiles.length,
        videosCount: videoFiles.length,
        photos: photoFiles.map(p => p.file.name),
        videos: videoFiles.map(v => v.file.name)
      });

      // Import plantsService dynamically to avoid circular imports
      const { plantsService } = await import('../services/plantsService');

      // Validate that at least one care type is selected
      if (!formData.care_types || formData.care_types.length === 0) {
        toast.error('Selecione pelo menos um tipo de cuidado');
        setLoading(false);
        return;
      }

      // Prepare form data for multipart upload
      const submitData = new FormData();

      // Send multiple care types
      formData.care_types.forEach(careType => {
        submitData.append('care_types', careType);
      });

      // Date-only format (YYYY-MM-DD) - send as is, backend will handle time
      submitData.append('date', formData.date);

      if (formData.description) submitData.append('description', formData.description);
      if (formData.notes) submitData.append('notes', formData.notes);

      // Adicionar múltiplas fotos
      console.log('Adicionando arquivos ao FormData:', photoFiles.length);
      photoFiles.forEach((photo, index) => {
        console.log(`Adicionando arquivo ${index + 1}:`, photo.file.name);
        submitData.append('files', photo.file);
      });

      // Adicionar múltiplos vídeos
      console.log('Adicionando vídeos ao FormData:', videoFiles.length);
      videoFiles.forEach((video, index) => {
        console.log(`Adicionando vídeo ${index + 1}:`, video.file.name);
        submitData.append('videos', video.file);
      });

      // Debug FormData
      console.log('FormData entries:');
      for (let [key, value] of submitData.entries()) {
        console.log(key, value instanceof File ? `File: ${value.name}` : value);
      }

      console.log('Enviando dados para API...', {
        care_types: formData.care_types,
        date: formData.date,
        filesCount: photos.length
      });

      const response = await plantsService.addPlantCareWithFile(plantId, submitData);
      const careId = response.data.id;

      console.log('Cuidado criado com sucesso:', careId);
      toast.success('Cuidado adicionado com sucesso!');

      // Perguntar se quer criar lembretes (múltiplos ou único)
      setCreatedCareId(careId);
      setCreatedCareTypes(formData.care_types);

      if (formData.care_types.length > 1) {
        setShowMultiReminderModal(true);
      } else {
        setShowReminderModal(true);
      }
    } catch (error) {
      console.error('Erro detalhado ao adicionar cuidado:', {
        error,
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });

      const errorMessage = error.response?.data?.detail ||
                          error.response?.data?.message ||
                          'Erro ao adicionar cuidado';
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <FormContainer>
      <FormTitle>
        <FaCalendarAlt />
        Adicionar Novo Cuidado
      </FormTitle>

      <form onSubmit={handleSubmit}>
        <FormGroup>
          <Label>Tipos de Cuidado * (selecione um ou mais)</Label>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '8px', marginTop: '8px' }}>
            {careTypes.map(type => (
              <label key={type.value} style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '8px',
                border: '1px solid #ddd',
                borderRadius: '4px',
                cursor: 'pointer',
                backgroundColor: formData.care_types.includes(type.value) ? '#e8f5e8' : 'white'
              }}>
                <input
                  type="checkbox"
                  checked={formData.care_types.includes(type.value)}
                  onChange={() => handleCareTypeToggle(type.value)}
                  style={{ margin: 0 }}
                />
                <span>{type.label}</span>
              </label>
            ))}
          </div>
          {formData.care_types.length === 0 && (
            <div style={{ color: '#e74c3c', fontSize: '0.9rem', marginTop: '4px' }}>
              Selecione pelo menos um tipo de cuidado
            </div>
          )}
        </FormGroup>

        <FormGroup>
          <Label htmlFor="date">Data do Cuidado *</Label>
          <Input
            id="date"
            type="date"
            value={formData.date}
            onChange={(e) => handleChange('date', e.target.value)}
            required
          />
        </FormGroup>

        <FormGroup>
          <Label htmlFor="description">Descrição</Label>
          <TextArea
            id="description"
            placeholder="Descreva o cuidado realizado..."
            value={formData.description}
            onChange={(e) => handleChange('description', e.target.value)}
          />
        </FormGroup>

        <FormGroup>
          <Label htmlFor="notes">Observações</Label>
          <TextArea
            id="notes"
            placeholder="Observações adicionais, resultados, próximos passos..."
            value={formData.notes}
            onChange={(e) => handleChange('notes', e.target.value)}
          />
        </FormGroup>

        <FormGroup>
          <Label>
            {userPlan?.allows_videos ? 'Fotos e Vídeos do Cuidado' : 'Fotos do Cuidado'} (opcional)
          </Label>
          <MultiPhotoCapture
            photos={photos}
            onPhotosChange={handlePhotosChange}
            maxPhotos={maxFiles}
            disabled={loading}
            allowVideos={userPlan?.allows_videos || false}
            userPlan={userPlan}
          />
          {userPlan?.allows_videos && (
            <div style={{ fontSize: '0.875rem', color: '#666', marginTop: '0.5rem' }}>
              ✅ Plano Premium: Upload de vídeos habilitado. Máximo 10 itens (fotos + vídeos) por cuidado.
            </div>
          )}
          {!userPlan?.allows_videos && (
            <div style={{ fontSize: '0.875rem', color: '#666', marginTop: '0.5rem' }}>
              💡 Faça upgrade para Premium e adicione vídeos aos seus cuidados!
            </div>
          )}
        </FormGroup>

        <ButtonGroup>
          <Button
            type="button"
            variant="secondary"
            onClick={onCancel}
            disabled={loading}
          >
            <FaTimes />
            Cancelar
          </Button>
          <Button
            type="submit"
            disabled={loading}
          >
            <FaCheck />
            {loading ? 'Adicionando...' : 'Adicionar Cuidado'}
          </Button>
        </ButtonGroup>
      </form>

      {/* Modal de criação de lembrete único */}
      <ReminderModal
        isOpen={showReminderModal}
        onClose={handleReminderCancel}
        plantId={plantId}
        careId={createdCareId}
        careType={formData.care_types[0]}
        onSuccess={handleReminderSuccess}
        mode="fromCare"
      />

      {/* Modal de criação de múltiplos lembretes */}
      {showMultiReminderModal && (
        <MultiReminderModal
          isOpen={showMultiReminderModal}
          onClose={handleMultiReminderCancel}
          plantId={plantId}
          careId={createdCareId}
          careTypes={createdCareTypes}
          onSuccess={handleMultiReminderSuccess}
        />
      )}
    </FormContainer>
  );
};

export default CareForm;
