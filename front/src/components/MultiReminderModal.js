import React, { useState } from 'react';
import styled from 'styled-components';
import { FaBell, FaTimes, FaCheck, FaCalendarAlt } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { useQueryClient } from 'react-query';
import remindersService from '../services/remindersService';
import { useReminder } from '../contexts/ReminderContext';

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
`;

const ModalContent = styled.div`
  background: white;
  border-radius: 12px;
  padding: 24px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
`;

const ModalHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
`;

const ModalTitle = styled.h2`
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  color: #333;
  font-size: 1.5rem;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  
  &:hover {
    background-color: #f5f5f5;
    color: #333;
  }
`;

const CareTypeSection = styled.div`
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;
`;

const CareTypeHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
`;

const CareTypeName = styled.h3`
  margin: 0;
  color: #2c5530;
  font-size: 1.2rem;
`;

const SkipToggle = styled.label`
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  color: #666;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
`;

const Label = styled.label`
  font-weight: 600;
  margin-bottom: 4px;
  color: #333;
  font-size: 0.9rem;
`;

const Input = styled.input`
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
  }
  
  &:disabled {
    background-color: #f5f5f5;
    color: #999;
  }
`;

const Select = styled.select`
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;
  
  &:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
  }
  
  &:disabled {
    background-color: #f5f5f5;
    color: #999;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #eee;
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  ${props => props.variant === 'secondary' ? `
    background-color: #6c757d;
    color: white;
    
    &:hover {
      background-color: #5a6268;
    }
  ` : `
    background-color: #4CAF50;
    color: white;
    
    &:hover {
      background-color: #45a049;
    }
  `}
  
  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
`;

const MultiReminderModal = ({ isOpen, onClose, plantId, careTypes, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const queryClient = useQueryClient();
  const { invalidateReminders } = useReminder();
  const [reminderSettings, setReminderSettings] = useState(() => {
    const careTypeMap = {
      'poda': 'Poda',
      'adubacao': 'Adubação',
      'transplante': 'Transplante',
      'aramacao': 'Aramação',
      'limpeza': 'Limpeza',
      'tratamento': 'Tratamento',
      'desfolha': 'Desfolha',
      'outro': 'Outro'
    };

    return careTypes.map(careType => ({
      careType,
      careTypeName: careTypeMap[careType] || careType,
      enabled: true,
      interval: 30,
      unit: 'days',
      scheduledDate: (() => {
        const date = new Date();
        date.setDate(date.getDate() + 30);
        return date.toISOString().split('T')[0];
      })()
    }));
  });

  const intervalUnits = [
    { value: 'days', label: 'Dias' },
    { value: 'weeks', label: 'Semanas' },
    { value: 'months', label: 'Meses' },
    { value: 'years', label: 'Anos' }
  ];

  const updateReminderSetting = (index, field, value) => {
    setReminderSettings(prev => prev.map((setting, i) => 
      i === index ? { ...setting, [field]: value } : setting
    ));
  };

  const calculateNextDate = (interval, unit) => {
    const date = new Date();
    switch (unit) {
      case 'days':
        date.setDate(date.getDate() + parseInt(interval));
        break;
      case 'weeks':
        date.setDate(date.getDate() + (parseInt(interval) * 7));
        break;
      case 'months':
        date.setMonth(date.getMonth() + parseInt(interval));
        break;
      case 'years':
        date.setFullYear(date.getFullYear() + parseInt(interval));
        break;
    }
    return date.toISOString().split('T')[0];
  };

  const handleIntervalChange = (index, interval, unit) => {
    const newDate = calculateNextDate(interval, unit);
    setReminderSettings(prev => prev.map((setting, i) => 
      i === index ? { ...setting, interval, unit, scheduledDate: newDate } : setting
    ));
  };

  const handleSubmit = async () => {
    setLoading(true);

    try {
      const enabledReminders = reminderSettings.filter(setting => setting.enabled);

      if (enabledReminders.length === 0) {
        toast.info('Nenhum lembrete será criado');
        onSuccess();
        return;
      }

      // Validar se todas as datas são futuras
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      for (const setting of enabledReminders) {
        const selectedDate = new Date(setting.scheduledDate);
        if (selectedDate < today) {
          toast.error(`A data do lembrete de ${setting.careTypeName} não pode ser anterior a hoje`);
          setLoading(false);
          return;
        }
      }

      // Create reminders for each enabled care type
      for (const setting of enabledReminders) {
        await remindersService.createReminder(plantId, {
          care_type: setting.careType,
          scheduled_date: setting.scheduledDate,
          description: `Lembrete de ${setting.careTypeName}`
        });
      }

      toast.success(`${enabledReminders.length} lembrete(s) criado(s) com sucesso!`);

      // Invalidate queries to refresh reminders
      queryClient.invalidateQueries(['plant-reminders', plantId]);
      queryClient.invalidateQueries('upcomingReminders');
      queryClient.invalidateQueries('notifications');

      // Use the reminder context to invalidate all reminder queries
      await invalidateReminders();

      onSuccess();
    } catch (error) {
      console.error('Erro ao criar lembretes:', error);
      toast.error('Erro ao criar lembretes');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <ModalOverlay onClick={onClose}>
      <ModalContent onClick={e => e.stopPropagation()}>
        <ModalHeader>
          <ModalTitle>
            <FaBell />
            Criar Lembretes Individuais
          </ModalTitle>
          <CloseButton onClick={onClose}>
            <FaTimes />
          </CloseButton>
        </ModalHeader>

        <p style={{ marginBottom: '24px', color: '#666' }}>
          Configure lembretes individuais para cada tipo de cuidado realizado:
        </p>

        {reminderSettings.map((setting, index) => (
          <CareTypeSection key={setting.careType}>
            <CareTypeHeader>
              <CareTypeName>{setting.careTypeName}</CareTypeName>
              <SkipToggle>
                <input
                  type="checkbox"
                  checked={setting.enabled}
                  onChange={(e) => updateReminderSetting(index, 'enabled', e.target.checked)}
                />
                Criar lembrete
              </SkipToggle>
            </CareTypeHeader>

            {setting.enabled && (
              <>
                <FormRow>
                  <FormGroup>
                    <Label>Intervalo</Label>
                    <Input
                      type="number"
                      min="1"
                      value={setting.interval}
                      onChange={(e) => handleIntervalChange(index, e.target.value, setting.unit)}
                    />
                  </FormGroup>
                  <FormGroup>
                    <Label>Unidade</Label>
                    <Select
                      value={setting.unit}
                      onChange={(e) => handleIntervalChange(index, setting.interval, e.target.value)}
                    >
                      {intervalUnits.map(unit => (
                        <option key={unit.value} value={unit.value}>
                          {unit.label}
                        </option>
                      ))}
                    </Select>
                  </FormGroup>
                  <FormGroup>
                    <Label>Próxima Data</Label>
                    <Input
                      type="date"
                      value={setting.scheduledDate}
                      onChange={(e) => updateReminderSetting(index, 'scheduledDate', e.target.value)}
                      min={new Date().toISOString().split('T')[0]}
                    />
                  </FormGroup>
                </FormRow>
              </>
            )}
          </CareTypeSection>
        ))}

        <ButtonGroup>
          <Button variant="secondary" onClick={onClose} disabled={loading}>
            <FaTimes />
            Pular Lembretes
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            <FaCheck />
            {loading ? 'Criando...' : 'Criar Lembretes'}
          </Button>
        </ButtonGroup>
      </ModalContent>
    </ModalOverlay>
  );
};

export default MultiReminderModal;
