import React, { useState, useRef } from 'react';
import styled from 'styled-components';
import { FaCamera, FaImage, FaTimes, FaPlay } from 'react-icons/fa';
import { toast } from 'react-toastify';
import Button from './UI/Button';

const CaptureContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.sm};
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
  justify-content: center;
  flex-wrap: wrap;
`;

const CaptureButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
  flex: 1;
  min-width: 120px;
`;

const HiddenInput = styled.input`
  display: none;
`;

const PreviewContainer = styled.div`
  position: relative;
  margin-top: ${props => props.theme.spacing.sm};
`;

const PreviewImage = styled.img`
  width: 100%;
  max-width: 200px;
  height: auto;
  border-radius: ${props => props.theme.borderRadius.md};
  border: 2px solid ${props => props.theme.colors.border};
`;

const PreviewVideo = styled.video`
  width: 100%;
  max-width: 200px;
  height: auto;
  border-radius: ${props => props.theme.borderRadius.md};
  border: 2px solid ${props => props.theme.colors.border};
`;

const VideoOverlay = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  pointer-events: none;
`;

const RemoveButton = styled.button`
  position: absolute;
  top: -8px;
  right: -8px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;

  &:hover {
    background: #c82333;
  }
`;

const PhotoCapture = ({
  onPhotoCapture,
  onPhotoRemove,
  preview,
  disabled = false,
  allowVideos = false,
  userPlan = null,
  isVideo = false
}) => {
  const fileInputRef = useRef(null);
  const cameraInputRef = useRef(null);

  const handleFileSelect = (event, isCamera = false) => {
    const file = event.target.files[0];
    if (!file) return;

    const isImageFile = file.type.startsWith('image/');
    const isVideoFile = file.type.startsWith('video/');

    // Validate file type
    if (!isImageFile && !isVideoFile) {
      toast.error('Por favor, selecione apenas arquivos de imagem ou vídeo');
      return;
    }

    // Check if videos are allowed
    if (isVideoFile && !allowVideos) {
      toast.error('Upload de vídeos é exclusivo do plano Premium');
      return;
    }

    // Check user plan for videos
    if (isVideoFile && (!userPlan || !userPlan.allows_videos)) {
      toast.error('Upload de vídeos é exclusivo do plano Premium. Faça upgrade para acessar esta funcionalidade.');
      return;
    }

    // Validate file size (images: 5MB, videos: 100MB)
    const maxSize = isVideoFile ? 100 * 1024 * 1024 : 5 * 1024 * 1024;
    const maxSizeText = isVideoFile ? '100MB' : '5MB';
    if (file.size > maxSize) {
      toast.error(`O arquivo deve ter no máximo ${maxSizeText}`);
      return;
    }

    // Validate video formats
    if (isVideoFile) {
      const allowedVideoTypes = [
        'video/mp4', 'video/quicktime', 'video/x-msvideo',
        'video/x-matroska', 'video/webm', 'video/x-m4v'
      ];
      if (!allowedVideoTypes.includes(file.type)) {
        toast.error(`Formato de vídeo não suportado. Use MP4, MOV, AVI, MKV, WEBM ou M4V`);
        return;
      }
    }

    // Call the callback with the file and type info
    onPhotoCapture(file, isCamera, isVideoFile);

    // Reset input
    event.target.value = '';
  };

  const handleGalleryClick = () => {
    fileInputRef.current?.click();
  };

  const handleCameraClick = async () => {
    // Detecta se é dispositivo móvel
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    if (isMobile && navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      try {
        // Tenta usar a API de câmera diretamente
        const stream = await navigator.mediaDevices.getUserMedia({
          video: { facingMode: 'environment' },
          audio: false
        });

        // Para o stream imediatamente (só queríamos testar se a câmera está disponível)
        stream.getTracks().forEach(track => track.stop());

        // Agora clica no input file com os atributos corretos
        if (cameraInputRef.current) {
          const input = cameraInputRef.current;
          input.setAttribute('capture', 'environment');
          input.setAttribute('accept', allowVideos ? 'image/*,video/*' : 'image/*');
          input.click();
        }
      } catch (error) {
        console.log('Câmera não disponível, usando input file padrão:', error);
        // Fallback para o comportamento padrão
        if (cameraInputRef.current) {
          cameraInputRef.current.click();
        }
      }
    } else {
      // Desktop ou fallback
      if (cameraInputRef.current) {
        cameraInputRef.current.click();
      }
    }
  };

  const handleRemove = () => {
    onPhotoRemove();
  };

  return (
    <CaptureContainer>
      {/* Hidden inputs */}
      <HiddenInput
        ref={fileInputRef}
        type="file"
        accept={allowVideos ? "image/*,video/*" : "image/*"}
        onChange={(e) => handleFileSelect(e, false)}
        disabled={disabled}
      />
      <HiddenInput
        ref={cameraInputRef}
        type="file"
        accept={allowVideos ? "image/*,video/*" : "image/*"}
        capture="environment"
        captureMode="camera"
        onChange={(e) => handleFileSelect(e, true)}
        disabled={disabled}
      />

      {!preview ? (
        <ButtonGroup>
          <CaptureButton
            type="button"
            variant="outline"
            onClick={handleCameraClick}
            disabled={disabled}
          >
            <FaCamera />
            {allowVideos ? 'Câmera' : 'Tirar Foto'}
          </CaptureButton>
          <CaptureButton
            type="button"
            variant="outline"
            onClick={handleGalleryClick}
            disabled={disabled}
          >
            <FaImage />
            Galeria
          </CaptureButton>
        </ButtonGroup>
      ) : (
        <PreviewContainer>
          {isVideo ? (
            <>
              <PreviewVideo
                src={preview}
                muted
                preload="metadata"
                controls={false}
              />
              <VideoOverlay>
                <FaPlay />
              </VideoOverlay>
            </>
          ) : (
            <PreviewImage src={preview} alt="Preview" />
          )}
          <RemoveButton
            type="button"
            onClick={handleRemove}
            disabled={disabled}
          >
            <FaTimes />
          </RemoveButton>
        </PreviewContainer>
      )}
    </CaptureContainer>
  );
};

export default PhotoCapture;
