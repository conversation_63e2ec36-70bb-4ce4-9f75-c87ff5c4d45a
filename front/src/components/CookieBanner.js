import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FaCheck, FaTimes, FaCog } from 'react-icons/fa';

const BannerContainer = styled.div`
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: ${props => props.theme.colors.surface};
  border-top: 3px solid ${props => props.theme.colors.primary};
  box-shadow: ${props => props.theme.shadows.lg};
  padding: ${props => props.theme.spacing.lg};
  z-index: 10000;
  transform: translateY(${props => props.isVisible ? '0' : '100%'});
  transition: transform 0.3s ease-in-out;

  @media (max-width: 768px) {
    padding: ${props => props.theme.spacing.md};
  }
`;

const BannerContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.lg};

  @media (max-width: 768px) {
    flex-direction: column;
    gap: ${props => props.theme.spacing.md};
    text-align: center;
  }
`;

const CookieIcon = styled.div`
  color: ${props => props.theme.colors.primary};
  font-size: 2rem;
  flex-shrink: 0;

  @media (max-width: 768px) {
    font-size: 1.5rem;
  }
`;

const TextContent = styled.div`
  flex: 1;
  color: ${props => props.theme.colors.text.primary};
  line-height: 1.5;

  p {
    margin: 0 0 ${props => props.theme.spacing.sm} 0;
  }

  a {
    color: ${props => props.theme.colors.primary};
    text-decoration: none;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
  flex-shrink: 0;

  @media (max-width: 768px) {
    flex-direction: column;
    width: 100%;
  }
`;

const Button = styled.button`
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.lg};
  border: none;
  border-radius: ${props => props.theme.borderRadius.md};
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
  transition: all 0.2s ease;
  white-space: nowrap;

  @media (max-width: 768px) {
    justify-content: center;
    padding: ${props => props.theme.spacing.md};
  }
`;

const AcceptButton = styled(Button)`
  background: ${props => props.theme.colors.primary};
  color: white;

  &:hover {
    background: ${props => props.theme.colors.primaryDark};
  }
`;

const RejectButton = styled(Button)`
  background: transparent;
  color: ${props => props.theme.colors.text.secondary};
  border: 1px solid ${props => props.theme.colors.border};

  &:hover {
    background: ${props => props.theme.colors.background};
  }
`;

const SettingsButton = styled(Button)`
  background: transparent;
  color: ${props => props.theme.colors.primary};
  border: 1px solid ${props => props.theme.colors.primary};

  &:hover {
    background: ${props => props.theme.colors.primaryLight};
  }
`;

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10001;
  padding: ${props => props.theme.spacing.md};
`;

const Modal = styled.div`
  background: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.xl};
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
`;

const ModalHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const ModalTitle = styled.h2`
  color: ${props => props.theme.colors.text.primary};
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.text.secondary};
  cursor: pointer;
  padding: ${props => props.theme.spacing.xs};
  border-radius: ${props => props.theme.borderRadius.sm};

  &:hover {
    background: ${props => props.theme.colors.background};
  }
`;

const CookieCategory = styled.div`
  margin-bottom: ${props => props.theme.spacing.lg};
  padding: ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
`;

const CategoryHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${props => props.theme.spacing.sm};
`;

const CategoryTitle = styled.h3`
  color: ${props => props.theme.colors.text.primary};
  margin: 0;
`;

const Toggle = styled.button`
  width: 50px;
  height: 24px;
  border-radius: 12px;
  border: none;
  background: ${props => props.active ? props.theme.colors.primary : props.theme.colors.border};
  position: relative;
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  opacity: ${props => props.disabled ? 0.5 : 1};
  transition: background 0.2s ease;

  &::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: white;
    top: 2px;
    left: ${props => props.active ? '28px' : '2px'};
    transition: left 0.2s ease;
  }
`;

const CategoryDescription = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.4;
`;

const CookieBanner = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [preferences, setPreferences] = useState({
    essential: true,
    analytics: false,
    marketing: false
  });

  useEffect(() => {
    // Check if user has already made a choice
    const cookieConsent = localStorage.getItem('cookieConsent');
    if (!cookieConsent) {
      // Show banner after a short delay
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, []);

  const handleAcceptAll = () => {
    const consent = {
      essential: true,
      analytics: true,
      marketing: true,
      timestamp: new Date().toISOString()
    };
    
    localStorage.setItem('cookieConsent', JSON.stringify(consent));
    
    // Initialize Google Analytics if accepted
    if (consent.analytics && window.gtag) {
      window.gtag('consent', 'update', {
        'analytics_storage': 'granted'
      });
    }
    
    setIsVisible(false);
  };

  const handleRejectAll = () => {
    const consent = {
      essential: true,
      analytics: false,
      marketing: false,
      timestamp: new Date().toISOString()
    };
    
    localStorage.setItem('cookieConsent', JSON.stringify(consent));
    
    // Deny Google Analytics
    if (window.gtag) {
      window.gtag('consent', 'update', {
        'analytics_storage': 'denied'
      });
    }
    
    setIsVisible(false);
  };

  const handleSavePreferences = () => {
    const consent = {
      ...preferences,
      timestamp: new Date().toISOString()
    };
    
    localStorage.setItem('cookieConsent', JSON.stringify(consent));
    
    // Update Google Analytics consent
    if (window.gtag) {
      window.gtag('consent', 'update', {
        'analytics_storage': preferences.analytics ? 'granted' : 'denied'
      });
    }
    
    setShowSettings(false);
    setIsVisible(false);
  };

  const togglePreference = (category) => {
    if (category === 'essential') return; // Essential cookies cannot be disabled
    
    setPreferences(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };

  if (!isVisible) return null;

  return (
    <>
      <BannerContainer isVisible={isVisible}>
        <BannerContent>
          <CookieIcon>
            <FaCookie />
          </CookieIcon>
          
          <TextContent>
            <p>
              <strong>🍪 Utilizamos cookies para melhorar sua experiência</strong>
            </p>
            <p>
              Usamos cookies essenciais para o funcionamento do site e cookies de analytics para melhorar nossos serviços. 
              Você pode escolher quais cookies aceitar. Consulte nossa{' '}
              <a href="/privacy-policy" target="_blank">Política de Privacidade</a> para mais detalhes.
            </p>
          </TextContent>
          
          <ButtonGroup>
            <AcceptButton onClick={handleAcceptAll}>
              <FaCheck />
              Aceitar Todos
            </AcceptButton>
            <RejectButton onClick={handleRejectAll}>
              <FaTimes />
              Rejeitar
            </RejectButton>
            <SettingsButton onClick={() => setShowSettings(true)}>
              <FaCog />
              Configurar
            </SettingsButton>
          </ButtonGroup>
        </BannerContent>
      </BannerContainer>

      {showSettings && (
        <ModalOverlay onClick={() => setShowSettings(false)}>
          <Modal onClick={(e) => e.stopPropagation()}>
            <ModalHeader>
              <ModalTitle>
                <FaCog />
                Configurações de Cookies
              </ModalTitle>
              <CloseButton onClick={() => setShowSettings(false)}>
                <FaTimes />
              </CloseButton>
            </ModalHeader>

            <CookieCategory>
              <CategoryHeader>
                <CategoryTitle>Cookies Essenciais</CategoryTitle>
                <Toggle active={true} disabled={true} />
              </CategoryHeader>
              <CategoryDescription>
                Necessários para o funcionamento básico do site, como manter você logado e salvar suas preferências. 
                Estes cookies não podem ser desabilitados.
              </CategoryDescription>
            </CookieCategory>

            <CookieCategory>
              <CategoryHeader>
                <CategoryTitle>Cookies de Analytics</CategoryTitle>
                <Toggle 
                  active={preferences.analytics} 
                  onClick={() => togglePreference('analytics')}
                />
              </CategoryHeader>
              <CategoryDescription>
                Nos ajudam a entender como você usa o site para melhorarmos a experiência. 
                Utilizamos Google Analytics com dados anonimizados.
              </CategoryDescription>
            </CookieCategory>

            <CookieCategory>
              <CategoryHeader>
                <CategoryTitle>Cookies de Marketing</CategoryTitle>
                <Toggle 
                  active={preferences.marketing} 
                  onClick={() => togglePreference('marketing')}
                />
              </CategoryHeader>
              <CategoryDescription>
                Utilizados para personalizar anúncios e medir a eficácia de campanhas publicitárias. 
                Atualmente não utilizamos cookies de marketing.
              </CategoryDescription>
            </CookieCategory>

            <ButtonGroup>
              <AcceptButton onClick={handleSavePreferences}>
                <FaCheck />
                Salvar Preferências
              </AcceptButton>
              <RejectButton onClick={() => setShowSettings(false)}>
                Cancelar
              </RejectButton>
            </ButtonGroup>
          </Modal>
        </ModalOverlay>
      )}
    </>
  );
};

export default CookieBanner;
