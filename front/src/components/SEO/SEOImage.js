import React from 'react';
import styled from 'styled-components';

const StyledImage = styled.img`
  max-width: 100%;
  height: auto;
  display: block;
`;

const SEOImage = ({ 
  src, 
  alt, 
  title, 
  width, 
  height, 
  loading = "lazy",
  className,
  style,
  ...props 
}) => {
  // Generate better alt text if not provided
  const generateAltText = () => {
    if (alt) return alt;
    
    // Extract filename and create descriptive alt text
    const filename = src?.split('/').pop()?.split('.')[0] || '';
    
    if (filename.includes('logo')) {
      return 'MeuBonsai.App - Logo da plataforma de gerenciamento de bonsai e plantas';
    }
    
    if (filename.includes('bonsai') || filename.includes('plant')) {
      return 'Imagem de bonsai ou planta - MeuBonsai.App';
    }
    
    return 'Imagem relacionada ao gerenciamento de plantas e bonsai';
  };

  const optimizedAlt = generateAltText();

  return (
    <StyledImage
      src={src}
      alt={optimizedAlt}
      title={title || optimizedAlt}
      width={width}
      height={height}
      loading={loading}
      className={className}
      style={style}
      {...props}
    />
  );
};

export default SEOImage;
