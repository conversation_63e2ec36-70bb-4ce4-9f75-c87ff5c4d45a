import React from 'react';
import { Helmet } from 'react-helmet-async';

const SEOHead = ({
  title = "MeuBonsai.App - Gerenciamento Profissional de Bonsai e Plantas",
  description = "Plataforma completa para cuidar dos seus bonsais e plantas. Controle de rega, poda, adubação, galeria de fotos e lembretes inteligentes. Ideal para iniciantes e especialistas em bonsai.",
  keywords = "bonsai, plantas, jardinagem, cuidados com plantas, rega, poda, adubação, galeria de plantas, lembretes de cuidados, cultivo de bonsai, plantas ornamentais, jardinagem urbana",
  image = "https://meubonsai.app/logo_meubonsai.png",
  url = "https://meubonsai.app",
  type = "website",
  author = "MeuBonsai.App",
  siteName = "MeuBonsai.App"
}) => {
  const fullTitle = title.includes('MeuBonsai.App') ? title : `${title} | MeuBonsai.App`;
  
  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="author" content={author} />
      <meta name="robots" content="index, follow" />
      <meta name="language" content="pt-BR" />
      <meta name="revisit-after" content="7 days" />
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={image} />
      <meta property="og:url" content={url} />
      <meta property="og:type" content={type} />
      <meta property="og:site_name" content={siteName} />
      <meta property="og:locale" content="pt_BR" />
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />
      <meta name="twitter:site" content="@meubonsaiapp" />
      <meta name="twitter:creator" content="@meubonsaiapp" />
      
      {/* Additional SEO Meta Tags */}
      <meta name="theme-color" content="#4CAF50" />
      <meta name="msapplication-TileColor" content="#4CAF50" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="MeuBonsai.App" />
      
      {/* Canonical URL */}
      <link rel="canonical" href={url} />
      
      {/* Structured Data - Organization */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "WebApplication",
          "name": "MeuBonsai.App",
          "description": description,
          "url": "https://meubonsai.app",
          "applicationCategory": "LifestyleApplication",
          "operatingSystem": "Web, iOS, Android",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "BRL"
          },
          "author": {
            "@type": "Organization",
            "name": "MeuBonsai.App",
            "url": "https://meubonsai.app"
          },
          "publisher": {
            "@type": "Organization",
            "name": "MeuBonsai.App",
            "logo": {
              "@type": "ImageObject",
              "url": "https://meubonsai.app/logo_meubonsai.png"
            }
          }
        })}
      </script>
    </Helmet>
  );
};

export default SEOHead;
