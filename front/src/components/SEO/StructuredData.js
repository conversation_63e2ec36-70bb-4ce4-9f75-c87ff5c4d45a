import React from 'react';
import { Helmet } from 'react-helmet-async';

const StructuredData = ({ type = "WebApplication", data = {} }) => {
  const getStructuredData = () => {
    const baseData = {
      "@context": "https://schema.org",
      "@type": type,
      "name": "MeuBonsai.App",
      "description": "Plataforma completa para gerenciamento de bonsai e plantas. Controle de rega, poda, adubação, galeria de fotos e lembretes inteligentes.",
      "url": "https://meubonsai.app",
      "applicationCategory": "LifestyleApplication",
      "operatingSystem": "Web, iOS, Android",
      "author": {
        "@type": "Organization",
        "name": "MeuBonsai.App",
        "url": "https://meubonsai.app"
      },
      "publisher": {
        "@type": "Organization",
        "name": "MeuBonsai.App",
        "logo": {
          "@type": "ImageObject",
          "url": "https://meubonsai.app/logo_meubonsai.png"
        }
      }
    };

    // Merge with custom data
    return { ...baseData, ...data };
  };

  const structuredData = getStructuredData();

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(structuredData)}
      </script>
    </Helmet>
  );
};

// Predefined structured data for common pages
export const HomePageStructuredData = () => (
  <StructuredData 
    type="WebApplication"
    data={{
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "BRL",
        "availability": "https://schema.org/InStock"
      },
      "featureList": [
        "Gerenciamento de plantas e bonsai",
        "Galeria de fotos com histórico",
        "Lembretes de cuidados personalizados",
        "Controle de rega, poda e adubação",
        "Interface mobile-friendly"
      ]
    }}
  />
);

export const PricingPageStructuredData = () => (
  <StructuredData 
    type="Product"
    data={{
      "@type": "Product",
      "name": "MeuBonsai.App - Planos Premium",
      "description": "Planos premium para gerenciamento avançado de bonsai e plantas",
      "offers": [
        {
          "@type": "Offer",
          "name": "Plano Básico",
          "price": "23.90",
          "priceCurrency": "BRL",
          "availability": "https://schema.org/InStock",
          "priceValidUntil": "2025-12-31"
        },
        {
          "@type": "Offer", 
          "name": "Plano Premium",
          "price": "59.90",
          "priceCurrency": "BRL",
          "availability": "https://schema.org/InStock",
          "priceValidUntil": "2025-12-31"
        }
      ]
    }}
  />
);

export const LoginPageStructuredData = () => (
  <StructuredData 
    type="WebPage"
    data={{
      "@type": "WebPage",
      "name": "Login - MeuBonsai.App",
      "description": "Faça login no MeuBonsai.App para acessar suas plantas e bonsais",
      "url": "https://meubonsai.app/login"
    }}
  />
);

export default StructuredData;
