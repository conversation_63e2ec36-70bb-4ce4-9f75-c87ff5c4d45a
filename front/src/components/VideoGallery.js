import React, { useState } from 'react';
import styled from 'styled-components';
import { FaPlay, FaTimes, FaExpand } from 'react-icons/fa';
import { getImageUrlSync as getImageUrl } from '../services/api';

// Video placeholder SVG
const VIDEO_PLACEHOLDER = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjZjVmNWY1Ii8+Cjxwb2x5Z29uIHBvaW50cz0iODAsNDAgMTIwLDYwIDgwLDgwIiBmaWxsPSIjNjY2Ii8+Cjx0ZXh0IHg9IjEwMCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5IiBmb250LXNpemU9IjEyIj5WaWRlbzwvdGV4dD4KPC9zdmc+";

const GalleryContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
`;

const VideoThumbnail = styled.div`
  position: relative;
  width: 100%;
  height: 150px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  background: #f5f5f5;
  border: 1px solid #ddd;
  transition: transform 0.2s, box-shadow 0.2s;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
`;

const VideoElement = styled.video`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const PlayOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s;

  ${VideoThumbnail}:hover & {
    opacity: 1;
  }
`;

const PlayButton = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  font-size: 20px;
`;

const VideoInfo = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: white;
  padding: 1rem 0.5rem 0.5rem;
  font-size: 0.75rem;
`;

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
`;

const ModalContent = styled.div`
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
`;

const ModalVideo = styled.video`
  width: 100%;
  height: auto;
  max-height: 80vh;
`;

const CloseButton = styled.button`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  z-index: 1001;

  &:hover {
    background: rgba(0, 0, 0, 0.9);
  }
`;

const FullscreenButton = styled.button`
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem;
  cursor: pointer;
  font-size: 14px;
  z-index: 1001;

  &:hover {
    background: rgba(0, 0, 0, 0.9);
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 2rem;
  color: #666;
  font-style: italic;
`;

const VideoGallery = ({ videos = [], onVideoClick, showCaption = true }) => {
  const [selectedVideo, setSelectedVideo] = useState(null);

  // Função para gerar token simples baseado no token atual
  const generateSimpleToken = () => {
    const token = localStorage.getItem('token');
    if (!token) return '';

    // Extrair payload do JWT atual para pegar user_id
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const userId = payload.sub;

      // Criar um token simples com expiração de 1 hora
      const expiration = Math.floor(Date.now() / 1000) + 3600; // 1 hora
      const simplePayload = {
        exp: expiration,
        sub: userId
      };

      // Para simplificar, vamos usar o mesmo token atual
      return token;
    } catch (error) {
      console.error('Erro ao processar token:', error);
      return '';
    }
  };

  const handleVideoClick = (video) => {
    setSelectedVideo(video);
    if (onVideoClick) {
      onVideoClick(video);
    }
  };

  const handleCloseModal = () => {
    setSelectedVideo(null);
  };

  const handleFullscreen = () => {
    const videoElement = document.querySelector('#modal-video');
    if (videoElement) {
      if (videoElement.requestFullscreen) {
        videoElement.requestFullscreen();
      } else if (videoElement.webkitRequestFullscreen) {
        videoElement.webkitRequestFullscreen();
      } else if (videoElement.mozRequestFullScreen) {
        videoElement.mozRequestFullScreen();
      }
    }
  };

  const getVideoUrl = (video) => {
    console.log('🔍 [VideoGallery] Processing video:', video);

    // Se é um arquivo local (upload), usar preview
    if (video.preview) {
      console.log('📱 [VideoGallery] Using preview URL:', video.preview);
      return video.preview;
    }

    // Para vídeos do servidor, usar getImageUrl() como outros componentes
    if (video.image) {
      console.log('🎥 [VideoGallery] Using image field:', video.image);
      return getImageUrl(video.image);
    }

    // Fallback para estrutura antiga - CONVERTER para URL de API
    if (video.video_path || video.video) {
      const filename = video.video_path || video.video;
      const plantId = video.plant_id;

      console.log('⚠️ [VideoGallery] Using fallback for:', { filename, plantId, care_id: video.care_id });

      // Construir URL de API em vez de R2 direto
      const token = generateSimpleToken();

      if (video.care_id) {
        // Vídeo de cuidado
        const apiUrl = `/api/v1/plants/${plantId}/care-video-stream/${filename}?token=${token}`;
        console.log('🔄 [VideoGallery] Converting to care API URL:', apiUrl);
        return getImageUrl(apiUrl);
      } else {
        // Vídeo de planta
        const apiUrl = `/api/v1/plants/${plantId}/video-stream/${filename}?token=${token}`;
        console.log('🔄 [VideoGallery] Converting to plant API URL:', apiUrl);
        return getImageUrl(apiUrl);
      }
    }

    console.log('❌ [VideoGallery] No valid URL found for video:', video);
    return '';
  };

  if (videos.length === 0) {
    return (
      <EmptyState>
        Nenhum vídeo disponível
      </EmptyState>
    );
  }

  return (
    <>
      <GalleryContainer>
        {videos.map((video, index) => (
          <VideoThumbnail key={index} onClick={() => handleVideoClick(video)}>
            <VideoElement
              src={getVideoUrl(video)}
              muted
              preload="none"
              poster={video.thumbnail_url || VIDEO_PLACEHOLDER}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                // Load video on first click, then play/pause
                if (e.target.readyState === 0) {
                  e.target.load();
                  e.target.addEventListener('loadeddata', () => {
                    e.target.play();
                  }, { once: true });
                } else if (e.target.paused) {
                  e.target.play();
                } else {
                  e.target.pause();
                }
              }}
              onError={(e) => {
                console.error('❌ [VideoGallery] Error loading:', getVideoUrl(video));
              }}
              onLoadedMetadata={(e) => {
                console.log('🎥 [VideoGallery] Loaded metadata:', getVideoUrl(video));
              }}
            />
            <PlayOverlay>
              <PlayButton>
                <FaPlay />
              </PlayButton>
            </PlayOverlay>
            {showCaption && video.caption && (
              <VideoInfo>
                {video.caption}
              </VideoInfo>
            )}
          </VideoThumbnail>
        ))}
      </GalleryContainer>

      {selectedVideo && (
        <ModalOverlay onClick={handleCloseModal}>
          <ModalContent onClick={(e) => e.stopPropagation()}>
            <CloseButton onClick={handleCloseModal}>
              <FaTimes />
            </CloseButton>
            <FullscreenButton onClick={handleFullscreen}>
              <FaExpand />
            </FullscreenButton>
            <ModalVideo
              id="modal-video"
              src={getVideoUrl(selectedVideo)}
              controls
              autoPlay
            />
          </ModalContent>
        </ModalOverlay>
      )}
    </>
  );
};

export default VideoGallery;
