import React from 'react';
import styled from 'styled-components';
import { FaCheck } from 'react-icons/fa';

const ConsentContainer = styled.div`
  margin: ${props => props.theme.spacing.lg} 0;
  padding: ${props => props.theme.spacing.md};
  background: ${props => props.theme.colors.background};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
`;

const ConsentItem = styled.div`
  display: flex;
  align-items: flex-start;
  gap: ${props => props.theme.spacing.sm};
  margin-bottom: ${props => props.theme.spacing.md};

  &:last-child {
    margin-bottom: 0;
  }
`;

const CheckboxContainer = styled.div`
  position: relative;
  flex-shrink: 0;
  margin-top: 2px;
`;

const HiddenCheckbox = styled.input.attrs({ type: 'checkbox' })`
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
`;

const StyledCheckbox = styled.div`
  width: 20px;
  height: 20px;
  border: 2px solid ${props => props.checked ? props.theme.colors.primary : props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.sm};
  background: ${props => props.checked ? props.theme.colors.primary : 'transparent'};
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: ${props => props.theme.colors.primary};
  }

  svg {
    color: white;
    font-size: 12px;
    opacity: ${props => props.checked ? 1 : 0};
    transition: opacity 0.2s ease;
  }
`;

const ConsentText = styled.div`
  flex: 1;
  color: ${props => props.theme.colors.text.secondary};
  font-size: 0.9rem;
  line-height: 1.5;

  a {
    color: ${props => props.theme.colors.primary};
    text-decoration: none;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }

  strong {
    color: ${props => props.theme.colors.text.primary};
  }
`;

const RequiredLabel = styled.span`
  color: #e74c3c;
  font-weight: 500;
  margin-left: ${props => props.theme.spacing.xs};
`;





const ErrorMessage = styled.div`
  color: #e74c3c;
  font-size: 0.8rem;
  margin-top: ${props => props.theme.spacing.xs};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
`;

const ConsentCheckbox = ({
  consents,
  onChange,
  errors = {}
}) => {
  const handleConsentChange = (consentType) => {
    onChange({
      ...consents,
      [consentType]: !consents[consentType]
    });
  };

  return (
    <ConsentContainer>
      {/* Consentimento obrigatório unificado */}
      <ConsentItem>
        <CheckboxContainer>
          <HiddenCheckbox
            checked={consents.terms || false}
            onChange={() => handleConsentChange('terms')}
          />
          <StyledCheckbox
            checked={consents.terms || false}
            onClick={() => handleConsentChange('terms')}
          >
            <FaCheck />
          </StyledCheckbox>
        </CheckboxContainer>
        <ConsentText>
          Li e concordo com os{' '}
          <a href="/terms-of-service" target="_blank" rel="noopener noreferrer">
            Termos de Uso
          </a>{' '}
          e{' '}
          <a href="/privacy-policy" target="_blank" rel="noopener noreferrer">
            Política de Privacidade
          </a>, autorizando o tratamento dos meus dados para prestação dos serviços.
          <RequiredLabel>*</RequiredLabel>
          {errors.terms && (
            <ErrorMessage>
              ⚠️ Aceite obrigatório para criar sua conta
            </ErrorMessage>
          )}
        </ConsentText>
      </ConsentItem>
    </ConsentContainer>
  );
};

export default ConsentCheckbox;
