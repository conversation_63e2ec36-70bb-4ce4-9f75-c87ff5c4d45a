import React, { useState, useMemo, forwardRef, useImperativeHandle } from 'react';
import Calendar from 'react-calendar';
import styled from 'styled-components';
import { FaCalendarAlt, FaCamera, FaLeaf, FaTimes, FaChartBar, FaBell, FaPlus, FaEdit, FaTrash, FaCheck } from 'react-icons/fa';
import { getImageUrlSync as getImageUrl } from '../services/api';
import remindersService from '../services/remindersService';
import ImageModal from './ImageModal';
import ReminderModal from './ReminderModal';
import { toast } from 'react-toastify';
import 'react-calendar/dist/Calendar.css';

const CalendarWrapper = styled.div`
  flex: 1;
  min-width: 0; /* Permite que o calendário se ajuste */

  @media (max-width: 1024px) {
    min-width: 100%;
  }
`;

const CalendarContainer = styled.div`
  background: transparent;
  border-radius: 0;
  padding: 0;

  .react-calendar {
    width: 100%;
    border: none;
    font-family: inherit;
  }

  .react-calendar__navigation {
    display: flex;
    height: 44px;
    margin-bottom: 1em;
  }

  .react-calendar__navigation button {
    min-width: 44px;
    background: none;
    border: none;
    font-size: 16px;
    font-weight: bold;
    color: ${props => props.theme.colors.text.primary};
    cursor: pointer;
    padding: 8px;
    border-radius: ${props => props.theme.borderRadius.sm};
    transition: background-color 0.2s ease;

    &:hover {
      background-color: ${props => props.theme.colors.background.secondary};
    }

    &:disabled {
      background-color: transparent;
      color: ${props => props.theme.colors.text.disabled};
    }
  }

  .react-calendar__month-view__weekdays {
    text-align: center;
    text-transform: uppercase;
    font-weight: bold;
    font-size: 0.75em;
    color: ${props => props.theme.colors.text.secondary};
  }

  .react-calendar__month-view__weekdays__weekday {
    padding: 0.5em;
  }

  .react-calendar__month-view__days__day {
    position: relative;
    padding: 8px;
    background: none;
    border: none;
    cursor: pointer;
    border-radius: ${props => props.theme.borderRadius.sm};
    transition: all 0.2s ease;
    min-height: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;

    &:hover {
      background-color: ${props => props.theme.colors.background.secondary};
    }

    &.react-calendar__month-view__days__day--neighboringMonth {
      color: ${props => props.theme.colors.text.disabled};
    }
  }

  .react-calendar__tile--active {
    background: ${props => props.theme.colors.primary} !important;
    color: white !important;
  }

  .react-calendar__tile--now {
    background: ${props => props.theme.colors.background.accent};
    font-weight: bold;
  }
`;

const DayIndicators = styled.div`
  display: flex;
  gap: ${props => props.gap || '2px'};
  margin-top: 4px;
  flex-wrap: wrap;
  justify-content: center;
  min-height: 8px; /* Garante espaço mínimo */
`;

const Indicator = styled.div`
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: ${props => props.color || '#6c757d'};
`;

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background: white;
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.lg};
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${props => props.theme.spacing.lg};
  padding-bottom: ${props => props.theme.spacing.sm};
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const ModalTitle = styled.h3`
  margin: 0;
  color: ${props => props.theme.colors.text.primary};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: ${props => props.theme.colors.text.secondary};
  padding: 4px;
  border-radius: ${props => props.theme.borderRadius.sm};

  &:hover {
    background-color: ${props => props.theme.colors.background.secondary};
  }
`;

const EventList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.md};
`;

const EventItem = styled.div`
  display: flex;
  align-items: flex-start;
  gap: ${props => props.theme.spacing.sm};
  padding: ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.md};
  background-color: ${props => props.theme.colors.background.secondary};
`;

const EventIcon = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${props => {
    switch (props.type) {
      case 'care': return '#28a745';
      case 'photo': return '#007bff';
      default: return '#6c757d';
    }
  }};
  color: white;
  font-size: 14px;
`;

const EventContent = styled.div`
  flex: 1;
`;

const EventTitle = styled.div`
  font-weight: bold;
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: 4px;
`;

const EventDescription = styled.div`
  color: ${props => props.theme.colors.text.secondary};
  font-size: 14px;
`;

const EventActions = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.xs};
  margin-top: ${props => props.theme.spacing.xs};
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.sm};
  background-color: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text.secondary};
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;

  &:hover {
    background-color: ${props => {
      if (props.variant === 'danger') return '#dc3545';
      if (props.variant === 'success') return '#28a745';
      return props.theme.colors.primary;
    }};
    color: white;
    border-color: ${props => {
      if (props.variant === 'danger') return '#dc3545';
      if (props.variant === 'success') return '#28a745';
      return props.theme.colors.primary;
    }};
  }

  &:active {
    transform: scale(0.95);
  }
`;

const EventImage = styled.img`
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: ${props => props.theme.borderRadius.sm};
  cursor: pointer;
  margin-top: 8px;

  &:hover {
    opacity: 0.8;
  }
`;

const CalendarLayout = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.lg};
  align-items: flex-start;
  height: 100%;

  @media (max-width: 1024px) {
    flex-direction: column;
  }
`;

const DashboardContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.sm};
  width: 300px;
  flex-shrink: 0;

  @media (max-width: 1024px) {
    width: 100%;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  @media (max-width: 768px) {
    justify-content: center;
  }
`;

const CareCard = styled.div`
  background: ${props => props.theme.colors.background.secondary};
  border-radius: ${props => props.theme.borderRadius.md};
  padding: ${props => props.theme.spacing.md};
  text-align: center;
  border-left: 4px solid ${props => props.color || '#ccc'};
  width: 100%;

  @media (max-width: 1024px) {
    width: calc(50% - 8px);
    min-width: 180px;
  }

  @media (max-width: 768px) {
    width: calc(50% - 8px);
    min-width: 160px;
  }

  @media (max-width: 480px) {
    width: 100%;
  }
`;

const CareType = styled.div`
  font-size: 12px;
  font-weight: bold;
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: 4px;
  text-transform: uppercase;
`;

const DaysAgo = styled.div`
  font-size: 18px;
  font-weight: bold;
  color: ${props => props.color || props.theme.colors.text.secondary};
  margin-bottom: 2px;
`;

const LastDate = styled.div`
  color: ${props => props.theme.colors.text.secondary};
  font-size: 11px;
`;

const Legend = styled.div`
  display: flex;
  justify-content: center;
  gap: ${props => props.theme.spacing.md};
  margin-bottom: ${props => props.theme.spacing.md};
  flex-wrap: wrap;
`;

const LegendItem = styled.div`
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: ${props => props.theme.colors.text.secondary};
`;

const LegendDot = styled.div`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: ${props => props.color || '#6c757d'};
`;



// Cores para cada tipo de cuidado
const CARE_COLORS = {
  'poda': '#e74c3c',           // Vermelho
  'adubacao': '#f39c12',       // Laranja
  'transplante': '#8e44ad',    // Roxo
  'limpeza': '#3498db',        // Azul
  'aramacao': '#2ecc71',       // Verde
  'tratamento': '#e67e22',     // Laranja escuro
  'outro': '#95a5a6'           // Cinza
};

// Cores para lembretes (mais claras que os cuidados)
const REMINDER_COLORS = {
  'poda': '#ff6b6b',           // Vermelho claro
  'adubacao': '#ffa726',       // Laranja claro
  'transplante': '#ab47bc',    // Roxo claro
  'limpeza': '#42a5f5',        // Azul claro
  'aramacao': '#66bb6a',       // Verde claro
  'tratamento': '#ff7043',     // Laranja escuro claro
  'outro': '#bdbdbd'           // Cinza claro
};

const PlantCalendar = forwardRef(({ plant, cares = [], images = [], reminders = [], onReminderSuccess }, ref) => {
  const [selectedDate, setSelectedDate] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [showImageModal, setShowImageModal] = useState(false);
  const [modalImageSrc, setModalImageSrc] = useState('');
  const [showReminderModal, setShowReminderModal] = useState(false);
  const [editingReminder, setEditingReminder] = useState(null);
  const [showEditReminderModal, setShowEditReminderModal] = useState(false);

  // Expor função para componente pai
  useImperativeHandle(ref, () => ({
    openReminderModal: () => setShowReminderModal(true)
  }));

  // Processar eventos por data
  const eventsByDate = useMemo(() => {
    const events = {};

    // Adicionar cuidados
    cares.forEach(care => {
      // Criar data local para evitar problemas de timezone
      const careDate = new Date(care.date);
      // Ajustar para timezone local
      const localDate = new Date(careDate.getTime() + careDate.getTimezoneOffset() * 60000);
      const dateKey = localDate.toDateString();

      if (!events[dateKey]) events[dateKey] = [];
      events[dateKey].push({
        type: 'care',
        id: care.id,
        title: care.care_type_display || care.care_type,
        description: care.description,
        notes: care.notes,
        image: care.image,
        date: care.date,
        care_type: care.care_type,
        care_types: care.care_types // Add care_types for proper identification
      });
    });

    // Adicionar fotos
    images.forEach(image => {
      if (image.photo_date) {
        const dateKey = new Date(image.photo_date).toDateString();
        if (!events[dateKey]) events[dateKey] = [];
        events[dateKey].push({
          type: 'photo',
          id: image.id,
          title: 'Foto adicionada',
          description: image.caption || 'Sem descrição',
          image: image.image,
          date: image.photo_date
        });
      }
    });

    // Adicionar lembretes
    reminders.forEach(reminder => {
      if (reminder.scheduled_date && !reminder.is_completed) {
        // Criar data local para evitar problemas de timezone
        const dateParts = reminder.scheduled_date.split('-');
        const localDate = new Date(parseInt(dateParts[0]), parseInt(dateParts[1]) - 1, parseInt(dateParts[2]));
        const dateKey = localDate.toDateString();

        if (!events[dateKey]) events[dateKey] = [];
        events[dateKey].push({
          type: 'reminder',
          id: reminder.id,
          title: `Lembrete: ${reminder.care_type_display}`,
          description: reminder.description || `Lembrete de ${reminder.care_type_display.toLowerCase()}`,
          date: reminder.scheduled_date,
          care_type: reminder.care_type,
          is_overdue: reminder.is_overdue,
          days_until_due: reminder.days_until_due
        });
      }
    });

    return events;
  }, [cares, images, reminders]);

  // Calcular últimos cuidados por tipo
  const lastCaresByType = useMemo(() => {
    const careTypes = ['poda', 'adubacao', 'transplante', 'limpeza', 'aramacao', 'desfolha'];
    const lastCares = {};

    careTypes.forEach(type => {
      const caresOfType = cares.filter(care => {
        // Support both old care_type and new care_types array
        if (care.care_types && Array.isArray(care.care_types)) {
          return care.care_types.includes(type);
        }
        return care.care_type === type;
      });
      if (caresOfType.length > 0) {
        const lastCare = caresOfType.reduce((latest, current) =>
          new Date(current.date) > new Date(latest.date) ? current : latest
        );

        // Criar datas locais para evitar problemas de timezone
        const today = new Date();
        const careDate = new Date(lastCare.date);

        // Ajustar para timezone local
        const localCareDate = new Date(careDate.getTime() + careDate.getTimezoneOffset() * 60000);

        // Zerar as horas para comparar apenas as datas
        today.setHours(0, 0, 0, 0);
        localCareDate.setHours(0, 0, 0, 0);

        const daysAgo = Math.floor((today.getTime() - localCareDate.getTime()) / (1000 * 60 * 60 * 24));
        lastCares[type] = {
          date: lastCare.date,
          daysAgo: daysAgo,
          care: lastCare
        };
      } else {
        lastCares[type] = null;
      }
    });

    return lastCares;
  }, [cares]);

  // Função para renderizar conteúdo do dia/mês/ano
  const tileContent = ({ date, view }) => {
    let eventsToShow = [];

    if (view === 'month') {
      // Visualização de mês: mostrar eventos do dia específico
      const dateKey = date.toDateString();
      eventsToShow = eventsByDate[dateKey] || [];
    } else if (view === 'year') {
      // Visualização de ano: agregar eventos do mês
      const year = date.getFullYear();
      const month = date.getMonth();

      eventsToShow = Object.values(eventsByDate).flat().filter(event => {
        const eventDate = new Date(event.date);
        return eventDate.getFullYear() === year && eventDate.getMonth() === month;
      });
    } else if (view === 'decade') {
      // Visualização de década: agregar eventos do ano
      const year = date.getFullYear();

      eventsToShow = Object.values(eventsByDate).flat().filter(event => {
        const eventDate = new Date(event.date);
        return eventDate.getFullYear() === year;
      });
    }

    if (!eventsToShow || eventsToShow.length === 0) return null;

    // Separar eventos por tipo
    const careEvents = eventsToShow.filter(event => event.type === 'care');
    const photoEvents = eventsToShow.filter(event => event.type === 'photo');
    const reminderEvents = eventsToShow.filter(event => event.type === 'reminder');

    // Ajustar tamanho dos indicadores baseado na visualização
    const indicatorSize = view === 'month' ? '6px' : view === 'year' ? '4px' : '3px';
    const indicatorGap = view === 'month' ? '2px' : '1px';

    // Para visualizações agregadas, mostrar apenas um indicador por tipo de cuidado
    const uniqueCareTypes = [...new Set(careEvents.flatMap(care => {
      // Support both old care_type and new care_types array
      if (care.care_types && Array.isArray(care.care_types)) {
        return care.care_types;
      }
      return care.care_type ? [care.care_type] : [];
    }))];

    return (
      <DayIndicators style={{ gap: indicatorGap }}>
        {/* Indicadores para tipos únicos de cuidado */}
        {uniqueCareTypes.map((careType, index) => (
          <Indicator
            key={`care-${careType}-${index}`}
            style={{
              backgroundColor: CARE_COLORS[careType] || '#95a5a6',
              width: indicatorSize,
              height: indicatorSize,
              minWidth: indicatorSize,
              minHeight: indicatorSize
            }}
          />
        ))}

        {/* Indicador para lembretes (se houver) */}
        {reminderEvents.length > 0 && (
          <Indicator
            key="reminders"
            style={{
              backgroundColor: '#ffa726',
              opacity: 0.8,
              width: indicatorSize,
              height: indicatorSize,
              minWidth: indicatorSize,
              minHeight: indicatorSize
            }}
          />
        )}

        {/* Indicador para fotos (se houver) */}
        {photoEvents.length > 0 && (
          <Indicator
            key="photos"
            style={{
              backgroundColor: '#007bff',
              width: indicatorSize,
              height: indicatorSize,
              minWidth: indicatorSize,
              minHeight: indicatorSize
            }}
          />
        )}
      </DayIndicators>
    );
  };

  // Função para lidar com clique no dia
  const handleDayClick = (date) => {
    const dateKey = date.toDateString();
    const dayEvents = eventsByDate[dateKey];

    if (dayEvents && dayEvents.length > 0) {
      setSelectedDate(date);
      setShowModal(true);
    }
  };

  const selectedEvents = selectedDate ? eventsByDate[selectedDate.toDateString()] || [] : [];

  const handleEventImageClick = (imageSrc, title) => {
    setModalImageSrc(imageSrc);
    setShowImageModal(true);
  };

  const handleAddReminder = () => {
    setShowReminderModal(true);
  };

  const handleReminderSuccess = () => {
    setShowReminderModal(false);
    if (onReminderSuccess) {
      onReminderSuccess();
    }
  };

  const handleReminderCancel = () => {
    setShowReminderModal(false);
  };

  const handleEditReminder = (reminder) => {
    setEditingReminder(reminder);
    setShowEditReminderModal(true);
  };

  const handleEditReminderSuccess = () => {
    setShowEditReminderModal(false);
    setEditingReminder(null);
    if (onReminderSuccess) {
      onReminderSuccess();
    }
  };

  const handleEditReminderCancel = () => {
    setShowEditReminderModal(false);
    setEditingReminder(null);
  };

  const handleDeleteReminder = async (reminderId) => {
    if (window.confirm('Tem certeza que deseja remover este lembrete?')) {
      try {
        await remindersService.deleteReminder(reminderId);
        toast.success('Lembrete removido com sucesso!');
        if (onReminderSuccess) {
          onReminderSuccess();
        }
      } catch (error) {
        console.error('Erro ao remover lembrete:', error);
        toast.error('Erro ao remover lembrete');
      }
    }
  };

  const handleCompleteReminder = async (reminderId) => {
    try {
      await remindersService.completeReminder(reminderId);
      toast.success('Lembrete marcado como concluído!');
      if (onReminderSuccess) {
        onReminderSuccess();
      }
    } catch (error) {
      console.error('Erro ao completar lembrete:', error);
      toast.error('Erro ao completar lembrete');
    }
  };

  const getCareDisplayName = (type) => {
    const names = {
      'poda': 'Poda',
      'adubacao': 'Adubação',
      'transplante': 'Transplante',
      'limpeza': 'Limpeza',
      'aramacao': 'Aramação'
    };
    return names[type] || type;
  };

  return (
    <>
      <CalendarLayout>
        <CalendarWrapper>
          <CalendarContainer>
            <Legend>
              <LegendItem>
                <LegendDot style={{ backgroundColor: CARE_COLORS.poda }} />
                Poda
              </LegendItem>
              <LegendItem>
                <LegendDot style={{ backgroundColor: CARE_COLORS.adubacao }} />
                Adubação
              </LegendItem>
              <LegendItem>
                <LegendDot style={{ backgroundColor: CARE_COLORS.transplante }} />
                Transplante
              </LegendItem>
              <LegendItem>
                <LegendDot style={{ backgroundColor: CARE_COLORS.limpeza }} />
                Limpeza
              </LegendItem>
              <LegendItem>
                <LegendDot style={{ backgroundColor: CARE_COLORS.aramacao }} />
                Aramação
              </LegendItem>
              <LegendItem>
                <LegendDot style={{ backgroundColor: '#007bff' }} />
                Fotos
              </LegendItem>
              <LegendItem>
                <LegendDot style={{ backgroundColor: '#ffa726', opacity: 0.8 }} />
                Lembretes
              </LegendItem>
            </Legend>

            <Calendar
              onChange={handleDayClick}
              value={new Date()}
              tileContent={tileContent}
              locale="pt-BR"
            />
          </CalendarContainer>
        </CalendarWrapper>

        <DashboardContainer>
          {['poda', 'adubacao', 'transplante', 'limpeza', 'aramacao'].map(type => {
            const lastCare = lastCaresByType[type];
            return (
              <CareCard key={type} color={CARE_COLORS[type]}>
                <CareType>{getCareDisplayName(type)}</CareType>
                {lastCare ? (
                  <>
                    <DaysAgo color={CARE_COLORS[type]}>
                      {lastCare.daysAgo === 0 ? 'Hoje' :
                       lastCare.daysAgo === 1 ? '1 dia' :
                       `${lastCare.daysAgo} dias`}
                    </DaysAgo>
                    <LastDate>
                      {(() => {
                        const careDate = new Date(lastCare.date);
                        const localDate = new Date(careDate.getTime() + careDate.getTimezoneOffset() * 60000);
                        return localDate.toLocaleDateString('pt-BR');
                      })()}
                    </LastDate>
                  </>
                ) : (
                  <>
                    <DaysAgo>-</DaysAgo>
                    <LastDate>Nunca realizado</LastDate>
                  </>
                )}
              </CareCard>
            );
          })}
        </DashboardContainer>
      </CalendarLayout>

      {showModal && (
        <Modal>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>
                <FaCalendarAlt style={{ marginRight: '8px' }} />
                {selectedDate?.toLocaleDateString('pt-BR', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </ModalTitle>
              <CloseButton onClick={() => setShowModal(false)}>
                <FaTimes />
              </CloseButton>
            </ModalHeader>

            <EventList>
              {selectedEvents.map((event, index) => (
                <EventItem key={`${event.type}-${event.id}-${index}`}>
                  <EventIcon type={event.type}>
                    {event.type === 'care' ? <FaLeaf /> :
                     event.type === 'reminder' ? <FaBell /> : <FaCamera />}
                  </EventIcon>
                  <EventContent>
                    <EventTitle>{event.title}</EventTitle>
                    <EventDescription>{event.description}</EventDescription>
                    {event.notes && (
                      <EventDescription style={{ fontStyle: 'italic', marginTop: '4px' }}>
                        {event.notes}
                      </EventDescription>
                    )}
                    {event.image && (
                      <EventImage
                        src={getImageUrl(event.image)}
                        alt={event.title}
                        onClick={() => handleEventImageClick(getImageUrl(event.image), event.title)}
                      />
                    )}
                    {event.type === 'reminder' && (
                      <EventActions>
                        <ActionButton
                          onClick={() => handleCompleteReminder(event.id)}
                          title="Marcar como concluído"
                          variant="success"
                        >
                          <FaCheck />
                        </ActionButton>
                        <ActionButton
                          onClick={() => handleEditReminder(event)}
                          title="Editar lembrete"
                        >
                          <FaEdit />
                        </ActionButton>
                        <ActionButton
                          onClick={() => handleDeleteReminder(event.id)}
                          title="Remover lembrete"
                          variant="danger"
                        >
                          <FaTrash />
                        </ActionButton>
                      </EventActions>
                    )}
                  </EventContent>
                </EventItem>
              ))}
            </EventList>
          </ModalContent>
        </Modal>
      )}

      <ImageModal
        isOpen={showImageModal}
        onClose={() => setShowImageModal(false)}
        images={[{
          src: modalImageSrc,
          title: 'Foto do evento',
          description: ''
        }]}
        currentIndex={0}
      />

      <ReminderModal
        isOpen={showReminderModal}
        onClose={handleReminderCancel}
        plantId={plant?.id}
        onSuccess={handleReminderSuccess}
        mode="manual"
      />

      <ReminderModal
        isOpen={showEditReminderModal}
        onClose={handleEditReminderCancel}
        plantId={plant?.id}
        onSuccess={handleEditReminderSuccess}
        mode="edit"
        reminder={editingReminder}
      />
    </>
  );
});

export default PlantCalendar;
