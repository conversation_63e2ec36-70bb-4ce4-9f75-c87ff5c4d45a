import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaCalendar<PERSON>lt, FaExclamation<PERSON><PERSON>gle, FaUser, FaCreditCard, FaUsers, FaHeart, FaComment } from 'react-icons/fa';
import { useQuery } from 'react-query';
import remindersService from '../services/remindersService';
import notificationsService from '../services/notificationsService';
import { toast } from 'react-toastify';

const NotificationContainer = styled.div`
  position: relative;
  display: inline-block;
`;

const BellButton = styled.button`
  background: rgba(44, 62, 80, 0.1);
  border: none;
  color: #2c3e50;
  font-size: 1.1rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  position: relative;
  transition: all 0.2s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: rgba(44, 62, 80, 0.15);
    color: #2c3e50;
  }
`;

const Badge = styled.span`
  position: absolute;
  top: 0;
  right: 0;
  background-color: #e74c3c;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 0.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
`;

const Dropdown = styled.div`
  position: absolute;
  top: 100%;
  right: 0;
  background: ${props => props.theme.colors.surface};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.lg};
  box-shadow: ${props => props.theme.shadows.lg};
  width: 350px;
  max-height: 400px;
  overflow-y: auto;
  z-index: 1000;
  margin-top: ${props => props.theme.spacing.xs};

  @media (max-width: 768px) {
    width: 320px;
    right: -10px;
  }

  @media (max-width: 480px) {
    width: 280px;
    right: -20px;
    max-height: 350px;
  }

  @media (max-width: 360px) {
    width: 250px;
    right: -30px;
  }
`;

const DropdownHeader = styled.div`
  padding: ${props => props.theme.spacing.md};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  font-weight: 600;
  color: ${props => props.theme.colors.text.primary};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const NotificationItem = styled.div`
  padding: ${props => props.theme.spacing.md};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${props => props.theme.colors.background};
  }

  &:last-child {
    border-bottom: none;
  }
`;

const NotificationContent = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: ${props => props.theme.spacing.sm};
`;

const NotificationInfo = styled.div`
  flex: 1;
`;

const PlantName = styled.div`
  font-weight: 600;
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing.xs};
`;

const CareType = styled.div`
  color: ${props => props.theme.colors.text.secondary};
  font-size: 0.9rem;
  margin-bottom: ${props => props.theme.spacing.xs};
`;

const DueDate = styled.div`
  font-size: 0.8rem;
  color: ${props => props.isOverdue ? '#e74c3c' : props.theme.colors.text.secondary};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
`;

const ActionButton = styled.button`
  background: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: ${props => props.theme.borderRadius.sm};
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  font-size: 0.8rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
  transition: background-color 0.2s ease;

  &:hover {
    background: ${props => props.theme.colors.primaryDark};
  }
`;

const EmptyState = styled.div`
  padding: ${props => props.theme.spacing.xl};
  text-align: center;
  color: ${props => props.theme.colors.text.secondary};
`;

const NotificationBell = () => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  const buttonRef = useRef(null);

  // Buscar histórico de notificações sociais e financeiras (últimas 50)
  const { data: notifications = [], refetch: refetchNotifications } = useQuery(
    ['socialNotifications', 'all'], // Nova chave para forçar cache refresh
    () => {
      console.log('🔍 Buscando notificações com unreadOnly=false');
      return notificationsService.getNotifications(1, 50, false).then(res => {
        console.log('📊 Notificações recebidas:', res.length);
        return res;
      });
    },
    {
      refetchInterval: 2 * 60 * 1000, // Refetch a cada 2 minutos
      staleTime: 0, // Sempre considerar dados como stale
      cacheTime: 0, // Não manter cache
    }
  );

  // Verificar se é uma notificação que deve ser marcada como lida automaticamente
  const isAutoReadNotification = (type) => {
    return ['new_follower', 'plant_like', 'plant_comment', 'payment_success', 'payment_failed', 'subscription_renewal', 'subscription_expiring', 'subscription_expired'].includes(type);
  };

  // Buscar estatísticas para o badge (usar unseen_count)
  const { data: stats, refetch: refetchStats } = useQuery(
    'notificationStats',
    () => notificationsService.getNotificationStats(),
    {
      refetchInterval: 2 * 60 * 1000, // Refetch a cada 2 minutos
    }
  );

  // Filtrar apenas notificações sociais e financeiras (excluir lembretes)
  const socialAndFinancialNotifications = notifications.filter(n =>
    n.type && n.type !== 'reminder'
  );

  // HISTÓRICO: Badge sempre limpo (0), mas mantém histórico na lista
  // Apenas notificações realmente novas (não vistas) aparecem no badge
  const totalCount = Math.max(0, stats?.unseen_count || 0);

  // CORREÇÃO: Mostrar todas as notificações em uma lista única
  // Não separar por seen_at porque isso muda quando abre o dropdown

  // Fechar dropdown ao clicar fora
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        dropdownRef.current && 
        !dropdownRef.current.contains(event.target) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Abrir dropdown e limpar badge (marcar todas como vistas)
  const handleDropdownOpen = async () => {
    const wasOpen = isOpen;
    setIsOpen(!isOpen);

    // Se está abrindo (não estava aberto antes), processar notificações
    if (!wasOpen) {
      // Aguardar um pouco para o dropdown aparecer antes de processar
      setTimeout(async () => {
        try {
          // SEMPRE marcar como "vistas" para limpar o badge (histórico limpo)
          await notificationsService.markAllAsSeen();

          // Marcar notificações sociais e financeiras como lidas automaticamente
          const unreadAutoReadNotifications = socialAndFinancialNotifications.filter(
            notification => !notification.is_read && isAutoReadNotification(notification.type)
          );

          if (unreadAutoReadNotifications.length > 0) {
            // Marcar cada notificação social/financeira como lida
            await Promise.all(
              unreadAutoReadNotifications.map(notification =>
                notificationsService.markAsRead(notification.id)
              )
            );
          }

          // Atualizar apenas as estatísticas (não refetch das notificações para não sumir da tela)
          await refetchStats();
        } catch (error) {
          console.error('Erro ao processar notificações:', error);
        }
      }, 500); // Aguardar 500ms para o dropdown aparecer
    }
  };

  const handleMarkNotificationAsRead = async (notificationId, e) => {
    e.stopPropagation();
    try {
      await notificationsService.markAsRead(notificationId);
      // Refetch all queries to ensure consistency
      await Promise.all([
        refetchNotifications(),
        refetchStats()
      ]);
      toast.success('Notificação marcada como lida!');
    } catch (error) {
      console.error('Erro ao marcar notificação como lida:', error);
      toast.error('Erro ao marcar notificação como lida');
    }
  };

  // Função para lidar com clique em notificações sociais (já marcadas como lidas ao abrir)
  const handleSocialNotificationClick = async (notification, e) => {
    e.stopPropagation();
    try {
      // Navegar para o conteúdo relacionado se necessário
      if (notification.type === 'plant_like' || notification.type === 'plant_comment') {
        // Navegar para a planta se tiver plant_id nos dados
        if (notification.data?.plant_id) {
          window.location.href = `/plants/${notification.data.plant_id}`;
        }
      } else if (notification.type === 'new_follower') {
        // Navegar para o perfil do seguidor se tiver follower_id
        if (notification.data?.follower_id) {
          window.location.href = `/profile/${notification.data.follower_id}`;
        }
      }
    } catch (error) {
      console.error('Erro ao processar notificação social:', error);
    }
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'new_follower':
        return <FaUser />;
      case 'plant_like':
        return <FaHeart style={{ color: '#e74c3c' }} />;
      case 'plant_comment':
        return <FaComment style={{ color: '#3498db' }} />;
      case 'subscription_renewal':
      case 'subscription_expiring':
      case 'subscription_expired':
      case 'payment_success':
      case 'payment_failed':
        return <FaCreditCard />;
      case 'reminder':
        return <FaCalendarAlt />;
      default:
        return <FaBell />;
    }
  };

  const formatDueDate = (reminder) => {
    const daysUntilDue = reminder.days_until_due;
    
    if (daysUntilDue < 0) {
      return `${Math.abs(daysUntilDue)} dias atrasado`;
    } else if (daysUntilDue === 0) {
      return 'Hoje';
    } else if (daysUntilDue === 1) {
      return 'Amanhã';
    } else {
      return `Em ${daysUntilDue} dias`;
    }
  };

  return (
    <NotificationContainer>
      <BellButton
        ref={buttonRef}
        onClick={handleDropdownOpen}
        title="Histórico de notificações sociais e financeiras"
      >
        <FaBell />
        {totalCount > 0 && <Badge>{totalCount > 99 ? '99+' : totalCount}</Badge>}
      </BellButton>

      {isOpen && (
        <Dropdown ref={dropdownRef}>
          <DropdownHeader>
            <FaBell />
            Histórico de Notificações ({socialAndFinancialNotifications.length})
          </DropdownHeader>

          {socialAndFinancialNotifications.length === 0 ? (
            <EmptyState>
              <FaBell size={32} style={{ marginBottom: '8px', opacity: 0.5 }} />
              <div>Nenhuma notificação no histórico</div>
            </EmptyState>
          ) : (
            socialAndFinancialNotifications.map((notification) => (
              <NotificationItem
                key={`notification_${notification.id}`}
                onClick={isAutoReadNotification(notification.type)
                  ? (e) => handleSocialNotificationClick(notification, e)
                  : undefined}
                style={{
                  cursor: isAutoReadNotification(notification.type) ? 'pointer' : 'default',
                  opacity: notification.is_read ? 0.7 : 1
                }}
              >
                <NotificationContent>
                  <NotificationInfo>
                    <PlantName style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      {getNotificationIcon(notification.type)}
                      {notification.title}
                      {notification.is_read && <span style={{ fontSize: '12px', color: '#666' }}>(lida)</span>}
                    </PlantName>
                    <CareType>{notification.message}</CareType>
                    <DueDate>
                      {new Date(notification.created_at).toLocaleDateString('pt-BR')}
                    </DueDate>
                  </NotificationInfo>

                  {/* Notificações sociais e financeiras não mostram botão (auto-lidas ao abrir) */}
                  {!isAutoReadNotification(notification.type) && !notification.is_read && (
                    <ActionButton
                      onClick={(e) => handleMarkNotificationAsRead(notification.id, e)}
                      title="Marcar como lida"
                    >
                      <FaCheck />
                      Lida
                    </ActionButton>
                  )}
                </NotificationContent>
              </NotificationItem>
            ))
          )}
        </Dropdown>
      )}
    </NotificationContainer>
  );
};

export default NotificationBell;
