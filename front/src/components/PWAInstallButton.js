import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FaMobile, FaTimes } from 'react-icons/fa';

const InstallButton = styled.button`
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
  position: relative;
  overflow: hidden;

  &:hover {
    background: linear-gradient(135deg, #45a049, #3d8b40);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
  }

  &:active {
    transform: translateY(0);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover::before {
    left: 100%;
  }

  svg {
    font-size: 0.9rem;
  }

  @media (max-width: 768px) {
    padding: 6px 10px;
    font-size: 0.8rem;
    
    svg {
      font-size: 0.85rem;
    }
  }
`;

const InstallPrompt = styled.div`
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border-radius: 12px;
  padding: 16px 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  gap: 12px;
  max-width: 90vw;
  z-index: 1000;
  animation: slideUp 0.3s ease-out;

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateX(-50%) translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
  }

  @media (max-width: 480px) {
    bottom: 16px;
    left: 16px;
    right: 16px;
    transform: none;
    max-width: none;
    padding: 14px 16px;
  }
`;

const PromptContent = styled.div`
  flex: 1;
`;

const PromptTitle = styled.div`
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
  margin-bottom: 4px;
`;

const PromptText = styled.div`
  color: #666;
  font-size: 0.8rem;
  line-height: 1.3;
`;

const PromptActions = styled.div`
  display: flex;
  gap: 8px;
  align-items: center;
`;

const PromptButton = styled.button`
  background: ${props => props.primary ? '#4CAF50' : 'transparent'};
  color: ${props => props.primary ? 'white' : '#666'};
  border: ${props => props.primary ? 'none' : '1px solid #ddd'};
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${props => props.primary ? '#45a049' : '#f5f5f5'};
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: #f5f5f5;
    color: #666;
  }
`;

const PWAInstallButton = () => {
  const [deferredPrompt, setDeferredPrompt] = useState(null);
  const [showInstallPrompt, setShowInstallPrompt] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Detect if device is mobile
    const checkMobile = () => {
      const userAgent = navigator.userAgent || navigator.vendor || window.opera;
      const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent.toLowerCase());
      const isSmallScreen = window.innerWidth <= 768;
      const hasTouchScreen = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      return isMobileDevice || (isSmallScreen && hasTouchScreen);
    };

    setIsMobile(checkMobile());

    // Check if app is already installed
    const checkInstalled = () => {
      // Check if running in standalone mode (installed PWA)
      if (window.matchMedia('(display-mode: standalone)').matches) {
        setIsInstalled(true);
        return;
      }
      
      // Check if running in fullscreen mode (some mobile browsers)
      if (window.navigator.standalone === true) {
        setIsInstalled(true);
        return;
      }
    };

    checkInstalled();

    // Listen for beforeinstallprompt event
    const handleBeforeInstallPrompt = (e) => {
      console.log('PWA: beforeinstallprompt event fired');
      e.preventDefault();
      setDeferredPrompt(e);
    };

    // Listen for appinstalled event
    const handleAppInstalled = () => {
      console.log('PWA: App was installed');
      setIsInstalled(true);
      setDeferredPrompt(null);
      setShowInstallPrompt(false);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    // Show install prompt after a delay if conditions are met
    const timer = setTimeout(() => {
      if (!isInstalled && isMobile && deferredPrompt) {
        // Check if user recently dismissed the prompt
        const dismissedTime = localStorage.getItem('pwa-install-dismissed');
        if (dismissedTime) {
          const timeDiff = Date.now() - parseInt(dismissedTime);
          const hoursDiff = timeDiff / (1000 * 60 * 60);
          if (hoursDiff < 24) {
            return; // Don't show if dismissed within 24 hours
          }
        }
        setShowInstallPrompt(true);
      }
    }, 8000); // Show after 8 seconds

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
      clearTimeout(timer);
    };
  }, [isInstalled, isMobile, deferredPrompt]);

  const handleInstallClick = async () => {
    if (!deferredPrompt) {
      console.log('PWA: No deferred prompt available');
      return;
    }

    try {
      // Show the install prompt
      deferredPrompt.prompt();
      
      // Wait for the user to respond to the prompt
      const { outcome } = await deferredPrompt.userChoice;
      
      console.log(`PWA: User response to install prompt: ${outcome}`);
      
      if (outcome === 'accepted') {
        console.log('PWA: User accepted the install prompt');
      } else {
        console.log('PWA: User dismissed the install prompt');
      }
      
      // Clear the deferred prompt
      setDeferredPrompt(null);
      setShowInstallPrompt(false);
    } catch (error) {
      console.error('PWA: Error during installation:', error);
    }
  };

  const handleDismissPrompt = () => {
    setShowInstallPrompt(false);
    // Remember user dismissed the prompt (don't show again for 24 hours)
    localStorage.setItem('pwa-install-dismissed', Date.now().toString());
  };

  // Don't show anything if not mobile or already installed
  if (!isMobile || isInstalled) {
    return null;
  }

  // Show header button if prompt is available
  if (deferredPrompt && !showInstallPrompt) {
    return (
      <InstallButton onClick={handleInstallClick} title="Instalar App">
        <FaMobile />
        Instalar
      </InstallButton>
    );
  }

  // Show bottom prompt
  if (showInstallPrompt && deferredPrompt) {
    return (
      <InstallPrompt>
        <FaMobile style={{ color: '#4CAF50', fontSize: '1.2rem' }} />
        <PromptContent>
          <PromptTitle>📱 Instalar MeuBonsai.App</PromptTitle>
          <PromptText>
            Adicione à tela inicial para acesso rápido e experiência completa
          </PromptText>
        </PromptContent>
        <PromptActions>
          <PromptButton primary onClick={handleInstallClick}>
            Instalar
          </PromptButton>
          <CloseButton onClick={handleDismissPrompt}>
            <FaTimes />
          </CloseButton>
        </PromptActions>
      </InstallPrompt>
    );
  }

  return null;
};

export default PWAInstallButton;
