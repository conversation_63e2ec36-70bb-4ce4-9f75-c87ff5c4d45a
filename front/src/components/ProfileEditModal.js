import React, { useState, useRef } from 'react';
import styled from 'styled-components';
import { FaTimes, FaUser, FaCamera, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaSpinner } from 'react-icons/fa';
import Button from './UI/Button';
import { useAuth } from '../contexts/AuthContext';
import { useSocial } from '../contexts/SocialContext';
import { toast } from 'react-toastify';
import PhotoCapture from './PhotoCapture';

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: ${props => props.theme.spacing.lg};
`;

const ModalContent = styled.div`
  background: white;
  border-radius: ${props => props.theme.borderRadius.lg};
  box-shadow: ${props => props.theme.shadows.lg};
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${props => props.theme.spacing.lg};
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const ModalTitle = styled.h2`
  margin: 0;
  color: ${props => props.theme.colors.text.primary};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.text.secondary};
  cursor: pointer;
  padding: ${props => props.theme.spacing.xs};
  border-radius: ${props => props.theme.borderRadius.sm};
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${props => props.theme.colors.background};
  }
`;

const ModalBody = styled.div`
  padding: ${props => props.theme.spacing.lg};
`;

const AvatarSection = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const AvatarContainer = styled.div`
  position: relative;
  margin-bottom: ${props => props.theme.spacing.md};
`;

const Avatar = styled.div`
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background-color: ${props => props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: ${props => props.theme.typography.fontSize.xxl};
  font-weight: ${props => props.theme.typography.fontWeight.bold};
  overflow: hidden;
  position: relative;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const AvatarOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
  cursor: pointer;
  border-radius: 50%;

  ${Avatar}:hover & {
    opacity: 1;
  }
`;

const HiddenFileInput = styled.input`
  display: none;
`;

const FormGroup = styled.div`
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const Label = styled.label`
  display: block;
  margin-bottom: ${props => props.theme.spacing.xs};
  color: ${props => props.theme.colors.text.primary};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
`;

const Input = styled.input`
  width: 100%;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.md};
  transition: border-color 0.2s ease;
  box-sizing: border-box;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.md};
  transition: border-color 0.2s ease;
  box-sizing: border-box;
  resize: vertical;
  min-height: 80px;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const UsernameContainer = styled.div`
  position: relative;
`;

const UsernameInput = styled(Input)`
  padding-right: 40px;
  border-color: ${props =>
    props.isValid === false ? props.theme.colors.error :
    props.isValid === true ? props.theme.colors.success :
    props.theme.colors.border
  };
`;

const ValidationIcon = styled.div`
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: ${props =>
    props.isChecking ? props.theme.colors.primary :
    props.isValid === true ? props.theme.colors.success :
    props.isValid === false ? props.theme.colors.error :
    props.theme.colors.text.secondary
  };
  font-size: 1.2rem;

  ${props => props.isChecking && `
    animation: spin 1s linear infinite;
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
  `}
`;

const ValidationMessage = styled.p`
  color: ${props =>
    props.type === 'error' ? props.theme.colors.error :
    props.type === 'success' ? props.theme.colors.success :
    props.theme.colors.text.secondary
  };
  font-size: 0.8rem;
  margin: ${props => props.theme.spacing.xs} 0 0 0;
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  justify-content: flex-end;
  margin-top: ${props => props.theme.spacing.xl};
`;



const ProfileEditModal = ({ isOpen, onClose, onSuccess }) => {
  const { user, updateProfile } = useAuth();
  const { checkUsernameAvailability, updateUsername } = useSocial();
  const fileInputRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [avatarPreview, setAvatarPreview] = useState(null);
  const [formData, setFormData] = useState({
    first_name: user?.first_name || '',
    last_name: user?.last_name || '',
    username: user?.username || '',
    bio: user?.bio || '',
    city: user?.city || '',
    state: user?.state || '',
    country: user?.country || '',
    avatar: null
  });
  const [usernameValidation, setUsernameValidation] = useState({
    isValid: null,
    message: '',
    isChecking: false
  });
  const [checkTimeout, setCheckTimeout] = useState(null);

  // Função para gerar username aleatório
  const generateRandomUsername = () => {
    const adjectives = ['verde', 'belo', 'zen', 'natural', 'pequeno', 'grande', 'forte', 'delicado'];
    const nouns = ['bonsai', 'folha', 'galho', 'raiz', 'jardim', 'vaso', 'terra', 'musgo'];
    const numbers = Math.floor(Math.random() * 999) + 1;

    const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
    const noun = nouns[Math.floor(Math.random() * nouns.length)];

    return `${adjective}${noun}${numbers}`;
  };

  // Função para validar formato do username
  const validateUsernameFormat = (value) => {
    if (!value || value.trim() === '') {
      return { isValid: null, message: '', isChecking: false };
    }

    const cleanValue = value.trim();

    if (cleanValue.length < 3) {
      return { isValid: false, message: 'Username deve ter pelo menos 3 caracteres', isChecking: false };
    }

    if (cleanValue.length > 30) {
      return { isValid: false, message: 'Username deve ter no máximo 30 caracteres', isChecking: false };
    }

    const validPattern = /^[a-zA-Z0-9_]+$/;
    if (!validPattern.test(cleanValue)) {
      return { isValid: false, message: 'Apenas letras, números e _ são permitidos', isChecking: false };
    }

    if (cleanValue.startsWith('_') || cleanValue.endsWith('_')) {
      return { isValid: false, message: 'Username não pode começar ou terminar com _', isChecking: false };
    }

    if (cleanValue.includes('__')) {
      return { isValid: false, message: 'Username não pode ter __ consecutivos', isChecking: false };
    }

    return { isValid: null, message: 'Verificando disponibilidade...', isChecking: true };
  };

  // Função para verificar disponibilidade
  const checkUsernameWithCache = async (value) => {
    const cleanValue = value.trim();

    if (cleanValue === user?.username) {
      return { isValid: true, message: 'Username atual', isChecking: false };
    }

    try {
      const result = await checkUsernameAvailability(cleanValue);

      if (result.available) {
        return { isValid: true, message: 'Username disponível!', isChecking: false };
      } else {
        const messages = {
          already_taken: 'Username já está em uso',
          invalid_length: 'Comprimento inválido',
          invalid_characters: 'Caracteres inválidos',
          invalid_format: 'Formato inválido',
          error: 'Erro ao verificar disponibilidade'
        };
        return {
          isValid: false,
          message: messages[result.reason] || 'Username não disponível',
          isChecking: false
        };
      }
    } catch (error) {
      return { isValid: false, message: 'Erro ao verificar disponibilidade', isChecking: false };
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    if (name === 'username') {
      // Limpar caracteres inválidos automaticamente
      let cleanValue = value.replace(/[^a-zA-Z0-9_]/g, '');
      if (cleanValue.length > 30) {
        cleanValue = cleanValue.substring(0, 30);
      }

      setFormData(prev => ({
        ...prev,
        [name]: cleanValue
      }));

      // Validar formato
      const formatValidation = validateUsernameFormat(cleanValue);
      setUsernameValidation(formatValidation);

      // Se formato é válido, verificar disponibilidade com debounce
      if (formatValidation.isChecking) {
        if (checkTimeout) {
          clearTimeout(checkTimeout);
        }

        const timeout = setTimeout(async () => {
          const availabilityValidation = await checkUsernameWithCache(cleanValue);
          setUsernameValidation(availabilityValidation);
        }, 800);

        setCheckTimeout(timeout);
      }
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };



  const handleAvatarCapture = (file, isCamera) => {
    setFormData(prev => ({
      ...prev,
      avatar: file
    }));

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setAvatarPreview(e.target.result);
    };
    reader.readAsDataURL(file);
  };

  const handleAvatarRemove = () => {
    setFormData(prev => ({
      ...prev,
      avatar: null
    }));
    setAvatarPreview(null);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Validar username se foi alterado
      if (formData.username !== user?.username && formData.username.trim()) {
        if (!usernameValidation.isValid) {
          toast.error('Username inválido ou não disponível');
          setLoading(false);
          return;
        }
      }

      // Atualizar perfil
      const formDataToSend = new FormData();
      formDataToSend.append('first_name', formData.first_name);
      formDataToSend.append('last_name', formData.last_name);
      formDataToSend.append('bio', formData.bio);
      formDataToSend.append('city', formData.city);
      formDataToSend.append('state', formData.state);
      formDataToSend.append('country', formData.country);

      if (formData.avatar) {
        formDataToSend.append('avatar', formData.avatar);
      }

      const result = await updateProfile(formDataToSend);

      if (result.success) {
        // Atualizar username se foi alterado
        if (formData.username !== user?.username && formData.username.trim() && usernameValidation.isValid) {
          await updateUsername(formData.username.trim());
        }

        toast.success('Perfil atualizado com sucesso!');
        onSuccess && onSuccess();
        onClose();
      }
    } catch (error) {
      console.error('Erro ao atualizar perfil:', error);
      toast.error('Erro ao atualizar perfil');
    } finally {
      setLoading(false);
    }
  };

  const getInitials = (firstName, lastName) => {
    return `${firstName?.charAt(0) || ''}${lastName?.charAt(0) || ''}`.toUpperCase();
  };



  if (!isOpen) return null;

  return (
    <ModalOverlay>
      <ModalContent>
        <ModalHeader>
          <ModalTitle>
            <FaUser />
            Editar Perfil
          </ModalTitle>
          <CloseButton onClick={onClose}>
            <FaTimes />
          </CloseButton>
        </ModalHeader>

        <ModalBody>
          <form onSubmit={handleSubmit}>
            <AvatarSection>
              <AvatarContainer>
                <Avatar>
                  {avatarPreview || user?.avatar ? (
                    <img src={avatarPreview || user.avatar} alt="Avatar" />
                  ) : (
                    getInitials(formData.first_name, formData.last_name) || <FaUser />
                  )}
                </Avatar>
              </AvatarContainer>
              <PhotoCapture
                onPhotoCapture={handleAvatarCapture}
                onPhotoRemove={handleAvatarRemove}
                preview={avatarPreview}
                disabled={loading}
              />
            </AvatarSection>

            <FormGroup>
              <Label>Username:</Label>
              <UsernameContainer>
                <UsernameInput
                  type="text"
                  name="username"
                  value={formData.username}
                  onChange={handleInputChange}
                  placeholder="Escolha um nome de usuário único"
                  isValid={usernameValidation.isValid}
                  maxLength={30}
                />
                <ValidationIcon
                  isValid={usernameValidation.isValid}
                  isChecking={usernameValidation.isChecking}
                >
                  {usernameValidation.isChecking && <FaSpinner />}
                  {!usernameValidation.isChecking && usernameValidation.isValid === true && <FaCheck />}
                  {!usernameValidation.isChecking && usernameValidation.isValid === false && <FaTimes />}
                </ValidationIcon>
              </UsernameContainer>
              {usernameValidation.message && (
                <ValidationMessage type={usernameValidation.isValid ? 'success' : 'error'}>
                  {usernameValidation.isValid ? <FaCheck /> : <FaTimes />}
                  {usernameValidation.message}
                </ValidationMessage>
              )}
            </FormGroup>

            <FormGroup>
              <Label>Nome:</Label>
              <Input
                type="text"
                name="first_name"
                value={formData.first_name}
                onChange={handleInputChange}
                required
              />
            </FormGroup>

            <FormGroup>
              <Label>Sobrenome:</Label>
              <Input
                type="text"
                name="last_name"
                value={formData.last_name}
                onChange={handleInputChange}
                required
              />
            </FormGroup>

            <FormGroup>
              <Label>Bio:</Label>
              <TextArea
                name="bio"
                value={formData.bio}
                onChange={handleInputChange}
                placeholder="Conte um pouco sobre você..."
              />
            </FormGroup>

            <FormGroup>
              <Label>Cidade:</Label>
              <Input
                type="text"
                name="city"
                value={formData.city}
                onChange={handleInputChange}
                placeholder="Sua cidade"
              />
            </FormGroup>

            <FormGroup>
              <Label>Estado:</Label>
              <Input
                type="text"
                name="state"
                value={formData.state}
                onChange={handleInputChange}
                placeholder="Seu estado"
              />
            </FormGroup>

            <FormGroup>
              <Label>País:</Label>
              <Input
                type="text"
                name="country"
                value={formData.country}
                onChange={handleInputChange}
                placeholder="Seu país"
              />
            </FormGroup>

            <ButtonGroup>
              <Button
                type="button"
                variant="secondary"
                onClick={onClose}
                disabled={loading}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={loading}
              >
                {loading ? 'Salvando...' : 'Salvar'}
              </Button>
            </ButtonGroup>
          </form>


        </ModalBody>
      </ModalContent>


    </ModalOverlay>
  );
};

export default ProfileEditModal;
