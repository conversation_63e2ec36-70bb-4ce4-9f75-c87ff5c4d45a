import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import { FaExclamationTriangle, FaCalendarAlt, FaCreditCard } from 'react-icons/fa';
import subscriptionService from '../services/subscriptionService';
import { getBasePlanDisplayName } from '../utils/planUtils';

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background: white;
  border-radius: 12px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
`;

const Title = styled.h2`
  color: #dc3545;
  margin-bottom: 1rem;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
`;

const InfoSection = styled.div`
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1rem 0;
  border-left: 4px solid #ffc107;
`;

const InfoRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const InfoLabel = styled.span`
  font-weight: 600;
  color: #495057;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const InfoValue = styled.span`
  color: #212529;
  font-weight: 500;
`;

const WarningBox = styled.div`
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
  color: #856404;
`;

const WarningTitle = styled.h4`
  margin: 0 0 0.5rem 0;
  color: #856404;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const BenefitsList = styled.ul`
  margin: 0.5rem 0;
  padding-left: 1.5rem;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
`;

const Button = styled.button`
  flex: 1;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  
  &.secondary {
    background: #6c757d;
    color: white;
    
    &:hover {
      background: #5a6268;
    }
  }
  
  &.danger {
    background: #dc3545;
    color: white;
    
    &:hover {
      background: #c82333;
    }
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const WarningAlert = styled.div`
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1.5rem 0;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
`;

const WarningContent = styled.div`
  flex: 1;
`;

const WarningText = styled.p`
  color: #856404;
  margin: 0;
  line-height: 1.5;
`;

const ConfirmationSection = styled.div`
  text-align: center;
  padding: 1rem 0;
`;

const ConfirmationTitle = styled.h3`
  color: #dc3545;
  margin: 0 0 1rem 0;
  font-size: 1.3rem;
  font-weight: 600;
`;

const ConfirmationText = styled.p`
  color: #6c757d;
  margin: 0 0 1.5rem 0;
  line-height: 1.6;
  font-size: 1rem;
`;

const CancelSubscriptionModal = ({ subscriptionId, onClose, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [cancellationInfo, setCancellationInfo] = useState(null);
  const [loadingInfo, setLoadingInfo] = useState(true);
  const [showFinalConfirmation, setShowFinalConfirmation] = useState(false);

  // Função helper para formatar nome do plano
  const formatPlanName = (planName) => {
    if (!planName) return '';

    // Remover "Mensal" e "Anual" do nome
    return planName
      .replace(' Mensal', '')
      .replace(' Anual', '')
      .trim();
  };

  useEffect(() => {
    loadCancellationInfo();
  }, [subscriptionId]);

  const loadCancellationInfo = async () => {
    try {
      setLoadingInfo(true);
      const response = await subscriptionService.getCancellationInfo(subscriptionId);
      setCancellationInfo(response.data);
    } catch (error) {
      console.error('Error loading cancellation info:', error);
      toast.error('Erro ao carregar informações da assinatura');
      onClose();
    } finally {
      setLoadingInfo(false);
    }
  };

  const handleInitialCancel = () => {
    // Mostrar confirmação final
    setShowFinalConfirmation(true);
  };

  const handleFinalCancel = async () => {
    setLoading(true);
    try {
      const response = await subscriptionService.cancelSubscriptionWithGracePeriod(subscriptionId);
      toast.success(response.data.message);
      onSuccess(response.data);
      onClose();
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      toast.error('Erro ao cancelar assinatura');
    } finally {
      setLoading(false);
    }
  };

  const handleBackToInfo = () => {
    setShowFinalConfirmation(false);
  };

  if (loadingInfo) {
    return (
      <ModalOverlay onClick={onClose}>
        <ModalContent onClick={(e) => e.stopPropagation()}>
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            Carregando informações...
          </div>
        </ModalContent>
      </ModalOverlay>
    );
  }

  if (!cancellationInfo) {
    return null;
  }

  return (
    <ModalOverlay onClick={onClose}>
      <ModalContent onClick={(e) => e.stopPropagation()}>
        <Title>
          <FaExclamationTriangle />
          {showFinalConfirmation ? 'Confirmar Cancelamento' : 'Sua Assinatura'}
        </Title>

        {showFinalConfirmation ? (
          // Tela de confirmação final
          <>
            <ConfirmationSection>
              <ConfirmationTitle>⚠️ Ação Irreversível</ConfirmationTitle>
              <ConfirmationText>
                Tem certeza de que deseja cancelar sua assinatura do plano <strong>{formatPlanName(cancellationInfo.plan_name)}</strong>?
              </ConfirmationText>

              <WarningAlert>
                <div style={{ color: '#856404', fontSize: '1.5rem', marginTop: '0.2rem' }}>
                  <FaExclamationTriangle />
                </div>
                <WarningContent>
                  <WarningTitle>Esta ação não pode ser desfeita!</WarningTitle>
                  <WarningText>
                    • Sua renovação automática será desabilitada<br/>
                    • Você manterá acesso até <strong>{cancellationInfo.expires_at ? new Date(cancellationInfo.expires_at).toLocaleDateString('pt-BR') : 'o final do período'}</strong><br/>
                    • Para reativar, será necessário fazer uma nova assinatura<br/>
                    • Você perderá qualquer desconto ou promoção atual
                  </WarningText>
                </WarningContent>
              </WarningAlert>
            </ConfirmationSection>

            <ButtonGroup>
              <Button type="button" className="secondary" onClick={handleBackToInfo}>
                ← Voltar
              </Button>
              <Button
                type="button"
                className="danger"
                onClick={handleFinalCancel}
                disabled={loading}
              >
                {loading ? 'Cancelando...' : 'Sim, Cancelar Definitivamente'}
              </Button>
            </ButtonGroup>
          </>
        ) : (
          // Tela de informações inicial
          <>
            <InfoSection>
          <InfoRow>
            <InfoLabel>
              <FaCreditCard />
              Plano Atual:
            </InfoLabel>
            <InfoValue>{formatPlanName(cancellationInfo.plan_name)}</InfoValue>
          </InfoRow>
          
          <InfoRow>
            <InfoLabel>
              <FaCalendarAlt />
              Ciclo de Cobrança:
            </InfoLabel>
            <InfoValue>
              {cancellationInfo.billing_cycle === 'monthly' ? 'Mensal' : 'Anual'}
            </InfoValue>
          </InfoRow>
          
          <InfoRow>
            <InfoLabel>Valor:</InfoLabel>
            <InfoValue>{cancellationInfo.price_display}</InfoValue>
          </InfoRow>
          
          <InfoRow>
            <InfoLabel>Dias Restantes:</InfoLabel>
            <InfoValue style={{ color: '#28a745', fontWeight: 'bold' }}>
              {cancellationInfo.days_remaining} dias
            </InfoValue>
          </InfoRow>
        </InfoSection>

        <WarningBox>
          <WarningTitle>
            <FaExclamationTriangle />
            O que acontece ao cancelar?
          </WarningTitle>
          <BenefitsList>
            <li>Você manterá acesso completo ao plano até {new Date(cancellationInfo.expires_at).toLocaleDateString('pt-BR')}</li>
            <li>A renovação automática será desabilitada</li>
            <li>Após a expiração, você terá 14 dias de período de graça</li>
            <li>Durante o período de graça, poderá reativar sem perder dados</li>
            <li>Após 14 dias, o acesso será limitado ao plano gratuito</li>
          </BenefitsList>
        </WarningBox>

        <p style={{ textAlign: 'center', color: '#6c757d', fontSize: '0.9rem' }}>
          {cancellationInfo.cancellation_note}
        </p>

        <ButtonGroup>
          <Button type="button" className="secondary" onClick={onClose}>
            Manter Assinatura
          </Button>
          <Button
            type="button"
            className="danger"
            onClick={handleInitialCancel}
            disabled={loading}
          >
            Cancelar Assinatura
          </Button>
        </ButtonGroup>
          </>
        )}
      </ModalContent>
    </ModalOverlay>
  );
};

export default CancelSubscriptionModal;
