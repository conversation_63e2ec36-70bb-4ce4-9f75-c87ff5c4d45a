import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { FaUser, FaUserPlus, FaUserMinus, FaTimes } from 'react-icons/fa';
import { useSocial } from '../contexts/SocialContext';
import { useAuth } from '../contexts/AuthContext';
import Button from './UI/Button';
import LoadingSpinner from './UI/LoadingSpinner';

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: ${props => props.theme.spacing.md};
`;

const ModalContent = styled.div`
  background: white;
  border-radius: ${props => props.theme.borderRadius.lg};
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
`;

const ModalHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${props => props.theme.spacing.lg};
  border-bottom: 1px solid ${props => props.theme.colors.border.light};
`;

const ModalTitle = styled.h2`
  margin: 0;
  color: ${props => props.theme.colors.text.primary};
  font-size: 1.2rem;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.text.secondary};
  font-size: 1.2rem;
  cursor: pointer;
  padding: ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.md};
  transition: all 0.2s ease;
  
  &:hover {
    background: ${props => props.theme.colors.background.secondary};
    color: ${props => props.theme.colors.text.primary};
  }
`;

const ModalBody = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: ${props => props.theme.spacing.md};
`;

const UserItem = styled.div`
  display: flex;
  align-items: center;
  padding: ${props => props.theme.spacing.md};
  border-radius: ${props => props.theme.borderRadius.md};
  transition: background 0.2s ease;
  
  &:hover {
    background: ${props => props.theme.colors.background.secondary};
  }
`;

const Avatar = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: ${props => props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: ${props => props.theme.spacing.md};
  color: white;
  font-size: 1.2rem;
  background-image: ${props => props.avatar ? `url(${props.avatar})` : 'none'};
  background-size: cover;
  background-position: center;
  cursor: pointer;
`;

const UserInfo = styled.div`
  flex: 1;
  cursor: pointer;
`;

const UserName = styled.div`
  font-weight: 600;
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing.xs};
`;

const Username = styled.div`
  color: ${props => props.theme.colors.text.secondary};
  font-size: 0.9rem;
`;

const ActionButton = styled(Button)`
  min-width: 100px;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${props => props.theme.spacing.xl};
  color: ${props => props.theme.colors.text.secondary};
`;

const LoadMoreButton = styled(Button)`
  width: 100%;
  margin-top: ${props => props.theme.spacing.md};
`;

const FollowersModal = ({ isOpen, onClose, type, userId }) => {
  const navigate = useNavigate();
  const { user: currentUser } = useAuth();
  const { getUserFollowers, getUserFollowing, followUser, unfollowUser } = useSocial();
  
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);

  useEffect(() => {
    if (isOpen && userId) {
      loadUsers(1);
    }
  }, [isOpen, userId, type]);

  const loadUsers = async (page = 1) => {
    try {
      setLoading(true);
      let result;
      
      if (type === 'followers') {
        result = await getUserFollowers(userId, page);
      } else {
        result = await getUserFollowing(userId, page);
      }
      
      // Verificar formato da resposta
      const usersData = result.followers || result.following || result.users || [];

      if (page === 1) {
        setUsers(usersData);
      } else {
        setUsers(prev => [...prev, ...usersData]);
      }

      setCurrentPage(page);
      setHasMore(result.has_next || false);
    } catch (error) {
      console.error('Error loading users:', error);
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  const handleFollowToggle = async (user) => {
    try {
      if (user.is_following) {
        await unfollowUser(user.id);
      } else {
        await followUser(user.id);
      }

      // Atualizar estado local
      setUsers(prev => prev.map(u => 
        u.id === user.id 
          ? { ...u, is_following: !u.is_following }
          : u
      ));
    } catch (error) {
      console.error('Follow toggle error:', error);
    }
  };

  const handleUserClick = (user) => {
    onClose();
    if (user.username) {
      navigate(`/profile/${user.username}`);
    } else {
      navigate(`/profile/user/${user.id}`);
    }
  };

  const handleLoadMore = () => {
    if (hasMore && !loading) {
      loadUsers(currentPage + 1);
    }
  };

  const getInitials = (firstName, lastName) => {
    return `${firstName?.charAt(0) || ''}${lastName?.charAt(0) || ''}`.toUpperCase();
  };

  if (!isOpen) return null;

  return (
    <ModalOverlay onClick={onClose}>
      <ModalContent onClick={(e) => e.stopPropagation()}>
        <ModalHeader>
          <ModalTitle>
            {type === 'followers' ? 'Seguidores' : 'Seguindo'}
          </ModalTitle>
          <CloseButton onClick={onClose}>
            <FaTimes />
          </CloseButton>
        </ModalHeader>
        
        <ModalBody>
          {loading && currentPage === 1 ? (
            <LoadingSpinner />
          ) : users.length === 0 ? (
            <EmptyState>
              <p>
                {type === 'followers' 
                  ? 'Nenhum seguidor encontrado' 
                  : 'Não está seguindo ninguém'
                }
              </p>
            </EmptyState>
          ) : (
            <>
              {users.map(user => (
                <UserItem key={user.id}>
                  <Avatar 
                    avatar={user.avatar}
                    onClick={() => handleUserClick(user)}
                  >
                    {!user.avatar && (getInitials(user.first_name, user.last_name) || <FaUser />)}
                  </Avatar>
                  
                  <UserInfo onClick={() => handleUserClick(user)}>
                    <UserName>
                      {user.first_name} {user.last_name}
                    </UserName>
                    {user.username && (
                      <Username>@{user.username}</Username>
                    )}
                  </UserInfo>
                  
                  {currentUser && user.id !== currentUser.id && (
                    <ActionButton
                      variant={user.is_following ? "outline" : "primary"}
                      size="small"
                      onClick={() => handleFollowToggle(user)}
                    >
                      {user.is_following ? (
                        <>
                          <FaUserMinus /> Deixar de seguir
                        </>
                      ) : (
                        <>
                          <FaUserPlus /> Seguir
                        </>
                      )}
                    </ActionButton>
                  )}
                </UserItem>
              ))}

              {hasMore && (
                <LoadMoreButton
                  variant="outline"
                  onClick={handleLoadMore}
                  disabled={loading}
                >
                  {loading ? 'Carregando...' : 'Carregar mais'}
                </LoadMoreButton>
              )}
            </>
          )}
        </ModalBody>
      </ModalContent>
    </ModalOverlay>
  );
};

export default FollowersModal;
