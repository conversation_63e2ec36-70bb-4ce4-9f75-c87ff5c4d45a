import React, { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { FaLeaf, FaUser, FaSignOutAlt, FaChevronDown, FaCrown, FaCog, FaSearch, FaShieldAlt, FaUsers } from 'react-icons/fa';
import { useAuth } from '../../contexts/AuthContext';
import NotificationBell from '../NotificationBell';
import ReminderBell from '../ReminderBell';

const HeaderContainer = styled.header`
  background-color: #f0f8f0;
  color: #2c3e50;
  padding: ${props => props.theme.spacing.md} 0;
  box-shadow: ${props => props.theme.shadows.sm};
  border-bottom: 1px solid #d4edda;
`;

const HeaderContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${props => props.theme.spacing.md};
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const Logo = styled(Link)`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  color: #2c3e50;
  text-decoration: none;

  &:hover {
    color: #2c3e50;
  }

  img {
    height: 44px;
    width: auto;
    object-fit: contain;
  }

  @media (max-width: 768px) {
    img {
      height: 38px;
    }
  }

  @media (max-width: 480px) {
    img {
      height: 32px;
    }
  }
`;

const Nav = styled.nav`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.lg};
`;

const NavLink = styled(Link)`
  color: ${props => props.theme.colors.text.secondary};
  text-decoration: none;
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.sm};
  transition: all 0.2s ease;
  font-weight: ${props => props.theme.typography.fontWeight.regular};
  font-size: 0.9rem;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: ${props => props.theme.colors.primary};
  }
`;

const UserMenu = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const UserDropdown = styled.div`
  position: relative;
`;

const UserButton = styled.button`
  background: rgba(44, 62, 80, 0.1);
  color: #2c3e50;
  border: none;
  padding: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: background-color 0.2s ease;
  cursor: pointer;
  position: relative;
  width: 40px;
  height: 40px;

  &:hover {
    background-color: rgba(44, 62, 80, 0.15);
  }
`;

const UserAvatar = styled.div`
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: ${props => props.theme.colors.primaryLight};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: ${props => props.theme.typography.fontWeight.bold};
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const ChevronIcon = styled(FaChevronDown)`
  font-size: 10px;
  position: absolute;
  bottom: 2px;
  right: 2px;
  background: rgba(44, 62, 80, 0.8);
  color: white;
  border-radius: 50%;
  padding: 2px;
  width: 12px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const DropdownMenu = styled.div`
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: ${props => props.theme.borderRadius.md};
  box-shadow: ${props => props.theme.shadows.lg};
  min-width: 180px;
  z-index: 1000;
  overflow: hidden;
  margin-top: ${props => props.theme.spacing.xs};

  ${props => !props.isOpen && 'display: none;'}
`;

const DropdownItem = styled(Link)`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  padding: ${props => props.theme.spacing.md};
  color: ${props => props.theme.colors.text.primary};
  text-decoration: none;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${props => props.theme.colors.background};
  }

  svg {
    color: ${props => props.theme.colors.text.secondary};
  }
`;

const DropdownButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  padding: ${props => props.theme.spacing.md};
  color: ${props => props.theme.colors.text.primary};
  background: none;
  border: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: inherit;
  font-family: inherit;

  &:hover {
    background-color: ${props => props.theme.colors.background};
  }

  svg {
    color: ${props => props.theme.colors.text.secondary};
  }
`;

const Header = () => {
  const { user, logout, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);

  const handleLogout = async () => {
    await logout();
    navigate('/');
    setIsDropdownOpen(false);
  };

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  // Fechar dropdown ao clicar fora
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const getInitials = (firstName, lastName) => {
    return `${firstName?.charAt(0) || ''}${lastName?.charAt(0) || ''}`.toUpperCase();
  };

  return (
    <HeaderContainer>
      <HeaderContent>
        <Logo to="/">
          <img src="/logo_meubonsai_header.png" alt="MeuBonsai.App" />
        </Logo>

        <Nav>
          {isAuthenticated ? (
            <UserMenu>
              <ReminderBell />
              <NotificationBell />
              <UserDropdown ref={dropdownRef}>
                <UserButton onClick={toggleDropdown}>
                  <UserAvatar>
                    {user?.avatar ? (
                      <img src={user.avatar} alt="Avatar" />
                    ) : (
                      getInitials(user?.first_name, user?.last_name) || <FaUser size={16} />
                    )}
                  </UserAvatar>
                  <ChevronIcon />
                </UserButton>
                <DropdownMenu isOpen={isDropdownOpen}>

                  <DropdownItem to="/search" onClick={() => setIsDropdownOpen(false)}>
                    <FaSearch size={16} />
                    Buscar Usuários
                  </DropdownItem>
                  <DropdownItem to="/profile" onClick={() => setIsDropdownOpen(false)}>
                    <FaUser size={16} />
                    Perfil
                  </DropdownItem>
                  <DropdownItem to="/pricing" onClick={() => setIsDropdownOpen(false)}>
                    <FaCrown size={16} />
                    Planos
                  </DropdownItem>
                  <DropdownItem to="/settings" onClick={() => setIsDropdownOpen(false)}>
                    <FaCog size={16} />
                    Preferências
                  </DropdownItem>

                  {user?.email === '<EMAIL>' && (
                    <DropdownItem to="/admin" onClick={() => setIsDropdownOpen(false)}>
                      <FaCog size={16} />
                      Admin
                    </DropdownItem>
                  )}
                  <DropdownButton onClick={handleLogout}>
                    <FaSignOutAlt size={16} />
                    Sair
                  </DropdownButton>
                </DropdownMenu>
              </UserDropdown>
            </UserMenu>
          ) : (
            <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
              <NavLink to="/login">Entrar</NavLink>
              <NavLink to="/register">Cadastrar</NavLink>
            </div>
          )}
        </Nav>
      </HeaderContent>
    </HeaderContainer>
  );
};

export default Header;
