import React from 'react';
import styled from 'styled-components';
import { <PERSON>a<PERSON><PERSON><PERSON>, FaEnvelope } from 'react-icons/fa';
import { Link } from 'react-router-dom';

const FooterContainer = styled.footer`
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  padding: ${props => props.theme.spacing.xl} 0 ${props => props.theme.spacing.lg};
  margin-top: auto;
`;

const FooterContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${props => props.theme.spacing.lg};
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: ${props => props.theme.spacing.xl};
  align-items: center;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    text-align: center;
    gap: ${props => props.theme.spacing.lg};
  }
`;

const FooterSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.sm};
`;

const FooterBrand = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const BrandLogo = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  font-size: 1.5rem;
  font-weight: 700;
  color: #27ae60;
`;

const BrandTagline = styled.p`
  font-size: 0.9rem;
  color: #bdc3c7;
  margin: 0;
  text-align: center;
`;

const FooterLinks = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.xs};

  @media (max-width: 768px) {
    align-items: center;
  }
`;

const FooterLink = styled(Link)`
  color: #bdc3c7;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.2s ease;
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};

  &:hover {
    color: #27ae60;
  }
`;

const ContactInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.xs};
  text-align: right;

  @media (max-width: 768px) {
    text-align: center;
  }
`;

const ContactItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
  font-size: 0.9rem;
  color: #bdc3c7;
  justify-content: flex-end;

  @media (max-width: 768px) {
    justify-content: center;
  }
`;

const Copyright = styled.div`
  border-top: 1px solid #34495e;
  margin-top: ${props => props.theme.spacing.lg};
  padding-top: ${props => props.theme.spacing.md};
  text-align: center;
  color: #95a5a6;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${props => props.theme.spacing.xs};
`;

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <FooterContainer>
      <FooterContent>
        <FooterSection>
          <FooterLinks>
            <FooterLink to="/privacy-policy">
              Política de Privacidade
            </FooterLink>
            <FooterLink to="/terms-of-service">
              Termos de Uso
            </FooterLink>
            <FooterLink to="/contact">
              <FaEnvelope />
              Contato
            </FooterLink>

          </FooterLinks>
        </FooterSection>

        <FooterBrand>
          <BrandLogo>
            MeuBonsai.App
          </BrandLogo>
          <BrandTagline>
            Cuidando dos seus bonsais com tecnologia
          </BrandTagline>
        </FooterBrand>

        <ContactInfo>
          <ContactItem>
            <FaEnvelope />
            <EMAIL>
          </ContactItem>
        </ContactInfo>
      </FooterContent>

      <Copyright>
        © {currentYear} MeuBonsai.App - Todos os direitos reservados
        <FaHeart style={{ color: '#e74c3c' }} />
        Feito com amor para os amantes de bonsai
      </Copyright>
    </FooterContainer>
  );
};

export default Footer;
