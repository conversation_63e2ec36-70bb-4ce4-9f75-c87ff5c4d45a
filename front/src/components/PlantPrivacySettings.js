import React, { useState } from 'react';
import styled from 'styled-components';
import { <PERSON>a<PERSON>ye, FaEyeSlash, FaUsers, FaLock } from 'react-icons/fa';
import { toast } from 'react-toastify';
import plantsService from '../services/plantsService';

const PrivacyContainer = styled.div`
  background: ${props => props.theme.colors.surface};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  padding: ${props => props.theme.spacing.lg};
  margin-bottom: ${props => props.theme.spacing.md};
`;

const PrivacyHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  margin-bottom: ${props => props.theme.spacing.md};
  color: ${props => props.theme.colors.text.primary};
  font-weight: 600;
`;

const PrivacyOption = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.sm};
  margin-bottom: ${props => props.theme.spacing.sm};
  background: ${props => props.theme.colors.background};

  &:last-child {
    margin-bottom: 0;
  }
`;

const OptionInfo = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const OptionIcon = styled.div`
  color: ${props => props.isActive ? props.theme.colors.primary : props.theme.colors.text.secondary};
  font-size: 1.2rem;
`;

const OptionText = styled.div``;

const OptionTitle = styled.div`
  font-weight: 600;
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: 2px;
`;

const OptionDescription = styled.div`
  font-size: 0.9rem;
  color: ${props => props.theme.colors.text.secondary};
`;

const ToggleSwitch = styled.button`
  position: relative;
  width: 50px;
  height: 24px;
  border-radius: 12px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  background: ${props => props.isActive ? props.theme.colors.primary : '#ccc'};

  &::after {
    content: '';
    position: absolute;
    top: 2px;
    left: ${props => props.isActive ? '28px' : '2px'};
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: white;
    transition: all 0.2s ease;
  }
`;

const PlantPrivacySettings = ({ plant, onUpdate }) => {
  const [isPublic, setIsPublic] = useState(plant.is_public);
  const [allowCareSharing, setAllowCareSharing] = useState(plant.allow_care_sharing);
  const [loading, setLoading] = useState(false);

  const handlePrivacyUpdate = async (newIsPublic, newAllowCareSharing) => {
    setLoading(true);
    try {
      await plantsService.updatePlantPrivacy(plant.id, {
        is_public: newIsPublic,
        allow_care_sharing: newAllowCareSharing
      });
      
      setIsPublic(newIsPublic);
      setAllowCareSharing(newAllowCareSharing);
      
      if (onUpdate) {
        onUpdate({ ...plant, is_public: newIsPublic, allow_care_sharing: newAllowCareSharing });
      }
      
      toast.success('Configurações de privacidade atualizadas!');
    } catch (error) {
      console.error('Erro ao atualizar privacidade:', error);
      toast.error('Erro ao atualizar configurações de privacidade');
    } finally {
      setLoading(false);
    }
  };

  const handlePublicToggle = () => {
    const newIsPublic = !isPublic;
    handlePrivacyUpdate(newIsPublic, allowCareSharing);
  };

  const handleCareToggle = () => {
    const newAllowCareSharing = !allowCareSharing;
    handlePrivacyUpdate(isPublic, newAllowCareSharing);
  };

  return (
    <PrivacyContainer>
      <PrivacyHeader>
        <FaLock />
        Configurações de Privacidade
      </PrivacyHeader>

      <PrivacyOption>
        <OptionInfo>
          <OptionIcon isActive={isPublic}>
            {isPublic ? <FaEye /> : <FaEyeSlash />}
          </OptionIcon>
          <OptionText>
            <OptionTitle>Planta Pública</OptionTitle>
            <OptionDescription>
              {isPublic 
                ? 'Esta planta aparece no seu perfil público' 
                : 'Esta planta está oculta do seu perfil público'
              }
            </OptionDescription>
          </OptionText>
        </OptionInfo>
        <ToggleSwitch 
          isActive={isPublic} 
          onClick={handlePublicToggle}
          disabled={loading}
        />
      </PrivacyOption>

      <PrivacyOption>
        <OptionInfo>
          <OptionIcon isActive={allowCareSharing}>
            {allowCareSharing ? <FaUsers /> : <FaLock />}
          </OptionIcon>
          <OptionText>
            <OptionTitle>Compartilhar Cuidados</OptionTitle>
            <OptionDescription>
              {allowCareSharing 
                ? 'Seus seguidores podem ver os cuidados desta planta' 
                : 'Os cuidados desta planta estão privados'
              }
            </OptionDescription>
          </OptionText>
        </OptionInfo>
        <ToggleSwitch 
          isActive={allowCareSharing} 
          onClick={handleCareToggle}
          disabled={loading}
        />
      </PrivacyOption>
    </PrivacyContainer>
  );
};

export default PlantPrivacySettings;
