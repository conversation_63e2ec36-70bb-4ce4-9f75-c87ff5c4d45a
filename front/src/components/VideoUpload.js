import React, { useState, useRef } from 'react';
import styled from 'styled-components';
import { FaVideo, FaTimes, FaUpload } from 'react-icons/fa';

const VideoUploadContainer = styled.div`
  margin: 1rem 0;
`;

const VideoUploadButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: ${props => props.disabled ? '#f5f5f5' : '#007bff'};
  color: ${props => props.disabled ? '#999' : 'white'};
  border: none;
  border-radius: 8px;
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  font-size: 0.9rem;
  transition: background-color 0.2s;

  &:hover:not(:disabled) {
    background: #0056b3;
  }
`;

const VideoPreviewContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 1rem;
`;

const VideoPreview = styled.div`
  position: relative;
  width: 200px;
  height: 150px;
  border-radius: 8px;
  overflow: hidden;
  background: #f5f5f5;
  border: 2px dashed #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const VideoElement = styled.video`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const RemoveButton = styled.button`
  position: absolute;
  top: 5px;
  right: 5px;
  background: rgba(255, 0, 0, 0.8);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;

  &:hover {
    background: rgba(255, 0, 0, 1);
  }
`;

const VideoInfo = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
`;

const ErrorMessage = styled.div`
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.5rem;
`;

const HiddenInput = styled.input`
  display: none;
`;

const VideoUpload = ({ 
  videos = [], 
  onVideosChange, 
  maxVideos = null, 
  disabled = false,
  allowedFormats = ['mp4', 'mov', 'avi', 'mkv', 'webm', 'm4v'],
  maxSizeMB = 100,
  showPreview = true 
}) => {
  const [error, setError] = useState('');
  const fileInputRef = useRef(null);

  const handleVideoSelect = (event) => {
    const files = Array.from(event.target.files);
    setError('');

    // Validate number of videos
    if (maxVideos && videos.length + files.length > maxVideos) {
      setError(`Máximo de ${maxVideos} vídeo(s) permitido(s)`);
      return;
    }

    // Validate each file
    const validVideos = [];
    for (const file of files) {
      // Check file type
      const extension = file.name.split('.').pop().toLowerCase();
      if (!allowedFormats.includes(extension)) {
        setError(`Formato não suportado: ${extension}. Formatos aceitos: ${allowedFormats.join(', ')}`);
        continue;
      }

      // Check file size
      const sizeMB = file.size / (1024 * 1024);
      if (sizeMB > maxSizeMB) {
        setError(`Arquivo muito grande: ${file.name}. Tamanho máximo: ${maxSizeMB}MB`);
        continue;
      }

      // Create preview URL
      const videoWithPreview = {
        file,
        preview: URL.createObjectURL(file),
        name: file.name,
        size: file.size
      };

      validVideos.push(videoWithPreview);
    }

    if (validVideos.length > 0) {
      onVideosChange([...videos, ...validVideos]);
    }

    // Reset input
    event.target.value = '';
  };

  const handleRemoveVideo = (index) => {
    const newVideos = [...videos];
    // Revoke object URL to prevent memory leaks
    if (newVideos[index].preview) {
      URL.revokeObjectURL(newVideos[index].preview);
    }
    newVideos.splice(index, 1);
    onVideosChange(newVideos);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <VideoUploadContainer>
      <VideoUploadButton
        type="button"
        onClick={() => fileInputRef.current?.click()}
        disabled={disabled || (maxVideos && videos.length >= maxVideos)}
      >
        <FaVideo />
        {videos.length === 0 ? 'Adicionar Vídeos' : 'Adicionar Mais Vídeos'}
      </VideoUploadButton>

      <HiddenInput
        ref={fileInputRef}
        type="file"
        accept="video/*"
        multiple={!maxVideos || maxVideos > 1}
        onChange={handleVideoSelect}
      />

      {error && <ErrorMessage>{error}</ErrorMessage>}

      {showPreview && videos.length > 0 && (
        <VideoPreviewContainer>
          {videos.map((video, index) => (
            <VideoPreview key={index}>
              <VideoElement
                src={video.preview}
                controls
                muted
                preload="metadata"
              />
              <RemoveButton onClick={() => handleRemoveVideo(index)}>
                <FaTimes />
              </RemoveButton>
              <VideoInfo>
                {video.name} ({formatFileSize(video.size)})
              </VideoInfo>
            </VideoPreview>
          ))}
        </VideoPreviewContainer>
      )}
    </VideoUploadContainer>
  );
};

export default VideoUpload;
