import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { <PERSON>a<PERSON><PERSON>, FaCheck, FaTimes, FaSpinner } from 'react-icons/fa';
import { useSocial } from '../contexts/SocialContext';
import Button from './UI/Button';
import Modal from './UI/Modal';

const ModalContent = styled.div`
  padding: ${props => props.theme.spacing.xl};
  text-align: center;
`;

const Icon = styled.div`
  color: ${props => props.theme.colors.primary};
  margin-bottom: ${props => props.theme.spacing.lg};
  display: flex;
  justify-content: center;
`;

const Title = styled.h2`
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing.md};
`;

const Description = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  margin-bottom: ${props => props.theme.spacing.xl};
  line-height: 1.5;
`;

const FormGroup = styled.div`
  margin-bottom: ${props => props.theme.spacing.lg};
  text-align: left;
`;

const Label = styled.label`
  display: block;
  color: ${props => props.theme.colors.text.primary};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  margin-bottom: ${props => props.theme.spacing.xs};
`;

const UsernameContainer = styled.div`
  position: relative;
  margin-bottom: ${props => props.theme.spacing.sm};
`;

const UsernameInput = styled.input`
  width: 100%;
  padding: ${props => props.theme.spacing.md};
  padding-right: 40px;
  border: 2px solid ${props =>
    props.isValid === false ? props.theme.colors.error :
    props.isValid === true ? props.theme.colors.success :
    props.theme.colors.border.light
  };
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: 1rem;
  transition: all 0.2s ease;

  &:focus {
    outline: none;
    border-color: ${props =>
      props.isValid === false ? props.theme.colors.error :
      props.isValid === true ? props.theme.colors.success :
      props.theme.colors.primary
    };
  }
`;

const ValidationIcon = styled.div`
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: ${props =>
    props.isChecking ? props.theme.colors.primary :
    props.isValid === true ? props.theme.colors.success :
    props.isValid === false ? props.theme.colors.error :
    props.theme.colors.text.secondary
  };
  font-size: 1.2rem;

  ${props => props.isChecking && `
    animation: spin 1s linear infinite;
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
  `}
`;

const ValidationMessage = styled.p`
  color: ${props =>
    props.type === 'error' ? props.theme.colors.error :
    props.type === 'success' ? props.theme.colors.success :
    props.theme.colors.text.secondary
  };
  font-size: 0.9rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 4px;
`;

const UsernameHelp = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  font-size: 0.9rem;
  margin: 0;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  justify-content: center;
  margin-top: ${props => props.theme.spacing.xl};
`;

const UsernameRequiredModal = ({ isOpen, onClose, onSuccess }) => {
  const [username, setUsername] = useState('');
  const [usernameValidation, setUsernameValidation] = useState({
    isValid: null,
    message: '',
    isChecking: false
  });
  const [checkTimeout, setCheckTimeout] = useState(null);
  const [loading, setLoading] = useState(false);
  const { checkUsernameAvailability, updateUsername } = useSocial();

  // Função para gerar username aleatório
  const generateRandomUsername = () => {
    const adjectives = ['verde', 'belo', 'zen', 'natural', 'pequeno', 'grande', 'forte', 'delicado'];
    const nouns = ['bonsai', 'folha', 'galho', 'raiz', 'jardim', 'vaso', 'terra', 'musgo'];
    const numbers = Math.floor(Math.random() * 999) + 1;

    const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
    const noun = nouns[Math.floor(Math.random() * nouns.length)];

    return `${adjective}${noun}${numbers}`;
  };

  // Função para validar formato do username
  const validateUsernameFormat = (value) => {
    if (!value || value.trim() === '') {
      return { isValid: null, message: '', isChecking: false };
    }

    const cleanValue = value.trim();

    if (cleanValue.length < 3) {
      return { isValid: false, message: 'Username deve ter pelo menos 3 caracteres', isChecking: false };
    }

    if (cleanValue.length > 30) {
      return { isValid: false, message: 'Username deve ter no máximo 30 caracteres', isChecking: false };
    }

    const validPattern = /^[a-zA-Z0-9_]+$/;
    if (!validPattern.test(cleanValue)) {
      return { isValid: false, message: 'Apenas letras, números e _ são permitidos', isChecking: false };
    }

    if (cleanValue.startsWith('_') || cleanValue.endsWith('_')) {
      return { isValid: false, message: 'Username não pode começar ou terminar com _', isChecking: false };
    }

    if (cleanValue.includes('__')) {
      return { isValid: false, message: 'Username não pode ter __ consecutivos', isChecking: false };
    }

    return { isValid: null, message: 'Verificando disponibilidade...', isChecking: true };
  };

  // Função para verificar disponibilidade
  const checkUsernameWithDebounce = async (value) => {
    const cleanValue = value.trim();

    try {
      const result = await checkUsernameAvailability(cleanValue);

      if (result.available) {
        return { isValid: true, message: 'Username disponível!', isChecking: false };
      } else {
        const messages = {
          already_taken: 'Username já está em uso',
          invalid_length: 'Comprimento inválido',
          invalid_characters: 'Caracteres inválidos',
          invalid_format: 'Formato inválido'
        };
        return {
          isValid: false,
          message: messages[result.reason] || 'Username não disponível',
          isChecking: false
        };
      }
    } catch (error) {
      return { isValid: false, message: 'Erro ao verificar disponibilidade', isChecking: false };
    }
  };

  // Effect para validar username em tempo real
  useEffect(() => {
    if (!username) {
      setUsernameValidation({ isValid: null, message: '', isChecking: false });
      return;
    }

    const formatValidation = validateUsernameFormat(username);
    setUsernameValidation(formatValidation);

    if (formatValidation.isChecking) {
      if (checkTimeout) {
        clearTimeout(checkTimeout);
      }

      const timeout = setTimeout(async () => {
        const availabilityValidation = await checkUsernameWithDebounce(username);
        setUsernameValidation(availabilityValidation);
      }, 800);

      setCheckTimeout(timeout);
    }

    return () => {
      if (checkTimeout) {
        clearTimeout(checkTimeout);
      }
    };
  }, [username]);

  // Gerar username inicial quando modal abre
  useEffect(() => {
    if (isOpen && !username) {
      const randomUsername = generateRandomUsername();
      setUsername(randomUsername);
    }
  }, [isOpen]);

  const handleUsernameChange = (e) => {
    let value = e.target.value;
    // Remover caracteres inválidos automaticamente
    value = value.replace(/[^a-zA-Z0-9_]/g, '');
    // Limitar comprimento
    if (value.length > 30) {
      value = value.substring(0, 30);
    }
    setUsername(value);
  };



  const handleSave = async () => {
    if (!usernameValidation.isValid) {
      return;
    }

    setLoading(true);
    try {
      await updateUsername(username.trim());
      onSuccess();
    } catch (error) {
      console.error('Error setting username:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} closeOnOverlayClick={false}>
      <ModalContent>
        <Icon>
          <FaUser size={48} />
        </Icon>
        
        <Title>Escolha seu Nome de Usuário</Title>
        
        <Description>
          Para completar seu cadastro, você precisa escolher um nome de usuário único.
          Este será usado na URL do seu perfil público.
        </Description>

        <FormGroup>
          <Label htmlFor="username">Nome de Usuário</Label>
          <UsernameContainer>
            <UsernameInput
              id="username"
              type="text"
              value={username}
              onChange={handleUsernameChange}
              placeholder="seuusername"
              isValid={usernameValidation.isValid}
              maxLength={30}
            />
            <ValidationIcon
              isValid={usernameValidation.isValid}
              isChecking={usernameValidation.isChecking}
            >
              {usernameValidation.isChecking && <FaSpinner />}
              {!usernameValidation.isChecking && usernameValidation.isValid === true && <FaCheck />}
              {!usernameValidation.isChecking && usernameValidation.isValid === false && <FaTimes />}
            </ValidationIcon>
          </UsernameContainer>

          <UsernameHelp>
            Seu perfil ficará disponível em: meubonsai.app/profile/{username || 'seuusername'}
          </UsernameHelp>

          {usernameValidation.message && (
            <ValidationMessage type={usernameValidation.isValid ? 'success' : 'error'}>
              {usernameValidation.isValid ? <FaCheck /> : <FaTimes />}
              {usernameValidation.message}
            </ValidationMessage>
          )}
        </FormGroup>

        <ButtonGroup>
          <Button
            variant="primary"
            onClick={handleSave}
            disabled={!usernameValidation.isValid || loading}
          >
            {loading ? 'Salvando...' : 'Confirmar Username'}
          </Button>
        </ButtonGroup>
      </ModalContent>
    </Modal>
  );
};

export default UsernameRequiredModal;
