import React from 'react';
import styled from 'styled-components';
import { FaExclamationTriangle, FaCrown } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import Button from './UI/Button';

const Container = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  font-size: ${props => props.theme.typography.fontSize.sm};
`;

const ProgressBar = styled.div`
  flex: 1;
  height: 6px;
  background: ${props => props.theme.colors.background.tertiary};
  border-radius: ${props => props.theme.borderRadius.full};
  overflow: hidden;
`;

const Progress = styled.div`
  height: 100%;
  background: ${props => {
    if (props.percentage >= 90) return props.theme.colors.error;
    if (props.percentage >= 75) return props.theme.colors.warning;
    return props.theme.colors.success;
  }};
  width: ${props => Math.min(props.percentage, 100)}%;
  transition: all 0.3s ease;
`;

const UsageText = styled.span`
  color: ${props => props.theme.colors.text.secondary};
  font-weight: 500;
  min-width: fit-content;
`;

const WarningIcon = styled(FaExclamationTriangle)`
  color: ${props => props.theme.colors.warning};
`;

const UpgradeButton = styled(Button)`
  font-size: ${props => props.theme.typography.fontSize.xs};
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  height: auto;
`;

const UnlimitedBadge = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
  color: ${props => props.theme.colors.primary};
  font-weight: 600;
  
  svg {
    color: ${props => props.theme.colors.warning};
  }
`;

const UsageIndicator = ({ 
  current, 
  limit, 
  label, 
  showUpgrade = true,
  compact = false 
}) => {
  const navigate = useNavigate();

  // If unlimited (no limit)
  if (!limit) {
    return (
      <Container>
        <UnlimitedBadge>
          <FaCrown size={12} />
          {compact ? 'Ilimitado' : `${label}: Ilimitado`}
        </UnlimitedBadge>
      </Container>
    );
  }

  const percentage = (current / limit) * 100;
  const isNearLimit = percentage >= 75;
  const isAtLimit = current >= limit;

  return (
    <Container>
      {!compact && (
        <UsageText>
          {label}: {current}/{limit}
        </UsageText>
      )}
      
      <ProgressBar>
        <Progress percentage={percentage} />
      </ProgressBar>

      {compact && (
        <UsageText>
          {current}/{limit}
        </UsageText>
      )}

      {isNearLimit && (
        <WarningIcon size={14} />
      )}

      {showUpgrade && isNearLimit && (
        <UpgradeButton
          variant="primary"
          size="small"
          onClick={() => navigate('/pricing')}
        >
          {isAtLimit ? 'Upgrade' : 'Upgrade'}
        </UpgradeButton>
      )}
    </Container>
  );
};

export default UsageIndicator;
