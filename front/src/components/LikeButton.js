import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FaSpinner } from 'react-icons/fa';
import { toast } from 'react-toastify';
import socialInteractionsApi from '../services/socialInteractionsApi';

const LikeContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
`;

const LikeButtonStyled = styled.button`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  background: ${props => props.isLiked ? props.theme.colors.error : 'transparent'};
  border: 1px solid ${props => props.isLiked ? props.theme.colors.error : props.theme.colors.border.light};
  border-radius: ${props => props.theme.borderRadius.md};
  color: ${props => props.isLiked ? 'white' : props.theme.colors.text.secondary};
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover:not(:disabled) {
    background: ${props => props.isLiked ? props.theme.colors.error : props.theme.colors.error + '10'};
    border-color: ${props => props.theme.colors.error};
    color: ${props => props.isLiked ? 'white' : props.theme.colors.error};
    transform: translateY(-1px);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
  
  svg {
    font-size: 0.9rem;
  }
  
  @media (max-width: 768px) {
    padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
    font-size: 0.8rem;
    
    svg {
      font-size: 0.8rem;
    }
  }
`;

const LikeCount = styled.span`
  color: ${props => props.theme.colors.text.secondary};
  font-size: 0.85rem;
  font-weight: 500;
  
  @media (max-width: 768px) {
    font-size: 0.8rem;
  }
`;

const LikeButton = ({ 
  plantId, 
  initialLikesCount = 0, 
  initialIsLiked = false, 
  canLike = true,
  size = 'normal' 
}) => {
  const [likesCount, setLikesCount] = useState(initialLikesCount);
  const [isLiked, setIsLiked] = useState(initialIsLiked);
  const [loading, setLoading] = useState(false);

  // Update state when props change
  useEffect(() => {
    setLikesCount(initialLikesCount);
    setIsLiked(initialIsLiked);
  }, [initialLikesCount, initialIsLiked]);

  const handleLike = async () => {
    if (!canLike || loading) return;

    // Optimistic update
    const previousLikesCount = likesCount;
    const previousIsLiked = isLiked;

    if (isLiked) {
      // Unlike
      setIsLiked(false);
      setLikesCount(prev => prev - 1);
    } else {
      // Like
      setIsLiked(true);
      setLikesCount(prev => prev + 1);
    }

    setLoading(true);

    try {
      const response = isLiked
        ? await socialInteractionsApi.unlikePlant(plantId)
        : await socialInteractionsApi.likePlant(plantId);

      // Update with server response
      setLikesCount(response.likes_count);
      setIsLiked(response.is_liked);

      if (response.success) {
        toast.success(response.message);
      }
    } catch (error) {
      // Revert optimistic update on error
      setLikesCount(previousLikesCount);
      setIsLiked(previousIsLiked);

      console.error('Error liking/unliking plant:', error);
      const errorMessage = error.response?.data?.detail || 'Erro ao curtir/descurtir planta';
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const formatLikesCount = (count) => {
    if (count === 0) return '0';
    if (count === 1) return '1';
    if (count < 1000) return count.toString();
    if (count < 1000000) return `${(count / 1000).toFixed(1)}k`;
    return `${(count / 1000000).toFixed(1)}M`;
  };

  if (!canLike) {
    return (
      <LikeContainer>
        <LikeCount>
          {formatLikesCount(likesCount)} {likesCount === 1 ? 'curtida' : 'curtidas'}
        </LikeCount>
      </LikeContainer>
    );
  }

  return (
    <LikeContainer>
      <LikeButtonStyled
        onClick={handleLike}
        disabled={loading || !canLike}
        isLiked={isLiked}
        size={size}
      >
        {loading ? (
          <FaSpinner className="spinning" />
        ) : (
          <FaHeart />
        )}
        {isLiked ? 'Curtido' : 'Curtir'}
      </LikeButtonStyled>
      
      <LikeCount>
        {formatLikesCount(likesCount)} {likesCount === 1 ? 'curtida' : 'curtidas'}
      </LikeCount>
    </LikeContainer>
  );
};

export default LikeButton;
