import React, { useState } from 'react';
import styled from 'styled-components';
import { FaEdit, FaTrash, FaInfo, FaTimes, FaCheck, FaUndo, FaRedo, FaImages, FaPlay } from 'react-icons/fa';
import { toast } from 'react-toastify';
import Button from './UI/Button';
import { getImageUrlSync as getImageUrl } from '../services/api';
import ImageModal from './ImageModal';

// Video placeholder SVG
const VIDEO_PLACEHOLDER = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjZjVmNWY1Ii8+Cjxwb2x5Z29uIHBvaW50cz0iODAsNDAgMTIwLDYwIDgwLDgwIiBmaWxsPSIjNjY2Ii8+Cjx0ZXh0IHg9IjEwMCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5IiBmb250LXNpemU9IjEyIj5WaWRlbzwvdGV4dD4KPC9zdmc+";

const GalleryContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.md};
`;

const ImageGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: ${props => props.theme.spacing.md};
`;

const ImageCard = styled.div`
  position: relative;
  aspect-ratio: 1;
  border-radius: ${props => props.theme.borderRadius.md};
  overflow: hidden;
  box-shadow: ${props => props.theme.shadows.sm};
  background-color: ${props => props.theme.colors.background};
  
  &:hover .image-overlay {
    opacity: 1;
  }
`;

const ImageElement = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
`;

const VideoElement = styled.video`
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
`;

const VideoOverlay = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  pointer-events: none;
`;

const ImageOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${props => props.theme.spacing.sm};
  opacity: 0;
  transition: opacity 0.3s ease;
`;

const IconButton = styled.button`
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: white;
    transform: scale(1.1);
  }
  
  &.danger {
    background: rgba(220, 53, 69, 0.9);
    color: white;
    
    &:hover {
      background: #dc3545;
    }
  }
`;

const PrimaryBadge = styled.div`
  position: absolute;
  top: ${props => props.theme.spacing.sm};
  left: ${props => props.theme.spacing.sm};
  background: ${props => props.theme.colors.primary};
  color: white;
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: ${props => props.theme.typography.fontSize.xs};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
`;

const TypeBadge = styled.div`
  position: absolute;
  top: ${props => props.theme.spacing.sm};
  right: ${props => props.theme.spacing.sm};
  background: ${props => props.type === 'care' ? '#28a745' : '#6c757d'};
  color: white;
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: ${props => props.theme.typography.fontSize.xs};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  text-transform: uppercase;
`;

const DateBadge = styled.div`
  position: absolute;
  bottom: ${props => props.theme.spacing.sm};
  left: ${props => props.theme.spacing.sm};
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: ${props => props.theme.typography.fontSize.xs};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
`;

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: ${props => props.theme.spacing.lg};
`;

const ModalContent = styled.div`
  background: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.xl};
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const ModalTitle = styled.h3`
  margin: 0;
  color: ${props => props.theme.colors.text.primary};
`;

const FormGroup = styled.div`
  margin-bottom: ${props => props.theme.spacing.md};
`;

const Label = styled.label`
  display: block;
  margin-bottom: ${props => props.theme.spacing.xs};
  color: ${props => props.theme.colors.text.primary};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 80px;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.md};
  resize: vertical;
  
  &:focus {
    border-color: ${props => props.theme.colors.primary};
    outline: none;
  }
`;

const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const Checkbox = styled.input`
  width: 16px;
  height: 16px;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
  justify-content: flex-end;
  margin-top: ${props => props.theme.spacing.lg};
`;

const PreviewContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const PreviewImage = styled.img`
  max-width: 250px;
  max-height: 250px;
  object-fit: contain;
  border-radius: ${props => props.theme.borderRadius.md};
  transform: rotate(${props => props.rotation || 0}deg);
  transition: transform 0.3s ease;
`;

const RotationControls = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
`;

const RotationButtons = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
`;

const RotationButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  background-color: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text.primary};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${props => props.theme.colors.primary};
    color: white;
    border-color: ${props => props.theme.colors.primary};
  }

  &:active {
    transform: scale(0.95);
  }
`;

const RotationText = styled.p`
  font-size: ${props => props.theme.typography.fontSize.sm};
  color: ${props => props.theme.colors.text.secondary};
  margin: 0;
  text-align: center;
`;

const ViewMoreCard = styled.div`
  position: relative;
  aspect-ratio: 1;
  border-radius: ${props => props.theme.borderRadius.md};
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px dashed #dee2e6;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    border-color: ${props => props.theme.colors.primary};
    background: linear-gradient(135deg, ${props => props.theme.colors.primary}08 0%, ${props => props.theme.colors.primary}15 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(0);
  }
`;

const ViewMoreContent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
  color: ${props => props.theme.colors.text.secondary};
  text-align: center;
`;

const ViewMoreText = styled.span`
  font-size: ${props => props.theme.typography.fontSize.sm};
  font-weight: 500;
  color: inherit;
`;

const ImageGallery = ({
  images,
  plantId,
  onImagesChange,
  showCareImages = false,
  compact = false,
  maxImages = 3,
  readOnly = false,
  onImageClick = null,
  disableModal = false
}) => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showImageModal, setShowImageModal] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [editData, setEditData] = useState({});
  const [loading, setLoading] = useState(false);
  const [rotation, setRotation] = useState(0);


  // Função para formatar data
  const formatPhotoDate = (dateString) => {
    if (!dateString) return null;
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    } catch (error) {
      return null;
    }
  };



  const handleRotateLeft = () => {
    const newRotation = (rotation - 90) % 360;
    setRotation(newRotation);
  };

  const handleRotateRight = () => {
    const newRotation = (rotation + 90) % 360;
    setRotation(newRotation);
  };

  const handleImageClick = (image, index) => {
    if (image.is_video) {
      // For videos, open edit modal instead of image modal
      setSelectedImage(image);
      setEditData({
        caption: image.caption || '',
        photo_date: image.photo_date || '',
        is_primary: image.is_primary || false
      });
      setShowEditModal(true);
    } else {
      // Check if external onImageClick handler is provided
      if (onImageClick) {
        onImageClick(image, index, images);
      } else {
        // Default behavior: open internal image modal
        setCurrentImageIndex(index);
        setShowImageModal(true);
      }
    }
  };

  const handleEditClick = (image) => {
    if (readOnly) return; // Não permitir edição em modo somente leitura

    setSelectedImage(image);

    // Format photo_date for date input (only date, no time)
    let formattedPhotoDate = '';
    if (image.photo_date) {
      const date = new Date(image.photo_date);
      // Format as YYYY-MM-DD for date input
      formattedPhotoDate = date.toISOString().split('T')[0];
    }

    setEditData({
      caption: image.caption || '',
      is_primary: image.is_primary || false,
      photo_date: formattedPhotoDate
    });
    setRotation(0); // Reset rotation when opening modal
    setShowEditModal(true);
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      const { plantsService } = await import('../services/plantsService');

      const isPlantImage = selectedImage.type === 'plant';
      const isCareImage = selectedImage.type === 'care';

      if (isPlantImage) {
        // Handle plant images and videos
        if (rotation !== 0 && !selectedImage.is_video) {
          try {
            await plantsService.rotatePlantImage(plantId, selectedImage.source_id, rotation);

            if (editData.caption !== selectedImage.caption ||
                editData.is_primary !== selectedImage.is_primary ||
                editData.photo_date !== selectedImage.photo_date) {
              await plantsService.updatePlantImage(plantId, selectedImage.source_id, {
                caption: editData.caption,
                is_primary: editData.is_primary,
                photo_date: editData.photo_date
              });
            }

            toast.success('Imagem rotacionada e atualizada com sucesso!');
          } catch (rotationError) {
            console.error('Erro ao rotacionar imagem:', rotationError);
            await plantsService.updatePlantImage(plantId, selectedImage.source_id, {
              caption: editData.caption,
              is_primary: editData.is_primary,
              photo_date: editData.photo_date
            });
            toast.warning('Metadados atualizados, mas não foi possível rotacionar a imagem');
          }
        } else {
          await plantsService.updatePlantImage(plantId, selectedImage.source_id, {
            caption: editData.caption,
            is_primary: editData.is_primary,
            photo_date: editData.photo_date
          });
          toast.success('Imagem atualizada com sucesso!');
        }
      } else if (isCareImage) {
        // Handle care media - extract the actual media ID
        let mediaId;
        if (selectedImage.id.startsWith('care_image_')) {
          // New care images from care_images table
          mediaId = selectedImage.id.replace('care_image_', '');
        } else if (selectedImage.id.startsWith('care_video_')) {
          // Care videos - can't be rotated/edited
          toast.info('Vídeos não podem ser editados. Você pode apenas removê-los.');
          setShowEditModal(false);
          setRotation(0);
          return;
        } else if (selectedImage.id.startsWith('care_')) {
          // Legacy care images - these can't be rotated/edited individually
          toast.info('Esta é uma foto legacy do cuidado. Para editá-la, edite o cuidado no histórico.');
          setShowEditModal(false);
          setRotation(0);
          return;
        } else {
          toast.error('Tipo de mídia não reconhecido');
          setShowEditModal(false);
          setRotation(0);
          return;
        }

        // Handle care images from care_images table
        if (rotation !== 0) {
          try {
            await plantsService.rotateCareImage(plantId, selectedImage.source_id, mediaId, rotation);

            if (editData.caption !== selectedImage.caption ||
                editData.is_primary !== selectedImage.is_primary) {
              await plantsService.updateCareImage(plantId, selectedImage.source_id, mediaId, {
                caption: editData.caption,
                is_primary: editData.is_primary
              });
            }

            toast.success('Foto do cuidado rotacionada e atualizada com sucesso!');
          } catch (rotationError) {
            console.error('Erro ao rotacionar foto do cuidado:', rotationError);
            await plantsService.updateCareImage(plantId, selectedImage.source_id, mediaId, {
              caption: editData.caption,
              is_primary: editData.is_primary
            });
            toast.warning('Metadados atualizados, mas não foi possível rotacionar a imagem');
          }
        } else {
          await plantsService.updateCareImage(plantId, selectedImage.source_id, mediaId, {
            caption: editData.caption,
            is_primary: editData.is_primary
          });
          toast.success('Foto do cuidado atualizada com sucesso!');
        }
      }

      setShowEditModal(false);
      setRotation(0);
      onImagesChange();
    } catch (error) {
      console.error('Erro ao atualizar imagem:', error);
      toast.error('Erro ao atualizar imagem');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (image) => {
    const isPlantImage = image.type === 'plant';
    const isCareImage = image.type === 'care';

    let confirmMessage;
    if (isPlantImage) {
      confirmMessage = 'Tem certeza que deseja remover esta imagem da galeria?';
    } else if (isCareImage) {
      confirmMessage = 'Tem certeza que deseja remover esta foto? Ela será removida tanto da galeria quanto do cuidado.';
    }

    if (!window.confirm(confirmMessage)) {
      return;
    }

    setLoading(true);
    try {
      const { plantsService } = await import('../services/plantsService');

      if (isPlantImage) {
        // Check if it's a video or image
        if (image.is_video) {
          await plantsService.deletePlantVideo(plantId, image.source_id);
          toast.success('Vídeo removido com sucesso!');
        } else {
          await plantsService.deletePlantImage(plantId, image.source_id);
          toast.success('Imagem removida com sucesso!');
        }
      } else if (isCareImage) {
        // Handle care media deletion (images and videos)
        if (image.id.startsWith('care_image_')) {
          // New care images from care_images table
          const imageId = image.id.replace('care_image_', '');
          await plantsService.deleteCareImage(plantId, image.source_id, imageId);
          toast.success('Foto do cuidado removida com sucesso!');
        } else if (image.id.startsWith('care_video_')) {
          // Care videos from care_videos table
          const videoId = image.id.replace('care_video_', '');
          await plantsService.deleteCareVideo(plantId, image.source_id, videoId);
          toast.success('Vídeo do cuidado removido com sucesso!');
        } else if (image.id.startsWith('care_')) {
          // Legacy care images - inform user
          toast.info('Esta é uma foto legacy do cuidado. Para removê-la, edite o cuidado no histórico.');
          setLoading(false);
          return;
        } else {
          toast.error('Tipo de mídia não reconhecido');
          setLoading(false);
          return;
        }
      }

      onImagesChange();
    } catch (error) {
      console.error('Erro ao remover imagem:', error);
      toast.error('Erro ao remover imagem');
    } finally {
      setLoading(false);
    }
  };

  if (!images || images.length === 0) {
    return null;
  }

  // Determinar quais imagens mostrar
  const displayImages = compact ? images.slice(0, maxImages) : images;
  const hasMoreImages = compact && images.length > maxImages;

  return (
    <GalleryContainer>
      <ImageGrid>
        {displayImages.map((image, index) => (
          <ImageCard key={image.id}>
            {image.is_video ? (
              <>
                <VideoElement
                  src={getImageUrl(image.image)}
                  muted
                  preload="none"
                  controls={false}
                  playsInline
                  poster={image.thumbnail_url || VIDEO_PLACEHOLDER}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    // Load video on first click, then play/pause
                    if (e.target.readyState === 0) {
                      e.target.load();
                      e.target.addEventListener('loadeddata', () => {
                        e.target.play();
                      }, { once: true });
                    } else if (e.target.paused) {
                      e.target.play();
                    } else {
                      e.target.pause();
                    }
                  }}
                  style={{ cursor: 'pointer' }}
                  onError={(e) => {
                    e.target.style.display = 'none';
                  }}
                />
                <VideoOverlay>
                  <FaPlay />
                </VideoOverlay>
              </>
            ) : (
              <ImageElement
                src={getImageUrl(image.image)}
                alt={image.caption || 'Foto da planta'}
                onClick={() => handleImageClick(image, index)}
                style={{ cursor: 'pointer' }}
                onError={(e) => {
                  e.target.style.display = 'none';
                }}
              />
            )}
            {image.is_primary && <PrimaryBadge>Principal</PrimaryBadge>}
            {showCareImages && (
              <TypeBadge type={image.type}>
                {image.type === 'care' ? image.care_type || 'Cuidado' : 'Planta'}
              </TypeBadge>
            )}
            {formatPhotoDate(image.photo_date) && (
              <DateBadge>{formatPhotoDate(image.photo_date)}</DateBadge>
            )}
            <ImageOverlay className="image-overlay">
              {!readOnly && (
                <IconButton onClick={(e) => {
                  e.stopPropagation();
                  handleEditClick(image);
                }}>
                  <FaEdit />
                </IconButton>
              )}
              <IconButton onClick={(e) => {
                e.stopPropagation();
                handleImageClick(image, index);
              }}>
                <FaInfo />
              </IconButton>
              {!readOnly && (
                <IconButton
                  className="danger"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDelete(image);
                  }}
                  disabled={loading}
                >
                  <FaTrash />
                </IconButton>
              )}
            </ImageOverlay>
          </ImageCard>
        ))}

        {hasMoreImages && (
          <ViewMoreCard onClick={() => window.location.href = `/plants/${plantId}/gallery`}>
            <ViewMoreContent>
              <FaImages size={24} />
              <ViewMoreText>
                +{images.length - maxImages} {images.some(img => img.is_video) ? 'itens' : 'fotos'}
              </ViewMoreText>
            </ViewMoreContent>
          </ViewMoreCard>
        )}
      </ImageGrid>

      {showEditModal && selectedImage && !readOnly && (
        <Modal>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>Editar {selectedImage.is_video ? 'Vídeo' : 'Imagem'}</ModalTitle>
              <IconButton onClick={() => setShowEditModal(false)}>
                <FaTimes />
              </IconButton>
            </ModalHeader>

            <PreviewContainer>
              {selectedImage.is_video ? (
                <video
                  src={getImageUrl(selectedImage.image)}
                  controls
                  preload="metadata"
                  playsInline
                  style={{
                    width: '100%',
                    maxWidth: '400px',
                    maxHeight: '300px',
                    borderRadius: '8px'
                  }}
                  onError={(e) => {
                    // Video failed to load
                  }}
                />
              ) : (
                <>
                  <PreviewImage
                    src={getImageUrl(selectedImage.image)}
                    alt="Preview"
                    rotation={rotation}
                  />
                  <RotationControls>
                    <RotationText>Rotacionar imagem</RotationText>
                    <RotationButtons>
                      <RotationButton
                        type="button"
                        onClick={handleRotateLeft}
                        title="Rotacionar 90° para esquerda"
                      >
                        <FaUndo />
                      </RotationButton>
                      <RotationButton
                        type="button"
                        onClick={handleRotateRight}
                        title="Rotacionar 90° para direita"
                      >
                        <FaRedo />
                      </RotationButton>
                    </RotationButtons>
                    {rotation !== 0 && (
                      <RotationText style={{ fontSize: '12px', color: '#666' }}>
                        Rotação: {rotation}°
                      </RotationText>
                    )}
                  </RotationControls>
                </>
              )}
            </PreviewContainer>

            <FormGroup>
              <Label htmlFor="caption">Legenda</Label>
              <TextArea
                id="caption"
                value={editData.caption}
                onChange={(e) => setEditData(prev => ({ ...prev, caption: e.target.value }))}
                placeholder={`Adicione uma descrição para este ${selectedImage.is_video ? 'vídeo' : 'foto'}...`}
              />
            </FormGroup>

            <FormGroup>
              <Label htmlFor="photoDate">Data {selectedImage.is_video ? 'do vídeo' : 'da foto'}</Label>
              <input
                id="photoDate"
                type="date"
                value={editData.photo_date || ''}
                onChange={(e) => setEditData(prev => ({ ...prev, photo_date: e.target.value }))}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  fontSize: '14px'
                }}
              />

            </FormGroup>

            {!selectedImage.is_video && (
              <FormGroup>
                <CheckboxContainer>
                  <Checkbox
                    id="isPrimary"
                    type="checkbox"
                    checked={editData.is_primary}
                    onChange={(e) => setEditData(prev => ({ ...prev, is_primary: e.target.checked }))}
                  />
                  <Label htmlFor="isPrimary">Definir como foto principal</Label>
                </CheckboxContainer>
              </FormGroup>
            )}

            <ButtonGroup>
              <Button
                type="button"
                variant="secondary"
                onClick={() => setShowEditModal(false)}
                disabled={loading}
              >
                Cancelar
              </Button>
              <Button
                type="button"
                onClick={handleSave}
                disabled={loading}
              >
                <FaCheck />
                {loading ? 'Salvando...' : 'Salvar'}
              </Button>
            </ButtonGroup>
          </ModalContent>
        </Modal>
      )}

      {!disableModal && (
        <ImageModal
          isOpen={showImageModal}
          onClose={() => setShowImageModal(false)}
          images={images.filter(img => !img.is_video).map(img => ({
            src: getImageUrl(img.image, 'large', plantId),
            title: img.caption || (img.type === 'care' ? `${img.care_type || 'Cuidado'} - ${formatPhotoDate(img.photo_date)}` : 'Foto da planta'),
            description: img.type === 'care' ? img.description : img.caption
          }))}
          currentIndex={currentImageIndex}
          onIndexChange={setCurrentImageIndex}
        />
      )}
    </GalleryContainer>
  );
};

export default ImageGallery;
