import React, { useState } from 'react';
import styled from 'styled-components';
import { FaTicketAlt, FaPlus, FaEdit, FaTrash, FaEye, FaSearch } from 'react-icons/fa';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { toast } from 'react-toastify';
import adminService from '../../services/adminService';
import Button from '../UI/Button';
import Loading from '../UI/Loading';
import Modal from '../UI/Modal';
import CouponForm from './CouponForm';

const CouponManagementContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.lg};
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: ${props => props.theme.spacing.md};
`;

const Title = styled.h2`
  color: ${props => props.theme.colors.text.primary};
  font-size: ${props => props.theme.typography.fontSize.xl};
  font-weight: ${props => props.theme.typography.fontWeight.semibold};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  margin: 0;
`;

const Controls = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  align-items: center;
  flex-wrap: wrap;
`;

const SearchContainer = styled.div`
  position: relative;
  max-width: 300px;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.sm} ${props => props.theme.spacing.sm} 40px;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.md};
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const SearchIcon = styled.div`
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: ${props => props.theme.colors.text.secondary};
`;

const CouponsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: ${props => props.theme.spacing.lg};

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const CouponCard = styled.div`
  background: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.lg};
  box-shadow: ${props => props.theme.shadows.sm};
  border: 1px solid ${props => props.theme.colors.border};
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  position: relative;

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.md};
  }
`;

const CouponHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${props => props.theme.spacing.md};
`;

const CouponCode = styled.div`
  background: ${props => props.theme.colors.primary};
  color: white;
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-family: monospace;
  font-weight: ${props => props.theme.typography.fontWeight.bold};
  font-size: ${props => props.theme.typography.fontSize.sm};
`;

const CouponStatus = styled.div`
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: ${props => props.theme.typography.fontSize.xs};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  text-transform: uppercase;
  
  ${props => props.active ? `
    background: #c6f6d5;
    color: #22543d;
  ` : `
    background: #fed7d7;
    color: #742a2a;
  `}
`;

const CouponName = styled.h3`
  color: ${props => props.theme.colors.text.primary};
  font-size: ${props => props.theme.typography.fontSize.lg};
  font-weight: ${props => props.theme.typography.fontWeight.semibold};
  margin: 0 0 ${props => props.theme.spacing.sm} 0;
`;

const CouponDescription = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  font-size: ${props => props.theme.typography.fontSize.sm};
  margin: 0 0 ${props => props.theme.spacing.md} 0;
  line-height: 1.4;
`;

const CouponDetails = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${props => props.theme.spacing.md};
  margin-bottom: ${props => props.theme.spacing.md};
`;

const DetailItem = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.xs};
`;

const DetailLabel = styled.span`
  font-size: ${props => props.theme.typography.fontSize.xs};
  color: ${props => props.theme.colors.text.secondary};
  text-transform: uppercase;
  font-weight: ${props => props.theme.typography.fontWeight.medium};
`;

const DetailValue = styled.span`
  font-size: ${props => props.theme.typography.fontSize.sm};
  color: ${props => props.theme.colors.text.primary};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
`;

const UsageBar = styled.div`
  width: 100%;
  height: 6px;
  background: ${props => props.theme.colors.background};
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: ${props => props.theme.spacing.md};
`;

const UsageProgress = styled.div`
  height: 100%;
  background: ${props => props.percentage > 80 ? '#e53e3e' : props.percentage > 60 ? '#d69e2e' : '#38a169'};
  width: ${props => props.percentage}%;
  transition: width 0.3s ease;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.xs};
  justify-content: flex-end;
`;

const ActionButton = styled.button`
  background: none;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.sm};
  padding: ${props => props.theme.spacing.xs};
  cursor: pointer;
  transition: all 0.2s ease;
  color: ${props => props.theme.colors.text.secondary};
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: ${props => props.theme.colors.background};
    color: ${props => props.theme.colors.primary};
    border-color: ${props => props.theme.colors.primary};
  }

  &.danger:hover {
    color: #e53e3e;
    border-color: #e53e3e;
  }
`;

const CouponManagement = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedCoupon, setSelectedCoupon] = useState(null);
  const queryClient = useQueryClient();

  const { data: coupons, isLoading } = useQuery(
    'admin-coupons',
    () => adminService.getCoupons(),
    {
      onError: (error) => {
        toast.error('Erro ao carregar cupons');
      }
    }
  );

  const createCouponMutation = useMutation(
    adminService.createCoupon,
    {
      onSuccess: () => {
        toast.success('Cupom criado com sucesso');
        queryClient.invalidateQueries('admin-coupons');
        setShowCreateModal(false);
      },
      onError: (error) => {
        toast.error(error.response?.data?.detail || 'Erro ao criar cupom');
      }
    }
  );

  const updateCouponMutation = useMutation(
    ({ couponId, couponData }) => adminService.updateCoupon(couponId, couponData),
    {
      onSuccess: () => {
        toast.success('Cupom atualizado com sucesso');
        queryClient.invalidateQueries('admin-coupons');
        setShowEditModal(false);
        setSelectedCoupon(null);
      },
      onError: (error) => {
        toast.error(error.response?.data?.detail || 'Erro ao atualizar cupom');
      }
    }
  );

  const deleteCouponMutation = useMutation(
    adminService.deleteCoupon,
    {
      onSuccess: () => {
        toast.success('Cupom excluído com sucesso');
        queryClient.invalidateQueries('admin-coupons');
      },
      onError: () => {
        toast.error('Erro ao excluir cupom');
      }
    }
  );

  const filteredCoupons = coupons?.filter(coupon =>
    coupon.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    coupon.name.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const handleCreateCoupon = (couponData) => {
    createCouponMutation.mutate(couponData);
  };

  const handleEditCoupon = (coupon) => {
    setSelectedCoupon(coupon);
    setShowEditModal(true);
  };

  const handleUpdateCoupon = (couponData) => {
    if (selectedCoupon) {
      updateCouponMutation.mutate({
        couponId: selectedCoupon.id,
        couponData
      });
    }
  };

  const handleDeleteCoupon = (couponId) => {
    if (window.confirm('Tem certeza que deseja excluir este cupom?')) {
      deleteCouponMutation.mutate(couponId);
    }
  };

  const formatDiscount = (coupon) => {
    if (coupon.coupon_type === 'percentage') {
      return `${coupon.discount_value}%`;
    } else {
      return `R$ ${coupon.discount_value.toFixed(2)}`;
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Sem limite';
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  if (isLoading) {
    return <Loading />;
  }

  return (
    <CouponManagementContainer>
      <Header>
        <Title>
          <FaTicketAlt />
          Gerenciamento de Cupons
        </Title>
        <Controls>
          <SearchContainer>
            <SearchIcon>
              <FaSearch />
            </SearchIcon>
            <SearchInput
              type="text"
              placeholder="Buscar cupons..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </SearchContainer>
          <Button onClick={() => setShowCreateModal(true)}>
            <FaPlus />
            Novo Cupom
          </Button>
        </Controls>
      </Header>

      <CouponsGrid>
        {filteredCoupons.map(coupon => (
          <CouponCard key={coupon.id}>
            <CouponHeader>
              <CouponCode>{coupon.code}</CouponCode>
              <CouponStatus active={coupon.is_active}>
                {coupon.is_active ? 'Ativo' : 'Inativo'}
              </CouponStatus>
            </CouponHeader>

            <CouponName>{coupon.name}</CouponName>
            
            {coupon.description && (
              <CouponDescription>{coupon.description}</CouponDescription>
            )}

            <CouponDetails>
              <DetailItem>
                <DetailLabel>Desconto</DetailLabel>
                <DetailValue>{formatDiscount(coupon)}</DetailValue>
              </DetailItem>
              <DetailItem>
                <DetailLabel>Usos</DetailLabel>
                <DetailValue>
                  {coupon.current_uses} / {coupon.max_uses || '∞'}
                </DetailValue>
              </DetailItem>
              <DetailItem>
                <DetailLabel>Válido até</DetailLabel>
                <DetailValue>{formatDate(coupon.valid_until)}</DetailValue>
              </DetailItem>
              <DetailItem>
                <DetailLabel>Status</DetailLabel>
                <DetailValue>{coupon.is_valid ? 'Válido' : 'Expirado'}</DetailValue>
              </DetailItem>
            </CouponDetails>

            {coupon.max_uses && (
              <UsageBar>
                <UsageProgress percentage={coupon.usage_percentage} />
              </UsageBar>
            )}

            <ActionButtons>
              <ActionButton
                onClick={() => handleEditCoupon(coupon)}
                title="Editar cupom"
              >
                <FaEdit />
              </ActionButton>
              
              <ActionButton
                className="danger"
                onClick={() => handleDeleteCoupon(coupon.id)}
                title="Excluir cupom"
              >
                <FaTrash />
              </ActionButton>
            </ActionButtons>
          </CouponCard>
        ))}
      </CouponsGrid>

      {/* Create Coupon Modal */}
      {showCreateModal && (
        <Modal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          title="Criar Novo Cupom"
        >
          <CouponForm
            onSubmit={handleCreateCoupon}
            onCancel={() => setShowCreateModal(false)}
            isLoading={createCouponMutation.isLoading}
          />
        </Modal>
      )}

      {/* Edit Coupon Modal */}
      {showEditModal && selectedCoupon && (
        <Modal
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setSelectedCoupon(null);
          }}
          title="Editar Cupom"
        >
          <CouponForm
            coupon={selectedCoupon}
            onSubmit={handleUpdateCoupon}
            onCancel={() => {
              setShowEditModal(false);
              setSelectedCoupon(null);
            }}
            isLoading={updateCouponMutation.isLoading}
          />
        </Modal>
      )}
    </CouponManagementContainer>
  );
};

export default CouponManagement;
