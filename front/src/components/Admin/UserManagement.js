import React, { useState } from 'react';
import styled from 'styled-components';
import { <PERSON>a<PERSON>sers, FaEdit, FaBan, FaCrown, FaSearch, FaCheck, FaTimes, FaHistory } from 'react-icons/fa';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { toast } from 'react-toastify';
import adminService from '../../services/adminService';
import Button from '../UI/Button';
import Loading from '../UI/Loading';
import Modal from '../UI/Modal';
import UserHistoryModal from './UserHistoryModal';

const UserManagementContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.lg};
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: ${props => props.theme.spacing.md};
`;

const Title = styled.h2`
  color: ${props => props.theme.colors.text.primary};
  font-size: ${props => props.theme.typography.fontSize.xl};
  font-weight: ${props => props.theme.typography.fontWeight.semibold};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  margin: 0;
`;

const SearchContainer = styled.div`
  position: relative;
  max-width: 300px;
  flex: 1;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.sm} ${props => props.theme.spacing.sm} 40px;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.md};
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const SearchIcon = styled.div`
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: ${props => props.theme.colors.text.secondary};
`;

const UsersTable = styled.div`
  background: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.lg};
  overflow: hidden;
  box-shadow: ${props => props.theme.shadows.sm};
  border: 1px solid ${props => props.theme.colors.border};
`;

const TableHeader = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 150px;
  gap: ${props => props.theme.spacing.md};
  padding: ${props => props.theme.spacing.lg};
  background: ${props => props.theme.colors.background};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  font-weight: ${props => props.theme.typography.fontWeight.semibold};
  color: ${props => props.theme.colors.text.secondary};
  font-size: ${props => props.theme.typography.fontSize.sm};

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: ${props => props.theme.spacing.sm};
  }
`;

const TableRow = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 150px;
  gap: ${props => props.theme.spacing.md};
  padding: ${props => props.theme.spacing.lg};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  transition: background-color 0.2s ease;
  align-items: center;

  &:hover {
    background: ${props => props.theme.colors.background};
  }

  &:last-child {
    border-bottom: none;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: ${props => props.theme.spacing.sm};
  }
`;

const UserInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.xs};
`;

const UserName = styled.span`
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  color: ${props => props.theme.colors.text.primary};
`;

const UserEmail = styled.span`
  font-size: ${props => props.theme.typography.fontSize.sm};
  color: ${props => props.theme.colors.text.secondary};
`;

const StatusBadge = styled.span`
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: ${props => props.theme.typography.fontSize.xs};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  text-transform: uppercase;
  
  ${props => {
    if (props.status === 'active') {
      return `
        background: #c6f6d5;
        color: #22543d;
      `;
    } else if (props.status === 'premium') {
      return `
        background: #fef5e7;
        color: #744210;
      `;
    } else {
      return `
        background: #fed7d7;
        color: #742a2a;
      `;
    }
  }}
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.xs};
  justify-content: flex-end;

  @media (max-width: 768px) {
    justify-content: flex-start;
  }
`;

const ActionButton = styled.button`
  background: none;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.sm};
  padding: ${props => props.theme.spacing.xs};
  cursor: pointer;
  transition: all 0.2s ease;
  color: ${props => props.theme.colors.text.secondary};
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: ${props => props.theme.colors.background};
    color: ${props => props.theme.colors.primary};
    border-color: ${props => props.theme.colors.primary};
  }

  &.danger:hover {
    color: #e53e3e;
    border-color: #e53e3e;
  }
`;

const DateInput = styled.input`
  padding: ${props => props.theme.spacing.sm};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.md};
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const DateLabel = styled.label`
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  color: ${props => props.theme.colors.text.primary};
  font-size: ${props => props.theme.typography.fontSize.sm};
`;

const DateHelperText = styled.small`
  color: ${props => props.theme.colors.text.secondary};
  font-size: ${props => props.theme.typography.fontSize.xs};
`;

const Select = styled.select`
  padding: ${props => props.theme.spacing.sm};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.md};
  background: white;
  cursor: pointer;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.sm};
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  justify-content: flex-end;
  margin-top: ${props => props.theme.spacing.lg};
`;

const UserManagement = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState(null);
  const [showPlanModal, setShowPlanModal] = useState(false);
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  const [historyUserId, setHistoryUserId] = useState(null);
  const [expirationDate, setExpirationDate] = useState('');
  const [selectedPlanId, setSelectedPlanId] = useState('');
  const queryClient = useQueryClient();

  const { data: users, isLoading } = useQuery(
    'admin-users',
    () => adminService.getUsers(),
    {
      onError: (error) => {
        toast.error('Erro ao carregar usuários');
      }
    }
  );

  const { data: plans, isLoading: plansLoading, error: plansError } = useQuery(
    'admin-plans',
    adminService.getPlans,
    {
      onError: (error) => {
        console.error('Erro ao carregar planos:', error);
        toast.error('Erro ao carregar planos');
      }
    }
  );

  const deactivatePlanMutation = useMutation(
    adminService.deactivateUserPlan,
    {
      onSuccess: () => {
        toast.success('Plano desativado com sucesso');
        queryClient.invalidateQueries('admin-users');
      },
      onError: () => {
        toast.error('Erro ao desativar plano');
      }
    }
  );

  const changePlanMutation = useMutation(
    ({ userId, planData }) => adminService.changeUserPlan(userId, planData),
    {
      onSuccess: () => {
        toast.success('Plano alterado com sucesso');
        queryClient.invalidateQueries('admin-users');
        setShowPlanModal(false);
        setSelectedUser(null);
        setExpirationDate('');
        setSelectedPlanId('');
      },
      onError: () => {
        toast.error('Erro ao alterar plano');
      }
    }
  );

  const filteredUsers = users?.filter(user =>
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.full_name.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const handleDeactivatePlan = (userId) => {
    if (window.confirm('Tem certeza que deseja desativar o plano deste usuário?')) {
      deactivatePlanMutation.mutate(userId);
    }
  };

  const handleChangePlan = (user) => {
    setSelectedUser(user);
    setShowPlanModal(true);
    // Set default expiration to 1 year from now
    const defaultExpiration = new Date();
    defaultExpiration.setFullYear(defaultExpiration.getFullYear() + 1);
    setExpirationDate(defaultExpiration.toISOString().split('T')[0]);
    // Set current plan as default selection
    setSelectedPlanId(user.current_plan_id || '');
  };

  const handleShowHistory = (userId) => {
    setHistoryUserId(userId);
    setShowHistoryModal(true);
  };

  const handlePlanSubmit = () => {
    if (selectedUser && selectedPlanId) {
      const selectedPlan = plans?.find(p => p.id === selectedPlanId);
      const isFreeplan = selectedPlan?.price_cents === 0;

      const planData = {
        user_id: selectedUser.id,
        new_plan_id: selectedPlanId,
        expires_at: isFreeplan ? null : (expirationDate ? new Date(expirationDate + 'T23:59:59').toISOString() : null)
      };
      changePlanMutation.mutate({ userId: selectedUser.id, planData });
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Nunca';
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  if (isLoading) {
    return <Loading />;
  }

  return (
    <UserManagementContainer>
      <Header>
        <Title>
          <FaUsers />
          Gerenciamento de Usuários
        </Title>
        <SearchContainer>
          <SearchIcon>
            <FaSearch />
          </SearchIcon>
          <SearchInput
            type="text"
            placeholder="Buscar usuários..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </SearchContainer>
      </Header>

      <UsersTable>
        <TableHeader>
          <div>Usuário</div>
          <div>Plano</div>
          <div>Status</div>
          <div>Plantas</div>
          <div>Expira em</div>
          <div>Ações</div>
        </TableHeader>

        {filteredUsers.map(user => (
          <TableRow key={user.id}>
            <UserInfo>
              <UserName>{user.full_name}</UserName>
              <UserEmail>{user.email}</UserEmail>
            </UserInfo>
            
            <div>{user.current_plan_display_name || 'Gratuito'}</div>
            
            <StatusBadge status={user.subscription_status === 'active' ? 'active' : 'inactive'}>
              {user.subscription_status === 'active' ? 'Ativo' : 'Inativo'}
            </StatusBadge>
            
            <div>{user.plants_count}</div>
            
            <div>{formatDate(user.subscription_expires_at)}</div>
            
            <ActionButtons>
              <ActionButton
                onClick={() => handleShowHistory(user.id)}
                title="Ver histórico"
              >
                <FaHistory />
              </ActionButton>

              <ActionButton
                onClick={() => handleChangePlan(user)}
                title="Alterar plano"
              >
                <FaCrown />
              </ActionButton>

              {user.subscription_status === 'active' && (
                <ActionButton
                  className="danger"
                  onClick={() => handleDeactivatePlan(user.id)}
                  title="Desativar plano"
                >
                  <FaBan />
                </ActionButton>
              )}
            </ActionButtons>
          </TableRow>
        ))}
      </UsersTable>

      {/* Plan Change Modal */}
      {showPlanModal && selectedUser && (
        <Modal
          isOpen={showPlanModal}
          onClose={() => {
            setShowPlanModal(false);
            setSelectedUser(null);
            setExpirationDate('');
            setSelectedPlanId('');
          }}
          title={`Alterar Plano - ${selectedUser.full_name}`}
        >
          <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
            <p style={{ margin: 0, color: '#374151', fontSize: '14px' }}>
              Configure o plano e a data de expiração para <strong>{selectedUser.full_name}</strong>:
            </p>

            {/* Plan Selection */}
            <FormGroup>
              <DateLabel>
                Plano de Assinatura:
              </DateLabel>
              {plansLoading ? (
                <div style={{ textAlign: 'center', padding: '20px' }}>
                  <Loading />
                  <p>Carregando planos...</p>
                </div>
              ) : plansError ? (
                <div style={{ textAlign: 'center', padding: '20px', color: '#e53e3e' }}>
                  <p>Erro ao carregar planos. Tente novamente.</p>
                </div>
              ) : plans && plans.length > 0 ? (
                <Select
                  value={selectedPlanId}
                  onChange={(e) => {
                    const newPlanId = e.target.value;
                    setSelectedPlanId(newPlanId);

                    // Clear expiration date if free plan is selected
                    const selectedPlan = plans?.find(p => p.id === newPlanId);
                    if (selectedPlan?.price_cents === 0) {
                      setExpirationDate('');
                    } else if (newPlanId && !expirationDate) {
                      // Set default expiration to 1 year from now for premium plans
                      const defaultExpiration = new Date();
                      defaultExpiration.setFullYear(defaultExpiration.getFullYear() + 1);
                      setExpirationDate(defaultExpiration.toISOString().split('T')[0]);
                    }
                  }}
                >
                  <option value="">Selecione um plano...</option>
                  {plans
                    .filter(plan => plan.is_active !== false) // Only show active plans
                    .reduce((uniquePlans, plan) => {
                      // Group plans by type: Free or Premium
                      if (plan.price_cents === 0) {
                        // Free plan
                        if (!uniquePlans.find(p => p.type === 'free')) {
                          uniquePlans.push({
                            id: plan.id,
                            type: 'free',
                            displayText: 'Gratuito',
                            price_cents: 0
                          });
                        }
                      } else {
                        // Premium plan - show only one Premium option
                        if (!uniquePlans.find(p => p.type === 'premium')) {
                          uniquePlans.push({
                            id: plan.id,
                            type: 'premium',
                            displayText: 'Premium',
                            price_cents: plan.price_cents
                          });
                        }
                      }
                      return uniquePlans;
                    }, [])
                    .map(plan => (
                      <option key={plan.id} value={plan.id}>
                        {plan.displayText}
                      </option>
                    ))}
                </Select>
              ) : (
                <div style={{ textAlign: 'center', padding: '20px' }}>
                  <p>Nenhum plano disponível.</p>
                </div>
              )}
            </FormGroup>

            {/* Date Input - Only for Premium plans */}
            <FormGroup>
              <DateLabel>
                Data de Expiração:
              </DateLabel>
              <DateInput
                type="date"
                value={expirationDate}
                onChange={(e) => setExpirationDate(e.target.value)}
                disabled={(() => {
                  const selectedPlan = plans?.find(p => p.id === selectedPlanId);
                  return selectedPlan?.price_cents === 0; // Disable for free plan
                })()}
              />
              <DateHelperText>
                {(() => {
                  const selectedPlan = plans?.find(p => p.id === selectedPlanId);
                  if (selectedPlan?.price_cents === 0) {
                    return 'Plano gratuito nunca expira';
                  }
                  return 'Defina quando o plano Premium deve expirar';
                })()}
              </DateHelperText>
            </FormGroup>

            {/* Action Buttons */}
            <ButtonGroup>
              <Button
                variant="secondary"
                onClick={() => {
                  setShowPlanModal(false);
                  setSelectedUser(null);
                  setExpirationDate('');
                  setSelectedPlanId('');
                }}
                disabled={changePlanMutation.isLoading}
              >
                Cancelar
              </Button>
              <Button
                variant="primary"
                onClick={handlePlanSubmit}
                disabled={(() => {
                  if (!selectedPlanId || changePlanMutation.isLoading) return true;

                  const selectedPlan = plans?.find(p => p.id === selectedPlanId);
                  const isFreeplan = selectedPlan?.price_cents === 0;

                  // For premium plans, require expiration date
                  if (!isFreeplan && !expirationDate) return true;

                  return false;
                })()}
              >
                {changePlanMutation.isLoading ? 'Salvando...' : 'Salvar Alterações'}
              </Button>
            </ButtonGroup>
          </div>
        </Modal>
      )}

      {/* User History Modal */}
      <UserHistoryModal
        isOpen={showHistoryModal}
        onClose={() => {
          setShowHistoryModal(false);
          setHistoryUserId(null);
        }}
        userId={historyUserId}
      />
    </UserManagementContainer>
  );
};

export default UserManagement;
