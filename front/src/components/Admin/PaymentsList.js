import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FaEye, FaDownload, FaFilter, FaSearch } from 'react-icons/fa';
import api from '../../services/api';
import { toast } from 'react-toastify';

const Container = styled.div`
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const Header = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
`;

const Title = styled.h2`
  color: #2d3748;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
`;

const Filters = styled.div`
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
`;

const SearchInput = styled.input`
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  min-width: 200px;
  
  &:focus {
    outline: none;
    border-color: #48bb78;
    box-shadow: 0 0 0 3px rgba(72, 187, 120, 0.1);
  }
`;

const FilterSelect = styled.select`
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  
  &:focus {
    outline: none;
    border-color: #48bb78;
  }
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-top: 16px;
`;

const Th = styled.th`
  background: #f7fafc;
  padding: 12px;
  text-align: left;
  font-weight: 600;
  color: #4a5568;
  border-bottom: 1px solid #e2e8f0;
  font-size: 14px;
`;

const Td = styled.td`
  padding: 12px;
  border-bottom: 1px solid #e2e8f0;
  font-size: 14px;
  color: #2d3748;
`;

const StatusBadge = styled.span`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  
  ${props => {
    switch (props.status) {
      case 'completed':
        return 'background: #c6f6d5; color: #22543d;';
      case 'pending':
        return 'background: #fef5e7; color: #c05621;';
      case 'failed':
        return 'background: #fed7d7; color: #c53030;';
      case 'refunded':
        return 'background: #e2e8f0; color: #4a5568;';
      default:
        return 'background: #e2e8f0; color: #4a5568;';
    }
  }}
`;

const ActionButton = styled.button`
  background: none;
  border: none;
  color: #48bb78;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background: #f0fff4;
  }
`;

const LoadingMessage = styled.div`
  text-align: center;
  padding: 40px;
  color: #718096;
`;

const EmptyMessage = styled.div`
  text-align: center;
  padding: 40px;
  color: #718096;
`;

const Pagination = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  margin-top: 24px;
`;

const PaginationButton = styled.button`
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  
  &:hover:not(:disabled) {
    background: #f7fafc;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const PaymentsList = () => {
  const [payments, setPayments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(50);

  useEffect(() => {
    fetchPayments();
  }, [currentPage]);

  const fetchPayments = async () => {
    try {
      setLoading(true);
      const skip = (currentPage - 1) * itemsPerPage;
      const response = await api.get(`/api/v1/admin/payments?skip=${skip}&limit=${itemsPerPage}`);
      setPayments(response.data);
    } catch (error) {
      console.error('Error fetching payments:', error);
      toast.error('Erro ao carregar histórico de pagamentos');
    } finally {
      setLoading(false);
    }
  };

  const filteredPayments = payments.filter(payment => {
    const matchesSearch = !searchTerm || 
      payment.user_email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.user_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.external_payment_id?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = !statusFilter || payment.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('pt-BR');
  };

  const getStatusText = (status) => {
    const statusMap = {
      'completed': 'Pago',
      'pending': 'Pendente',
      'failed': 'Falhou',
      'refunded': 'Reembolsado'
    };
    return statusMap[status] || status;
  };

  const getPaymentMethodText = (method) => {
    const methodMap = {
      'credit_card': 'Cartão de Crédito',
      'pix': 'PIX',
      'boleto': 'Boleto'
    };
    return methodMap[method] || method || 'N/A';
  };

  if (loading) {
    return (
      <Container>
        <LoadingMessage>Carregando histórico de pagamentos...</LoadingMessage>
      </Container>
    );
  }

  return (
    <Container>
      <Header>
        <Title>Histórico de Faturas</Title>
        <Filters>
          <SearchInput
            type="text"
            placeholder="Buscar por email, nome ou ID..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <FilterSelect
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="">Todos os status</option>
            <option value="completed">Pago</option>
            <option value="pending">Pendente</option>
            <option value="failed">Falhou</option>
            <option value="refunded">Reembolsado</option>
          </FilterSelect>
        </Filters>
      </Header>

      {filteredPayments.length === 0 ? (
        <EmptyMessage>Nenhum pagamento encontrado</EmptyMessage>
      ) : (
        <>
          <Table>
            <thead>
              <tr>
                <Th>Data</Th>
                <Th>Usuário</Th>
                <Th>Plano</Th>
                <Th>Valor</Th>
                <Th>Status</Th>
                <Th>Método</Th>
                <Th>ID Externo</Th>
                <Th>Ações</Th>
              </tr>
            </thead>
            <tbody>
              {filteredPayments.map((payment) => (
                <tr key={payment.id}>
                  <Td>{formatDate(payment.created_at)}</Td>
                  <Td>
                    <div>
                      <div style={{ fontWeight: '500' }}>{payment.user_name}</div>
                      <div style={{ fontSize: '12px', color: '#718096' }}>{payment.user_email}</div>
                    </div>
                  </Td>
                  <Td>{payment.plan_display_name || 'N/A'}</Td>
                  <Td style={{ fontWeight: '600' }}>{payment.amount_display}</Td>
                  <Td>
                    <StatusBadge status={payment.status}>
                      {getStatusText(payment.status)}
                    </StatusBadge>
                  </Td>
                  <Td>{getPaymentMethodText(payment.payment_method)}</Td>
                  <Td style={{ fontSize: '12px', fontFamily: 'monospace' }}>
                    {payment.external_payment_id || 'N/A'}
                  </Td>
                  <Td>
                    <ActionButton title="Ver detalhes">
                      <FaEye />
                    </ActionButton>
                  </Td>
                </tr>
              ))}
            </tbody>
          </Table>

          <Pagination>
            <PaginationButton
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              Anterior
            </PaginationButton>
            <span>Página {currentPage}</span>
            <PaginationButton
              onClick={() => setCurrentPage(prev => prev + 1)}
              disabled={filteredPayments.length < itemsPerPage}
            >
              Próxima
            </PaginationButton>
          </Pagination>
        </>
      )}
    </Container>
  );
};

export default PaymentsList;
