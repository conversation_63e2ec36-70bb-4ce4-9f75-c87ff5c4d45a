import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import Button from '../UI/Button';

const FormContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.lg};
  max-width: 500px;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.sm};
`;

const Label = styled.label`
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  color: ${props => props.theme.colors.text.primary};
  font-size: ${props => props.theme.typography.fontSize.sm};
`;

const Input = styled.input`
  padding: ${props => props.theme.spacing.sm};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.md};
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const TextArea = styled.textarea`
  padding: ${props => props.theme.spacing.sm};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.md};
  min-height: 80px;
  resize: vertical;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const Select = styled.select`
  padding: ${props => props.theme.spacing.sm};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.md};
  background: white;
  cursor: pointer;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const Checkbox = styled.input`
  width: 18px;
  height: 18px;
  cursor: pointer;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${props => props.theme.spacing.md};

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  justify-content: flex-end;
  margin-top: ${props => props.theme.spacing.lg};
`;

const CouponForm = ({ coupon, onSubmit, onCancel, isLoading }) => {
  const [formData, setFormData] = useState({
    code: '',
    name: '',
    description: '',
    coupon_type: 'percentage',
    discount_value: '',
    max_uses: '',
    max_uses_per_user: '1',
    valid_from: '',
    valid_until: '',
    is_active: true
  });

  useEffect(() => {
    if (coupon) {
      setFormData({
        code: coupon.code || '',
        name: coupon.name || '',
        description: coupon.description || '',
        coupon_type: coupon.coupon_type || 'percentage',
        discount_value: coupon.discount_value?.toString() || '',
        max_uses: coupon.max_uses?.toString() || '',
        max_uses_per_user: coupon.max_uses_per_user?.toString() || '1',
        valid_from: coupon.valid_from ? new Date(coupon.valid_from).toISOString().split('T')[0] : '',
        valid_until: coupon.valid_until ? new Date(coupon.valid_until).toISOString().split('T')[0] : '',
        is_active: coupon.is_active ?? true
      });
    }
  }, [coupon]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Validate form
    if (!formData.code || !formData.name || !formData.discount_value) {
      alert('Por favor, preencha todos os campos obrigatórios.');
      return;
    }

    // Prepare data for submission
    const submitData = {
      ...formData,
      discount_value: parseFloat(formData.discount_value),
      max_uses: formData.max_uses ? parseInt(formData.max_uses) : null,
      max_uses_per_user: parseInt(formData.max_uses_per_user),
      valid_from: formData.valid_from ? new Date(formData.valid_from).toISOString() : null,
      valid_until: formData.valid_until ? new Date(formData.valid_until).toISOString() : null
    };

    onSubmit(submitData);
  };

  return (
    <FormContainer>
      <form onSubmit={handleSubmit}>
        <FormGroup>
          <Label>Código do Cupom *</Label>
          <Input
            type="text"
            name="code"
            value={formData.code}
            onChange={handleChange}
            placeholder="Ex: DESCONTO20"
            required
            disabled={!!coupon} // Don't allow editing code for existing coupons
          />
        </FormGroup>

        <FormGroup>
          <Label>Nome do Cupom *</Label>
          <Input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            placeholder="Ex: Desconto de 20%"
            required
          />
        </FormGroup>

        <FormGroup>
          <Label>Descrição</Label>
          <TextArea
            name="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="Descrição opcional do cupom..."
          />
        </FormGroup>

        <FormRow>
          <FormGroup>
            <Label>Tipo de Desconto *</Label>
            <Select
              name="coupon_type"
              value={formData.coupon_type}
              onChange={handleChange}
              required
            >
              <option value="percentage">Porcentagem (%)</option>
              <option value="fixed">Valor Fixo (R$)</option>
            </Select>
          </FormGroup>

          <FormGroup>
            <Label>Valor do Desconto *</Label>
            <Input
              type="number"
              name="discount_value"
              value={formData.discount_value}
              onChange={handleChange}
              placeholder={formData.coupon_type === 'percentage' ? '20' : '50.00'}
              step={formData.coupon_type === 'percentage' ? '1' : '0.01'}
              min="0"
              max={formData.coupon_type === 'percentage' ? '100' : undefined}
              required
            />
          </FormGroup>
        </FormRow>

        <FormRow>
          <FormGroup>
            <Label>Máximo de Usos</Label>
            <Input
              type="number"
              name="max_uses"
              value={formData.max_uses}
              onChange={handleChange}
              placeholder="Deixe vazio para ilimitado"
              min="1"
            />
          </FormGroup>

          <FormGroup>
            <Label>Usos por Usuário *</Label>
            <Input
              type="number"
              name="max_uses_per_user"
              value={formData.max_uses_per_user}
              onChange={handleChange}
              min="1"
              required
            />
          </FormGroup>
        </FormRow>

        <FormRow>
          <FormGroup>
            <Label>Válido a partir de</Label>
            <Input
              type="date"
              name="valid_from"
              value={formData.valid_from}
              onChange={handleChange}
            />
          </FormGroup>

          <FormGroup>
            <Label>Válido até</Label>
            <Input
              type="date"
              name="valid_until"
              value={formData.valid_until}
              onChange={handleChange}
            />
          </FormGroup>
        </FormRow>

        <FormGroup>
          <CheckboxContainer>
            <Checkbox
              type="checkbox"
              name="is_active"
              checked={formData.is_active}
              onChange={handleChange}
            />
            <Label>Cupom ativo</Label>
          </CheckboxContainer>
        </FormGroup>

        <ButtonGroup>
          <Button
            type="button"
            variant="secondary"
            onClick={onCancel}
            disabled={isLoading}
          >
            Cancelar
          </Button>
          <Button
            type="submit"
            disabled={isLoading}
          >
            {isLoading ? 'Salvando...' : coupon ? 'Atualizar' : 'Criar'} Cupom
          </Button>
        </ButtonGroup>
      </form>
    </FormContainer>
  );
};

export default CouponForm;
