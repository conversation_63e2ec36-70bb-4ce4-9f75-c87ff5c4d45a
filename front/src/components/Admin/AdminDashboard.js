import React from 'react';
import styled from 'styled-components';
import { <PERSON>a<PERSON><PERSON><PERSON>, FaUser<PERSON>heck, FaUserTimes, FaCrown, FaLeaf, FaCamera, FaTicketAlt, FaDollarSign } from 'react-icons/fa';

const DashboardContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.xl};
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${props => props.theme.spacing.lg};
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const StatCard = styled.div`
  background: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.lg};
  box-shadow: ${props => props.theme.shadows.sm};
  border: 1px solid ${props => props.theme.colors.border};
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.md};
  }
`;

const StatHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${props => props.theme.spacing.md};
`;

const StatIcon = styled.div`
  width: 48px;
  height: 48px;
  border-radius: ${props => props.theme.borderRadius.md};
  background: ${props => props.color || props.theme.colors.primary};
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
`;

const StatValue = styled.div`
  font-size: ${props => props.theme.typography.fontSize.xxl};
  font-weight: ${props => props.theme.typography.fontWeight.bold};
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing.xs};
`;

const StatLabel = styled.div`
  font-size: ${props => props.theme.typography.fontSize.sm};
  color: ${props => props.theme.colors.text.secondary};
`;

const SectionTitle = styled.h3`
  color: ${props => props.theme.colors.text.primary};
  font-size: ${props => props.theme.typography.fontSize.lg};
  font-weight: ${props => props.theme.typography.fontWeight.semibold};
  margin-bottom: ${props => props.theme.spacing.md};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const ChartsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: ${props => props.theme.spacing.lg};

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const ChartCard = styled.div`
  background: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.lg};
  box-shadow: ${props => props.theme.shadows.sm};
  border: 1px solid ${props => props.theme.colors.border};
`;

const PlanDistribution = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.md};
`;

const PlanItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${props => props.theme.spacing.sm};
  background: ${props => props.theme.colors.background};
  border-radius: ${props => props.theme.borderRadius.md};
`;

const PlanName = styled.span`
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  color: ${props => props.theme.colors.text.primary};
`;

const PlanCount = styled.span`
  background: ${props => props.theme.colors.primary};
  color: white;
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: ${props => props.theme.typography.fontSize.sm};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
`;

const RecentActivity = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.sm};
  max-height: 300px;
  overflow-y: auto;
`;

const ActivityItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${props => props.theme.spacing.sm};
  background: ${props => props.theme.colors.background};
  border-radius: ${props => props.theme.borderRadius.md};
  border-left: 3px solid ${props => props.theme.colors.primary};
`;

const ActivityInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

const ActivityText = styled.span`
  font-size: ${props => props.theme.typography.fontSize.sm};
  color: ${props => props.theme.colors.text.primary};
`;

const ActivityDate = styled.span`
  font-size: ${props => props.theme.typography.fontSize.xs};
  color: ${props => props.theme.colors.text.secondary};
`;

const AdminDashboard = ({ data }) => {
  if (!data) return null;

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const stats = [
    {
      icon: FaUsers,
      value: data.total_users,
      label: 'Total de Usuários',
      color: '#3182ce'
    },
    {
      icon: FaUserCheck,
      value: data.active_users,
      label: 'Usuários Ativos',
      color: '#38a169'
    },
    {
      icon: FaCrown,
      value: data.active_subscriptions,
      label: 'Assinaturas Ativas',
      color: '#d69e2e'
    },
    {
      icon: FaLeaf,
      value: data.total_plants,
      label: 'Total de Plantas',
      color: '#48bb78'
    },
    {
      icon: FaCamera,
      value: data.total_photos,
      label: 'Total de Fotos',
      color: '#805ad5'
    },
    {
      icon: FaTicketAlt,
      value: data.active_coupons,
      label: 'Cupons Ativos',
      color: '#e53e3e'
    }
  ];

  return (
    <DashboardContainer>
      <StatsGrid>
        {stats.map((stat, index) => {
          const IconComponent = stat.icon;
          return (
            <StatCard key={index}>
              <StatHeader>
                <div>
                  <StatValue>{stat.value.toLocaleString('pt-BR')}</StatValue>
                  <StatLabel>{stat.label}</StatLabel>
                </div>
                <StatIcon color={stat.color}>
                  <IconComponent />
                </StatIcon>
              </StatHeader>
            </StatCard>
          );
        })}
      </StatsGrid>

      <ChartsGrid>
        <ChartCard>
          <SectionTitle>
            <FaCrown />
            Distribuição de Planos
          </SectionTitle>
          <PlanDistribution>
            {data.plan_distribution?.map((plan, index) => (
              <PlanItem key={index}>
                <PlanName>{plan.plan}</PlanName>
                <PlanCount>{plan.count}</PlanCount>
              </PlanItem>
            ))}
          </PlanDistribution>
        </ChartCard>

        <ChartCard>
          <SectionTitle>
            <FaUsers />
            Novos Usuários
          </SectionTitle>
          <RecentActivity>
            {data.recent_signups?.slice(0, 5).map((user, index) => (
              <ActivityItem key={index}>
                <ActivityInfo>
                  <ActivityText>{user.full_name}</ActivityText>
                  <ActivityDate>{user.email}</ActivityDate>
                </ActivityInfo>
                <ActivityDate>{formatDate(user.created_at)}</ActivityDate>
              </ActivityItem>
            ))}
          </RecentActivity>
        </ChartCard>
      </ChartsGrid>
    </DashboardContainer>
  );
};

export default AdminDashboard;
