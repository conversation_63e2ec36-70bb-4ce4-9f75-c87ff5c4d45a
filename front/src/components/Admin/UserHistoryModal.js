import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FaTimes, FaHistory, FaCreditCard, FaCalendarAlt, FaCheckCircle, FaTimesCircle, FaClock } from 'react-icons/fa';
import api from '../../services/api';
import { toast } from 'react-toastify';
import { getBasePlanDisplayName } from '../../utils/planUtils';

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
`;

const ModalContent = styled.div`
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
`;

const ModalHeader = styled.div`
  padding: 24px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  background: white;
  z-index: 10;
`;

const ModalTitle = styled.h2`
  margin: 0;
  color: #2d3748;
  font-size: 24px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: #718096;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background: #f7fafc;
    color: #2d3748;
  }
`;

const ModalBody = styled.div`
  padding: 24px;
`;

const UserInfo = styled.div`
  background: #f7fafc;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 24px;
`;

const UserName = styled.h3`
  margin: 0 0 8px 0;
  color: #2d3748;
  font-size: 18px;
`;

const UserEmail = styled.p`
  margin: 0 0 12px 0;
  color: #718096;
  font-size: 14px;
`;

const CurrentPlan = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  color: #48bb78;
  font-weight: 500;
`;

const Section = styled.div`
  margin-bottom: 32px;
`;

const SectionTitle = styled.h3`
  color: #2d3748;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-top: 8px;
`;

const Th = styled.th`
  background: #f7fafc;
  padding: 12px;
  text-align: left;
  font-weight: 600;
  color: #4a5568;
  border-bottom: 1px solid #e2e8f0;
  font-size: 14px;
`;

const Td = styled.td`
  padding: 12px;
  border-bottom: 1px solid #e2e8f0;
  font-size: 14px;
  color: #2d3748;
`;

const StatusBadge = styled.span`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  
  ${props => {
    switch (props.status) {
      case 'active':
        return 'background: #c6f6d5; color: #22543d;';
      case 'cancelled':
        return 'background: #fed7d7; color: #c53030;';
      case 'expired':
        return 'background: #e2e8f0; color: #4a5568;';
      case 'completed':
        return 'background: #c6f6d5; color: #22543d;';
      case 'pending':
        return 'background: #fef5e7; color: #c05621;';
      case 'failed':
        return 'background: #fed7d7; color: #c53030;';
      default:
        return 'background: #e2e8f0; color: #4a5568;';
    }
  }}
`;

const LoadingMessage = styled.div`
  text-align: center;
  padding: 40px;
  color: #718096;
`;

const EmptyMessage = styled.div`
  text-align: center;
  padding: 20px;
  color: #718096;
  font-style: italic;
`;

const UserHistoryModal = ({ isOpen, onClose, userId }) => {
  const [history, setHistory] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen && userId) {
      fetchUserHistory();
    }
  }, [isOpen, userId]);

  const fetchUserHistory = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/api/v1/admin/users/${userId}/history`);
      setHistory(response.data);
    } catch (error) {
      console.error('Error fetching user history:', error);
      toast.error('Erro ao carregar histórico do usuário');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString('pt-BR');
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active':
      case 'completed':
        return <FaCheckCircle />;
      case 'cancelled':
      case 'failed':
        return <FaTimesCircle />;
      case 'pending':
        return <FaClock />;
      default:
        return null;
    }
  };

  const getStatusText = (status) => {
    const statusMap = {
      'active': 'Ativo',
      'cancelled': 'Cancelado',
      'expired': 'Expirado',
      'completed': 'Pago',
      'pending': 'Pendente',
      'failed': 'Falhou',
      'refunded': 'Reembolsado'
    };
    return statusMap[status] || status;
  };

  const getPaymentMethodText = (method) => {
    const methodMap = {
      'credit_card': 'Cartão de Crédito',
      'pix': 'PIX',
      'boleto': 'Boleto'
    };
    return methodMap[method] || method || 'N/A';
  };

  // Função helper para formatar nome do plano
  const formatPlanName = (planName) => {
    if (!planName) return 'N/A';

    // Remover "Mensal" e "Anual" do nome
    return planName
      .replace(' Mensal', '')
      .replace(' Anual', '')
      .trim();
  };

  if (!isOpen) return null;

  return (
    <ModalOverlay onClick={onClose}>
      <ModalContent onClick={(e) => e.stopPropagation()}>
        <ModalHeader>
          <ModalTitle>
            <FaHistory />
            Histórico do Usuário
          </ModalTitle>
          <CloseButton onClick={onClose}>
            <FaTimes size={20} />
          </CloseButton>
        </ModalHeader>

        <ModalBody>
          {loading ? (
            <LoadingMessage>Carregando histórico...</LoadingMessage>
          ) : history ? (
            <>
              <UserInfo>
                <UserName>{history.user.full_name}</UserName>
                <UserEmail>{history.user.email}</UserEmail>
                <CurrentPlan>
                  <FaCheckCircle />
                  Plano Atual: {history.user.current_plan}
                  {history.user.subscription_expires_at && (
                    <span style={{ color: '#718096', marginLeft: '8px' }}>
                      (Expira em {formatDate(history.user.subscription_expires_at)})
                    </span>
                  )}
                </CurrentPlan>
              </UserInfo>

              <Section>
                <SectionTitle>
                  <FaCalendarAlt />
                  Histórico de Planos
                </SectionTitle>
                {history.subscriptions.length === 0 ? (
                  <EmptyMessage>Nenhuma assinatura encontrada</EmptyMessage>
                ) : (
                  <Table>
                    <thead>
                      <tr>
                        <Th>Plano</Th>
                        <Th>Status</Th>
                        <Th>Início</Th>
                        <Th>Expiração</Th>
                        <Th>Cancelamento</Th>
                      </tr>
                    </thead>
                    <tbody>
                      {history.subscriptions.map((subscription) => (
                        <tr key={subscription.id}>
                          <Td>{formatPlanName(subscription.plan_display_name)}</Td>
                          <Td>
                            <StatusBadge status={subscription.status}>
                              {getStatusIcon(subscription.status)}
                              {getStatusText(subscription.status)}
                            </StatusBadge>
                          </Td>
                          <Td>{formatDate(subscription.started_at)}</Td>
                          <Td>{formatDate(subscription.expires_at)}</Td>
                          <Td>{formatDate(subscription.cancelled_at)}</Td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                )}
              </Section>

              <Section>
                <SectionTitle>
                  <FaCreditCard />
                  Histórico de Pagamentos
                </SectionTitle>
                {history.payments.length === 0 ? (
                  <EmptyMessage>Nenhum pagamento encontrado</EmptyMessage>
                ) : (
                  <Table>
                    <thead>
                      <tr>
                        <Th>Data</Th>
                        <Th>Plano</Th>
                        <Th>Valor</Th>
                        <Th>Status</Th>
                        <Th>Método</Th>
                        <Th>ID Externo</Th>
                      </tr>
                    </thead>
                    <tbody>
                      {history.payments.map((payment) => (
                        <tr key={payment.id}>
                          <Td>{formatDate(payment.created_at)}</Td>
                          <Td>{formatPlanName(payment.subscription_info?.plan_display_name)}</Td>
                          <Td style={{ fontWeight: '600' }}>{payment.amount_display}</Td>
                          <Td>
                            <StatusBadge status={payment.status}>
                              {getStatusIcon(payment.status)}
                              {getStatusText(payment.status)}
                            </StatusBadge>
                          </Td>
                          <Td>{getPaymentMethodText(payment.payment_method)}</Td>
                          <Td style={{ fontSize: '12px', fontFamily: 'monospace' }}>
                            {payment.external_payment_id || 'N/A'}
                          </Td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                )}
              </Section>
            </>
          ) : (
            <EmptyMessage>Erro ao carregar dados do usuário</EmptyMessage>
          )}
        </ModalBody>
      </ModalContent>
    </ModalOverlay>
  );
};

export default UserHistoryModal;
