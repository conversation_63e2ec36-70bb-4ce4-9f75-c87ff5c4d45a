import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FaCom<PERSON>, FaSpinner } from 'react-icons/fa';
import { toast } from 'react-toastify';
import CommentItem from './CommentItem';
import CommentForm from './CommentForm';
import socialInteractionsApi from '../services/socialInteractionsApi';

const CommentsContainer = styled.div`
  background: ${props => props.theme.colors.background.primary};
  border: 1px solid ${props => props.theme.colors.border.light};
  border-radius: ${props => props.theme.borderRadius.lg};
  overflow: hidden;
`;

const CommentsHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  padding: ${props => props.theme.spacing.md};
  background: ${props => props.theme.colors.background.secondary};
  border-bottom: 1px solid ${props => props.theme.colors.border.light};
  
  svg {
    color: ${props => props.theme.colors.primary};
  }
  
  h3 {
    margin: 0;
    font-size: 1rem;
    color: ${props => props.theme.colors.text.primary};
  }
  
  @media (max-width: 768px) {
    padding: ${props => props.theme.spacing.sm};
    
    h3 {
      font-size: 0.9rem;
    }
  }
`;

const CommentsContent = styled.div`
  max-height: 400px;
  overflow-y: auto;
  
  @media (max-width: 768px) {
    max-height: 300px;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: ${props => props.theme.spacing.lg};
  color: ${props => props.theme.colors.text.secondary};
  
  svg {
    margin-right: ${props => props.theme.spacing.sm};
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${props => props.theme.spacing.lg};
  color: ${props => props.theme.colors.text.secondary};
  font-size: 0.85rem;
  
  @media (max-width: 768px) {
    padding: ${props => props.theme.spacing.md};
    font-size: 0.8rem;
  }
`;

const LoadMoreButton = styled.button`
  width: 100%;
  padding: ${props => props.theme.spacing.sm};
  background: ${props => props.theme.colors.background.secondary};
  border: none;
  border-top: 1px solid ${props => props.theme.colors.border.light};
  color: ${props => props.theme.colors.text.secondary};
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover:not(:disabled) {
    background: ${props => props.theme.colors.primary}10;
    color: ${props => props.theme.colors.primary};
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  svg {
    margin-right: ${props => props.theme.spacing.xs};
  }
`;

const CommentList = ({ plantId, plantOwnerId, canComment = true, initialCommentsCount = 0 }) => {
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [total, setTotal] = useState(initialCommentsCount);

  const loadComments = async (pageNum = 1, append = false) => {
    try {
      if (pageNum === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const response = await socialInteractionsApi.getPlantComments(plantId, pageNum, 20);
      
      if (append) {
        setComments(prev => [...prev, ...response.comments]);
      } else {
        setComments(response.comments);
      }
      
      setTotal(response.total);
      setHasMore(response.has_next);
      setPage(pageNum);
      
    } catch (error) {
      console.error('Error loading comments:', error);
      if (pageNum === 1) {
        const errorMessage = error.response?.data?.detail || 'Erro ao carregar comentários';
        toast.error(errorMessage);
      }
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const handleCommentAdded = (newComment) => {
    setComments(prev => [newComment, ...prev]);
    setTotal(prev => prev + 1);
  };

  const handleCommentDeleted = (commentId) => {
    setComments(prev => prev.filter(comment => comment.id !== commentId));
    setTotal(prev => prev - 1);
  };

  const handleLoadMore = () => {
    if (!loadingMore && hasMore) {
      loadComments(page + 1, true);
    }
  };

  useEffect(() => {
    loadComments();
  }, [plantId]);

  return (
    <CommentsContainer>
      <CommentsHeader>
        <FaComment />
        <h3>Comentários ({total})</h3>
      </CommentsHeader>

      {canComment && (
        <CommentForm
          plantId={plantId}
          onCommentAdded={handleCommentAdded}
          canComment={canComment}
        />
      )}

      <CommentsContent>
        {loading ? (
          <LoadingContainer>
            <FaSpinner />
            Carregando comentários...
          </LoadingContainer>
        ) : comments.length === 0 ? (
          <EmptyState>
            {canComment 
              ? 'Seja o primeiro a comentar nesta planta!'
              : 'Nenhum comentário ainda.'
            }
          </EmptyState>
        ) : (
          comments.map((comment) => (
            <CommentItem
              key={comment.id}
              comment={comment}
              plantOwnerId={plantOwnerId}
              onCommentDeleted={handleCommentDeleted}
            />
          ))
        )}
      </CommentsContent>

      {hasMore && (
        <LoadMoreButton 
          onClick={handleLoadMore} 
          disabled={loadingMore}
        >
          {loadingMore ? (
            <>
              <FaSpinner />
              Carregando...
            </>
          ) : (
            'Carregar mais comentários'
          )}
        </LoadMoreButton>
      )}
    </CommentsContainer>
  );
};

export default CommentList;
