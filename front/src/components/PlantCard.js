import React from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { FaLeaf, FaCalendarAlt, FaMapMarkerAlt } from 'react-icons/fa';
import { getImageUrlSync as getImageUrl } from '../services/api';
import { formatDateLocal } from '../utils/dateUtils';

const Card = styled(Link)`
  display: block;
  background: ${props => props.theme.colors.background.primary};
  border: 1px solid ${props => props.theme.colors.border.light};
  border-radius: ${props => props.theme.borderRadius.lg};
  overflow: hidden;
  text-decoration: none;
  color: inherit;
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: ${props => props.theme.colors.primary};
  }
`;

const ImageContainer = styled.div`
  width: 100%;
  height: 200px;
  background: ${props => props.theme.colors.background.secondary};
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
`;

const PlantImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const PlaceholderIcon = styled(FaLeaf)`
  font-size: 3rem;
  color: ${props => props.theme.colors.text.secondary};
`;

const CardContent = styled.div`
  padding: ${props => props.theme.spacing.lg};
`;

const PlantName = styled.h3`
  margin: 0 0 ${props => props.theme.spacing.sm} 0;
  color: ${props => props.theme.colors.text.primary};
  font-size: 1.1rem;
  font-weight: 600;
`;

const PlantType = styled.p`
  margin: 0 0 ${props => props.theme.spacing.sm} 0;
  color: ${props => props.theme.colors.text.secondary};
  font-size: 0.9rem;
  text-transform: capitalize;
`;

const PlantMeta = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.xs};
  font-size: 0.8rem;
  color: ${props => props.theme.colors.text.secondary};
`;

const MetaItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
`;

const PlantCard = ({ plant, isPublic = false }) => {

  // For public profiles, link to a public plant view (to be implemented)
  // For private use, link to the regular plant detail
  const linkTo = isPublic ? `/public/plants/${plant.id}` : `/plants/${plant.id}`;

  return (
    <Card to={linkTo}>
      <ImageContainer>
        {plant.primary_image ? (
          <PlantImage src={plant.primary_image} alt={plant.name} />
        ) : (
          <PlaceholderIcon />
        )}
      </ImageContainer>
      
      <CardContent>
        <PlantName>{plant.name}</PlantName>
        
        {plant.scientific_name && (
          <PlantType>{plant.scientific_name}</PlantType>
        )}
        
        <PlantMeta>
          {plant.plant_type && (
            <MetaItem>
              <FaLeaf size={12} />
              <span>{plant.plant_type_display || plant.plant_type}</span>
            </MetaItem>
          )}
          
          {plant.location && (
            <MetaItem>
              <FaMapMarkerAlt size={12} />
              <span>{plant.location}</span>
            </MetaItem>
          )}
          
          {plant.acquisition_date && (
            <MetaItem>
              <FaCalendarAlt size={12} />
              <span>Desde {formatDateLocal(plant.acquisition_date)}</span>
            </MetaItem>
          )}
        </PlantMeta>
      </CardContent>
    </Card>
  );
};

export default PlantCard;
