import React from 'react';
import styled from 'styled-components';
import { FaCrown, FaTimes, FaLeaf, FaCamera, FaHeart } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import Button from './UI/Button';

const Overlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: ${props => props.theme.spacing.lg};
`;

const Modal = styled.div`
  background: ${props => props.theme.colors.background.primary};
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.xl};
  max-width: 500px;
  width: 100%;
  position: relative;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
`;

const CloseButton = styled.button`
  position: absolute;
  top: ${props => props.theme.spacing.lg};
  right: ${props => props.theme.spacing.lg};
  background: none;
  border: none;
  color: ${props => props.theme.colors.text.secondary};
  cursor: pointer;
  padding: ${props => props.theme.spacing.xs};
  border-radius: ${props => props.theme.borderRadius.sm};
  
  &:hover {
    background: ${props => props.theme.colors.background.secondary};
  }
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const Icon = styled.div`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, ${props => props.theme.colors.primary}, ${props => props.theme.colors.secondary});
  border-radius: 50%;
  margin-bottom: ${props => props.theme.spacing.lg};
  
  svg {
    color: white;
    font-size: 24px;
  }
`;

const Title = styled.h2`
  color: ${props => props.theme.colors.text.primary};
  margin: 0 0 ${props => props.theme.spacing.sm} 0;
  font-size: ${props => props.theme.typography.fontSize.xl};
`;

const Subtitle = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  margin: 0;
  font-size: ${props => props.theme.typography.fontSize.md};
`;

const Content = styled.div`
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const LimitInfo = styled.div`
  background: ${props => props.theme.colors.background.secondary};
  border-radius: ${props => props.theme.borderRadius.md};
  padding: ${props => props.theme.spacing.lg};
  margin-bottom: ${props => props.theme.spacing.lg};
  text-align: center;
`;

const LimitText = styled.div`
  color: ${props => props.theme.colors.text.primary};
  font-weight: 600;
  margin-bottom: ${props => props.theme.spacing.xs};
`;

const LimitSubtext = styled.div`
  color: ${props => props.theme.colors.text.secondary};
  font-size: ${props => props.theme.typography.fontSize.sm};
`;

const Benefits = styled.div`
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const BenefitItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  margin-bottom: ${props => props.theme.spacing.sm};
  color: ${props => props.theme.colors.text.primary};
  
  svg {
    color: ${props => props.theme.colors.success};
    flex-shrink: 0;
  }
`;

const Actions = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  
  @media (max-width: 480px) {
    flex-direction: column;
  }
`;

const UpgradePrompt = ({ 
  isOpen, 
  onClose, 
  limitType, 
  currentUsage, 
  limit,
  planName = 'free'
}) => {
  const navigate = useNavigate();

  if (!isOpen) return null;

  const handleUpgrade = () => {
    navigate('/pricing');
    onClose();
  };

  const getLimitInfo = () => {
    switch (limitType) {
      case 'plants':
        return {
          icon: <FaLeaf />,
          title: 'Limite de Plantas Atingido',
          subtitle: 'Você atingiu o limite máximo de plantas do seu plano',
          current: currentUsage,
          max: limit,
          unit: 'plantas',
          benefits: [
            'Plantas ilimitadas',
            'Mais fotos por planta',
            'Recursos avançados',
            'Suporte prioritário'
          ]
        };
      case 'photos':
        return {
          icon: <FaCamera />,
          title: 'Limite de Fotos Atingido',
          subtitle: 'Esta planta atingiu o limite máximo de fotos',
          current: currentUsage,
          max: limit,
          unit: 'fotos',
          benefits: [
            'Fotos ilimitadas por planta',
            'Galeria expandida',
            'Backup automático',
            'Qualidade premium'
          ]
        };
      case 'care_photos':
        return {
          icon: <FaHeart />,
          title: 'Limite de Fotos por Cuidado',
          subtitle: 'Você atingiu o limite de fotos para este cuidado',
          current: currentUsage,
          max: limit,
          unit: 'fotos por cuidado',
          benefits: [
            'Mais fotos por cuidado',
            'Documentação completa',
            'Histórico detalhado',
            'Melhor acompanhamento'
          ]
        };
      default:
        return {
          icon: <FaCrown />,
          title: 'Limite Atingido',
          subtitle: 'Você atingiu um limite do seu plano atual',
          current: currentUsage,
          max: limit,
          unit: 'itens',
          benefits: [
            'Limites expandidos',
            'Recursos premium',
            'Suporte prioritário',
            'Experiência completa'
          ]
        };
    }
  };

  const limitInfo = getLimitInfo();

  return (
    <Overlay onClick={onClose}>
      <Modal onClick={(e) => e.stopPropagation()}>
        <CloseButton onClick={onClose}>
          <FaTimes />
        </CloseButton>

        <Header>
          <Icon>
            {limitInfo.icon}
          </Icon>
          <Title>{limitInfo.title}</Title>
          <Subtitle>{limitInfo.subtitle}</Subtitle>
        </Header>

        <Content>
          <LimitInfo>
            <LimitText>
              {limitInfo.current}/{limitInfo.max} {limitInfo.unit}
            </LimitText>
            <LimitSubtext>
              Plano {planName === 'free' ? 'Gratuito' : planName}
            </LimitSubtext>
          </LimitInfo>

          <Benefits>
            <div style={{ marginBottom: '16px', fontWeight: '600', color: '#333' }}>
              Com o upgrade você terá:
            </div>
            {limitInfo.benefits.map((benefit, index) => (
              <BenefitItem key={index}>
                <FaCrown size={14} />
                {benefit}
              </BenefitItem>
            ))}
          </Benefits>
        </Content>

        <Actions>
          <Button
            variant="secondary"
            onClick={onClose}
            style={{ flex: 1 }}
          >
            Continuar no Gratuito
          </Button>
          <Button
            variant="primary"
            onClick={handleUpgrade}
            style={{ flex: 1 }}
          >
            Ver Planos
          </Button>
        </Actions>
      </Modal>
    </Overlay>
  );
};

export default UpgradePrompt;
