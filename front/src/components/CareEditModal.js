import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { FaCalendarAlt, FaCheck, FaTimes, FaCamera, FaTrash, FaPlay } from 'react-icons/fa';
import { toast } from 'react-toastify';
import Button from './UI/Button';
import MultiPhotoCapture from './MultiPhotoCapture';
import { getImageUrlSync as getImageUrl } from '../services/api';

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: ${props => props.theme.colors.background};
  padding: ${props => props.theme.spacing.lg};
  border-radius: ${props => props.theme.borderRadius.lg};
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
`;

const ModalTitle = styled.h3`
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing.lg};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const FormGroup = styled.div`
  margin-bottom: ${props => props.theme.spacing.md};
`;

const Label = styled.label`
  display: block;
  margin-bottom: ${props => props.theme.spacing.xs};
  color: ${props => props.theme.colors.text.primary};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
`;

const Select = styled.select`
  width: 100%;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.md};
  background-color: white;
  
  &:focus {
    border-color: ${props => props.theme.colors.primary};
    outline: none;
  }
`;

const Input = styled.input`
  width: 100%;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.md};
  
  &:focus {
    border-color: ${props => props.theme.colors.primary};
    outline: none;
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 80px;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.md};
  resize: vertical;
  
  &:focus {
    border-color: ${props => props.theme.colors.primary};
    outline: none;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
  justify-content: flex-end;
  margin-top: ${props => props.theme.spacing.lg};
`;

const ImageUploadArea = styled.div`
  border: 2px dashed ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  padding: ${props => props.theme.spacing.lg};
  text-align: center;
  cursor: pointer;
  transition: border-color 0.2s ease;

  &:hover {
    border-color: ${props => props.theme.colors.primary};
  }
`;

const HiddenInput = styled.input`
  display: none;
`;

const PreviewContainer = styled.div`
  position: relative;
  display: inline-block;
  margin-top: ${props => props.theme.spacing.sm};
`;

const PreviewImage = styled.img`
  max-width: 200px;
  max-height: 150px;
  object-fit: cover;
  border-radius: ${props => props.theme.borderRadius.md};
`;

const RemoveImageButton = styled.button`
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: none;
  background-color: #dc3545;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;

  &:hover {
    background-color: #c82333;
  }
`;

const ImagesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: ${props => props.theme.spacing.sm};
  margin-top: ${props => props.theme.spacing.sm};
`;

const ImageGridItem = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const GridImage = styled.img`
  width: 120px;
  height: 90px;
  object-fit: cover;
  border-radius: ${props => props.theme.borderRadius.md};
  border: 2px solid ${props => props.isPrimary ? props.theme.colors.primary : props.theme.colors.border};
`;

const GridVideo = styled.video`
  width: 120px;
  height: 90px;
  object-fit: cover;
  border-radius: ${props => props.theme.borderRadius.md};
  border: 2px solid ${props => props.isPrimary ? props.theme.colors.primary : props.theme.colors.border};
`;

const VideoOverlay = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  pointer-events: none;
`;

const ImageLabel = styled.div`
  font-size: 10px;
  color: ${props => props.theme.colors.text.secondary};
  margin-top: 4px;
  text-align: center;
`;

const PrimaryBadge = styled.div`
  position: absolute;
  top: 4px;
  left: 4px;
  background: ${props => props.theme.colors.primary};
  color: white;
  padding: 2px 4px;
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: 8px;
  font-weight: bold;
`;

const CareEditModal = ({ care, plantId, onSuccess, onCancel, allImages = [] }) => {
  const [formData, setFormData] = useState({
    care_types: [], // Changed to array for multiple selection
    description: '',
    date: '',
    notes: ''
  });
  const [loading, setLoading] = useState(false);
  const [newPhotos, setNewPhotos] = useState([]);
  const [existingImages, setExistingImages] = useState([]);
  const [imagesToDelete, setImagesToDelete] = useState([]);

  const careTypes = [
    { value: 'poda', label: 'Poda' },
    { value: 'adubacao', label: 'Adubação' },
    { value: 'transplante', label: 'Transplante' },
    { value: 'aramacao', label: 'Aramação' },
    { value: 'limpeza', label: 'Limpeza' },
    { value: 'tratamento', label: 'Tratamento' },
    { value: 'desfolha', label: 'Desfolha' },
    { value: 'outro', label: 'Outro' }
  ];

  useEffect(() => {
    if (care) {
      // Format date for date input (date-only)
      const date = new Date(care.date);
      const formattedDate = date.toISOString().split('T')[0];

      // Handle both single and multiple care types
      let careTypes = [];
      if (care.care_types_list && care.care_types_list.length > 0) {
        careTypes = care.care_types_list;
      } else if (care.care_type) {
        careTypes = [care.care_type];
      } else {
        careTypes = []; // Start empty if no existing types
      }

      setFormData({
        care_types: careTypes,
        description: care.description || '',
        date: formattedDate,
        notes: care.notes || ''
      });

      // Load existing images and videos from gallery data (with correct R2 URLs)
      const careImagesFromGallery = allImages.filter(img =>
        img.type === 'care' && img.source_id === care.id
      );

      const images = careImagesFromGallery.map(galleryImage => ({
        id: galleryImage.id.replace(/^(care_image_|care_video_)/, ''), // Remove prefix for backend compatibility
        src: galleryImage.image, // Use complete R2 URL from gallery
        isLegacy: false,
        isPrimary: galleryImage.is_primary,
        caption: galleryImage.caption,
        isVideo: galleryImage.is_video || false
      }));

      // Fallback to legacy image if no gallery images found
      if (images.length === 0 && care.image) {
        images.push({
          id: 'legacy',
          src: getImageUrl(care.image),
          isLegacy: true,
          isPrimary: true
        });
      }

      setExistingImages(images);

      // Reset file selection
      setNewPhotos([]);
      setImagesToDelete([]);
    }
  }, [care]);

  const handleNewPhotosChange = (photos) => {
    // Validate total number of images (existing + new)
    const totalImages = existingImages.length + photos.length;
    if (totalImages > 10) {
      toast.error('Máximo de 10 imagens por cuidado');
      return;
    }
    setNewPhotos(photos);
  };

  const handleRemoveExistingImage = (imageId) => {
    setImagesToDelete(prev => [...prev, imageId]);
    setExistingImages(prev => prev.filter(img => img.id !== imageId));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Import plantsService dynamically to avoid circular imports
      const { plantsService } = await import('../services/plantsService');



      // Validate that at least one care type is selected
      if (!formData.care_types || formData.care_types.length === 0) {
        toast.error('Selecione pelo menos um tipo de cuidado');
        setLoading(false);
        return;
      }

      // Always use FormData since backend expects it
      const submitData = new FormData();

      // Send multiple care types
      formData.care_types.forEach(careType => {
        submitData.append('care_types', careType);
      });

      // Date-only format (YYYY-MM-DD) - send as is, backend will handle time
      submitData.append('date', formData.date);
      if (formData.description) submitData.append('description', formData.description);
      if (formData.notes) submitData.append('notes', formData.notes);

      // Add new files
      newPhotos.forEach((photo, index) => {
        submitData.append(`files`, photo.file);
      });

      // Add images to delete
      if (imagesToDelete.length > 0) {
        submitData.append('images_to_delete', JSON.stringify(imagesToDelete));
      }

      const response = await plantsService.updatePlantCareWithFile(plantId, care.id, submitData);

      toast.success('Cuidado atualizado com sucesso!');
      onSuccess();
    } catch (error) {
      console.error('Erro ao atualizar cuidado:', error);
      toast.error('Erro ao atualizar cuidado');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCareTypeToggle = (careType) => {
    setFormData(prev => ({
      ...prev,
      care_types: prev.care_types.includes(careType)
        ? prev.care_types.filter(type => type !== careType)
        : [...prev.care_types, careType]
    }));
  };

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      onCancel();
    }
  };

  return (
    <ModalOverlay onClick={handleOverlayClick}>
      <ModalContent>
        <ModalTitle>
          <FaCalendarAlt />
          Editar Cuidado
        </ModalTitle>

        <form onSubmit={handleSubmit}>
          <FormGroup>
            <Label>Tipos de Cuidado * (selecione um ou mais)</Label>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '8px', marginTop: '8px' }}>
              {careTypes.map(type => (
                <label key={type.value} style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  padding: '8px',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  backgroundColor: formData.care_types.includes(type.value) ? '#e8f5e8' : 'white'
                }}>
                  <input
                    type="checkbox"
                    checked={formData.care_types.includes(type.value)}
                    onChange={() => handleCareTypeToggle(type.value)}
                    style={{ margin: 0 }}
                  />
                  <span>{type.label}</span>
                </label>
              ))}
            </div>
            {formData.care_types.length === 0 && (
              <div style={{ color: '#e74c3c', fontSize: '0.9rem', marginTop: '4px' }}>
                Selecione pelo menos um tipo de cuidado
              </div>
            )}
          </FormGroup>

          <FormGroup>
            <Label htmlFor="date">Data do Cuidado *</Label>
            <Input
              id="date"
              type="date"
              value={formData.date}
              onChange={(e) => handleChange('date', e.target.value)}
              required
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="description">Descrição</Label>
            <TextArea
              id="description"
              placeholder="Descreva o cuidado realizado..."
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="notes">Observações</Label>
            <TextArea
              id="notes"
              placeholder="Observações adicionais, resultados, próximos passos..."
              value={formData.notes}
              onChange={(e) => handleChange('notes', e.target.value)}
            />
          </FormGroup>

          <FormGroup>
            <Label>Fotos e Vídeos do Cuidado ({existingImages.length + newPhotos.length}/10)</Label>

            {/* Grid de imagens existentes */}
            {existingImages.length > 0 && (
              <>
                <Label style={{ fontSize: '14px', marginTop: '16px', marginBottom: '8px' }}>
                  Mídia Existente:
                </Label>
                <ImagesGrid>
                  {existingImages.map((image) => (
                    <ImageGridItem key={image.id}>
                      {image.isVideo ? (
                        <>
                          <GridVideo
                            src={getImageUrl(image.src)}
                            muted
                            preload="metadata"
                            playsInline
                            isPrimary={false}
                          />
                          <VideoOverlay>
                            <FaPlay />
                          </VideoOverlay>
                        </>
                      ) : (
                        <GridImage
                          src={image.src}
                          alt="Imagem do cuidado"
                          isPrimary={false}
                        />
                      )}
                      <RemoveImageButton
                        type="button"
                        onClick={() => handleRemoveExistingImage(image.id)}
                      >
                        <FaTrash />
                      </RemoveImageButton>
                      <ImageLabel>
                        {image.isVideo ? 'Vídeo do cuidado' : 'Foto do cuidado'}
                      </ImageLabel>
                    </ImageGridItem>
                  ))}
                </ImagesGrid>
              </>
            )}

            {/* Seção para adicionar novas fotos */}
            <Label style={{ fontSize: '14px', marginTop: '16px', marginBottom: '8px' }}>
              Adicionar Novas Fotos:
            </Label>
            <MultiPhotoCapture
              photos={newPhotos}
              onPhotosChange={handleNewPhotosChange}
              maxPhotos={10 - existingImages.length}
              disabled={loading}
            />
          </FormGroup>

          <ButtonGroup>
            <Button
              type="button"
              variant="secondary"
              onClick={onCancel}
              disabled={loading}
            >
              <FaTimes />
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={loading}
            >
              <FaCheck />
              {loading ? 'Salvando...' : 'Salvar Alterações'}
            </Button>
          </ButtonGroup>
        </form>
      </ModalContent>
    </ModalOverlay>
  );
};

export default CareEditModal;
