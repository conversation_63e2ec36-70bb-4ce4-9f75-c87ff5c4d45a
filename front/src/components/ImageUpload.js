import React, { useState, useRef } from 'react';
import styled from 'styled-components';
import { FaCamera, FaUpload, FaTimes, FaUndo, FaRedo } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { useQuery } from 'react-query';
import Button from './UI/Button';
import PhotoCapture from './PhotoCapture';
import subscriptionService from '../services/subscriptionService';

const UploadContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.md};
`;



const PreviewContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.md};
  padding: ${props => props.theme.spacing.md};
  border: 2px dashed ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  background-color: ${props => props.theme.colors.background};
`;

const PreviewImage = styled.img`
  max-width: 100%;
  max-height: 200px;
  object-fit: contain;
  border-radius: ${props => props.theme.borderRadius.md};
  transform: rotate(${props => props.rotation || 0}deg);
  transition: transform 0.3s ease;
`;

const RotationControls = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
  margin: ${props => props.theme.spacing.sm} 0;
`;

const RotationButtons = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
`;

const RotationText = styled.p`
  font-size: ${props => props.theme.typography.fontSize.sm};
  color: ${props => props.theme.colors.text.secondary};
  margin: 0;
  text-align: center;
`;

const InfoText = styled.p`
  font-size: ${props => props.theme.typography.fontSize.xs};
  color: ${props => props.theme.colors.text.secondary};
  margin: ${props => props.theme.spacing.sm} 0;
  text-align: center;
  font-style: italic;
`;

const RotationButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  background-color: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text.primary};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${props => props.theme.colors.primary};
    color: white;
    border-color: ${props => props.theme.colors.primary};
  }

  &:active {
    transform: scale(0.95);
  }
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.xs};
`;

const Label = styled.label`
  color: ${props => props.theme.colors.text.primary};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
`;

const Input = styled.input`
  width: 100%;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.md};
  
  &:focus {
    border-color: ${props => props.theme.colors.primary};
    outline: none;
  }
`;

const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const Checkbox = styled.input`
  width: 16px;
  height: 16px;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
  justify-content: flex-end;
`;

const ImageUpload = ({ plantId, onUploadSuccess, onCancel, isFirstPhoto = false }) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [preview, setPreview] = useState(null);
  const [caption, setCaption] = useState('');
  const [isPrimary, setIsPrimary] = useState(isFirstPhoto); // Auto-mark first photo as primary
  const [uploading, setUploading] = useState(false);
  const [rotation, setRotation] = useState(0);
  const [photoDate, setPhotoDate] = useState('');
  const [isVideo, setIsVideo] = useState(false);

  // Buscar informações do plano do usuário
  const { data: userPlan } = useQuery(
    'user-plan',
    () => subscriptionService.getCurrentSubscription().then(res => res.data?.plan),
    {
      staleTime: 5 * 60 * 1000, // 5 minutos
      retry: false
    }
  );

  // Função para detectar orientação baseada nas dimensões da imagem
  const detectOrientationByDimensions = (file) => {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        const width = img.width;
        const height = img.height;
        const isPortrait = height > width;
        const aspectRatio = height / width;

        console.log(`📐 Dimensões da imagem: ${width}x${height}`);
        console.log(`📐 Aspect ratio: ${aspectRatio.toFixed(2)}`);
        console.log(`📐 É portrait: ${isPortrait}`);

        // Lógica específica para detectar se foto precisa de rotação física
        if (isPortrait) {
          // Se a imagem é portrait (altura > largura), mas está sendo exibida errada,
          // pode precisar de rotação física para salvar corretamente
          if (aspectRatio > 1.2) {
            console.log(`📐 ✅ Imagem portrait detectada (${width}x${height}, ratio: ${aspectRatio.toFixed(2)})`);
            console.log(`📐 🔄 Sugerindo rotação 270° (direção oposta) para correção física no salvamento`);
            resolve(270);
          } else {
            console.log(`📐 ⚠️ Imagem quase quadrada (ratio: ${aspectRatio.toFixed(2)}), não rotacionando`);
            resolve(0);
          }
        } else {
          // Se é landscape, pode estar correto ou pode estar rotacionado
          const landscapeRatio = width / height;
          console.log(`📐 Imagem landscape detectada (${width}x${height}, ratio: ${landscapeRatio.toFixed(2)})`);

          if (landscapeRatio > 2.0) {
            console.log(`📐 ⚠️ Imagem muito landscape, pode estar rotacionada incorretamente`);
            // Pode ser uma foto portrait que foi rotacionada para landscape incorretamente
            resolve(90); // Tentar rotação oposta (invertida)
          } else {
            console.log(`📐 ✅ Imagem landscape normal, mantendo sem rotação`);
            resolve(0);
          }
        }

        URL.revokeObjectURL(img.src);
      };

      img.onerror = () => {
        console.error('❌ Erro ao carregar imagem para detectar dimensões');
        resolve(0);
        URL.revokeObjectURL(img.src);
      };

      img.src = URL.createObjectURL(file);
    });
  };

  // Função para detectar orientação da imagem com melhor precisão
  const detectImageOrientation = (file) => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const arrayBuffer = e.target.result;
        const dataView = new DataView(arrayBuffer);

        // Verificar se é JPEG
        if (dataView.getUint16(0, false) !== 0xFFD8) {
          console.log('Não é JPEG, assumindo orientação normal');
          resolve(1);
          return;
        }

        let offset = 2;
        let marker;
        let orientation = 1; // Default

        try {
          // Procurar pelo marcador EXIF
          while (offset < dataView.byteLength - 1) {
            marker = dataView.getUint16(offset, false);

            if (marker === 0xFFE1) { // APP1 marker (EXIF)
              const exifLength = dataView.getUint16(offset + 2, false);
              const exifStart = offset + 4;

              // Verificar se há espaço suficiente
              if (exifStart + 10 > dataView.byteLength) break;

              // Verificar assinatura EXIF
              const exifHeader = dataView.getUint32(exifStart, false);
              if (exifHeader === 0x45786966) { // "Exif"
                const tiffStart = exifStart + 6;

                // Verificar se há espaço para TIFF header
                if (tiffStart + 8 > dataView.byteLength) break;

                const byteOrder = dataView.getUint16(tiffStart, false);
                const littleEndian = byteOrder === 0x4949;

                // Verificar magic number TIFF
                const tiffMagic = dataView.getUint16(tiffStart + 2, littleEndian);
                if (tiffMagic !== 0x002A) break;

                const ifdOffset = dataView.getUint32(tiffStart + 4, littleEndian);
                const ifdStart = tiffStart + ifdOffset;

                // Verificar se IFD está dentro dos limites
                if (ifdStart + 2 > dataView.byteLength) break;

                const entryCount = dataView.getUint16(ifdStart, littleEndian);

                // Verificar se há espaço para todas as entradas
                if (ifdStart + 2 + (entryCount * 12) > dataView.byteLength) break;

                // Procurar pela tag de orientação (0x0112)
                for (let i = 0; i < entryCount; i++) {
                  const entryOffset = ifdStart + 2 + (i * 12);
                  const tag = dataView.getUint16(entryOffset, littleEndian);

                  if (tag === 0x0112) { // Orientation tag
                    orientation = dataView.getUint16(entryOffset + 8, littleEndian);
                    console.log('EXIF Orientation encontrada:', orientation);
                    break;
                  }
                }
              }
              break;
            }

            // Pular para próximo marcador
            if (marker >= 0xFFD0 && marker <= 0xFFD7) {
              offset += 2; // RST markers não têm length
            } else if (marker === 0xFFD8 || marker === 0xFFD9) {
              offset += 2; // SOI e EOI não têm length
            } else {
              const segmentLength = dataView.getUint16(offset + 2, false);
              offset += 2 + segmentLength;
            }
          }
        } catch (error) {
          console.error('Erro ao ler EXIF:', error);
        }

        console.log('Orientação final detectada:', orientation);
        resolve(orientation);
      };

      reader.readAsArrayBuffer(file.slice(0, 128 * 1024)); // Ler apenas os primeiros 128KB
    });
  };

  // Função para rotacionar imagem usando Canvas
  const rotateImage = (file, degrees) => {
    return new Promise((resolve, reject) => {
      console.log(`Rotacionando imagem ${degrees}°`);

      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        try {
          // Normalizar ângulo para valores positivos
          const normalizedDegrees = ((degrees % 360) + 360) % 360;

          // Calcular dimensões baseadas na rotação
          const isRotated90or270 = normalizedDegrees === 90 || normalizedDegrees === 270;
          canvas.width = isRotated90or270 ? img.height : img.width;
          canvas.height = isRotated90or270 ? img.width : img.height;

          // Limpar canvas
          ctx.clearRect(0, 0, canvas.width, canvas.height);

          // Mover para o centro e rotacionar
          ctx.translate(canvas.width / 2, canvas.height / 2);
          ctx.rotate((normalizedDegrees * Math.PI) / 180);
          ctx.drawImage(img, -img.width / 2, -img.height / 2);

          // Converter canvas para blob
          canvas.toBlob((blob) => {
            if (blob) {
              const rotatedFile = new File([blob], file.name, {
                type: file.type,
                lastModified: Date.now(),
              });
              console.log('Imagem rotacionada com sucesso');
              resolve(rotatedFile);
            } else {
              reject(new Error('Falha ao converter canvas para blob'));
            }
          }, file.type, 0.95); // Aumentar qualidade para 0.95
        } catch (error) {
          console.error('Erro ao rotacionar imagem:', error);
          reject(error);
        }
      };

      img.onerror = () => {
        reject(new Error('Falha ao carregar imagem para rotação'));
      };

      img.src = URL.createObjectURL(file);
    });
  };

  const handleRotateLeft = () => {
    const newRotation = (rotation - 90) % 360;
    setRotation(newRotation);
  };

  const handleRotateRight = () => {
    const newRotation = (rotation + 90) % 360;
    setRotation(newRotation);
  };

  const handlePhotoCapture = async (file, isCamera, isVideoFile = false) => {
    setSelectedFile(file);
    setIsVideo(isVideoFile);

    // Auto-fill date/time for camera photos/videos
    if (isCamera) {
      const now = new Date();
      const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000)
        .toISOString()
        .slice(0, 16); // Format: YYYY-MM-DDTHH:MM
      setPhotoDate(localDateTime);
    } else if (!isVideoFile) {
      // For gallery images, try to extract EXIF date
      try {
        const { extractExifDate } = await import('../utils/imageUtils');
        const exifDate = await extractExifDate(file);
        if (exifDate) {
          // Convert to date-only format for the input (YYYY-MM-DD)
          const dateOnly = exifDate.split('T')[0];
          setPhotoDate(dateOnly);
        } else {
          setPhotoDate('');
        }
      } catch (error) {
        console.log('Error extracting EXIF date:', error);
        setPhotoDate('');
      }
    } else {
      setPhotoDate('');
    }

    // Start with no rotation - user can adjust manually (only for images)
    setRotation(0);

    // Create preview
    if (isVideoFile) {
      // For videos, create object URL
      setPreview(URL.createObjectURL(file));
    } else {
      // For images, use FileReader
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handlePhotoRemove = () => {
    // Revoke object URL if it's a video to prevent memory leaks
    if (isVideo && preview) {
      URL.revokeObjectURL(preview);
    }

    setSelectedFile(null);
    setPreview(null);
    setCaption('');
    setIsPrimary(isFirstPhoto); // Reset to default based on whether it's first photo
    setRotation(0);
    setPhotoDate('');
    setIsVideo(false);
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      toast.error(`Por favor, selecione ${isVideo ? 'um vídeo' : 'uma imagem'}`);
      return;
    }

    setUploading(true);
    try {
      // Apply rotation if needed (only for images)
      let fileToUpload = selectedFile;
      if (!isVideo && rotation !== 0) {
        fileToUpload = await rotateImage(selectedFile, rotation);
      }

      // Import plantsService dynamically to avoid circular imports
      const { plantsService } = await import('../services/plantsService');

      if (isVideo) {
        // Upload video to plant gallery
        await plantsService.addPlantVideo(plantId, {
          video: fileToUpload,
          caption,
          video_date: photoDate
        });
        toast.success('Vídeo adicionado com sucesso!');
      } else {
        // Upload image to plant gallery
        await plantsService.addPlantImage(plantId, {
          image: fileToUpload,
          caption,
          is_primary: isPrimary,
          photo_date: photoDate
        });
        toast.success('Foto adicionada com sucesso!');
      }

      onUploadSuccess();

      // Reset form
      handlePhotoRemove();
    } catch (error) {
      console.error('Erro ao fazer upload:', error);
      toast.error(`Erro ao adicionar ${isVideo ? 'vídeo' : 'foto'}`);
    } finally {
      setUploading(false);
    }
  };

  const handleCancel = () => {
    handlePhotoRemove();
    onCancel();
  };

  return (
    <UploadContainer>
      {!selectedFile ? (
        <PhotoCapture
          onPhotoCapture={handlePhotoCapture}
          onPhotoRemove={handlePhotoRemove}
          preview={null}
          disabled={uploading}
          allowVideos={userPlan?.allows_videos || false}
          userPlan={userPlan}
          isVideo={false}
        />
      ) : (
        <PreviewContainer>
          {isVideo ? (
            <video
              src={preview}
              controls
              style={{
                width: '100%',
                maxWidth: '400px',
                height: 'auto',
                borderRadius: '8px',
                border: '2px solid #ddd'
              }}
            />
          ) : (
            <PreviewImage src={preview} alt="Preview" rotation={rotation} />
          )}

          {!isVideo && (
            <RotationControls>
              <RotationText>Ajustar orientação da foto</RotationText>
              <RotationButtons>
                <RotationButton
                  type="button"
                  onClick={handleRotateLeft}
                  title="Rotacionar 90° para esquerda"
                >
                  <FaUndo />
                </RotationButton>
                <RotationButton
                  type="button"
                  onClick={handleRotateRight}
                  title="Rotacionar 90° para direita"
                >
                  <FaRedo />
                </RotationButton>
              </RotationButtons>
              <InfoText>
                💡 Use os botões ↻ ↺ para ajustar a orientação da foto se necessário.
              </InfoText>
            </RotationControls>
          )}

          <FormGroup>
            <Label htmlFor="caption">Legenda (opcional)</Label>
            <Input
              id="caption"
              type="text"
              placeholder={`Adicione uma legenda para ${isVideo ? 'o vídeo' : 'a foto'}`}
              value={caption}
              onChange={(e) => setCaption(e.target.value)}
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="photoDate">Data {isVideo ? 'do vídeo' : 'da foto'} (opcional)</Label>
            <Input
              id="photoDate"
              type="date"
              value={photoDate}
              onChange={(e) => setPhotoDate(e.target.value)}
              title={`Se não preenchida, tentaremos extrair automaticamente dos dados ${isVideo ? 'do vídeo' : 'da foto'}`}
            />
            <InfoText>
              💡 Deixe vazio para extrair automaticamente dos dados {isVideo ? 'do vídeo' : 'EXIF da foto'}
            </InfoText>
          </FormGroup>

          {!isVideo && (
            <CheckboxContainer>
              <Checkbox
                id="isPrimary"
                type="checkbox"
                checked={isPrimary}
                onChange={(e) => setIsPrimary(e.target.checked)}
              />
              <Label htmlFor="isPrimary">Definir como foto principal</Label>
            </CheckboxContainer>
          )}

          <ButtonGroup>
            <Button
              type="button"
              variant="secondary"
              onClick={handleCancel}
              disabled={uploading}
            >
              <FaTimes />
              Cancelar
            </Button>
            <Button
              type="button"
              onClick={handleUpload}
              disabled={uploading}
            >
              <FaUpload />
              {uploading ? 'Enviando...' : `Adicionar ${isVideo ? 'Vídeo' : 'Foto'}`}
            </Button>
          </ButtonGroup>
        </PreviewContainer>
      )}
    </UploadContainer>
  );
};

export default ImageUpload;
