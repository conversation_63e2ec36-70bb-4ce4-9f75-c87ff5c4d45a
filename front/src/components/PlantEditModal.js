import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FaTimes, FaEdit } from 'react-icons/fa';
import Button from './UI/Button';
import plantsService from '../services/plantsService';
import { toast } from 'react-toastify';

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: ${props => props.theme.spacing.lg};
`;

const ModalContent = styled.div`
  background: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.xl};
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-sizing: border-box;

  @media (max-width: 768px) {
    padding: ${props => props.theme.spacing.lg};
    max-height: 95vh;
  }

  @media (max-width: 480px) {
    padding: ${props => props.theme.spacing.md};
    margin: ${props => props.theme.spacing.sm};
    max-height: 98vh;
  }
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const ModalTitle = styled.h3`
  margin: 0;
  color: ${props => props.theme.colors.text.primary};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: ${props => props.theme.colors.text.secondary};
  padding: 4px;
  
  &:hover {
    color: ${props => props.theme.colors.text.primary};
  }
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.md};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.xs};
`;

const Label = styled.label`
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  color: ${props => props.theme.colors.text.primary};
`;

const Input = styled.input`
  padding: ${props => props.theme.spacing.sm};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.body};

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const TextArea = styled.textarea`
  padding: ${props => props.theme.spacing.sm};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.body};
  min-height: 100px;
  resize: vertical;
  font-family: inherit;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const Select = styled.select`
  padding: ${props => props.theme.spacing.sm};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.body};
  background: white;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  justify-content: flex-end;
  margin-top: ${props => props.theme.spacing.lg};

  @media (max-width: 480px) {
    flex-direction: column;
    gap: ${props => props.theme.spacing.sm};
  }
`;



const PlantEditModal = ({ plant, onSuccess, onCancel }) => {
  const [formData, setFormData] = useState({
    name: '',
    plant_type: '',
    scientific_name: '',
    acquisition_date: '',
    location: '',
    style: '',
    estimated_age: '',
    description: ''
  });
  const [loading, setLoading] = useState(false);

  // Map backend plant types to frontend values
  const mapPlantTypeToFrontend = (plant) => {
    // Se tem categoria específica, usa ela
    if (plant.plant_category) {
      return plant.plant_category;
    }

    // Fallback para o tipo geral
    const mapping = {
      'bonsai': 'conifera', // Default to first option
      'suculenta': 'suculenta',
      'outro': 'outro'
    };
    return mapping[plant.plant_type] || 'conifera';
  };

  useEffect(() => {
    if (plant) {
      // Format acquisition_date for date input
      let formattedDate = '';
      if (plant.acquisition_date) {
        const date = new Date(plant.acquisition_date);
        formattedDate = date.toISOString().split('T')[0];
      }

      setFormData({
        name: plant.name || '',
        plant_type: mapPlantTypeToFrontend(plant) || '',
        scientific_name: plant.scientific_name || '',
        acquisition_date: formattedDate,
        location: plant.location || '',
        style: plant.style || '',
        estimated_age: plant.estimated_age || '',
        description: plant.description || ''
      });
    }
  }, [plant]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Map frontend plant types to backend enum values
  const mapPlantTypeToBackend = (frontendType) => {
    const mapping = {
      'conifera': 'bonsai',
      'caducifolia': 'bonsai',
      'perene_folhosa': 'bonsai',
      'frutifera': 'bonsai',
      'florifera': 'bonsai',
      'suculenta': 'suculenta',
      'outro': 'outro'
    };
    return mapping[frontendType] || 'bonsai';
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Transform frontend data to match backend schema
      const plantData = {
        ...formData,
        plant_type: mapPlantTypeToBackend(formData.plant_type),
        plant_category: formData.plant_type, // Enviar a categoria específica
        // Tratar campos vazios adequadamente
        acquisition_date: formData.acquisition_date || null,
        scientific_name: formData.scientific_name || null,
        location: formData.location || null,
        style: formData.style || null,
        estimated_age: formData.estimated_age || null,
        description: formData.description || null
      };

      await plantsService.updatePlant(plant.id, plantData);
      toast.success('Planta atualizada com sucesso!');
      onSuccess();
    } catch (error) {
      console.error('Erro ao atualizar planta:', error);
      toast.error('Erro ao atualizar planta');
    } finally {
      setLoading(false);
    }
  };



  return (
    <ModalOverlay onClick={onCancel}>
      <ModalContent className="modal-content" onClick={(e) => e.stopPropagation()}>
        <ModalHeader>
          <ModalTitle>
            <FaEdit />
            Editar Planta
          </ModalTitle>
          <CloseButton onClick={onCancel}>
            <FaTimes />
          </CloseButton>
        </ModalHeader>



        <Form onSubmit={handleSubmit}>
          <FormGroup>
            <Label htmlFor="name">Nome *</Label>
            <Input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="plant_type">Categoria da Planta *</Label>
            <Select
              id="plant_type"
              name="plant_type"
              value={formData.plant_type}
              onChange={handleChange}
              required
            >
              <option value="">Selecione a categoria</option>
              <option value="conifera">Conífera (Pinheiros, Juníperos)</option>
              <option value="caducifolia">Caducifólia (Perde folhas no outono)</option>
              <option value="perene_folhosa">Perene Folhosa (Ficus, Carmona)</option>
              <option value="frutifera">Frutífera (Jabuticaba, Pitanga, Romã)</option>
              <option value="florifera">Florífera (Azaléia, Cerejeira, Bougainvillea)</option>
              <option value="suculenta">Suculenta (Jade, Portulacaria)</option>
              <option value="outro">Outro</option>
            </Select>
          </FormGroup>

          <FormGroup>
            <Label htmlFor="scientific_name">Nome Científico</Label>
            <Input
              type="text"
              id="scientific_name"
              name="scientific_name"
              value={formData.scientific_name}
              onChange={handleChange}
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="acquisition_date">Data de Aquisição</Label>
            <Input
              type="date"
              id="acquisition_date"
              name="acquisition_date"
              value={formData.acquisition_date}
              onChange={handleChange}
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="location">Localização</Label>
            <Input
              type="text"
              id="location"
              name="location"
              value={formData.location}
              onChange={handleChange}
              placeholder="Ex: Varanda, Jardim, Estufa..."
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="style">Estilo da Planta</Label>
            <Select
              id="style"
              name="style"
              value={formData.style}
              onChange={handleChange}
            >
              <option value="">Selecione o estilo (opcional)</option>
              <option value="Chokkan (Formal Ereto)">Chokkan (Formal Ereto)</option>
              <option value="Moyogi (Informal Ereto)">Moyogi (Informal Ereto)</option>
              <option value="Shakan (Inclinado)">Shakan (Inclinado)</option>
              <option value="Kengai (Cascata)">Kengai (Cascata)</option>
              <option value="Han-Kengai (Semi-cascata)">Han-Kengai (Semi-cascata)</option>
              <option value="Bunjingi (Literati)">Bunjingi (Literati)</option>
              <option value="Hokidachi (Vassoura)">Hokidachi (Vassoura)</option>
              <option value="Kabudachi (Múltiplos troncos)">Kabudachi (Múltiplos troncos)</option>
              <option value="Yose-ue (Floresta)">Yose-ue (Floresta)</option>
              <option value="Ishitsuki (Sobre rocha)">Ishitsuki (Sobre rocha)</option>
              <option value="Neagari (Raízes expostas)">Neagari (Raízes expostas)</option>
              <option value="Outro/Em desenvolvimento">Outro/Em desenvolvimento</option>
            </Select>
          </FormGroup>

          <FormGroup>
            <Label htmlFor="estimated_age">Idade Estimada</Label>
            <Input
              type="text"
              id="estimated_age"
              name="estimated_age"
              value={formData.estimated_age}
              onChange={handleChange}
              placeholder="Ex: 5 anos, 10-15 anos..."
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="description">
              Descrição
              <span style={{ color: '#666', fontWeight: 'normal', fontSize: '12px' }}>
                ({formData.description.length}/2000 caracteres)
              </span>
            </Label>
            <TextArea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Descreva características especiais, história da planta..."
              maxLength={2000}
            />
          </FormGroup>

          <ButtonGroup>
            <Button
              type="button"
              variant="secondary"
              onClick={onCancel}
              disabled={loading}
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={loading}
            >
              {loading ? 'Salvando...' : 'Salvar Alterações'}
            </Button>
          </ButtonGroup>
        </Form>
      </ModalContent>
    </ModalOverlay>
  );
};

export default PlantEditModal;
