import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import userService from '../services/userService';
import subscriptionService from '../services/subscriptionService';
import { getBasePlanDisplayName } from '../utils/planUtils';

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background: white;
  border-radius: 12px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
`;

const Title = styled.h2`
  color: #2d5016;
  margin-bottom: 1rem;
  text-align: center;
`;

const PlanInfo = styled.div`
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  text-align: center;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
`;

const Label = styled.label`
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
`;

const Input = styled.input`
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: #28a745;
  }
`;

const Select = styled.select`
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  background: white;
  
  &:focus {
    outline: none;
    border-color: #28a745;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
`;

const Button = styled.button`
  flex: 1;
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;

  &.primary {
    background: #28a745;
    color: white;
    
    &:hover {
      background: #218838;
    }
    
    &:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }
  }

  &.secondary {
    background: #6c757d;
    color: white;
    
    &:hover {
      background: #5a6268;
    }
  }
`;

const ErrorText = styled.span`
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
`;

const PricingInfo = styled.div`
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
  border-left: 4px solid #28a745;
`;

const PricingRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;

  &:last-child {
    margin-bottom: 0;
    font-weight: 600;
    font-size: 1.1rem;
    border-top: 1px solid #dee2e6;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
  }
`;

const CreditInfo = styled.div`
  background: #e8f5e8;
  border-radius: 6px;
  padding: 0.75rem;
  margin: 0.5rem 0;
  font-size: 0.9rem;
  color: #155724;
`;

const CouponSection = styled.div`
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
`;

const CouponTitle = styled.h4`
  margin: 0 0 0.75rem 0;
  color: #495057;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const CouponInputGroup = styled.div`
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
`;

const CouponInput = styled.input`
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 0.9rem;
  text-transform: uppercase;

  &:focus {
    outline: none;
    border-color: #28a745;
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.25);
  }
`;

const CouponButton = styled.button`
  padding: 0.75rem 1rem;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  white-space: nowrap;

  &:hover {
    background: #218838;
  }

  &:disabled {
    background: #6c757d;
    cursor: not-allowed;
  }
`;

const CouponSuccess = styled.div`
  background: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 6px;
  padding: 0.75rem;
  color: #155724;
  font-size: 0.9rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const CouponError = styled.div`
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 6px;
  padding: 0.75rem;
  color: #721c24;
  font-size: 0.9rem;
`;

const RemoveCouponButton = styled.button`
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  font-size: 0.8rem;
  text-decoration: underline;

  &:hover {
    color: #c82333;
  }
`;

const CheckoutModal = ({ plan, onClose, onSuccess }) => {
  const [formData, setFormData] = useState({
    cpf_cnpj: '',
    document_type: 'CPF'
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [upgradeCost, setUpgradeCost] = useState(null);
  const [loadingUpgradeCost, setLoadingUpgradeCost] = useState(false);
  const [isCheckingPayment, setIsCheckingPayment] = useState(false);
  const [paymentCheckInterval, setPaymentCheckInterval] = useState(null);

  // Estados do cupom
  const [couponCode, setCouponCode] = useState('');
  const [couponData, setCouponData] = useState(null);
  const [couponLoading, setCouponLoading] = useState(false);
  const [couponError, setCouponError] = useState('');

  // Calcular custo de upgrade para o plano específico
  useEffect(() => {
    const calculateUpgradeCost = async () => {
      if (!plan || !plan.id) return;

      setLoadingUpgradeCost(true);
      try {
        const response = await subscriptionService.calculateUpgradeCost(plan);
        setUpgradeCost(response.data);
      } catch (error) {
        console.error('Error calculating upgrade cost:', error);
        setUpgradeCost(null);
      } finally {
        setLoadingUpgradeCost(false);
      }
    };

    calculateUpgradeCost();
  }, [plan]);

  const validateCPF = (cpf) => {
    cpf = cpf.replace(/[^\d]/g, '');
    if (cpf.length !== 11) return false;
    
    // Verificar se todos os dígitos são iguais
    if (/^(\d)\1{10}$/.test(cpf)) return false;
    
    // Validar dígitos verificadores
    let sum = 0;
    for (let i = 0; i < 9; i++) {
      sum += parseInt(cpf.charAt(i)) * (10 - i);
    }
    let remainder = (sum * 10) % 11;
    if (remainder === 10 || remainder === 11) remainder = 0;
    if (remainder !== parseInt(cpf.charAt(9))) return false;
    
    sum = 0;
    for (let i = 0; i < 10; i++) {
      sum += parseInt(cpf.charAt(i)) * (11 - i);
    }
    remainder = (sum * 10) % 11;
    if (remainder === 10 || remainder === 11) remainder = 0;
    if (remainder !== parseInt(cpf.charAt(10))) return false;
    
    return true;
  };

  const validateCNPJ = (cnpj) => {
    cnpj = cnpj.replace(/[^\d]/g, '');
    if (cnpj.length !== 14) return false;
    
    // Verificar se todos os dígitos são iguais
    if (/^(\d)\1{13}$/.test(cnpj)) return false;
    
    // Validar primeiro dígito verificador
    let sum = 0;
    let weight = 2;
    for (let i = 11; i >= 0; i--) {
      sum += parseInt(cnpj.charAt(i)) * weight;
      weight = weight === 9 ? 2 : weight + 1;
    }
    let remainder = sum % 11;
    let digit1 = remainder < 2 ? 0 : 11 - remainder;
    if (digit1 !== parseInt(cnpj.charAt(12))) return false;
    
    // Validar segundo dígito verificador
    sum = 0;
    weight = 2;
    for (let i = 12; i >= 0; i--) {
      sum += parseInt(cnpj.charAt(i)) * weight;
      weight = weight === 9 ? 2 : weight + 1;
    }
    remainder = sum % 11;
    let digit2 = remainder < 2 ? 0 : 11 - remainder;
    if (digit2 !== parseInt(cnpj.charAt(13))) return false;
    
    return true;
  };

  const formatDocument = (value, type) => {
    const numbers = value.replace(/[^\d]/g, '');
    
    if (type === 'CPF') {
      return numbers
        .slice(0, 11)
        .replace(/(\d{3})(\d)/, '$1.$2')
        .replace(/(\d{3})(\d)/, '$1.$2')
        .replace(/(\d{3})(\d{1,2})/, '$1-$2');
    } else {
      return numbers
        .slice(0, 14)
        .replace(/(\d{2})(\d)/, '$1.$2')
        .replace(/(\d{3})(\d)/, '$1.$2')
        .replace(/(\d{3})(\d)/, '$1/$2')
        .replace(/(\d{4})(\d{1,2})/, '$1-$2');
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    
    if (name === 'cpf_cnpj') {
      const formatted = formatDocument(value, formData.document_type);
      setFormData(prev => ({ ...prev, [name]: formatted }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
      
      // Se mudou o tipo de documento, limpar o campo
      if (name === 'document_type') {
        setFormData(prev => ({ ...prev, cpf_cnpj: '' }));
      }
    }
    
    // Limpar erro do campo
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.cpf_cnpj.trim()) {
      newErrors.cpf_cnpj = `${formData.document_type} é obrigatório`;
    } else {
      const cleanDoc = formData.cpf_cnpj.replace(/[^\d]/g, '');
      
      if (formData.document_type === 'CPF') {
        if (!validateCPF(cleanDoc)) {
          newErrors.cpf_cnpj = 'CPF inválido';
        }
      } else {
        if (!validateCNPJ(cleanDoc)) {
          newErrors.cpf_cnpj = 'CNPJ inválido';
        }
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Usar o plano que foi passado como prop (já com ciclo definido)
  const getSelectedPlan = () => {
    return plan;
  };

  // Validar cupom
  const validateCoupon = async () => {
    if (!couponCode.trim()) {
      setCouponError('Digite um código de cupom');
      return;
    }

    setCouponLoading(true);
    setCouponError('');

    try {
      const response = await subscriptionService.validateCoupon(couponCode.trim().toUpperCase(), plan);
      setCouponData(response.data);
      toast.success(`Cupom aplicado! Desconto de ${response.data.discount.discount_percentage}%`);
    } catch (error) {
      console.error('Error validating coupon:', error);
      setCouponError(error.response?.data?.detail || 'Erro ao validar cupom');
      setCouponData(null);
    } finally {
      setCouponLoading(false);
    }
  };

  // Remover cupom
  const removeCoupon = () => {
    setCouponCode('');
    setCouponData(null);
    setCouponError('');
    toast.info('Cupom removido');
  };

  // Função para verificar se o pagamento foi confirmado
  const checkPaymentStatus = async () => {
    try {
      // Verificar se o usuário tem uma assinatura ativa
      const response = await subscriptionService.getCurrentSubscription();
      if (response.data && response.data.subscription && response.data.subscription.status === 'active') {
        // Pagamento confirmado!
        setIsCheckingPayment(false);
        if (paymentCheckInterval) {
          clearInterval(paymentCheckInterval);
          setPaymentCheckInterval(null);
        }
        toast.success('Pagamento confirmado! Plano ativado com sucesso!');
        onSuccess(); // Fechar modal e atualizar página
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error checking payment status:', error);
      return false;
    }
  };

  // Iniciar verificação periódica do pagamento
  const startPaymentCheck = () => {
    setIsCheckingPayment(true);

    // Verificar imediatamente
    checkPaymentStatus();

    // Verificar a cada 5 segundos
    const interval = setInterval(async () => {
      const confirmed = await checkPaymentStatus();
      if (confirmed) {
        clearInterval(interval);
      }
    }, 5000);

    setPaymentCheckInterval(interval);

    // Parar verificação após 10 minutos (120 verificações)
    setTimeout(() => {
      if (interval) {
        clearInterval(interval);
        setIsCheckingPayment(false);
        setPaymentCheckInterval(null);
      }
    }, 600000); // 10 minutos
  };

  // Limpar interval quando componente for desmontado
  useEffect(() => {
    return () => {
      if (paymentCheckInterval) {
        clearInterval(paymentCheckInterval);
      }
    };
  }, [paymentCheckInterval]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    
    try {
      // Primeiro, atualizar o perfil do usuário com CPF/CNPJ
      const cleanDoc = formData.cpf_cnpj.replace(/[^\d]/g, '');
      
      await userService.updateProfileData({
        cpf_cnpj: cleanDoc,
        document_type: formData.document_type
      });
      
      // Depois, criar a assinatura com o plano selecionado
      const selectedPlan = getSelectedPlan();
      const couponToUse = couponData ? couponCode.trim().toUpperCase() : null;
      const response = await subscriptionService.createSubscription(selectedPlan, couponToUse);

      if (response.data && response.data.success) {
        const data = response.data;

        if (data.init_point || data.sandbox_init_point) {
          // Iniciar verificação de pagamento antes de redirecionar
          startPaymentCheck();

          // Redirecionar para o Asaas
          subscriptionService.redirectToPayment(
            data.init_point,
            data.sandbox_init_point,
            true // usar sandbox em desenvolvimento
          );
        } else {
          // Plano gratuito ativado ou sem link de pagamento
          const selectedPlan = getSelectedPlan();
          if (selectedPlan.is_free) {
            toast.success('Plano ativado com sucesso!');
            onSuccess();
          } else {
            toast.error('Link de pagamento não encontrado');
          }
        }
      } else {
        toast.error(response.data?.error || 'Erro ao criar assinatura');
      }
    } catch (error) {
      console.error('Error in checkout:', error);
      toast.error('Erro ao processar checkout');
    } finally {
      setLoading(false);
    }
  };

  return (
    <ModalOverlay onClick={onClose}>
      <ModalContent onClick={(e) => e.stopPropagation()}>
        <Title>Finalizar Assinatura</Title>
        
        <PlanInfo>
          <h3>{plan.display_name}</h3>
          <p>{plan.description}</p>
        </PlanInfo>

        {/* Mostrar informações de preço com desconto */}
        {upgradeCost && upgradeCost.has_active_subscription && upgradeCost.credit_info.credit_amount > 0 && (
          <PricingInfo>
            <h4 style={{ margin: '0 0 0.5rem 0', color: '#28a745' }}>💰 Desconto Aplicado!</h4>
            <CreditInfo>
              {upgradeCost.credit_info.explanation}
            </CreditInfo>
            <PricingRow>
              <span>Preço original:</span>
              <span>R$ {upgradeCost.pricing.original_price.toFixed(2).replace('.', ',')}</span>
            </PricingRow>
            <PricingRow>
              <span>Desconto ({upgradeCost.pricing.discount_percentage}%):</span>
              <span style={{ color: '#28a745' }}>- R$ {upgradeCost.pricing.credit_amount.toFixed(2).replace('.', ',')}</span>
            </PricingRow>
            <PricingRow>
              <span>Valor final:</span>
              <span style={{ color: '#28a745' }}>R$ {upgradeCost.pricing.final_price.toFixed(2).replace('.', ',')}</span>
            </PricingRow>
          </PricingInfo>
        )}

        {loadingUpgradeCost && (
          <PricingInfo>
            <div style={{ textAlign: 'center' }}>Calculando desconto...</div>
          </PricingInfo>
        )}

        {isCheckingPayment && (
          <PricingInfo style={{ backgroundColor: '#e8f5e8', border: '1px solid #28a745' }}>
            <div style={{ textAlign: 'center', color: '#28a745' }}>
              <div style={{ marginBottom: '0.5rem' }}>🔄 Aguardando confirmação do pagamento...</div>
              <div style={{ fontSize: '0.9rem' }}>
                Você pode fechar esta janela. Atualizaremos automaticamente quando o pagamento for confirmado.
              </div>
            </div>
          </PricingInfo>
        )}

        <Form onSubmit={handleSubmit}>
          {/* Mostrar informações do plano selecionado */}
          <PricingInfo>
            <h4 style={{ margin: '0 0 0.5rem 0', color: '#28a745' }}>📋 Plano Selecionado</h4>
            <PricingRow>
              <span>Plano:</span>
              <span>{getBasePlanDisplayName(plan)}</span>
            </PricingRow>
            <PricingRow>
              <span>Ciclo:</span>
              <span>{plan.billing_cycle === 'yearly' ? 'Anual' : 'Mensal'}</span>
            </PricingRow>
            <PricingRow>
              <span>Valor:</span>
              <span style={{ color: '#28a745', fontWeight: 'bold' }}>{plan.price_display}</span>
            </PricingRow>
          </PricingInfo>

          <FormGroup>
            <Label>Tipo de Documento</Label>
            <Select
              name="document_type"
              value={formData.document_type}
              onChange={handleInputChange}
              required
            >
              <option value="CPF">CPF (Pessoa Física)</option>
              <option value="CNPJ">CNPJ (Pessoa Jurídica)</option>
            </Select>
          </FormGroup>

          <FormGroup>
            <Label>{formData.document_type}</Label>
            <Input
              type="text"
              name="cpf_cnpj"
              value={formData.cpf_cnpj}
              onChange={handleInputChange}
              placeholder={formData.document_type === 'CPF' ? '000.000.000-00' : '00.000.000/0000-00'}
              required
            />
            {errors.cpf_cnpj && <ErrorText>{errors.cpf_cnpj}</ErrorText>}
          </FormGroup>

          {/* Seção de Cupom - Apenas para planos anuais */}
          {plan.billing_cycle === 'yearly' && (
            <CouponSection>
            <CouponTitle>
              🎫 Cupom de Desconto
            </CouponTitle>

            {!couponData ? (
              <>
                <CouponInputGroup>
                  <CouponInput
                    type="text"
                    placeholder="Digite o código do cupom"
                    value={couponCode}
                    onChange={(e) => setCouponCode(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && validateCoupon()}
                  />
                  <CouponButton
                    type="button"
                    onClick={validateCoupon}
                    disabled={couponLoading || !couponCode.trim()}
                  >
                    {couponLoading ? 'Validando...' : 'Aplicar'}
                  </CouponButton>
                </CouponInputGroup>

                {couponError && (
                  <CouponError>
                    {couponError}
                  </CouponError>
                )}
              </>
            ) : (
              <CouponSuccess>
                <div>
                  <strong>{couponData.coupon.code}</strong> - {couponData.coupon.name}
                  <br />
                  <small>Desconto: R$ {couponData.discount.discount_amount.toFixed(2).replace('.', ',')} ({couponData.discount.discount_percentage}%)</small>
                </div>
                <RemoveCouponButton onClick={removeCoupon}>
                  Remover
                </RemoveCouponButton>
              </CouponSuccess>
            )}
            </CouponSection>
          )}

          {/* Mostrar preço final com cupom - Apenas para planos anuais */}
          {plan.billing_cycle === 'yearly' && couponData && (
            <PricingInfo>
              <h4 style={{ margin: '0 0 0.5rem 0', color: '#28a745' }}>💰 Desconto do Cupom Aplicado!</h4>
              <PricingRow>
                <span>Preço original:</span>
                <span>R$ {couponData.discount.original_price.toFixed(2).replace('.', ',')}</span>
              </PricingRow>
              <PricingRow>
                <span>Desconto ({couponData.discount.discount_percentage}%):</span>
                <span style={{ color: '#28a745' }}>- R$ {couponData.discount.discount_amount.toFixed(2).replace('.', ',')}</span>
              </PricingRow>
              <PricingRow>
                <span>Valor final:</span>
                <span style={{ color: '#28a745', fontWeight: 'bold' }}>R$ {couponData.discount.final_price.toFixed(2).replace('.', ',')}</span>
              </PricingRow>
            </PricingInfo>
          )}

          <ButtonGroup>
            <Button type="button" className="secondary" onClick={onClose}>
              Cancelar
            </Button>
            <Button type="submit" className="primary" disabled={loading}>
              {loading ? 'Processando...' : 'Continuar para Pagamento'}
            </Button>
          </ButtonGroup>
        </Form>
      </ModalContent>
    </ModalOverlay>
  );
};

export default CheckoutModal;
