import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FaGoogle, FaApple } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { useAuth } from '../contexts/AuthContext';
import socialAuthService from '../services/socialAuthService';

const SocialButtonsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.md};
  margin: ${props => props.theme.spacing.lg} 0;
`;

const SocialButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${props => props.theme.spacing.sm};
  width: 100%;
  padding: ${props => props.theme.spacing.md};
  border: 2px solid ${props => props.borderColor || '#ddd'};
  border-radius: ${props => props.theme.borderRadius.md};
  background: ${props => props.bgColor || 'white'};
  color: ${props => props.textColor || '#333'};
  font-size: ${props => props.theme.typography.fontSize.md};
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: ${props => props.hoverBorderColor || props.borderColor || '#bbb'};
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  svg {
    font-size: 20px;
  }
`;

const GoogleButton = styled(SocialButton)`
  border-color: #db4437;
  color: #db4437;
  
  &:hover {
    background: #db4437;
    color: white;
    border-color: #c23321;
  }
`;

const AppleButton = styled(SocialButton)`
  border-color: #000;
  color: #000;
  
  &:hover {
    background: #000;
    color: white;
    border-color: #333;
  }
`;

const Divider = styled.div`
  display: flex;
  align-items: center;
  margin: ${props => props.theme.spacing.lg} 0;
  
  &::before,
  &::after {
    content: '';
    flex: 1;
    height: 1px;
    background: #ddd;
  }
  
  span {
    padding: 0 ${props => props.theme.spacing.md};
    color: ${props => props.theme.colors.text.secondary};
    font-size: ${props => props.theme.typography.fontSize.sm};
  }
`;

const LoadingSpinner = styled.div`
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const SocialLoginButtons = ({ 
  onSuccess, 
  onError, 
  showDivider = true,
  dividerText = "ou continue com"
}) => {
  const [loading, setLoading] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const [appleLoading, setAppleLoading] = useState(false);
  const [socialConfig, setSocialConfig] = useState({
    google: { enabled: false },
    apple: { enabled: false }
  });
  const { loginWithToken } = useAuth();

  useEffect(() => {
    loadSocialConfig();
  }, []);

  const loadSocialConfig = async () => {
    try {
      const config = await socialAuthService.getSocialAuthConfig();
      setSocialConfig(config);
    } catch (error) {
      console.error('Error loading social config:', error);
    }
  };

  const handleGoogleLogin = async () => {
    if (googleLoading || loading) return;

    setGoogleLoading(true);
    setLoading(true);

    try {
      const result = await socialAuthService.promptGoogleLogin();

      if (result && result.access_token) {
        // Salva o token e dados do usuário
        localStorage.setItem('token', result.access_token);

        // Atualiza o contexto de autenticação passando dados do login social
        await loginWithToken(result.access_token, result.user, result);

        toast.success(
          result.is_new_user
            ? 'Conta criada com sucesso! Bem-vindo ao MeuBonsai!'
            : 'Login realizado com sucesso!'
        );

        if (onSuccess) {
          onSuccess(result);
        }
      } else {
        throw new Error('Resposta inválida do Google');
      }
    } catch (error) {
      console.error('Google login error:', error);

      let errorMessage = 'Erro ao fazer login com Google';

      if (error.message) {
        errorMessage = error.message;
      } else if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      }

      // Não mostrar erro se o usuário cancelou ou timeout
      const isCancelledError = error.message?.includes('popup_closed_by_user') ||
                              error.message?.includes('cancelled') ||
                              error.message?.includes('canceled') ||
                              error.message?.includes('cancelado') ||
                              error.message?.includes('Timeout');

      if (!isCancelledError) {
        toast.error(errorMessage);
      }

      if (onError) {
        onError(error);
      }
    } finally {
      setGoogleLoading(false);
      setLoading(false);
    }
  };

  const handleAppleLogin = async () => {
    if (appleLoading || loading) return;
    
    setAppleLoading(true);
    setLoading(true);
    
    try {
      const result = await socialAuthService.promptAppleLogin();
      
      if (result.access_token) {
        // Salva o token e dados do usuário
        localStorage.setItem('token', result.access_token);
        
        // Atualiza o contexto de autenticação passando dados do login social
        await loginWithToken(result.access_token, result.user, result);
        
        toast.success(
          result.is_new_user 
            ? 'Conta criada com sucesso! Bem-vindo ao MeuBonsai!' 
            : 'Login realizado com sucesso!'
        );
        
        if (onSuccess) {
          onSuccess(result);
        }
      }
    } catch (error) {
      console.error('Apple login error:', error);
      const errorMessage = error.response?.data?.detail || 'Erro ao fazer login com Apple';
      toast.error(errorMessage);
      
      if (onError) {
        onError(error);
      }
    } finally {
      setAppleLoading(false);
      setLoading(false);
    }
  };

  // Se nenhum provedor estiver habilitado, não renderiza nada
  if (!socialConfig.google.enabled && !socialConfig.apple.enabled) {
    return null;
  }

  return (
    <>
      {showDivider && (
        <Divider>
          <span>{dividerText}</span>
        </Divider>
      )}
      
      <SocialButtonsContainer>
        {socialConfig.google.enabled && (
          <GoogleButton
            onClick={handleGoogleLogin}
            disabled={loading}
          >
            {googleLoading ? (
              <LoadingSpinner />
            ) : (
              <FaGoogle />
            )}
            Continuar com Google
          </GoogleButton>
        )}
        
        {socialConfig.apple.enabled && (
          <AppleButton
            onClick={handleAppleLogin}
            disabled={loading}
          >
            {appleLoading ? (
              <LoadingSpinner />
            ) : (
              <FaApple />
            )}
            Continuar com Apple
          </AppleButton>
        )}
      </SocialButtonsContainer>
    </>
  );
};

export default SocialLoginButtons;
