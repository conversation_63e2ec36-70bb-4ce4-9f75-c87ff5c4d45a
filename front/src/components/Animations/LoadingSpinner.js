import React from 'react';
import { motion } from 'framer-motion';
import styled, { keyframes } from 'styled-components';

const spin = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

const pulse = keyframes`
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: ${props => props.fullScreen ? '100vh' : '200px'};
  padding: ${props => props.theme.spacing.xl};
`;

const SpinnerContainer = styled.div`
  position: relative;
  width: ${props => props.size}px;
  height: ${props => props.size}px;
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const SpinnerRing = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-top: 3px solid ${props => props.color};
  border-radius: 50%;
  animation: ${spin} ${props => props.duration}s linear infinite;
  animation-delay: ${props => props.delay}s;
`;

const SpinnerDot = styled(motion.div)`
  position: absolute;
  width: 8px;
  height: 8px;
  background: ${props => props.color};
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
`;

const LoadingText = styled(motion.p)`
  color: ${props => props.theme.colors.text.secondary};
  font-size: ${props => props.theme.typography.fontSize.md};
  text-align: center;
  margin: 0;
  animation: ${pulse} 2s ease-in-out infinite;
`;

const PlantIcon = styled(motion.div)`
  font-size: 2rem;
  margin-bottom: ${props => props.theme.spacing.md};
`;

const LoadingSpinner = ({ 
  size = 60,
  fullScreen = false,
  text = 'Carregando...',
  variant = 'default',
  showPlantIcon = true
}) => {
  const getColors = () => {
    switch (variant) {
      case 'primary':
        return ['#4caf50', '#66bb6a', '#81c784'];
      case 'secondary':
        return ['#8bc34a', '#9ccc65', '#aed581'];
      case 'success':
        return ['#4caf50', '#66bb6a', '#81c784'];
      case 'info':
        return ['#2196f3', '#42a5f5', '#64b5f6'];
      default:
        return ['#4caf50', '#66bb6a', '#81c784'];
    }
  };

  const colors = getColors();

  const plantVariants = {
    initial: { scale: 0.8, rotate: -10 },
    animate: { 
      scale: [0.8, 1.1, 0.9, 1],
      rotate: [-10, 10, -5, 0],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  const dotVariants = {
    initial: { scale: 0 },
    animate: {
      scale: [0, 1.2, 0],
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  return (
    <LoadingContainer fullScreen={fullScreen}>
      {showPlantIcon && (
        <PlantIcon
          variants={plantVariants}
          initial="initial"
          animate="animate"
        >
          🌱
        </PlantIcon>
      )}
      
      <SpinnerContainer size={size}>
        {colors.map((color, index) => (
          <SpinnerRing
            key={index}
            color={color}
            duration={1.5 + index * 0.2}
            delay={index * 0.1}
            style={{
              width: `${100 - index * 15}%`,
              height: `${100 - index * 15}%`,
              top: `${index * 7.5}%`,
              left: `${index * 7.5}%`,
            }}
          />
        ))}
        
        <SpinnerDot
          color={colors[0]}
          variants={dotVariants}
          initial="initial"
          animate="animate"
        />
      </SpinnerContainer>
      
      <LoadingText
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        {text}
      </LoadingText>
    </LoadingContainer>
  );
};

export default LoadingSpinner;
