import React from 'react';
import { motion } from 'framer-motion';
import styled, { keyframes } from 'styled-components';
import ParticleBackground from './ParticleBackground';

const gradientShift = keyframes`
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
`;

const floatingAnimation = keyframes`
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(5px) rotate(-1deg);
  }
`;

const BackgroundContainer = styled(motion.div)`
  position: relative;
  overflow: hidden;
  background: ${props => props.gradient};
  background-size: 400% 400%;
  animation: ${gradientShift} ${props => props.duration || 15}s ease infinite;
`;

const FloatingElement = styled.div`
  position: absolute;
  opacity: ${props => props.opacity || 0.1};
  animation: ${floatingAnimation} ${props => props.duration || 20}s ease-in-out infinite;
  animation-delay: ${props => props.delay || 0}s;
  color: ${props => props.color || 'rgba(76, 175, 80, 0.2)'};
  font-size: ${props => props.size || '2rem'};
  z-index: 0;
`;

const ContentWrapper = styled.div`
  position: relative;
  z-index: 1;
`;

const AnimatedBackground = ({
  children,
  variant = 'green',
  showFloatingElements = true,
  showParticles = true,
  className = '',
  ...props
}) => {
  const gradients = {
    green: 'linear-gradient(135deg, #e8f5e8, #f0f8f0, #e1f5fe, #f1f8e9)',
    blue: 'linear-gradient(135deg, #e3f2fd, #f0f8ff, #e8f5e8, #f3e5f5)',
    purple: 'linear-gradient(135deg, #f3e5f5, #e8eaf6, #e0f2f1, #fce4ec)',
    sunset: 'linear-gradient(135deg, #fff3e0, #fce4ec, #f3e5f5, #e8f5e8)',
    ocean: 'linear-gradient(135deg, #e0f7fa, #e8f5e8, #f1f8e9, #e3f2fd)'
  };

  const floatingElements = [
    { icon: '🌱', top: '10%', left: '10%', delay: 0, size: '1.5rem' },
    { icon: '🍃', top: '20%', right: '15%', delay: 2, size: '2rem' },
    { icon: '🌿', bottom: '30%', left: '20%', delay: 4, size: '1.8rem' },
    { icon: '🌾', top: '60%', right: '25%', delay: 6, size: '1.6rem' },
    { icon: '🌸', bottom: '20%', right: '10%', delay: 8, size: '1.4rem' },
    { icon: '🦋', top: '40%', left: '5%', delay: 10, size: '1.7rem' },
  ];

  return (
    <BackgroundContainer
      className={className}
      gradient={gradients[variant] || gradients.green}
      duration={15}
      {...props}
    >
      {showParticles && <ParticleBackground />}

      {showFloatingElements && floatingElements.map((element, index) => (
        <FloatingElement
          key={index}
          style={{
            top: element.top,
            left: element.left,
            right: element.right,
            bottom: element.bottom,
          }}
          delay={element.delay}
          size={element.size}
          duration={20 + index * 2}
          opacity={0.15}
        >
          {element.icon}
        </FloatingElement>
      ))}

      <ContentWrapper>
        {children}
      </ContentWrapper>
    </BackgroundContainer>
  );
};

export default AnimatedBackground;
