import React from 'react';
import { motion } from 'framer-motion';
import styled from 'styled-components';

const FloatingContainer = styled(motion.div)`
  position: relative;
  cursor: pointer;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(139, 195, 74, 0.1));
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
  }
  
  &:hover::before {
    opacity: 1;
  }
`;

const FloatingCard = ({ 
  children, 
  className = '',
  hoverScale = 1.05,
  hoverY = -8,
  shadowIntensity = 'medium',
  glowEffect = false,
  ...props 
}) => {
  const getShadowVariants = () => {
    const shadows = {
      light: {
        initial: '0 4px 20px rgba(0, 0, 0, 0.08)',
        hover: '0 8px 30px rgba(0, 0, 0, 0.12)'
      },
      medium: {
        initial: '0 8px 25px rgba(0, 0, 0, 0.1)',
        hover: '0 15px 40px rgba(0, 0, 0, 0.15)'
      },
      strong: {
        initial: '0 10px 30px rgba(0, 0, 0, 0.12)',
        hover: '0 20px 50px rgba(0, 0, 0, 0.2)'
      }
    };
    return shadows[shadowIntensity] || shadows.medium;
  };

  const shadowVariants = getShadowVariants();

  const cardVariants = {
    initial: {
      scale: 1,
      y: 0,
      boxShadow: shadowVariants.initial,
      filter: glowEffect ? 'drop-shadow(0 0 0px rgba(76, 175, 80, 0))' : 'none'
    },
    hover: {
      scale: hoverScale,
      y: hoverY,
      boxShadow: shadowVariants.hover,
      filter: glowEffect ? 'drop-shadow(0 0 20px rgba(76, 175, 80, 0.3))' : 'none'
    }
  };

  return (
    <FloatingContainer
      className={className}
      variants={cardVariants}
      initial="initial"
      whileHover="hover"
      transition={{
        type: "spring",
        stiffness: 300,
        damping: 20
      }}
      {...props}
    >
      {children}
    </FloatingContainer>
  );
};

export default FloatingCard;
