import React, { useEffect, useRef } from 'react';
import styled from 'styled-components';

const ParticleContainer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
`;

const Particle = styled.div`
  position: absolute;
  background: ${props => props.color || 'rgba(76, 175, 80, 0.1)'};
  border-radius: 50%;
  pointer-events: none;
  animation: float ${props => props.duration}s linear infinite;
  
  @keyframes float {
    0% {
      transform: translateY(100vh) translateX(${props => props.startX}px) rotate(0deg);
      opacity: 0;
    }
    10% {
      opacity: 1;
    }
    90% {
      opacity: 1;
    }
    100% {
      transform: translateY(-100px) translateX(${props => props.endX}px) rotate(360deg);
      opacity: 0;
    }
  }
`;

const ParticleBackground = ({ 
  particleCount = 15,
  colors = [
    'rgba(76, 175, 80, 0.1)',
    'rgba(139, 195, 74, 0.1)',
    'rgba(165, 214, 167, 0.1)',
    'rgba(200, 230, 201, 0.1)'
  ],
  minSize = 4,
  maxSize = 12,
  minDuration = 15,
  maxDuration = 25
}) => {
  const containerRef = useRef(null);
  const [particles, setParticles] = React.useState([]);

  const createParticle = (id) => {
    const size = Math.random() * (maxSize - minSize) + minSize;
    const startX = Math.random() * window.innerWidth;
    const endX = startX + (Math.random() - 0.5) * 200; // Movimento lateral sutil
    const duration = Math.random() * (maxDuration - minDuration) + minDuration;
    const color = colors[Math.floor(Math.random() * colors.length)];
    const delay = Math.random() * 5; // Delay inicial aleatório

    return {
      id,
      size,
      startX,
      endX,
      duration,
      color,
      delay
    };
  };

  useEffect(() => {
    // Criar partículas iniciais
    const initialParticles = Array.from({ length: particleCount }, (_, i) => 
      createParticle(i)
    );
    setParticles(initialParticles);

    // Recriar partículas periodicamente
    const interval = setInterval(() => {
      setParticles(prevParticles => 
        prevParticles.map(particle => ({
          ...createParticle(particle.id),
          key: Date.now() + particle.id // Force re-render
        }))
      );
    }, 8000); // Renovar a cada 8 segundos

    return () => clearInterval(interval);
  }, [particleCount, colors, minSize, maxSize, minDuration, maxDuration]);

  return (
    <ParticleContainer ref={containerRef}>
      {particles.map((particle) => (
        <Particle
          key={`${particle.id}-${particle.key || 0}`}
          style={{
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            animationDelay: `${particle.delay}s`,
            left: 0,
            bottom: 0
          }}
          color={particle.color}
          duration={particle.duration}
          startX={particle.startX}
          endX={particle.endX}
        />
      ))}
    </ParticleContainer>
  );
};

export default ParticleBackground;
