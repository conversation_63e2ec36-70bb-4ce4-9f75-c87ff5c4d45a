import React from 'react';
import { motion } from 'framer-motion';
import styled from 'styled-components';

const ButtonContainer = styled(motion.div)`
  position: relative;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.xl};
  font-family: ${props => props.theme.typography.fontFamily};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  font-size: ${props => props.theme.typography.fontSize.md};
  cursor: pointer;
  outline: none;
  transition: all 0.3s ease;
  text-decoration: none;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  &:hover::before {
    left: 100%;
  }
`;

const RippleEffect = styled(motion.span)`
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  transform: scale(0);
  pointer-events: none;
`;

const AnimatedButton = ({ 
  children, 
  variant = 'primary',
  size = 'medium',
  rippleEffect = true,
  glowEffect = false,
  className = '',
  onClick,
  ...props 
}) => {
  const [ripples, setRipples] = React.useState([]);

  const getVariantStyles = () => {
    const variants = {
      primary: {
        background: 'linear-gradient(135deg, #4caf50, #66bb6a)',
        color: 'white',
        boxShadow: '0 4px 15px rgba(76, 175, 80, 0.3)',
        hoverShadow: '0 6px 20px rgba(76, 175, 80, 0.4)',
        glow: '0 0 20px rgba(76, 175, 80, 0.5)'
      },
      secondary: {
        background: 'linear-gradient(135deg, #8bc34a, #9ccc65)',
        color: 'white',
        boxShadow: '0 4px 15px rgba(139, 195, 74, 0.3)',
        hoverShadow: '0 6px 20px rgba(139, 195, 74, 0.4)',
        glow: '0 0 20px rgba(139, 195, 74, 0.5)'
      },
      outline: {
        background: 'transparent',
        color: '#4caf50',
        border: '2px solid #4caf50',
        boxShadow: '0 2px 10px rgba(76, 175, 80, 0.1)',
        hoverShadow: '0 4px 15px rgba(76, 175, 80, 0.2)',
        glow: '0 0 15px rgba(76, 175, 80, 0.3)'
      },
      ghost: {
        background: 'rgba(76, 175, 80, 0.1)',
        color: '#4caf50',
        border: '1px solid rgba(76, 175, 80, 0.3)',
        boxShadow: 'none',
        hoverShadow: '0 4px 15px rgba(76, 175, 80, 0.2)',
        glow: '0 0 15px rgba(76, 175, 80, 0.3)'
      }
    };
    return variants[variant] || variants.primary;
  };

  const getSizeStyles = () => {
    const sizes = {
      small: {
        padding: '8px 16px',
        fontSize: '0.875rem'
      },
      medium: {
        padding: '12px 24px',
        fontSize: '1rem'
      },
      large: {
        padding: '16px 32px',
        fontSize: '1.125rem'
      }
    };
    return sizes[size] || sizes.medium;
  };

  const variantStyles = getVariantStyles();
  const sizeStyles = getSizeStyles();

  const buttonVariants = {
    initial: {
      scale: 1,
      boxShadow: variantStyles.boxShadow,
      filter: glowEffect ? `drop-shadow(${variantStyles.glow})` : 'none'
    },
    hover: {
      scale: 1.05,
      boxShadow: variantStyles.hoverShadow,
      filter: glowEffect ? `drop-shadow(${variantStyles.glow})` : 'none'
    },
    tap: {
      scale: 0.95
    }
  };

  const handleClick = (e) => {
    if (rippleEffect) {
      const rect = e.currentTarget.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      const x = e.clientX - rect.left - size / 2;
      const y = e.clientY - rect.top - size / 2;
      
      const newRipple = {
        x,
        y,
        size,
        id: Date.now()
      };
      
      setRipples(prev => [...prev, newRipple]);
      
      setTimeout(() => {
        setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id));
      }, 600);
    }
    
    if (onClick) {
      onClick(e);
    }
  };

  return (
    <ButtonContainer
      className={className}
      variants={buttonVariants}
      initial="initial"
      whileHover="hover"
      whileTap="tap"
      onClick={handleClick}
      style={{
        background: variantStyles.background,
        color: variantStyles.color,
        border: variantStyles.border || 'none',
        ...sizeStyles
      }}
      {...props}
    >
      {children}
      
      {ripples.map(ripple => (
        <RippleEffect
          key={ripple.id}
          initial={{ scale: 0, opacity: 1 }}
          animate={{ scale: 4, opacity: 0 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          style={{
            left: ripple.x,
            top: ripple.y,
            width: ripple.size,
            height: ripple.size
          }}
        />
      ))}
    </ButtonContainer>
  );
};

export default AnimatedButton;
