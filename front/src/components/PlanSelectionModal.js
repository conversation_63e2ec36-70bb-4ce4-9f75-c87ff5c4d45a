import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FaTimes, FaCheck, FaCrown } from 'react-icons/fa';
import Button from './UI/Button';
import { getBasePlanDisplayName } from '../utils/planUtils';
import subscriptionService from '../services/subscriptionService';
import { toast } from 'react-toastify';

const Overlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
`;

const Modal = styled.div`
  background: white;
  border-radius: 16px;
  padding: 2rem;
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
`;

const CloseButton = styled.button`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #666;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  
  &:hover {
    background: #f5f5f5;
    color: #333;
  }
`;

const Title = styled.h2`
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.5rem;
  text-align: center;
`;

const PlanOption = styled.div`
  border: 2px solid ${props => props.selected ? '#4CAF50' : '#e0e0e0'};
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  
  &:hover {
    border-color: #4CAF50;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);
  }
  
  ${props => props.selected && `
    background: linear-gradient(135deg, #f8fff8 0%, #e8f5e8 100%);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
  `}
`;

const PlanHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
`;

const PlanName = styled.h3`
  margin: 0;
  color: #2c3e50;
  font-size: 1.2rem;
`;

const PlanPrice = styled.div`
  font-size: 1.5rem;
  font-weight: 700;
  color: #4CAF50;
`;

const PlanDescription = styled.div`
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 1rem;
`;

const SavingsBadge = styled.div`
  position: absolute;
  top: -8px;
  right: 1rem;
  background: #FF6B35;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
`;

const CheckIcon = styled.div`
  position: absolute;
  top: 1rem;
  right: 1rem;
  color: #4CAF50;
  font-size: 1.2rem;
`;

const ButtonContainer = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
`;

const PlanSelectionModal = ({ plan, onClose, onSelectPlan }) => {
  const [selectedOption, setSelectedOption] = useState('annual'); // Default anual
  const [loading, setLoading] = useState(false);

  // Calcular opções de plano baseado no plano base
  const getPlanOptions = () => {
    if (!plan || plan.is_free) return null;

    // Extrair valor de price_display (ex: "R$ 23,90" -> 23.90)
    let basePrice;
    if (plan.price_display && plan.price_display !== 'Gratuito') {
      const priceMatch = plan.price_display.match(/[\d,]+/);
      if (priceMatch) {
        basePrice = parseFloat(priceMatch[0].replace(',', '.'));
      } else {
        return null;
      }
    } else {
      return null;
    }

    const yearlyPrice = 180.00; // Valor fixo anual
    const savings = basePrice * 12 - yearlyPrice; // Economia anual real

    return {
      plan_name: plan.display_name,
      monthly: {
        id: plan.id,
        name: plan.name,
        display_name: plan.display_name,
        price_display: `R$ ${basePrice.toFixed(2).replace('.', ',')}`,
        description: `R$ ${basePrice.toFixed(2).replace('.', ',')} por mês`,
        billing_cycle: 'monthly',
        price_cents: Math.round(basePrice * 100),
        is_free: false
      },
      annual: {
        id: plan.id,
        name: plan.name,
        display_name: plan.display_name,
        price_display: `R$ ${yearlyPrice.toFixed(2).replace('.', ',')}`,
        description: `R$ ${yearlyPrice.toFixed(2).replace('.', ',')} por ano`,
        billing_cycle: 'yearly',
        price_cents: Math.round(yearlyPrice * 100),
        is_free: false
      },
      savings: {
        percentage: 17,
        description: `Economize R$ ${savings.toFixed(2).replace('.', ',')} por ano`
      }
    };
  };

  const handleSelectPlan = () => {
    const planOptions = getPlanOptions();
    if (!planOptions) return;

    const selectedPlan = selectedOption === 'monthly'
      ? planOptions.monthly
      : planOptions.annual;

    onSelectPlan(selectedPlan);
  };

  const planOptions = getPlanOptions();

  if (loading) {
    return (
      <Overlay>
        <Modal>
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            Carregando opções...
          </div>
        </Modal>
      </Overlay>
    );
  }

  if (!planOptions) {
    return (
      <Overlay>
        <Modal>
          <CloseButton onClick={onClose}>
            <FaTimes />
          </CloseButton>
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            Erro ao carregar plano
          </div>
        </Modal>
      </Overlay>
    );
  }

  return (
    <Overlay>
      <Modal>
        <CloseButton onClick={onClose}>
          <FaTimes />
        </CloseButton>
        
        <Title>
          <FaCrown style={{ marginRight: '0.5rem', color: '#FFD700' }} />
          Escolha seu Plano {getBasePlanDisplayName({ display_name: planOptions.plan_name })}
        </Title>

        <PlanOption 
          selected={selectedOption === 'annual'}
          onClick={() => setSelectedOption('annual')}
        >
          {planOptions.savings.percentage > 0 && (
            <SavingsBadge>
              Economize {planOptions.savings.percentage}%
            </SavingsBadge>
          )}
          {selectedOption === 'annual' && (
            <CheckIcon>
              <FaCheck />
            </CheckIcon>
          )}
          <PlanHeader>
            <PlanName>Plano Anual</PlanName>
            <PlanPrice>{planOptions.annual.price_display}</PlanPrice>
          </PlanHeader>
          <PlanDescription>
            {planOptions.annual.description}
          </PlanDescription>
          <div style={{ fontSize: '0.8rem', color: '#4CAF50', fontWeight: '600' }}>
            {planOptions.savings.description}
          </div>
        </PlanOption>

        <PlanOption 
          selected={selectedOption === 'monthly'}
          onClick={() => setSelectedOption('monthly')}
        >
          {selectedOption === 'monthly' && (
            <CheckIcon>
              <FaCheck />
            </CheckIcon>
          )}
          <PlanHeader>
            <PlanName>Plano Mensal</PlanName>
            <PlanPrice>{planOptions.monthly.price_display}</PlanPrice>
          </PlanHeader>
          <PlanDescription>
            {planOptions.monthly.description}
          </PlanDescription>
        </PlanOption>

        <ButtonContainer>
          <Button 
            variant="secondary" 
            onClick={onClose}
            style={{ flex: 1 }}
          >
            Cancelar
          </Button>
          <Button 
            variant="primary" 
            onClick={handleSelectPlan}
            style={{ flex: 2 }}
          >
            Continuar com {selectedOption === 'monthly' ? 'Mensal' : 'Anual'}
          </Button>
        </ButtonContainer>
      </Modal>
    </Overlay>
  );
};

export default PlanSelectionModal;
