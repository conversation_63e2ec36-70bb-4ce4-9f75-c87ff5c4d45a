import styled from 'styled-components';

const Button = styled.button`
  background-color: ${props => {
    if (props.variant === 'secondary') return props.theme.colors.secondary;
    if (props.variant === 'outline') return 'transparent';
    if (props.variant === 'danger') return props.theme.colors.error;
    return props.theme.colors.primary;
  }};
  
  color: ${props => {
    if (props.variant === 'outline') return props.theme.colors.primary;
    return 'white';
  }};
  
  border: ${props => {
    if (props.variant === 'outline') return `2px solid ${props.theme.colors.primary}`;
    return 'none';
  }};
  
  padding: ${props => {
    if (props.size === 'small') return `${props.theme.spacing.xs} ${props.theme.spacing.md}`;
    if (props.size === 'large') return `${props.theme.spacing.md} ${props.theme.spacing.xl}`;
    return `${props.theme.spacing.sm} ${props.theme.spacing.lg}`;
  }};
  
  font-size: ${props => {
    if (props.size === 'small') return props.theme.typography.fontSize.sm;
    if (props.size === 'large') return props.theme.typography.fontSize.lg;
    return props.theme.typography.fontSize.md;
  }};
  
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  border-radius: ${props => props.theme.borderRadius.md};
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: ${props => props.theme.spacing.xs};
  text-decoration: none;
  
  width: ${props => props.fullWidth ? '100%' : 'auto'};
  max-width: 100%;
  box-sizing: border-box;
  word-wrap: break-word;

  @media (max-width: 480px) {
    font-size: ${props => {
      if (props.size === 'small') return props.theme.typography.fontSize.xs;
      if (props.size === 'large') return props.theme.typography.fontSize.md;
      return props.theme.typography.fontSize.sm;
    }};
    padding: ${props => {
      if (props.size === 'small') return `${props.theme.spacing.xs} ${props.theme.spacing.sm}`;
      if (props.size === 'large') return `${props.theme.spacing.sm} ${props.theme.spacing.lg}`;
      return `${props.theme.spacing.xs} ${props.theme.spacing.md}`;
    }};
  }

  /* Full width in modals on mobile */
  .modal-content & {
    @media (max-width: 768px) {
      width: 100%;
    }
  }

  &:hover:not(:disabled) {
    background-color: ${props => {
      if (props.variant === 'secondary') return props.theme.colors.primaryLight;
      if (props.variant === 'outline') return props.theme.colors.primary;
      if (props.variant === 'danger') return '#d32f2f';
      return props.theme.colors.primaryDark;
    }};
    
    color: ${props => {
      if (props.variant === 'outline') return 'white';
      return 'white';
    }};
    
    transform: translateY(-1px);
    box-shadow: ${props => props.theme.shadows.md};
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: ${props => props.theme.shadows.sm};
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`;

export default Button;
