import React from 'react';
import { LoadingSpinner as AnimatedLoadingSpinner } from '../Animations';

const LoadingSpinner = ({
  size = 40,
  text,
  showText = false,
  variant = 'primary',
  showPlantIcon = false
}) => {
  return (
    <AnimatedLoadingSpinner
      size={size}
      text={showText || text ? (text || 'Carregando...') : ''}
      variant={variant}
      showPlantIcon={showPlantIcon}
      fullScreen={false}
    />
  );
};

export default LoadingSpinner;
