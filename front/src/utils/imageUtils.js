// Image utilities for handling image operations

export const getImageDimensions = (file) => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      });
    };
    img.onerror = () => {
      resolve({ width: 0, height: 0 });
    };
    img.src = URL.createObjectURL(file);
  });
};

export const createImagePreview = (file) => {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target.result);
    reader.onerror = () => resolve(null);
    reader.readAsDataURL(file);
  });
};

/**
 * Compress image to reduce file size while maintaining quality
 * @param {File} file - Original image file
 * @param {Object} options - Compression options
 * @returns {Promise<File>} - Compressed image file
 */
export const compressImage = (file, options = {}) => {
  return new Promise((resolve, reject) => {
    const {
      maxWidth = 1920,
      maxHeight = 1080,
      quality = 0.8,
      maxSizeMB = 3,
      outputFormat = 'image/jpeg'
    } = options;

    // If file is already small enough, return as is
    if (file.size <= maxSizeMB * 1024 * 1024) {
      resolve(file);
      return;
    }

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      try {
        // Calculate new dimensions maintaining aspect ratio
        let { width, height } = img;
        
        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width *= ratio;
          height *= ratio;
        }

        // Set canvas dimensions
        canvas.width = width;
        canvas.height = height;

        // Draw and compress image
        ctx.drawImage(img, 0, 0, width, height);

        // Convert to blob with compression
        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('Failed to compress image'));
              return;
            }

            // Create new file from compressed blob
            const compressedFile = new File(
              [blob],
              file.name,
              {
                type: outputFormat,
                lastModified: Date.now()
              }
            );

            // If still too large, try with lower quality
            if (compressedFile.size > maxSizeMB * 1024 * 1024 && quality > 0.3) {
              const lowerQualityOptions = {
                ...options,
                quality: Math.max(0.3, quality - 0.2)
              };
              compressImage(file, lowerQualityOptions).then(resolve).catch(reject);
            } else {
              resolve(compressedFile);
            }
          },
          outputFormat,
          quality
        );
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error('Failed to load image for compression'));
    };

    img.src = URL.createObjectURL(file);
  });
};

/**
 * Validate and compress image if needed
 * @param {File} file - Image file to process
 * @returns {Promise<File>} - Processed image file
 */
export const processImageForUpload = async (file) => {
  try {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      throw new Error('File must be an image');
    }

    // Compress image if needed
    const compressedFile = await compressImage(file, {
      maxWidth: 1920,
      maxHeight: 1080,
      quality: 0.85,
      maxSizeMB: 3,
      outputFormat: 'image/jpeg'
    });

    return compressedFile;
  } catch (error) {
    console.error('Error processing image:', error);
    throw error;
  }
};

/**
 * Format file size for display
 * @param {number} bytes - File size in bytes
 * @returns {string} - Formatted file size
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Extract EXIF date from image file
 * @param {File} file - Image file
 * @returns {Promise<string|null>} ISO date string or null if not found
 */
export const extractExifDate = (file) => {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const arrayBuffer = e.target.result;
        const dataView = new DataView(arrayBuffer);

        // Check if it's JPEG
        if (dataView.getUint16(0, false) !== 0xFFD8) {
          resolve(null);
          return;
        }

        let offset = 2;
        let marker;

        // Look for EXIF marker
        while (offset < dataView.byteLength - 1) {
          marker = dataView.getUint16(offset, false);

          if (marker === 0xFFE1) { // APP1 marker (EXIF)
            const exifStart = offset + 4;

            // Check if there's enough space
            if (exifStart + 10 > dataView.byteLength) break;

            // Check EXIF signature
            const exifHeader = dataView.getUint32(exifStart, false);
            if (exifHeader === 0x45786966) { // "Exif"
              const tiffStart = exifStart + 6;

              // Check if there's space for TIFF header
              if (tiffStart + 8 > dataView.byteLength) break;

              const byteOrder = dataView.getUint16(tiffStart, false);
              const littleEndian = byteOrder === 0x4949;

              // Check TIFF magic number
              const tiffMagic = dataView.getUint16(tiffStart + 2, littleEndian);
              if (tiffMagic !== 0x002A) break;

              const ifdOffset = dataView.getUint32(tiffStart + 4, littleEndian);
              const ifdStart = tiffStart + ifdOffset;

              // Check if IFD is within bounds
              if (ifdStart + 2 > dataView.byteLength) break;

              const entryCount = dataView.getUint16(ifdStart, littleEndian);

              // Check if there's space for all entries
              if (ifdStart + 2 + (entryCount * 12) > dataView.byteLength) break;

              // Look for date tags (DateTimeOriginal: 0x9003, DateTime: 0x0132, DateTimeDigitized: 0x9004)
              const dateTags = [0x9003, 0x0132, 0x9004];

              for (let i = 0; i < entryCount; i++) {
                const entryOffset = ifdStart + 2 + (i * 12);
                const tag = dataView.getUint16(entryOffset, littleEndian);

                if (dateTags.includes(tag)) {
                  const dataType = dataView.getUint16(entryOffset + 2, littleEndian);
                  const dataCount = dataView.getUint32(entryOffset + 4, littleEndian);

                  if (dataType === 2 && dataCount > 0) { // ASCII string
                    let dateString = '';
                    const dataOffset = dataCount <= 4 ? entryOffset + 8 : dataView.getUint32(entryOffset + 8, littleEndian) + tiffStart;

                    // Check bounds
                    if (dataOffset + dataCount > dataView.byteLength) continue;

                    // Read the date string
                    for (let j = 0; j < Math.min(dataCount - 1, 19); j++) { // -1 to exclude null terminator, max 19 chars for date
                      const char = dataView.getUint8(dataOffset + j);
                      if (char === 0) break; // null terminator
                      dateString += String.fromCharCode(char);
                    }

                    // Parse EXIF date format: "YYYY:MM:DD HH:MM:SS"
                    if (dateString.length >= 19) {
                      try {
                        const datePart = dateString.substring(0, 10).replace(/:/g, '-');
                        const timePart = dateString.substring(11, 19);
                        const isoString = `${datePart}T${timePart}`;

                        // Validate the date
                        const testDate = new Date(isoString);
                        if (!isNaN(testDate.getTime())) {
                          resolve(isoString);
                          return;
                        }
                      } catch (error) {
                        console.log('Error parsing EXIF date:', error);
                      }
                    }
                  }
                }
              }
            }
            break;
          }

          // Skip to next marker
          if (marker >= 0xFFD0 && marker <= 0xFFD7) {
            offset += 2; // RST markers don't have length
          } else if (marker === 0xFFD8 || marker === 0xFFD9) {
            offset += 2; // SOI and EOI don't have length
          } else {
            const segmentLength = dataView.getUint16(offset + 2, false);
            offset += 2 + segmentLength;
          }
        }
      } catch (error) {
        console.log('Error extracting EXIF date:', error);
      }

      resolve(null);
    };

    reader.readAsArrayBuffer(file.slice(0, 128 * 1024)); // Read first 128KB
  });
};
