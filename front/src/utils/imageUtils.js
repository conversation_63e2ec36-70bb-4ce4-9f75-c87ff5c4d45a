// Image utilities for handling image operations

export const getImageDimensions = (file) => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      });
    };
    img.onerror = () => {
      resolve({ width: 0, height: 0 });
    };
    img.src = URL.createObjectURL(file);
  });
};

export const createImagePreview = (file) => {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target.result);
    reader.onerror = () => resolve(null);
    reader.readAsDataURL(file);
  });
};

/**
 * Compress image to reduce file size while maintaining quality
 * @param {File} file - Original image file
 * @param {Object} options - Compression options
 * @returns {Promise<File>} - Compressed image file
 */
export const compressImage = (file, options = {}) => {
  return new Promise((resolve, reject) => {
    const {
      maxWidth = 1920,
      maxHeight = 1080,
      quality = 0.8,
      maxSizeMB = 3,
      outputFormat = 'image/jpeg'
    } = options;

    // If file is already small enough, return as is
    if (file.size <= maxSizeMB * 1024 * 1024) {
      resolve(file);
      return;
    }

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      try {
        // Calculate new dimensions maintaining aspect ratio
        let { width, height } = img;
        
        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width *= ratio;
          height *= ratio;
        }

        // Set canvas dimensions
        canvas.width = width;
        canvas.height = height;

        // Draw and compress image
        ctx.drawImage(img, 0, 0, width, height);

        // Convert to blob with compression
        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('Failed to compress image'));
              return;
            }

            // Create new file from compressed blob
            const compressedFile = new File(
              [blob],
              file.name,
              {
                type: outputFormat,
                lastModified: Date.now()
              }
            );

            // If still too large, try with lower quality
            if (compressedFile.size > maxSizeMB * 1024 * 1024 && quality > 0.3) {
              const lowerQualityOptions = {
                ...options,
                quality: Math.max(0.3, quality - 0.2)
              };
              compressImage(file, lowerQualityOptions).then(resolve).catch(reject);
            } else {
              resolve(compressedFile);
            }
          },
          outputFormat,
          quality
        );
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error('Failed to load image for compression'));
    };

    img.src = URL.createObjectURL(file);
  });
};

/**
 * Validate and compress image if needed
 * @param {File} file - Image file to process
 * @returns {Promise<File>} - Processed image file
 */
export const processImageForUpload = async (file) => {
  try {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      throw new Error('File must be an image');
    }

    // Compress image if needed
    const compressedFile = await compressImage(file, {
      maxWidth: 1920,
      maxHeight: 1080,
      quality: 0.85,
      maxSizeMB: 3,
      outputFormat: 'image/jpeg'
    });

    return compressedFile;
  } catch (error) {
    console.error('Error processing image:', error);
    throw error;
  }
};

/**
 * Format file size for display
 * @param {number} bytes - File size in bytes
 * @returns {string} - Formatted file size
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Extract EXIF date from image file using exif-js library
 * @param {File} file - Image file
 * @returns {Promise<string|null>} ISO date string or null if not found
 */
export const extractExifDate = async (file) => {
  // Check if it's an image file
  if (!file.type.startsWith('image/')) {
    return null;
  }

  try {
    // Try to load EXIF library
    const EXIF = await import('exif-js');
    const exifLib = EXIF.default || EXIF;

    return new Promise((resolve) => {
      exifLib.getData(file, function() {
        // Try different date tags in order of preference
        const dateTags = ['DateTimeOriginal', 'DateTime', 'DateTimeDigitized'];

        for (const tag of dateTags) {
          const dateValue = exifLib.getTag(this, tag);

          if (dateValue) {
            try {
              // EXIF date format: "YYYY:MM:DD HH:MM:SS"
              const dateStr = dateValue.toString();
              if (dateStr.match(/^\d{4}:\d{2}:\d{2} \d{2}:\d{2}:\d{2}$/)) {
                // Convert EXIF format to ISO format
                const datePart = dateStr.substring(0, 10).replace(/:/g, '-');
                const timePart = dateStr.substring(11);
                const isoString = `${datePart}T${timePart}`;

                // Validate the date
                const testDate = new Date(isoString);
                if (!isNaN(testDate.getTime())) {
                  resolve(isoString);
                  return;
                }
              }
            } catch (error) {
              // Continue to next tag if this one fails
              continue;
            }
          }
        }

        // Fallback to file's lastModified date
        if (file.lastModified) {
          const lastModifiedDate = new Date(file.lastModified);
          const isoString = lastModifiedDate.toISOString();
          resolve(isoString);
          return;
        }

        resolve(null);
      });
    });
  } catch (error) {
    // Fallback to file's lastModified date
    if (file.lastModified) {
      const lastModifiedDate = new Date(file.lastModified);
      const isoString = lastModifiedDate.toISOString();
      return isoString;
    }

    return null;
  }
};
