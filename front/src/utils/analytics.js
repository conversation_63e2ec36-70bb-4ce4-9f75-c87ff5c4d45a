// Google Analytics and SEO tracking utilities

// Google Analytics 4 configuration
export const GA_TRACKING_ID = process.env.REACT_APP_GA_TRACKING_ID || 'G-XXXXXXXXXX';

// Initialize Google Analytics
export const initGA = () => {
  if (typeof window !== 'undefined' && GA_TRACKING_ID && GA_TRACKING_ID !== 'G-XXXXXXXXXX') {
    // Load Google Analytics script
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`;
    document.head.appendChild(script);

    // Initialize gtag
    window.dataLayer = window.dataLayer || [];
    function gtag() {
      window.dataLayer.push(arguments);
    }
    window.gtag = gtag;
    
    gtag('js', new Date());
    gtag('config', GA_TRACKING_ID, {
      page_title: document.title,
      page_location: window.location.href,
    });
  }
};

// Track page views
export const trackPageView = (url, title) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', GA_TRACKING_ID, {
      page_title: title,
      page_location: url,
    });
  }
};

// Track events
export const trackEvent = (action, category = 'General', label = '', value = 0) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    });
  }
};

// Track user registration
export const trackRegistration = (method = 'email') => {
  trackEvent('sign_up', 'User', method);
};

// Track user login
export const trackLogin = (method = 'email') => {
  trackEvent('login', 'User', method);
};

// Track plant creation
export const trackPlantCreation = () => {
  trackEvent('create_plant', 'Plants', 'New Plant Added');
};

// Track subscription events
export const trackSubscription = (planName, value) => {
  trackEvent('purchase', 'Subscription', planName, value);
};

// Track photo uploads
export const trackPhotoUpload = (context = 'plant') => {
  trackEvent('upload_photo', 'Content', context);
};

// SEO utilities
export const updatePageTitle = (title) => {
  if (typeof document !== 'undefined') {
    document.title = title.includes('MeuBonsai.App') ? title : `${title} | MeuBonsai.App`;
  }
};

export const updateMetaDescription = (description) => {
  if (typeof document !== 'undefined') {
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', description);
    }
  }
};

// Schema.org utilities
export const generateBreadcrumbSchema = (breadcrumbs) => {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": crumb.name,
      "item": crumb.url
    }))
  };
};

export const generatePlantSchema = (plant) => {
  return {
    "@context": "https://schema.org",
    "@type": "Thing",
    "name": plant.name,
    "description": plant.description || `Bonsai ${plant.category} gerenciado no MeuBonsai.App`,
    "image": plant.primary_image,
    "category": plant.category,
    "additionalProperty": [
      {
        "@type": "PropertyValue",
        "name": "Localização",
        "value": plant.location
      },
      {
        "@type": "PropertyValue", 
        "name": "Estilo",
        "value": plant.style
      }
    ]
  };
};
