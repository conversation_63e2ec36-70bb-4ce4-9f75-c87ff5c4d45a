/**
 * Utilitários para manipulação de planos de assinatura
 */

/**
 * Extrai o nome base do plano removendo "Mensal" e "Anual"
 * @param {Object} planInfo - Informações do plano
 * @returns {string} - Nome base do plano (ex: "Básico" em vez de "Básico Mensal")
 */
export const getBasePlanDisplayName = (planInfo) => {
  if (!planInfo) return '';
  
  // Se for plano gratuito, retornar como está
  if (planInfo.name === 'free') {
    return planInfo.display_name;
  }
  
  // Para outros planos, extrair apenas o tipo base
  const displayName = planInfo.display_name || '';
  
  // Remover "Mensal" e "Anual" do nome
  return displayName
    .replace(' Mensal', '')
    .replace(' Anual', '')
    .trim();
};

/**
 * Extrai o tipo base do plano a partir do nome técnico
 * @param {string} planName - Nome técnico do plano (ex: "basic_monthly", "premium_annual")
 * @returns {string} - Tipo base do plano (ex: "basic", "premium")
 */
export const getBasePlanType = (planName) => {
  if (!planName) return '';
  
  // Se for plano gratuito, retornar como está
  if (planName === 'free') {
    return 'free';
  }
  
  // Para outros planos, extrair apenas o tipo base
  return planName.split('_')[0]; // basic_monthly -> basic
};

/**
 * Formata o nome do plano para exibição
 * @param {string} planType - Tipo base do plano
 * @returns {string} - Nome formatado para exibição
 */
export const formatPlanDisplayName = (planType) => {
  const planNames = {
    'free': 'Gratuito',
    'basic': 'Básico',
    'premium': 'Premium'
  };
  
  return planNames[planType] || planType;
};

/**
 * Verifica se um plano é superior a outro na hierarquia
 * @param {string} currentPlan - Plano atual
 * @param {string} targetPlan - Plano alvo
 * @returns {boolean} - True se o plano alvo é superior ao atual
 */
export const isPlanUpgrade = (currentPlan, targetPlan) => {
  const planHierarchy = {
    'free': 0,
    'basic': 1,
    'premium': 2
  };
  
  const currentLevel = planHierarchy[getBasePlanType(currentPlan)] || 0;
  const targetLevel = planHierarchy[getBasePlanType(targetPlan)] || 0;
  
  return targetLevel > currentLevel;
};

/**
 * Verifica se um plano é inferior a outro na hierarquia
 * @param {string} currentPlan - Plano atual
 * @param {string} targetPlan - Plano alvo
 * @returns {boolean} - True se o plano alvo é inferior ao atual
 */
export const isPlanDowngrade = (currentPlan, targetPlan) => {
  const planHierarchy = {
    'free': 0,
    'basic': 1,
    'premium': 2
  };
  
  const currentLevel = planHierarchy[getBasePlanType(currentPlan)] || 0;
  const targetLevel = planHierarchy[getBasePlanType(targetPlan)] || 0;
  
  return targetLevel < currentLevel;
};
