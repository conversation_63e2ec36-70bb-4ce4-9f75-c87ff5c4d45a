/**
 * Utilitários para formatação de datas
 * Resolve problemas de timezone ao formatar datas
 */

/**
 * Formata uma data para exibição local sem problemas de timezone
 * @param {string} dateString - Data no formato YYYY-MM-DD ou ISO string
 * @param {object} options - Opções de formatação (opcional)
 * @returns {string} Data formatada em pt-BR
 */
export const formatDateLocal = (dateString, options = {}) => {
  if (!dateString) return '';
  
  // Se é uma data no formato YYYY-MM-DD, criar data local
  if (dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
    const dateParts = dateString.split('-');
    const localDate = new Date(
      parseInt(dateParts[0]), 
      parseInt(dateParts[1]) - 1, 
      parseInt(dateParts[2])
    );
    return localDate.toLocaleDateString('pt-BR', options);
  }
  
  // Para outros formatos, usar Date normal mas ajustar timezone
  const date = new Date(dateString);
  const localDate = new Date(date.getTime() + date.getTimezoneOffset() * 60000);
  return localDate.toLocaleDateString('pt-BR', options);
};

/**
 * Formata uma data para input do tipo date (YYYY-MM-DD)
 * @param {string} dateString - Data no formato ISO ou YYYY-MM-DD
 * @returns {string} Data no formato YYYY-MM-DD
 */
export const formatDateForInput = (dateString) => {
  if (!dateString) return '';
  
  // Se já está no formato correto, retornar
  if (dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
    return dateString;
  }
  
  // Converter para formato de input
  const date = new Date(dateString);
  return date.toISOString().split('T')[0];
};

/**
 * Calcula a diferença em dias entre duas datas
 * @param {string} dateString1 - Data mais recente
 * @param {string} dateString2 - Data mais antiga
 * @returns {number} Diferença em dias
 */
export const daysDifference = (dateString1, dateString2) => {
  const date1 = new Date(dateString1);
  const date2 = new Date(dateString2);
  
  // Zerar as horas para comparar apenas as datas
  date1.setHours(0, 0, 0, 0);
  date2.setHours(0, 0, 0, 0);
  
  const diffTime = Math.abs(date1.getTime() - date2.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

/**
 * Verifica se uma data é hoje
 * @param {string} dateString - Data para verificar
 * @returns {boolean} True se é hoje
 */
export const isToday = (dateString) => {
  const today = new Date();
  const date = new Date(dateString);
  
  return today.toDateString() === date.toDateString();
};

/**
 * Formata uma data relativa (ex: "há 2 dias", "hoje", "amanhã")
 * @param {string} dateString - Data para formatar
 * @returns {string} Data formatada relativamente
 */
export const formatRelativeDate = (dateString) => {
  const today = new Date();
  const date = new Date(dateString);
  
  // Zerar as horas para comparar apenas as datas
  today.setHours(0, 0, 0, 0);
  date.setHours(0, 0, 0, 0);
  
  const diffTime = today.getTime() - date.getTime();
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) return 'Hoje';
  if (diffDays === 1) return 'Ontem';
  if (diffDays === -1) return 'Amanhã';
  if (diffDays > 0) return `Há ${diffDays} dias`;
  if (diffDays < 0) return `Em ${Math.abs(diffDays)} dias`;
  
  return formatDateLocal(dateString);
};
