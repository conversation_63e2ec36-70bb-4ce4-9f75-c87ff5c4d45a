import React, { useState, useMemo } from 'react';
import styled from 'styled-components';
import { FaPlus, FaLeaf, FaArchive, FaH<PERSON>t, FaTrash, FaEdit, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'react-icons/fa';
import { useQuery, useQueryClient } from 'react-query';
import { Link, useNavigate } from 'react-router-dom';
import plantsService from '../services/plantsService';
import subscriptionService from '../services/subscriptionService';
import Button from '../components/UI/Button';
import Loading from '../components/UI/Loading';
import GracePeriodAlert from '../components/GracePeriodAlert';
import { getImageUrlSync as getImageUrl } from '../services/api';
import { toast } from 'react-toastify';

const PlantsContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: ${props => props.theme.spacing.lg};
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;

  @media (max-width: 768px) {
    padding: ${props => props.theme.spacing.md};
  }

  @media (max-width: 480px) {
    padding: ${props => props.theme.spacing.sm};
  }
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${props => props.theme.spacing.xl};
  flex-wrap: wrap;
  gap: ${props => props.theme.spacing.md};
  width: 100%;
  box-sizing: border-box;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
    gap: ${props => props.theme.spacing.lg};

    > div {
      display: flex;
      flex-wrap: wrap;
      gap: ${props => props.theme.spacing.sm};
      justify-content: center;
    }
  }

  @media (max-width: 480px) {
    > div {
      flex-direction: column;
      gap: ${props => props.theme.spacing.sm};
    }
  }
`;

const Title = styled.h1`
  color: ${props => props.theme.colors.text.primary};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const PlantsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: ${props => props.theme.spacing.lg};
  width: 100%;
  box-sizing: border-box;

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: ${props => props.theme.spacing.md};
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: ${props => props.theme.spacing.md};
  }
`;

const PlantCard = styled.div`
  background-color: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.lg};
  box-shadow: ${props => props.theme.shadows.sm};
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  text-decoration: none;
  color: inherit;
  position: relative;

  &:hover {
    transform: translateY(-4px);
    box-shadow: ${props => props.theme.shadows.md};
    color: inherit;
  }

  &:hover .plant-actions {
    opacity: 1;
  }
`;

const PlantLink = styled(Link)`
  text-decoration: none;
  color: inherit;
  display: block;
`;

const PlantImage = styled.div`
  height: 200px;
  background-color: ${props => props.theme.colors.background};
  background-image: url(${props => props.image});
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${props => props.theme.colors.text.secondary};
`;

const PlantInfo = styled.div`
  padding: ${props => props.theme.spacing.lg};
`;

const PlantName = styled.h3`
  margin-bottom: ${props => props.theme.spacing.sm};
  color: ${props => props.theme.colors.text.primary};
`;

const PlantType = styled.span`
  background-color: ${props => props.theme.colors.primary};
  color: white;
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: ${props => props.theme.typography.fontSize.sm};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
`;

const PlantMeta = styled.div`
  margin-top: ${props => props.theme.spacing.md};
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: ${props => props.theme.typography.fontSize.sm};
  color: ${props => props.theme.colors.text.secondary};
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${props => props.theme.spacing.xxl};
  color: ${props => props.theme.colors.text.secondary};
`;

const EmptyIcon = styled.div`
  font-size: 64px;
  margin-bottom: ${props => props.theme.spacing.lg};
  color: ${props => props.theme.colors.text.disabled};
`;

const PlantActions = styled.div`
  position: absolute;
  top: ${props => props.theme.spacing.sm};
  right: ${props => props.theme.spacing.sm};
  display: flex;
  gap: ${props => props.theme.spacing.xs};
  opacity: 0;
  transition: opacity 0.2s ease;
`;

const ActionButton = styled.button`
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: ${props => props.theme.colors.text.primary};

  &:hover {
    background: white;
    transform: scale(1.1);
  }

  &.favorite {
    color: ${props => props.active ? '#e74c3c' : props.theme.colors.text.secondary};
  }

  &.archive {
    color: ${props => props.theme.colors.text.secondary};
  }

  &.delete {
    color: #dc3545;
  }

  &.edit {
    color: ${props => props.theme.colors.primary};
  }
`;

const FavoriteIndicator = styled.div`
  position: absolute;
  top: ${props => props.theme.spacing.sm};
  left: ${props => props.theme.spacing.sm};
  background: rgba(231, 76, 60, 0.9);
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
`;

const ReadOnlyIndicator = styled.div`
  position: absolute;
  top: ${props => props.theme.spacing.sm};
  right: ${props => props.theme.spacing.sm};
  background: rgba(149, 165, 166, 0.9);
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  z-index: 2;
`;

// Novos componentes para filtros e controles
const ControlsSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.md};
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const ViewControls = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: ${props => props.theme.spacing.md};

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
`;

const ViewToggle = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.xs};
`;

const ViewButton = styled.button`
  background: ${props => props.active ? props.theme.colors.primary : 'transparent'};
  color: ${props => props.active ? 'white' : props.theme.colors.text.secondary};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  padding: ${props => props.theme.spacing.sm};
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;

  &:hover {
    background: ${props => props.active ? props.theme.colors.primaryDark : props.theme.colors.background};
  }
`;

const FiltersSection = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  flex-wrap: wrap;
  align-items: center;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
`;

const FilterGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.xs};
  min-width: 200px;

  @media (max-width: 768px) {
    min-width: auto;
  }
`;

const FilterLabel = styled.label`
  font-size: ${props => props.theme.typography.fontSize.sm};
  color: ${props => props.theme.colors.text.secondary};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
`;

const FilterInput = styled.input`
  padding: ${props => props.theme.spacing.sm};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.md};
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const FilterSelect = styled.select`
  padding: ${props => props.theme.spacing.sm};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.md};
  background: white;
  cursor: pointer;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const ClearFiltersButton = styled.button`
  background: transparent;
  color: ${props => props.theme.colors.text.secondary};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  cursor: pointer;
  font-size: ${props => props.theme.typography.fontSize.sm};
  transition: all 0.2s ease;
  align-self: flex-end;

  &:hover {
    background: ${props => props.theme.colors.background};
    border-color: ${props => props.theme.colors.primary};
  }

  @media (max-width: 768px) {
    align-self: stretch;
  }
`;

// Componentes para visualização em lista
const PlantsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.md};
  width: 100%;
`;

const PlantListItem = styled.div`
  background-color: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.lg};
  box-shadow: ${props => props.theme.shadows.sm};
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  position: relative;
  display: flex;
  align-items: center;

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.md};
  }

  &:hover .plant-actions {
    opacity: 1;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
`;

const ListPlantImage = styled.div`
  width: 120px;
  height: 120px;
  background-color: ${props => props.theme.colors.background};
  background-image: url(${props => props.image});
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${props => props.theme.colors.text.secondary};
  flex-shrink: 0;

  @media (max-width: 768px) {
    width: 100%;
    height: 150px;
  }
`;

const ListPlantInfo = styled.div`
  padding: ${props => props.theme.spacing.lg};
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.sm};
`;

const ListPlantHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: ${props => props.theme.spacing.md};

  @media (max-width: 768px) {
    flex-direction: column;
    gap: ${props => props.theme.spacing.sm};
  }
`;

const ListPlantDetails = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.lg};
  font-size: ${props => props.theme.typography.fontSize.sm};
  color: ${props => props.theme.colors.text.secondary};

  @media (max-width: 768px) {
    flex-direction: column;
    gap: ${props => props.theme.spacing.xs};
  }
`;

const ListPlantActions = styled.div`
  position: absolute;
  top: ${props => props.theme.spacing.sm};
  right: ${props => props.theme.spacing.sm};
  display: flex;
  gap: ${props => props.theme.spacing.xs};
  opacity: 0;
  transition: opacity 0.2s ease;

  @media (max-width: 768px) {
    opacity: 1;
    position: static;
    justify-content: center;
    padding: ${props => props.theme.spacing.md};
  }
`;

const Plants = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [showArchived, setShowArchived] = useState(false);
  const [showFavorites, setShowFavorites] = useState(false);

  // Estados para filtros e visualização
  const [viewMode, setViewMode] = useState('grid'); // 'grid' ou 'list'
  const [filters, setFilters] = useState({
    name: '',
    category: '',
    location: ''
  });

  // Estado para informações de acesso às plantas
  const [plantAccessInfo, setPlantAccessInfo] = useState(null);

  const { data: plants, isLoading, error } = useQuery(
    ['plants', showArchived, showFavorites],
    () => plantsService.getPlants(showArchived, showFavorites ? true : null).then(res => res.data.results || res.data),
    {
      retry: 1,
    }
  );

  // Buscar informações da assinatura para verificar período de graça
  const { data: subscriptionData } = useQuery(
    'currentSubscription',
    () => subscriptionService.getCurrentSubscription().then(res => res.data),
    {
      retry: 1,
    }
  );

  // Buscar informações de acesso às plantas
  const { data: accessInfo } = useQuery(
    'plantAccessInfo',
    () => plantsService.getPlantAccessInfo().then(res => res.data),
    {
      retry: 1,
      onSuccess: (data) => {
        setPlantAccessInfo(data);
      }
    }
  );

  // Função para verificar se uma planta é read-only
  const isPlantReadOnly = (plantId) => {
    return plantAccessInfo?.readonly_plant_ids?.includes(plantId) || false;
  };

  // Plantas filtradas
  const filteredPlants = useMemo(() => {
    if (!plants) return [];

    return plants.filter(plant => {
      const matchesName = !filters.name ||
        plant.name.toLowerCase().includes(filters.name.toLowerCase());

      const matchesCategory = !filters.category ||
        plant.plant_type_display.toLowerCase().includes(filters.category.toLowerCase());

      const matchesLocation = !filters.location ||
        (plant.location && plant.location.toLowerCase().includes(filters.location.toLowerCase()));

      return matchesName && matchesCategory && matchesLocation;
    });
  }, [plants, filters]);

  // Obter categorias únicas para o filtro
  const uniqueCategories = useMemo(() => {
    if (!plants) return [];
    const categories = [...new Set(plants.map(plant => plant.plant_type_display))];
    return categories.sort();
  }, [plants]);

  // Obter localizações únicas para o filtro
  const uniqueLocations = useMemo(() => {
    if (!plants) return [];
    const locations = [...new Set(plants.map(plant => plant.location).filter(Boolean))];
    return locations.sort();
  }, [plants]);

  const handleAddPlant = () => {
    navigate('/plants/add');
  };

  // Funções de controle dos filtros
  const handleFilterChange = (filterType, value) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      name: '',
      category: '',
      location: ''
    });
  };

  const hasActiveFilters = filters.name || filters.category || filters.location;

  const handleFavorite = async (e, plantId, currentFavorite) => {
    e.preventDefault();
    e.stopPropagation();
    try {
      await plantsService.favoritePlant(plantId, !currentFavorite);
      toast.success(currentFavorite ? 'Removido dos favoritos!' : 'Adicionado aos favoritos!');
      queryClient.invalidateQueries(['plants']);
    } catch (error) {
      console.error('Erro ao favoritar planta:', error);
      toast.error('Erro ao favoritar planta');
    }
  };

  const handleArchive = async (e, plantId, currentArchived) => {
    e.preventDefault();
    e.stopPropagation();
    try {
      await plantsService.archivePlant(plantId, !currentArchived);
      toast.success(currentArchived ? 'Planta desarquivada!' : 'Planta arquivada!');
      queryClient.invalidateQueries(['plants']);
    } catch (error) {
      console.error('Erro ao arquivar planta:', error);
      toast.error('Erro ao arquivar planta');
    }
  };

  const handleDelete = async (e, plantId, plantName) => {
    e.preventDefault();
    e.stopPropagation();
    if (window.confirm(`Tem certeza que deseja remover "${plantName}"? Esta ação não pode ser desfeita.`)) {
      try {
        await plantsService.deletePlant(plantId);
        toast.success('Planta removida com sucesso!');
        queryClient.invalidateQueries(['plants']);
      } catch (error) {
        console.error('Erro ao remover planta:', error);
        toast.error('Erro ao remover planta');
      }
    }
  };

  const handleEdit = (e, plantId) => {
    e.preventDefault();
    e.stopPropagation();
    navigate(`/plants/${plantId}`);
  };

  if (isLoading) return <Loading text="Carregando suas plantas..." />;

  if (error) {
    return (
      <PlantsContainer>
        <div>Erro ao carregar plantas: {error.message}</div>
      </PlantsContainer>
    );
  }

  return (
    <PlantsContainer>
      <Header>
        <Title>
          <FaLeaf />
          {showFavorites ? 'Plantas Favoritas' : showArchived ? 'Plantas Arquivadas' : 'Minhas Plantas'}
        </Title>
        <div style={{ display: 'flex', gap: '12px' }}>
          <Button
            variant={showFavorites ? 'primary' : 'secondary'}
            onClick={() => {
              setShowFavorites(!showFavorites);
              setShowArchived(false);
            }}
          >
            <FaHeart />
            {showFavorites ? 'Ver Todos' : 'Favoritos'}
          </Button>
          <Button
            variant={showArchived ? 'primary' : 'secondary'}
            onClick={() => {
              setShowArchived(!showArchived);
              setShowFavorites(false);
            }}
          >
            <FaArchive />
            {showArchived ? 'Ver Ativos' : 'Arquivados'}
          </Button>
          {!showArchived && !showFavorites && (
            <Button onClick={handleAddPlant}>
              <FaPlus />
              Nova Planta
            </Button>
          )}
        </div>
      </Header>

      {/* Alerta do período de graça */}
      <GracePeriodAlert
        gracePeriodInfo={subscriptionData?.grace_period_info}
        onRenewClick={() => navigate('/pricing')}
      />

      <ControlsSection>
        <ViewControls>
          <ViewToggle>
            <ViewButton
              active={viewMode === 'grid'}
              onClick={() => setViewMode('grid')}
              title="Visualização em grade"
            >
              <FaTh />
            </ViewButton>
            <ViewButton
              active={viewMode === 'list'}
              onClick={() => setViewMode('list')}
              title="Visualização em lista"
            >
              <FaList />
            </ViewButton>
          </ViewToggle>

          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', color: '#666' }}>
            <FaFilter size={14} />
            <span style={{ fontSize: '14px' }}>
              {filteredPlants.length} de {plants?.length || 0} plantas
            </span>
          </div>
        </ViewControls>

        <FiltersSection>
          <FilterGroup>
            <FilterLabel>Buscar por nome</FilterLabel>
            <FilterInput
              type="text"
              placeholder="Digite o nome da planta..."
              value={filters.name}
              onChange={(e) => handleFilterChange('name', e.target.value)}
            />
          </FilterGroup>

          <FilterGroup>
            <FilterLabel>Categoria</FilterLabel>
            <FilterSelect
              value={filters.category}
              onChange={(e) => handleFilterChange('category', e.target.value)}
            >
              <option value="">Todas as categorias</option>
              {uniqueCategories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </FilterSelect>
          </FilterGroup>

          <FilterGroup>
            <FilterLabel>Localização</FilterLabel>
            <FilterSelect
              value={filters.location}
              onChange={(e) => handleFilterChange('location', e.target.value)}
            >
              <option value="">Todas as localizações</option>
              {uniqueLocations.map(location => (
                <option key={location} value={location}>{location}</option>
              ))}
            </FilterSelect>
          </FilterGroup>

          {hasActiveFilters && (
            <ClearFiltersButton onClick={clearFilters}>
              Limpar filtros
            </ClearFiltersButton>
          )}
        </FiltersSection>
      </ControlsSection>

      {filteredPlants && filteredPlants.length > 0 ? (
        viewMode === 'grid' ? (
          <PlantsGrid>
            {filteredPlants.map((plant) => (
              <PlantCard key={plant.id}>
                <PlantLink to={`/plants/${plant.id}`}>
                  <PlantImage image={getImageUrl(plant.primary_image)}>
                    {!plant.primary_image && <FaLeaf size={48} />}
                  </PlantImage>
                  <PlantInfo>
                    <PlantName>{plant.name}</PlantName>
                    <PlantType>{plant.plant_type_display}</PlantType>
                    <PlantMeta>
                      <span>{plant.location || 'Sem localização'}</span>
                      <span>{plant.care_count} cuidados</span>
                    </PlantMeta>
                  </PlantInfo>
                </PlantLink>

                {plant.favorite && (
                  <FavoriteIndicator>
                    <FaHeart />
                  </FavoriteIndicator>
                )}

                {isPlantReadOnly(plant.id) && (
                  <ReadOnlyIndicator title="Planta travada para edição devido ao limite do plano">
                    <FaLock />
                  </ReadOnlyIndicator>
                )}

                <PlantActions className="plant-actions">
                  <ActionButton
                    className={`favorite ${plant.favorite ? 'active' : ''}`}
                    active={plant.favorite}
                    onClick={(e) => handleFavorite(e, plant.id, plant.favorite)}
                    title={plant.favorite ? 'Remover dos favoritos' : 'Adicionar aos favoritos'}
                    disabled={isPlantReadOnly(plant.id)}
                  >
                    <FaHeart />
                  </ActionButton>
                  <ActionButton
                    className="edit"
                    onClick={(e) => handleEdit(e, plant.id)}
                    title={isPlantReadOnly(plant.id) ? "Planta travada para edição devido ao limite do plano" : "Editar planta"}
                    disabled={isPlantReadOnly(plant.id)}
                  >
                    <FaEdit />
                  </ActionButton>
                  <ActionButton
                    className="archive"
                    onClick={(e) => handleArchive(e, plant.id, plant.archived)}
                    title={isPlantReadOnly(plant.id) ? "Planta travada para edição devido ao limite do plano" : (plant.archived ? 'Desarquivar' : 'Arquivar')}
                    disabled={isPlantReadOnly(plant.id)}
                  >
                    <FaArchive />
                  </ActionButton>
                  <ActionButton
                    className="delete"
                    onClick={(e) => handleDelete(e, plant.id, plant.name)}
                    title="Remover planta"
                  >
                    <FaTrash />
                  </ActionButton>
                </PlantActions>
              </PlantCard>
            ))}
          </PlantsGrid>
        ) : (
          <PlantsList>
            {filteredPlants.map((plant) => (
              <PlantListItem key={plant.id}>
                <PlantLink to={`/plants/${plant.id}`} style={{ display: 'flex', flex: 1, alignItems: 'center' }}>
                  <ListPlantImage image={getImageUrl(plant.primary_image)}>
                    {!plant.primary_image && <FaLeaf size={32} />}
                  </ListPlantImage>
                  <ListPlantInfo>
                    <ListPlantHeader>
                      <div>
                        <PlantName style={{ margin: 0, marginBottom: '4px' }}>{plant.name}</PlantName>
                        <PlantType>{plant.plant_type_display}</PlantType>
                      </div>
                      {plant.favorite && (
                        <FavoriteIndicator style={{ position: 'static', marginTop: '4px' }}>
                          <FaHeart />
                        </FavoriteIndicator>
                      )}
                      {isPlantReadOnly(plant.id) && (
                        <ReadOnlyIndicator style={{ position: 'static', marginTop: '4px', marginLeft: '8px' }} title="Planta travada para edição devido ao limite do plano">
                          <FaLock />
                        </ReadOnlyIndicator>
                      )}
                    </ListPlantHeader>
                    <ListPlantDetails>
                      <span><strong>Localização:</strong> {plant.location || 'Não informada'}</span>
                      <span><strong>Cuidados:</strong> {plant.care_count}</span>
                      {plant.scientific_name && (
                        <span><strong>Nome científico:</strong> {plant.scientific_name}</span>
                      )}
                    </ListPlantDetails>
                  </ListPlantInfo>
                </PlantLink>

                <ListPlantActions className="plant-actions">
                  <ActionButton
                    className={`favorite ${plant.favorite ? 'active' : ''}`}
                    active={plant.favorite}
                    onClick={(e) => handleFavorite(e, plant.id, plant.favorite)}
                    title={plant.favorite ? 'Remover dos favoritos' : 'Adicionar aos favoritos'}
                    disabled={isPlantReadOnly(plant.id)}
                  >
                    <FaHeart />
                  </ActionButton>
                  <ActionButton
                    className="edit"
                    onClick={(e) => handleEdit(e, plant.id)}
                    title={isPlantReadOnly(plant.id) ? "Planta travada para edição devido ao limite do plano" : "Editar planta"}
                    disabled={isPlantReadOnly(plant.id)}
                  >
                    <FaEdit />
                  </ActionButton>
                  <ActionButton
                    className="archive"
                    onClick={(e) => handleArchive(e, plant.id, plant.archived)}
                    title={isPlantReadOnly(plant.id) ? "Planta travada para edição devido ao limite do plano" : (plant.archived ? 'Desarquivar' : 'Arquivar')}
                    disabled={isPlantReadOnly(plant.id)}
                  >
                    <FaArchive />
                  </ActionButton>
                  <ActionButton
                    className="delete"
                    onClick={(e) => handleDelete(e, plant.id, plant.name)}
                    title="Remover planta"
                  >
                    <FaTrash />
                  </ActionButton>
                </ListPlantActions>
              </PlantListItem>
            ))}
          </PlantsList>
        )
      ) : (
        <EmptyState>
          <EmptyIcon>
            {hasActiveFilters ? <FaSearch /> : showFavorites ? <FaHeart /> : showArchived ? <FaArchive /> : <FaLeaf />}
          </EmptyIcon>
          <h3>
            {hasActiveFilters
              ? 'Nenhuma planta encontrada'
              : showFavorites
                ? 'Nenhuma planta favorita'
                : showArchived
                  ? 'Nenhuma planta arquivada'
                  : 'Nenhuma planta cadastrada'
            }
          </h3>
          <p>
            {hasActiveFilters
              ? 'Tente ajustar os filtros para encontrar suas plantas.'
              : showFavorites
                ? 'Você ainda não marcou nenhuma planta como favorita.'
                : showArchived
                  ? 'Você ainda não arquivou nenhuma planta.'
                  : 'Comece adicionando sua primeira planta!'
            }
          </p>
          {hasActiveFilters ? (
            <Button style={{ marginTop: '16px' }} onClick={clearFilters}>
              Limpar filtros
            </Button>
          ) : !showArchived && !showFavorites && (
            <Button style={{ marginTop: '16px' }} onClick={handleAddPlant}>
              <FaPlus />
              Adicionar Primeira Planta
            </Button>
          )}
        </EmptyState>
      )}
    </PlantsContainer>
  );
};

export default Plants;
