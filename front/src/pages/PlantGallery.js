import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import styled from 'styled-components';
import { FaArrowLeft, FaCalendarAlt, FaLeaf, FaImages } from 'react-icons/fa';
import plantsService from '../services/plantsService';
import ImageGallery from '../components/ImageGallery';
import VideoGallery from '../components/VideoGallery';
import Button from '../components/UI/Button';

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: ${props => props.theme.spacing.lg};
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.md};
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const BackButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
`;

const Title = styled.h1`
  font-size: ${props => props.theme.typography.fontSize.xl};
  color: ${props => props.theme.colors.text.primary};
  margin: 0;
`;

const PlantInfo = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  color: ${props => props.theme.colors.text.secondary};
  font-size: ${props => props.theme.typography.fontSize.sm};
`;

const FilterTabs = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
  margin-bottom: ${props => props.theme.spacing.lg};
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const FilterTab = styled.button`
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border: none;
  background: none;
  color: ${props => props.active ? props.theme.colors.primary : props.theme.colors.text.secondary};
  font-weight: ${props => props.active ? '600' : '400'};
  border-bottom: 2px solid ${props => props.active ? props.theme.colors.primary : 'transparent'};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    color: ${props => props.theme.colors.primary};
  }
`;

const DateSection = styled.div`
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const DateHeader = styled.h2`
  font-size: ${props => props.theme.typography.fontSize.lg};
  color: ${props => props.theme.colors.text.primary};
  margin: 0 0 ${props => props.theme.spacing.md} 0;
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const ImageCount = styled.span`
  font-size: ${props => props.theme.typography.fontSize.sm};
  color: ${props => props.theme.colors.text.secondary};
  font-weight: normal;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${props => props.theme.spacing.xl};
  color: ${props => props.theme.colors.text.secondary};

  svg {
    font-size: 48px;
    margin-bottom: ${props => props.theme.spacing.md};
    opacity: 0.5;
  }

  h3 {
    margin: 0 0 ${props => props.theme.spacing.sm} 0;
    color: ${props => props.theme.colors.text.primary};
  }

  p {
    margin: 0;
  }
`;

const PlantGallery = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [filter, setFilter] = useState('all'); // 'all', 'plant', 'care'
  const [mediaType, setMediaType] = useState('all'); // 'all', 'photos', 'videos'

  // Buscar dados da planta
  const { data: plant } = useQuery(
    ['plant', id],
    () => plantsService.getPlant(id).then(res => res.data),
    { enabled: !!id }
  );

  // Buscar todas as imagens
  const { data: allImages = [], isLoading } = useQuery(
    ['plant-all-images', id],
    () => plantsService.getAllPlantImages(id).then(res => res.data),
    { enabled: !!id }
  );

  // Buscar todos os vídeos (planta + cuidados)
  const { data: allVideos = [], isLoading: isLoadingVideos } = useQuery(
    ['plant-all-videos', id],
    async () => {
      // Buscar vídeos da planta
      const plantVideos = await plantsService.getPlantVideos(id).then(res => res.data);

      // Buscar todas as imagens (que inclui vídeos de cuidados)
      const allImages = await plantsService.getAllPlantImages(id).then(res => res.data);
      const careVideos = allImages.filter(img => img.is_video && img.type === 'care');

      // Combinar e retornar todos os vídeos
      return [...plantVideos, ...careVideos];
    },
    { enabled: !!id }
  );

  // Filtrar imagens
  const filteredImages = allImages.filter(image => {
    if (filter === 'all') return true;
    if (filter === 'plant') return image.type === 'plant';
    if (filter === 'care') return image.type === 'care';
    return true;
  });

  // Agrupar imagens por data
  const groupImagesByDate = (images) => {
    if (!images || images.length === 0) {
      return [];
    }

    const groups = {};

    images.forEach(image => {
      try {
        const date = new Date(image.photo_date || image.created_at);
        const dateKey = date.toLocaleDateString('pt-BR', {
          year: 'numeric',
          month: 'long'
        });

        if (!groups[dateKey]) {
          groups[dateKey] = [];
        }
        groups[dateKey].push(image);
      } catch (error) {
        // Fallback para grupo "Sem data"
        const fallbackKey = 'Sem data';
        if (!groups[fallbackKey]) {
          groups[fallbackKey] = [];
        }
        groups[fallbackKey].push(image);
      }
    });

    // Ordenar grupos por data (mais recente primeiro)
    const sortedGroups = Object.entries(groups).sort((a, b) => {
      try {
        const dateA = new Date(a[1][0].photo_date || a[1][0].created_at);
        const dateB = new Date(b[1][0].photo_date || b[1][0].created_at);
        return dateB - dateA;
      } catch (error) {
        return 0;
      }
    });

    return sortedGroups;
  };

  const imageGroups = groupImagesByDate(filteredImages);

  const handleImagesChange = () => {
    // Recarregar imagens após mudanças
    window.location.reload();
  };

  if (isLoading || isLoadingVideos) {
    return (
      <Container>
        <div style={{ textAlign: 'center', padding: '40px' }}>
          Carregando galeria...
        </div>
      </Container>
    );
  }

  return (
    <Container>
      <Header>
        <BackButton
          variant="secondary"
          size="small"
          onClick={() => navigate(`/plants/${id}`)}
        >
          <FaArrowLeft />
          Voltar
        </BackButton>
        <div>
          <Title>Galeria de Mídia</Title>
          {plant && (
            <PlantInfo>
              <FaLeaf />
              {plant.name}
              <span>•</span>
              <FaImages />
              {allImages.length} foto{allImages.length !== 1 ? 's' : ''} • {allVideos.length} vídeo{allVideos.length !== 1 ? 's' : ''}
            </PlantInfo>
          )}
        </div>
      </Header>

      <FilterTabs>
        <FilterTab
          active={mediaType === 'all'}
          onClick={() => setMediaType('all')}
        >
          Tudo ({allImages.length + allVideos.length})
        </FilterTab>
        <FilterTab
          active={mediaType === 'photos'}
          onClick={() => setMediaType('photos')}
        >
          Fotos ({allImages.length})
        </FilterTab>
        <FilterTab
          active={mediaType === 'videos'}
          onClick={() => setMediaType('videos')}
        >
          Vídeos ({allVideos.length})
        </FilterTab>
      </FilterTabs>

      {mediaType !== 'videos' && (
        <FilterTabs style={{ marginTop: '0.5rem', borderTop: 'none' }}>
          <FilterTab
            active={filter === 'all'}
            onClick={() => setFilter('all')}
          >
            Todas as Fotos ({allImages.length})
          </FilterTab>
          <FilterTab
            active={filter === 'plant'}
            onClick={() => setFilter('plant')}
          >
            Fotos da Planta ({allImages.filter(img => img.type === 'plant').length})
          </FilterTab>
          <FilterTab
            active={filter === 'care'}
            onClick={() => setFilter('care')}
          >
            Fotos de Cuidados ({allImages.filter(img => img.type === 'care').length})
          </FilterTab>
        </FilterTabs>
      )}

      {mediaType === 'videos' ? (
        // Mostrar apenas vídeos
        allVideos.length === 0 ? (
          <EmptyState>
            <FaImages />
            <h3>Nenhum vídeo encontrado</h3>
            <p>Esta planta ainda não possui vídeos. Upload de vídeos é exclusivo do plano Premium.</p>
          </EmptyState>
        ) : (
          <DateSection>
            <DateHeader>
              <FaImages />
              Todos os Vídeos
              <ImageCount>({allVideos.length} vídeo{allVideos.length !== 1 ? 's' : ''})</ImageCount>
            </DateHeader>
            <VideoGallery
              videos={allVideos}
              showCaption={true}
            />
          </DateSection>
        )
      ) : mediaType === 'photos' ? (
        // Mostrar apenas fotos
        filteredImages.length === 0 ? (
          <EmptyState>
            <FaImages />
            <h3>Nenhuma foto encontrada</h3>
            <p>
              {filter === 'all'
                ? 'Esta planta ainda não possui fotos.'
                : `Nenhuma foto de ${filter === 'plant' ? 'planta' : 'cuidados'} encontrada.`
              }
            </p>
          </EmptyState>
        ) : (
          imageGroups.map(([dateKey, images]) => (
            <DateSection key={dateKey}>
              <DateHeader>
                <FaCalendarAlt />
                {dateKey}
                <ImageCount>({images.length} foto{images.length !== 1 ? 's' : ''})</ImageCount>
              </DateHeader>
              <ImageGallery
                images={images}
                plantId={id}
                onImagesChange={handleImagesChange}
                showCareImages={true}
                compact={false}
              />
            </DateSection>
          ))
        )
      ) : (
        // Mostrar tudo (fotos + vídeos misturados por data)
        <>
          {filteredImages.length === 0 && allVideos.length === 0 ? (
            <EmptyState>
              <FaImages />
              <h3>Nenhuma mídia encontrada</h3>
              <p>Esta planta ainda não possui fotos ou vídeos.</p>
            </EmptyState>
          ) : (
            imageGroups.map(([dateKey, images]) => (
              <DateSection key={dateKey}>
                <DateHeader>
                  <FaCalendarAlt />
                  {dateKey}
                  <ImageCount>({images.length} foto{images.length !== 1 ? 's' : ''})</ImageCount>
                </DateHeader>
                <ImageGallery
                  images={images}
                  plantId={id}
                  onImagesChange={handleImagesChange}
                  showCareImages={true}
                  compact={false}
                />
              </DateSection>
            ))
          )}
        </>
      )}
    </Container>
  );
};

export default PlantGallery;
