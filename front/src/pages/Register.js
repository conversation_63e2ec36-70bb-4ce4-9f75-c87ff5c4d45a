import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import styled from 'styled-components';
import { FaLeaf, FaEye, FaEyeSlash, FaCheck, FaTimes, FaSpinner } from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';
import { checkUsernameAvailabilityPublic } from '../services/socialApi';
import Button from '../components/UI/Button';
import SocialLoginButtons from '../components/SocialLoginButtons';
import ConsentCheckbox from '../components/ConsentCheckbox';

const RegisterContainer = styled.div`
  min-height: calc(100vh - 80px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${props => props.theme.spacing.md};
`;

const RegisterCard = styled.div`
  background-color: ${props => props.theme.colors.surface};
  padding: ${props => props.theme.spacing.xxl};
  border-radius: ${props => props.theme.borderRadius.lg};
  box-shadow: ${props => props.theme.shadows.lg};
  width: 100%;
  max-width: 500px;
`;

const RegisterHeader = styled.div`
  text-align: center;
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const Logo = styled.div`
  color: ${props => props.theme.colors.primary};
  margin-bottom: ${props => props.theme.spacing.md};
  display: flex;
  justify-content: center;
`;

const Title = styled.h1`
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing.sm};
`;

const Subtitle = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  margin-bottom: 0;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.lg};
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${props => props.theme.spacing.md};

  @media (max-width: ${props => props.theme.breakpoints.sm}) {
    grid-template-columns: 1fr;
  }
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.xs};
`;

const Label = styled.label`
  color: ${props => props.theme.colors.text.primary};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
`;

const InputContainer = styled.div`
  position: relative;
`;

const Input = styled.input`
  width: 100%;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.error ? props.theme.colors.error : props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.md};
  
  &:focus {
    border-color: ${props => props.error ? props.theme.colors.error : props.theme.colors.primary};
    outline: none;
  }
`;

const PasswordToggle = styled.button`
  position: absolute;
  right: ${props => props.theme.spacing.sm};
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: ${props => props.theme.colors.text.secondary};
  cursor: pointer;
  padding: ${props => props.theme.spacing.xs};
`;

const ErrorMessage = styled.span`
  color: ${props => props.theme.colors.error};
  font-size: ${props => props.theme.typography.fontSize.sm};
`;

const UsernameContainer = styled.div`
  position: relative;
`;

const UsernameInput = styled(Input)`
  padding-right: 40px;
  border-color: ${props =>
    props.isValid === false ? props.theme.colors.error :
    props.isValid === true ? props.theme.colors.success :
    props.theme.colors.border
  };

  &:focus {
    border-color: ${props =>
      props.isValid === false ? props.theme.colors.error :
      props.isValid === true ? props.theme.colors.success :
      props.theme.colors.primary
    };
  }
`;

const ValidationIcon = styled.div`
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: ${props =>
    props.isChecking ? props.theme.colors.primary :
    props.isValid === true ? props.theme.colors.success :
    props.isValid === false ? props.theme.colors.error :
    props.theme.colors.text.secondary
  };
  font-size: 1rem;

  ${props => props.isChecking && `
    animation: spin 1s linear infinite;
    @keyframes spin {
      from { transform: translateY(-50%) rotate(0deg); }
      to { transform: translateY(-50%) rotate(360deg); }
    }
  `}
`;

const ValidationMessage = styled.span`
  color: ${props =>
    props.type === 'error' ? props.theme.colors.error :
    props.type === 'success' ? props.theme.colors.success :
    props.theme.colors.text.secondary
  };
  font-size: ${props => props.theme.typography.fontSize.sm};
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
`;

const RegisterFooter = styled.div`
  text-align: center;
  margin-top: ${props => props.theme.spacing.lg};
  color: ${props => props.theme.colors.text.secondary};
`;

const Register = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showPasswordConfirm, setShowPasswordConfirm] = useState(false);
  const [loading, setLoading] = useState(false);
  const [usernameValidation, setUsernameValidation] = useState({
    isValid: null,
    message: '',
    isChecking: false
  });
  const [checkTimeout, setCheckTimeout] = useState(null);
  const [consents, setConsents] = useState({
    terms: false
  });
  const [consentErrors, setConsentErrors] = useState({});
  const { register: registerUser } = useAuth();
  const navigate = useNavigate();
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setError
  } = useForm();

  const password = watch('password');
  const username = watch('username');

  // Função para validar formato do username
  const validateUsernameFormat = (value) => {
    if (!value || value.trim() === '') {
      return { isValid: null, message: '', isChecking: false };
    }

    const cleanValue = value.trim();

    if (cleanValue.length < 3) {
      return { isValid: false, message: 'Username deve ter pelo menos 3 caracteres', isChecking: false };
    }

    if (cleanValue.length > 30) {
      return { isValid: false, message: 'Username deve ter no máximo 30 caracteres', isChecking: false };
    }

    const validPattern = /^[a-zA-Z0-9_]+$/;
    if (!validPattern.test(cleanValue)) {
      return { isValid: false, message: 'Apenas letras, números e _ são permitidos', isChecking: false };
    }

    if (cleanValue.startsWith('_') || cleanValue.endsWith('_')) {
      return { isValid: false, message: 'Username não pode começar ou terminar com _', isChecking: false };
    }

    if (cleanValue.includes('__')) {
      return { isValid: false, message: 'Username não pode ter __ consecutivos', isChecking: false };
    }

    return { isValid: null, message: 'Verificando disponibilidade...', isChecking: true };
  };

  // Função para verificar disponibilidade
  const checkUsernameWithDebounce = async (value) => {
    const cleanValue = value.trim();

    try {
      const result = await checkUsernameAvailabilityPublic(cleanValue);

      if (result.available) {
        return { isValid: true, message: 'Username disponível!', isChecking: false };
      } else {
        const messages = {
          already_taken: 'Username já está em uso',
          invalid_length: 'Comprimento inválido',
          invalid_characters: 'Caracteres inválidos',
          invalid_format: 'Formato inválido'
        };
        return {
          isValid: false,
          message: messages[result.reason] || 'Username não disponível',
          isChecking: false
        };
      }
    } catch (error) {
      console.error('Error checking username:', error);
      return { isValid: false, message: 'Erro ao verificar disponibilidade', isChecking: false };
    }
  };

  // Effect para validar username em tempo real
  useEffect(() => {
    if (!username) {
      setUsernameValidation({ isValid: null, message: '', isChecking: false });
      return;
    }

    const formatValidation = validateUsernameFormat(username);
    setUsernameValidation(formatValidation);

    if (formatValidation.isChecking) {
      if (checkTimeout) {
        clearTimeout(checkTimeout);
      }

      const timeout = setTimeout(async () => {
        const availabilityValidation = await checkUsernameWithDebounce(username);
        setUsernameValidation(availabilityValidation);
      }, 800);

      setCheckTimeout(timeout);
    }

    return () => {
      if (checkTimeout) {
        clearTimeout(checkTimeout);
      }
    };
  }, [username]);

  const onSubmit = async (data) => {
    // Validar username antes de enviar
    if (!usernameValidation.isValid) {
      setError('username', { message: 'Username inválido ou não disponível' });
      return;
    }

    // Validar consentimento obrigatório
    const newConsentErrors = {};
    if (!consents.terms) {
      newConsentErrors.terms = true;
    }

    if (Object.keys(newConsentErrors).length > 0) {
      setConsentErrors(newConsentErrors);
      return;
    }

    setConsentErrors({});
    setLoading(true);

    // Adicionar consentimentos aos dados
    const registrationData = {
      ...data,
      consents: consents
    };

    const result = await registerUser(registrationData);

    if (result.success) {
      navigate('/login');
    } else {
      if (result.error && typeof result.error === 'object') {
        Object.keys(result.error).forEach(field => {
          setError(field, { message: result.error[field][0] });
        });
      }
    }
    setLoading(false);
  };

  const handleSocialLoginSuccess = () => {
    navigate('/profile');
  };

  const handleSocialLoginError = (error) => {
    console.error('Social login error:', error);
  };

  return (
    <RegisterContainer>
      <RegisterCard>
        <RegisterHeader>
          <Logo>
            <FaLeaf size={48} />
          </Logo>
          <Title>Criar Conta</Title>
          <Subtitle>Junte-se à comunidade MeuBonsai.App</Subtitle>
        </RegisterHeader>

        <Form onSubmit={handleSubmit(onSubmit)}>
          <FormRow>
            <FormGroup>
              <Label htmlFor="first_name">Nome</Label>
              <Input
                id="first_name"
                type="text"
                placeholder="Seu nome"
                error={errors.first_name}
                {...register('first_name', {
                  required: 'Nome é obrigatório'
                })}
              />
              {errors.first_name && <ErrorMessage>{errors.first_name.message}</ErrorMessage>}
            </FormGroup>

            <FormGroup>
              <Label htmlFor="last_name">Sobrenome</Label>
              <Input
                id="last_name"
                type="text"
                placeholder="Seu sobrenome"
                error={errors.last_name}
                {...register('last_name', {
                  required: 'Sobrenome é obrigatório'
                })}
              />
              {errors.last_name && <ErrorMessage>{errors.last_name.message}</ErrorMessage>}
            </FormGroup>
          </FormRow>



          <FormGroup>
            <Label htmlFor="username">Nome de Usuário</Label>
            <UsernameContainer>
              <UsernameInput
                id="username"
                type="text"
                placeholder="seuusername"
                error={errors.username}
                isValid={usernameValidation.isValid}
                {...register('username', {
                  required: 'Nome de usuário é obrigatório',
                  minLength: {
                    value: 3,
                    message: 'Username deve ter pelo menos 3 caracteres'
                  },
                  maxLength: {
                    value: 30,
                    message: 'Username deve ter no máximo 30 caracteres'
                  },
                  pattern: {
                    value: /^[a-zA-Z0-9_]+$/,
                    message: 'Apenas letras, números e _ são permitidos'
                  }
                })}
              />
              <ValidationIcon
                isValid={usernameValidation.isValid}
                isChecking={usernameValidation.isChecking}
              >
                {usernameValidation.isChecking && <FaSpinner />}
                {!usernameValidation.isChecking && usernameValidation.isValid === true && <FaCheck />}
                {!usernameValidation.isChecking && usernameValidation.isValid === false && <FaTimes />}
              </ValidationIcon>
            </UsernameContainer>
            {errors.username && <ErrorMessage>{errors.username.message}</ErrorMessage>}
            {usernameValidation.message && (
              <ValidationMessage type={usernameValidation.isValid ? 'success' : 'error'}>
                {usernameValidation.isValid ? <FaCheck /> : <FaTimes />}
                {usernameValidation.message}
              </ValidationMessage>
            )}
          </FormGroup>

          <FormGroup>
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              error={errors.email}
              {...register('email', {
                required: 'Email é obrigatório',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Email inválido'
                }
              })}
            />
            {errors.email && <ErrorMessage>{errors.email.message}</ErrorMessage>}
          </FormGroup>

          <FormGroup>
            <Label htmlFor="password">Senha</Label>
            <InputContainer>
              <Input
                id="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="Crie uma senha"
                error={errors.password}
                {...register('password', {
                  required: 'Senha é obrigatória',
                  minLength: {
                    value: 8,
                    message: 'Senha deve ter pelo menos 8 caracteres'
                  }
                })}
              />
              <PasswordToggle
                type="button"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <FaEyeSlash /> : <FaEye />}
              </PasswordToggle>
            </InputContainer>
            {errors.password && <ErrorMessage>{errors.password.message}</ErrorMessage>}
          </FormGroup>

          <FormGroup>
            <Label htmlFor="password_confirm">Confirmar Senha</Label>
            <InputContainer>
              <Input
                id="password_confirm"
                type={showPasswordConfirm ? 'text' : 'password'}
                placeholder="Confirme sua senha"
                error={errors.password_confirm}
                {...register('password_confirm', {
                  required: 'Confirmação de senha é obrigatória',
                  validate: value => value === password || 'As senhas não coincidem'
                })}
              />
              <PasswordToggle
                type="button"
                onClick={() => setShowPasswordConfirm(!showPasswordConfirm)}
              >
                {showPasswordConfirm ? <FaEyeSlash /> : <FaEye />}
              </PasswordToggle>
            </InputContainer>
            {errors.password_confirm && <ErrorMessage>{errors.password_confirm.message}</ErrorMessage>}
          </FormGroup>

          <ConsentCheckbox
            consents={consents}
            onChange={setConsents}
            errors={consentErrors}
          />

          <Button
            type="submit"
            fullWidth
            disabled={loading || usernameValidation.isValid === false || usernameValidation.isChecking}
          >
            {loading ? 'Criando conta...' : 'Criar Conta'}
          </Button>
        </Form>

        <SocialLoginButtons
          onSuccess={handleSocialLoginSuccess}
          onError={handleSocialLoginError}
          dividerText="ou cadastre-se com"
        />

        <RegisterFooter>
          Já tem uma conta?{' '}
          <Link to="/login">Faça login aqui</Link>
        </RegisterFooter>
      </RegisterCard>
    </RegisterContainer>
  );
};

export default Register;
