import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { <PERSON>a<PERSON><PERSON><PERSON>, FaChartBar, FaTicketAlt, FaCrown, FaEye, FaEdit, FaTrash, FaPlus } from 'react-icons/fa';
import { useQuery, useQueryClient } from 'react-query';
import { toast } from 'react-toastify';
import adminService from '../services/adminService';
import Button from '../components/UI/Button';
import Loading from '../components/UI/Loading';
import UserManagement from '../components/Admin/UserManagement';
import CouponManagement from '../components/Admin/CouponManagement';
import AdminDashboard from '../components/Admin/AdminDashboard';

const AdminContainer = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: ${props => props.theme.spacing.xl};

  @media (max-width: 768px) {
    padding: ${props => props.theme.spacing.md};
  }
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${props => props.theme.spacing.xl};
  flex-wrap: wrap;
  gap: ${props => props.theme.spacing.md};
`;

const Title = styled.h1`
  color: ${props => props.theme.colors.text.primary};
  font-size: ${props => props.theme.typography.fontSize.xxl};
  font-weight: ${props => props.theme.typography.fontWeight.bold};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.md};
  margin: 0;

  @media (max-width: 768px) {
    font-size: ${props => props.theme.typography.fontSize.xl};
  }
`;

const TabsContainer = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.xs};
  margin-bottom: ${props => props.theme.spacing.xl};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  overflow-x: auto;

  @media (max-width: 768px) {
    gap: 0;
  }
`;

const Tab = styled.button`
  background: none;
  border: none;
  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};
  color: ${props => props.active ? props.theme.colors.primary : props.theme.colors.text.secondary};
  font-weight: ${props => props.active ? props.theme.typography.fontWeight.semibold : props.theme.typography.fontWeight.normal};
  border-bottom: 2px solid ${props => props.active ? props.theme.colors.primary : 'transparent'};
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};

  &:hover {
    color: ${props => props.theme.colors.primary};
    background: ${props => props.theme.colors.background};
  }

  @media (max-width: 768px) {
    padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
    font-size: ${props => props.theme.typography.fontSize.sm};
  }
`;

const TabContent = styled.div`
  min-height: 500px;
`;

const ErrorMessage = styled.div`
  background: #fee;
  color: #c53030;
  padding: ${props => props.theme.spacing.lg};
  border-radius: ${props => props.theme.borderRadius.md};
  border: 1px solid #fed7d7;
  text-align: center;
  margin: ${props => props.theme.spacing.xl} 0;
`;

const Admin = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const queryClient = useQueryClient();

  // Check if user has admin access
  const { data: dashboardData, isLoading, error } = useQuery(
    'admin-dashboard',
    adminService.getDashboard,
    {
      retry: false,
      onError: (error) => {
        if (error.response?.status === 403) {
          toast.error('Acesso negado. Você não tem permissões de administrador.');
        } else {
          toast.error('Erro ao carregar dados do admin.');
        }
      }
    }
  );

  const tabs = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: FaChartBar
    },
    {
      id: 'users',
      label: 'Usuários',
      icon: FaUsers
    },
    {
      id: 'coupons',
      label: 'Cupons',
      icon: FaTicketAlt
    }
  ];

  if (isLoading) {
    return (
      <AdminContainer>
        <Loading />
      </AdminContainer>
    );
  }

  if (error) {
    return (
      <AdminContainer>
        <ErrorMessage>
          <h3>Acesso Negado</h3>
          <p>Você não tem permissões para acessar esta página.</p>
          <p>Apenas administradores podem visualizar o painel administrativo.</p>
        </ErrorMessage>
      </AdminContainer>
    );
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <AdminDashboard data={dashboardData} />;
      case 'users':
        return <UserManagement />;
      case 'coupons':
        return <CouponManagement />;
      default:
        return <AdminDashboard data={dashboardData} />;
    }
  };

  return (
    <AdminContainer>
      <Header>
        <Title>
          <FaCrown />
          Painel Administrativo
        </Title>
      </Header>

      <TabsContainer>
        {tabs.map(tab => {
          const IconComponent = tab.icon;
          return (
            <Tab
              key={tab.id}
              active={activeTab === tab.id}
              onClick={() => setActiveTab(tab.id)}
            >
              <IconComponent />
              {tab.label}
            </Tab>
          );
        })}
      </TabsContainer>

      <TabContent>
        {renderTabContent()}
      </TabContent>
    </AdminContainer>
  );
};

export default Admin;
