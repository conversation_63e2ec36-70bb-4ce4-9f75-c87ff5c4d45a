import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { FaSearch, Fa<PERSON>ser, FaUserPlus, FaUserMinus, FaNewspaper, FaUsers } from 'react-icons/fa';
import { useSocial } from '../contexts/SocialContext';
import Button from '../components/UI/Button';
import LoadingSpinner from '../components/UI/LoadingSpinner';
import FeedList from '../components/FeedList';

const Container = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: ${props => props.theme.spacing.xl};

  @media (max-width: 768px) {
    padding: ${props => props.theme.spacing.md};
  }
`;

const TabsContainer = styled.div`
  display: flex;
  border-bottom: 2px solid ${props => props.theme.colors.border.light};
  margin-bottom: ${props => props.theme.spacing.xl};

  @media (max-width: 768px) {
    margin-bottom: ${props => props.theme.spacing.lg};
  }
`;

const Tab = styled.button`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
  padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.lg};
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  color: ${props => props.active ? props.theme.colors.primary : props.theme.colors.text.secondary};
  font-size: 1rem;
  font-weight: ${props => props.active ? '600' : '400'};
  cursor: pointer;
  transition: all 0.2s ease;

  ${props => props.active && `
    border-bottom-color: ${props.theme.colors.primary};
  `}

  &:hover {
    color: ${props => props.theme.colors.primary};
  }

  svg {
    font-size: 0.9rem;
  }

  @media (max-width: 768px) {
    padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
    font-size: 0.9rem;

    svg {
      font-size: 0.8rem;
    }
  }
`;

const TabContent = styled.div`
  display: ${props => props.active ? 'block' : 'none'};
`;

const SearchContainer = styled.div`
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const SearchInput = styled.input`
  width: 100%;
  padding: ${props => props.theme.spacing.md};
  border: 2px solid ${props => props.theme.colors.border.light};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }
`;

const UserCard = styled.div`
  display: flex;
  align-items: center;
  padding: ${props => props.theme.spacing.lg};
  border: 1px solid ${props => props.theme.colors.border.light};
  border-radius: ${props => props.theme.borderRadius.md};
  margin-bottom: ${props => props.theme.spacing.md};
  background: ${props => props.theme.colors.background.primary};
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: ${props => props.theme.colors.primary};
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
`;

const Avatar = styled.div`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: ${props => props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: ${props => props.theme.spacing.lg};
  color: white;
  font-size: 1.5rem;
  background-image: ${props => props.avatar ? `url(${props.avatar})` : 'none'};
  background-size: cover;
  background-position: center;
`;

const UserInfo = styled.div`
  flex: 1;
`;

const UserName = styled.h3`
  margin: 0 0 ${props => props.theme.spacing.xs} 0;
  color: ${props => props.theme.colors.text.primary};
  font-size: 1.1rem;
`;

const Username = styled.p`
  margin: 0 0 ${props => props.theme.spacing.xs} 0;
  color: ${props => props.theme.colors.text.secondary};
  font-size: 0.9rem;
`;

const Bio = styled.p`
  margin: 0 0 ${props => props.theme.spacing.xs} 0;
  color: ${props => props.theme.colors.text.secondary};
  font-size: 0.9rem;
`;

const Stats = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  font-size: 0.8rem;
  color: ${props => props.theme.colors.text.secondary};
`;

const ActionButton = styled(Button)`
  margin-left: ${props => props.theme.spacing.md};
  min-width: 120px;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${props => props.theme.spacing.xl};
  color: ${props => props.theme.colors.text.secondary};
`;

const Pagination = styled.div`
  display: flex;
  justify-content: center;
  gap: ${props => props.theme.spacing.md};
  margin-top: ${props => props.theme.spacing.xl};
`;

const UserSearch = () => {
  const navigate = useNavigate();
  const { searchUsers, followUser, unfollowUser, loading } = useSocial();
  const [activeTab, setActiveTab] = useState('feed');
  const [query, setQuery] = useState('');
  const [searchResults, setSearchResults] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTimeout, setSearchTimeout] = useState(null);

  useEffect(() => {
    // Clear timeout on unmount
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTimeout]);

  const handleSearch = async (searchQuery, page = 1) => {
    if (!searchQuery.trim() || searchQuery.length < 2) {
      setSearchResults(null);
      return;
    }

    try {
      const results = await searchUsers(searchQuery, page);
      setSearchResults(results);
      setCurrentPage(page);
    } catch (error) {
      console.error('Search error:', error);
    }
  };

  const handleInputChange = (e) => {
    const value = e.target.value;
    setQuery(value);

    // Clear previous timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Set new timeout for search
    const timeout = setTimeout(() => {
      handleSearch(value);
    }, 500);

    setSearchTimeout(timeout);
  };

  const handleFollowToggle = async (user) => {
    try {
      if (user.is_following) {
        await unfollowUser(user.id);
      } else {
        await followUser(user.id);
      }

      // Update local state with new follow status and counters
      setSearchResults(prev => ({
        ...prev,
        users: prev.users.map(u =>
          u.id === user.id
            ? {
                ...u,
                is_following: !u.is_following,
                followers_count: u.is_following
                  ? Math.max(0, (u.followers_count || 0) - 1)
                  : (u.followers_count || 0) + 1
              }
            : u
        )
      }));

      // Refresh search results to get updated counters from server
      if (query.length >= 2) {
        setTimeout(() => {
          handleSearch(query, currentPage);
        }, 1000);
      }
    } catch (error) {
      console.error('Follow toggle error:', error);
    }
  };

  const handleUserClick = (user) => {
    if (user.username) {
      navigate(`/profile/${user.username}`);
    } else {
      navigate(`/profile/user/${user.id}`);
    }
  };

  const handlePageChange = (page) => {
    handleSearch(query, page);
  };

  return (
    <Container>
      <h1>🌱 Social</h1>

      <TabsContainer>
        <Tab
          active={activeTab === 'feed'}
          onClick={() => setActiveTab('feed')}
        >
          <FaNewspaper />
          Feed
        </Tab>
        <Tab
          active={activeTab === 'search'}
          onClick={() => setActiveTab('search')}
        >
          <FaUsers />
          Buscar Usuários
        </Tab>
      </TabsContainer>

      <TabContent active={activeTab === 'feed'}>
        <FeedList />
      </TabContent>

      <TabContent active={activeTab === 'search'}>
        <SearchContainer>
          <SearchInput
            type="text"
            placeholder="Busque por nome, username ou email..."
            value={query}
            onChange={handleInputChange}
          />
        </SearchContainer>

        {loading && <LoadingSpinner />}

      {searchResults && (
        <>
          <p>{searchResults.total} usuário(s) encontrado(s)</p>
          
          {searchResults.users.length === 0 ? (
            <EmptyState>
              <FaSearch size={48} />
              <p>Nenhum usuário encontrado</p>
            </EmptyState>
          ) : (
            searchResults.users.map(user => (
              <UserCard key={user.id}>
                <Avatar 
                  avatar={user.avatar}
                  onClick={() => handleUserClick(user)}
                >
                  {!user.avatar && <FaUser />}
                </Avatar>
                
                <UserInfo onClick={() => handleUserClick(user)}>
                  <UserName>
                    {user.first_name} {user.last_name}
                  </UserName>
                  {user.username && (
                    <Username>@{user.username}</Username>
                  )}
                  {user.bio && (
                    <Bio>{user.bio}</Bio>
                  )}
                  <Stats>
                    <span>{user.followers_count} seguidores</span>
                    <span>{user.following_count} seguindo</span>
                  </Stats>
                </UserInfo>
                
                <ActionButton
                  variant={user.is_following ? "outline" : "primary"}
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleFollowToggle(user);
                  }}
                >
                  {user.is_following ? (
                    <>
                      <FaUserMinus /> Deixar de seguir
                    </>
                  ) : (
                    <>
                      <FaUserPlus /> Seguir
                    </>
                  )}
                </ActionButton>
              </UserCard>
            ))
          )}

          {searchResults.total > 20 && (
            <Pagination>
              {searchResults.has_prev && (
                <Button
                  variant="outline"
                  onClick={() => handlePageChange(currentPage - 1)}
                >
                  Anterior
                </Button>
              )}
              
              <span>Página {searchResults.page}</span>
              
              {searchResults.has_next && (
                <Button
                  variant="outline"
                  onClick={() => handlePageChange(currentPage + 1)}
                >
                  Próxima
                </Button>
              )}
            </Pagination>
          )}
        </>
      )}

        {!searchResults && !loading && query.length === 0 && (
          <EmptyState>
            <FaSearch size={48} />
            <p>Digite pelo menos 2 caracteres para buscar usuários</p>
          </EmptyState>
        )}
      </TabContent>
    </Container>
  );
};

export default UserSearch;
