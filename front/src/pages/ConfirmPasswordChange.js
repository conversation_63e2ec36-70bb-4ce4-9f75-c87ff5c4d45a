import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { FaCheckCircle, FaTimesCircle, Fa<PERSON><PERSON>ner, FaLock } from 'react-icons/fa';
import { toast } from 'react-toastify';
import Button from '../components/UI/Button';
import userService from '../services/userService';

const Container = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: ${props => props.theme.spacing.lg};
`;

const Card = styled.div`
  background: white;
  border-radius: ${props => props.theme.borderRadius.lg};
  box-shadow: ${props => props.theme.shadows.lg};
  padding: ${props => props.theme.spacing.xl};
  width: 100%;
  max-width: 500px;
  text-align: center;
`;

const Icon = styled.div`
  font-size: 4rem;
  margin-bottom: ${props => props.theme.spacing.lg};
  
  &.success {
    color: #4caf50;
  }
  
  &.error {
    color: #f44336;
  }
  
  &.loading {
    color: ${props => props.theme.colors.primary};
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
`;

const Title = styled.h1`
  color: ${props => props.theme.colors.text};
  margin-bottom: ${props => props.theme.spacing.md};
  font-size: 1.8rem;
`;

const Message = styled.p`
  color: ${props => props.theme.colors.textLight};
  margin-bottom: ${props => props.theme.spacing.lg};
  line-height: 1.6;
`;

const ButtonContainer = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
  justify-content: center;
  flex-wrap: wrap;
`;

const ConfirmPasswordChange = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState('loading'); // loading, success, error
  const [message, setMessage] = useState('');
  const token = searchParams.get('token');

  useEffect(() => {
    const confirmPasswordChange = async () => {
      if (!token) {
        setStatus('error');
        setMessage('Token de confirmação não encontrado. Verifique o link do email.');
        return;
      }

      try {
        await userService.confirmPasswordChange(token);
        setStatus('success');
        setMessage('Senha alterada com sucesso! Você já pode fazer login com a nova senha.');
        toast.success('Senha alterada com sucesso!');
      } catch (error) {
        console.error('Erro ao confirmar alteração de senha:', error);
        setStatus('error');

        let errorMessage = 'Erro ao confirmar alteração de senha. O token pode estar expirado ou inválido.';

        // Tratar diferentes tipos de erro
        if (error.response?.data?.detail) {
          if (typeof error.response.data.detail === 'string') {
            errorMessage = error.response.data.detail;
          } else if (Array.isArray(error.response.data.detail)) {
            // Erro de validação do Pydantic
            errorMessage = 'Token inválido ou malformado.';
          }
        } else if (error.response?.data?.message) {
          errorMessage = error.response.data.message;
        }

        setMessage(errorMessage);
        toast.error(errorMessage);
      }
    };

    confirmPasswordChange();
  }, [token]);

  const handleGoToLogin = () => {
    navigate('/login');
  };

  const handleGoToProfile = () => {
    navigate('/profile');
  };

  const renderIcon = () => {
    switch (status) {
      case 'loading':
        return <Icon className="loading"><FaSpinner /></Icon>;
      case 'success':
        return <Icon className="success"><FaCheckCircle /></Icon>;
      case 'error':
        return <Icon className="error"><FaTimesCircle /></Icon>;
      default:
        return <Icon><FaLock /></Icon>;
    }
  };

  const renderTitle = () => {
    switch (status) {
      case 'loading':
        return 'Confirmando alteração...';
      case 'success':
        return 'Senha alterada com sucesso!';
      case 'error':
        return 'Erro na confirmação';
      default:
        return 'Confirmação de alteração de senha';
    }
  };

  const renderButtons = () => {
    if (status === 'loading') {
      return null;
    }

    return (
      <ButtonContainer>
        {status === 'success' ? (
          <>
            <Button onClick={handleGoToLogin}>
              Fazer Login
            </Button>
            <Button variant="secondary" onClick={handleGoToProfile}>
              Ir para Perfil
            </Button>
          </>
        ) : (
          <>
            <Button onClick={handleGoToLogin}>
              Ir para Login
            </Button>
            <Button variant="secondary" onClick={() => navigate('/')}>
              Página Inicial
            </Button>
          </>
        )}
      </ButtonContainer>
    );
  };

  return (
    <Container>
      <Card>
        {renderIcon()}
        <Title>{renderTitle()}</Title>
        <Message>{message}</Message>
        {renderButtons()}
      </Card>
    </Container>
  );
};

export default ConfirmPasswordChange;
