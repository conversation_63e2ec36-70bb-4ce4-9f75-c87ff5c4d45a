import React, { useState } from 'react';
import styled from 'styled-components';
import { <PERSON>a<PERSON>ser, FaEdit, FaLock, FaTrash, FaExclamationTriangle, FaExternalLinkAlt, FaCopy } from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import Button from '../components/UI/Button';
import ProfileEditModal from '../components/ProfileEditModal';
import PasswordChangeModal from '../components/PasswordChangeModal';
import userService from '../services/userService';

const ProfileContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: ${props => props.theme.spacing.lg};
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;

  @media (max-width: 768px) {
    padding: ${props => props.theme.spacing.md};
  }

  @media (max-width: 480px) {
    padding: ${props => props.theme.spacing.sm};
  }
`;

const ProfileCard = styled.div`
  background-color: ${props => props.theme.colors.surface};
  padding: ${props => props.theme.spacing.xl};
  border-radius: ${props => props.theme.borderRadius.lg};
  box-shadow: ${props => props.theme.shadows.sm};
  width: 100%;
  box-sizing: border-box;

  @media (max-width: 768px) {
    padding: ${props => props.theme.spacing.lg};
  }

  @media (max-width: 480px) {
    padding: ${props => props.theme.spacing.md};
  }
`;

const ProfileHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.lg};
  margin-bottom: ${props => props.theme.spacing.xl};
  padding-bottom: ${props => props.theme.spacing.lg};
  border-bottom: 1px solid ${props => props.theme.colors.divider};
  width: 100%;
  box-sizing: border-box;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: ${props => props.theme.spacing.md};
  }

  @media (max-width: 480px) {
    gap: ${props => props.theme.spacing.sm};
    margin-bottom: ${props => props.theme.spacing.lg};
    padding-bottom: ${props => props.theme.spacing.md};
  }
`;

const Avatar = styled.div`
  width: 80px;
  height: 80px;
  min-width: 80px;
  min-height: 80px;
  border-radius: 50%;
  background-color: ${props => props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: ${props => props.theme.typography.fontSize.xl};
  font-weight: ${props => props.theme.typography.fontWeight.bold};
  flex-shrink: 0;
  box-sizing: border-box;

  img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
  }

  @media (max-width: 480px) {
    width: 70px;
    height: 70px;
    min-width: 70px;
    min-height: 70px;
    font-size: ${props => props.theme.typography.fontSize.lg};
  }
`;

const ProfileInfo = styled.div`
  flex: 1;
  min-width: 0;

  @media (max-width: 768px) {
    flex: none;
    width: 100%;
    text-align: center;
  }
`;

const ProfileName = styled.h1`
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing.xs};
  word-wrap: break-word;
  overflow-wrap: break-word;

  @media (max-width: 480px) {
    font-size: ${props => props.theme.typography.fontSize.xl};
  }
`;

const ProfileEmail = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  margin-bottom: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;

  @media (max-width: 480px) {
    font-size: ${props => props.theme.typography.fontSize.sm};
  }
`;

const ProfileDetails = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${props => props.theme.spacing.lg};
  width: 100%;
  box-sizing: border-box;

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: ${props => props.theme.spacing.md};
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: ${props => props.theme.spacing.sm};
  }
`;

const DetailItem = styled.div`
  margin-bottom: ${props => props.theme.spacing.md};
`;

const DetailLabel = styled.strong`
  color: ${props => props.theme.colors.text.primary};
  display: block;
  margin-bottom: ${props => props.theme.spacing.xs};
`;

const DetailValue = styled.span`
  color: ${props => props.theme.colors.text.secondary};
  word-wrap: break-word;
  overflow-wrap: break-word;
`;

const ProfileActions = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  justify-content: flex-end;
  flex-wrap: wrap;
  margin-top: ${props => props.theme.spacing.xl};
  padding-top: ${props => props.theme.spacing.lg};
  border-top: 1px solid ${props => props.theme.colors.border};

  @media (max-width: 768px) {
    flex-direction: column;
    gap: ${props => props.theme.spacing.sm};
    justify-content: stretch;
  }
`;

const DeleteButton = styled(Button)`
  background-color: #dc3545;
  border-color: #dc3545;
  color: white;

  &:hover {
    background-color: #c82333;
    border-color: #bd2130;
  }

  &:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
  }
`;

const ConfirmationModal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: ${props => props.theme.spacing.md};
`;

const ConfirmationContent = styled.div`
  background-color: white;
  padding: ${props => props.theme.spacing.xl};
  border-radius: ${props => props.theme.borderRadius.lg};
  max-width: 500px;
  width: 100%;
  box-shadow: ${props => props.theme.shadows.lg};
`;

const ConfirmationTitle = styled.h3`
  color: #dc3545;
  margin-bottom: ${props => props.theme.spacing.md};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const ConfirmationText = styled.div`
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing.lg};
  line-height: 1.6;

  ul {
    margin: ${props => props.theme.spacing.md} 0;
    padding-left: ${props => props.theme.spacing.lg};
  }

  li {
    margin-bottom: ${props => props.theme.spacing.xs};
  }
`;

const ConfirmationActions = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  justify-content: flex-end;

  @media (max-width: 480px) {
    flex-direction: column;
  }
`;

const PublicProfileLink = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  padding: ${props => props.theme.spacing.sm};
  background-color: ${props => props.theme.colors.background};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  margin-top: ${props => props.theme.spacing.xs};

  @media (max-width: 480px) {
    flex-direction: column;
    align-items: stretch;
    gap: ${props => props.theme.spacing.xs};
  }
`;

const PublicProfileUrl = styled.a`
  color: ${props => props.theme.colors.primary};
  text-decoration: none;
  flex: 1;
  font-family: monospace;
  font-size: ${props => props.theme.typography.fontSize.sm};
  word-break: break-all;

  &:hover {
    text-decoration: underline;
  }
`;

const CopyButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.text.secondary};
  cursor: pointer;
  padding: ${props => props.theme.spacing.xs};
  border-radius: ${props => props.theme.borderRadius.sm};
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: ${props => props.theme.colors.background};
    color: ${props => props.theme.colors.primary};
  }
`;



const Profile = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [showEditModal, setShowEditModal] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  if (!user) {
    return (
      <ProfileContainer>
        <div>Carregando perfil...</div>
      </ProfileContainer>
    );
  }

  const getInitials = (firstName, lastName) => {
    return `${firstName?.charAt(0) || ''}${lastName?.charAt(0) || ''}`.toUpperCase();
  };

  const handleEditSuccess = () => {
    setShowEditModal(false);
  };

  const handleDeleteAccount = async () => {
    setIsDeleting(true);
    try {
      await userService.deleteAccount();
      toast.success('Conta excluída com sucesso!');
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Erro ao excluir conta:', error);
      toast.error('Erro ao excluir conta. Tente novamente.');
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  const handleCopyProfileLink = async () => {
    if (!user.username) {
      toast.error('Você precisa definir um username para ter um perfil público');
      return;
    }

    const profileUrl = `${window.location.origin}/profile/${user.username}`;

    try {
      await navigator.clipboard.writeText(profileUrl);
      toast.success('Link copiado para a área de transferência!');
    } catch (error) {
      // Fallback para navegadores que não suportam clipboard API
      const textArea = document.createElement('textarea');
      textArea.value = profileUrl;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      toast.success('Link copiado para a área de transferência!');
    }
  };



  return (
    <ProfileContainer>
      <ProfileCard>
        <ProfileHeader>
          <Avatar>
            {user.avatar ? (
              <img src={user.avatar} alt="Avatar" />
            ) : (
              getInitials(user.first_name, user.last_name) || <FaUser />
            )}
          </Avatar>
          <ProfileInfo>
            <ProfileName>{user.full_name || `${user.first_name} ${user.last_name}`}</ProfileName>
            <ProfileEmail>{user.email}</ProfileEmail>
          </ProfileInfo>
        </ProfileHeader>

        <ProfileDetails>
          <DetailItem>
            <DetailLabel>Email:</DetailLabel>
            <DetailValue>{user.email}</DetailValue>
          </DetailItem>

          <DetailItem>
            <DetailLabel>Cidade:</DetailLabel>
            <DetailValue>{user.city || ''}</DetailValue>
          </DetailItem>

          <DetailItem>
            <DetailLabel>Estado:</DetailLabel>
            <DetailValue>{user.state || ''}</DetailValue>
          </DetailItem>

          <DetailItem>
            <DetailLabel>País:</DetailLabel>
            <DetailValue>{user.country || ''}</DetailValue>
          </DetailItem>

          <DetailItem style={{ gridColumn: '1 / -1' }}>
            <DetailLabel>Bio:</DetailLabel>
            <DetailValue>{user.bio || ''}</DetailValue>
          </DetailItem>

          {user.username && (
            <DetailItem style={{ gridColumn: '1 / -1' }}>
              <DetailLabel>Perfil Público:</DetailLabel>
              <PublicProfileLink>
                <PublicProfileUrl
                  href={`/profile/${user.username}`}
                  onClick={(e) => {
                    e.preventDefault();
                    navigate(`/profile/${user.username}`);
                  }}
                >
                  {window.location.origin}/profile/{user.username}
                  <FaExternalLinkAlt style={{ marginLeft: '8px', fontSize: '12px' }} />
                </PublicProfileUrl>
                <CopyButton
                  onClick={handleCopyProfileLink}
                  title="Copiar link"
                >
                  <FaCopy />
                </CopyButton>
              </PublicProfileLink>
            </DetailItem>
          )}

          {!user.username && (
            <DetailItem style={{ gridColumn: '1 / -1' }}>
              <DetailLabel>Perfil Público:</DetailLabel>
              <DetailValue style={{ color: '#e74c3c' }}>
                Defina um username para ter um perfil público
              </DetailValue>
            </DetailItem>
          )}
        </ProfileDetails>

        <ProfileActions>
          {user.username && (
            <Button
              variant="outline"
              onClick={() => navigate(`/profile/${user.username}`)}
            >
              <FaExternalLinkAlt />
              Ver Perfil Público
            </Button>
          )}
          <Button onClick={() => setShowEditModal(true)}>
            <FaEdit />
            Editar Perfil
          </Button>
          <Button
            variant="secondary"
            onClick={() => setShowPasswordModal(true)}
          >
            <FaLock />
            Alterar Senha
          </Button>
          <DeleteButton
            onClick={() => setShowDeleteConfirm(true)}
            disabled={isDeleting}
          >
            <FaTrash />
            {isDeleting ? 'Excluindo...' : 'Excluir Conta'}
          </DeleteButton>
        </ProfileActions>
      </ProfileCard>

      <ProfileEditModal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        onSuccess={handleEditSuccess}
      />

      <PasswordChangeModal
        isOpen={showPasswordModal}
        onClose={() => setShowPasswordModal(false)}
      />

      {showDeleteConfirm && (
        <ConfirmationModal onClick={() => !isDeleting && setShowDeleteConfirm(false)}>
          <ConfirmationContent onClick={(e) => e.stopPropagation()}>
            <ConfirmationTitle>
              <FaExclamationTriangle />
              Confirmar Exclusão de Conta
            </ConfirmationTitle>
            <ConfirmationText>
              <strong>⚠️ Esta ação é irreversível!</strong>
              <br /><br />
              Ao confirmar, os seguintes dados serão permanentemente excluídos:
              <ul>
                <li>Todos os seus bonsais e suas fotos</li>
                <li>Todos os registros de cuidados</li>
                <li>Todos os lembretes</li>
                <li>Assinaturas ativas e futuras cobranças</li>
                <li>Seu perfil e dados pessoais</li>
                <li>Histórico de assinatura e pagamentos</li>
              </ul>
              <strong>Não será possível recuperar estes dados após a exclusão.</strong>
              <br /><br />
              Tem certeza que deseja continuar?
            </ConfirmationText>
            <ConfirmationActions>
              <Button
                variant="secondary"
                onClick={() => setShowDeleteConfirm(false)}
                disabled={isDeleting}
              >
                Cancelar
              </Button>
              <DeleteButton
                onClick={handleDeleteAccount}
                disabled={isDeleting}
              >
                <FaTrash />
                {isDeleting ? 'Excluindo...' : 'Sim, Excluir Conta'}
              </DeleteButton>
            </ConfirmationActions>
          </ConfirmationContent>
        </ConfirmationModal>
      )}

    </ProfileContainer>
  );
};

export default Profile;
