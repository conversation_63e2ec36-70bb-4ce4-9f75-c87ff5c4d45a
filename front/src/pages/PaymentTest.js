import React, { useState } from 'react';
import styled from 'styled-components';
import { toast } from 'react-toastify';
import api from '../services/api';

const Container = styled.div`
  max-width: 800px;
  margin: 2rem auto;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;

const Title = styled.h1`
  color: #2d5016;
  text-align: center;
  margin-bottom: 2rem;
`;

const TestSection = styled.div`
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
`;

const Button = styled.button`
  background: #28a745;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  margin: 0.5rem;
  
  &:hover {
    background: #218838;
  }
  
  &:disabled {
    background: #6c757d;
    cursor: not-allowed;
  }
`;

const ResultBox = styled.pre`
  background: #f1f3f4;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 1rem;
  margin-top: 1rem;
  white-space: pre-wrap;
  font-size: 0.9rem;
  max-height: 300px;
  overflow-y: auto;
`;

const PaymentTest = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);

  const testPaymentData = async () => {
    setLoading(true);
    try {
      const response = await api.get('/api/v1/subscriptions/test/payment-data');
      setResult(JSON.stringify(response.data, null, 2));
    } catch (error) {
      console.error('Error:', error);
      setResult(`Error: ${error.response?.data?.detail || error.message}`);
      toast.error('Erro ao testar dados de pagamento');
    } finally {
      setLoading(false);
    }
  };

  const testSimplePayment = async () => {
    setLoading(true);
    try {
      const response = await api.post('/api/v1/subscriptions/test/simple-payment');
      setResult(JSON.stringify(response.data, null, 2));
      
      if (response.data.success && response.data.init_point) {
        toast.success('Link de pagamento criado com sucesso!');
        // Opcional: abrir o link
        // window.open(response.data.init_point, '_blank');
      } else {
        toast.error('Falha ao criar link de pagamento');
      }
    } catch (error) {
      console.error('Error:', error);
      setResult(`Error: ${error.response?.data?.detail || error.message}`);
      toast.error('Erro ao testar pagamento simples');
    } finally {
      setLoading(false);
    }
  };

  const testMercadoPagoConfig = async () => {
    setLoading(true);
    try {
      const response = await api.get('/api/v1/subscriptions/test');
      setResult(JSON.stringify(response.data, null, 2));
    } catch (error) {
      console.error('Error:', error);
      setResult(`Error: ${error.response?.data?.detail || error.message}`);
      toast.error('Erro ao testar configuração');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container>
      <Title>🧪 Teste de Integração Asaas</Title>
      
      <TestSection>
        <h3>1. Configuração do Asaas</h3>
        <p>Verificar se as credenciais estão corretas e o modo de operação.</p>
        <Button onClick={testMercadoPagoConfig} disabled={loading}>
          Testar Configuração
        </Button>
      </TestSection>

      <TestSection>
        <h3>2. Dados de Pagamento</h3>
        <p>Verificar quais dados serão enviados ao Asaas.</p>
        <Button onClick={testPaymentData} disabled={loading}>
          Testar Dados de Pagamento
        </Button>
      </TestSection>

      <TestSection>
        <h3>3. Pagamento Simples</h3>
        <p>Criar um link de pagamento de R$ 5,00 para teste (valor mínimo do Asaas).</p>
        <Button onClick={testSimplePayment} disabled={loading}>
          Testar Pagamento Simples
        </Button>
      </TestSection>

      {result && (
        <TestSection>
          <h3>Resultado:</h3>
          <ResultBox>{result}</ResultBox>
        </TestSection>
      )}

      <TestSection>
        <h4>💡 Informações do Asaas:</h4>
        <ul>
          <li><strong>Valor Mínimo:</strong> R$ 5,00 por parcela</li>
          <li><strong>CPF Obrigatório:</strong> Usa CPF de teste em sandbox</li>
          <li><strong>Sandbox:</strong> Ambiente de teste ativo</li>
          <li><strong>Métodos:</strong> PIX, Cartão de Crédito, Boleto</li>
          <li><strong>Checkout:</strong> Direto no site do Asaas</li>
          <li><strong>Conversão:</strong> Melhor UX que Mercado Pago</li>
        </ul>
      </TestSection>
    </Container>
  );
};

export default PaymentTest;
