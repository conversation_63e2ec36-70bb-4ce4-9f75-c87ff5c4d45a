import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate, Link } from 'react-router-dom';
import styled from 'styled-components';
import { FaCheckCircle, FaTimesCircle, FaSpinner } from 'react-icons/fa';
import { toast } from 'react-toastify';
import api from '../services/api';
import Button from '../components/UI/Button';

const Container = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: ${props => props.theme.spacing.md};
`;

const Card = styled.div`
  background: white;
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.xl};
  box-shadow: ${props => props.theme.shadows.lg};
  text-align: center;
  max-width: 500px;
  width: 100%;
`;

const Icon = styled.div`
  font-size: 4rem;
  margin-bottom: ${props => props.theme.spacing.lg};
  
  &.success {
    color: #28a745;
  }
  
  &.error {
    color: #dc3545;
  }
  
  &.loading {
    color: #007bff;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
`;

const Title = styled.h1`
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing.md};
  font-size: 1.8rem;
`;

const Message = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  margin-bottom: ${props => props.theme.spacing.lg};
  line-height: 1.6;
`;

const Actions = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  justify-content: center;
  flex-wrap: wrap;
`;

const ActivateAccount = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState('loading'); // loading, success, error
  const [message, setMessage] = useState('');

  const token = searchParams.get('token');

  useEffect(() => {
    if (!token) {
      setStatus('error');
      setMessage('Token de ativação não encontrado na URL.');
      return;
    }

    activateAccount();
  }, [token]);

  const activateAccount = async () => {
    try {
      setStatus('loading');
      
      const response = await api.post('/api/v1/auth/activate-account', {
        token: token
      });

      setStatus('success');
      setMessage(response.data.message || 'Conta ativada com sucesso!');
      toast.success('Conta ativada com sucesso!');

    } catch (error) {
      console.error('Erro ao ativar conta:', error);
      setStatus('error');
      
      const errorMessage = error.response?.data?.detail || 
                          error.response?.data?.message || 
                          'Erro ao ativar conta. O token pode estar expirado ou inválido.';
      
      setMessage(errorMessage);
      toast.error(errorMessage);
    }
  };

  const renderIcon = () => {
    switch (status) {
      case 'loading':
        return <Icon className="loading"><FaSpinner /></Icon>;
      case 'success':
        return <Icon className="success"><FaCheckCircle /></Icon>;
      case 'error':
        return <Icon className="error"><FaTimesCircle /></Icon>;
      default:
        return null;
    }
  };

  const renderTitle = () => {
    switch (status) {
      case 'loading':
        return 'Ativando sua conta...';
      case 'success':
        return 'Conta Ativada!';
      case 'error':
        return 'Erro na Ativação';
      default:
        return '';
    }
  };

  return (
    <Container>
      <Card>
        {renderIcon()}
        <Title>{renderTitle()}</Title>
        <Message>{message}</Message>
        
        <Actions>
          {status === 'success' && (
            <Button onClick={() => navigate('/login')}>
              Fazer Login
            </Button>
          )}
          
          {status === 'error' && (
            <>
              <Button 
                variant="secondary" 
                onClick={() => navigate('/register')}
              >
                Criar Nova Conta
              </Button>
              <Button onClick={() => navigate('/login')}>
                Tentar Login
              </Button>
            </>
          )}
          
          {status === 'loading' && (
            <Button 
              variant="secondary" 
              onClick={() => navigate('/')}
            >
              Voltar ao Início
            </Button>
          )}
        </Actions>
        
        <div style={{ marginTop: '2rem', paddingTop: '1rem', borderTop: '1px solid #eee' }}>
          <Link 
            to="/" 
            style={{ 
              color: '#666', 
              textDecoration: 'none',
              fontSize: '0.9rem'
            }}
          >
            ← Voltar ao MeuBonsai
          </Link>
        </div>
      </Card>
    </Container>
  );
};

export default ActivateAccount;
