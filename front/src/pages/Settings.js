import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FaShieldAlt, FaEye, FaEyeSlash, FaUsers, FaSearch, FaLeaf, FaCheck, FaTimes, FaRandom, FaSpinner, FaCog, FaTrashAlt, FaChevronDown, FaChevronUp, FaDownload, FaFileAlt } from 'react-icons/fa';
import { useSocial } from '../contexts/SocialContext';
import { useAuth } from '../contexts/AuthContext';
import Button from '../components/UI/Button';
import LoadingSpinner from '../components/UI/LoadingSpinner';
import { toast } from 'react-toastify';
import dataExportService from '../services/dataExportService';

const Container = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: ${props => props.theme.spacing.xl};
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const Title = styled.h1`
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing.md};
`;

const Subtitle = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  font-size: 1.1rem;
`;

const SettingsCard = styled.div`
  background: ${props => props.theme.colors.background.primary};
  border: 1px solid ${props => props.theme.colors.border.light};
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.xl};
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const SectionTitle = styled.h2`
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing.lg};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.md};
  font-size: 1.3rem;
`;

const SettingItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: ${props => props.theme.spacing.lg} 0;
  border-bottom: 1px solid ${props => props.theme.colors.border.light};
  
  &:last-child {
    border-bottom: none;
  }
`;

const SettingInfo = styled.div`
  flex: 1;
  margin-right: ${props => props.theme.spacing.lg};
`;

const SettingLabel = styled.h3`
  color: ${props => props.theme.colors.text.primary};
  margin: 0 0 ${props => props.theme.spacing.xs} 0;
  font-size: 1.1rem;
`;

const SettingDescription = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.4;
`;

const Toggle = styled.button`
  width: 60px;
  height: 30px;
  border-radius: 15px;
  border: none;
  background: ${props => props.active ? props.theme.colors.success : props.theme.colors.border.light};
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:before {
    content: '';
    position: absolute;
    width: 26px;
    height: 26px;
    border-radius: 50%;
    background: white;
    top: 2px;
    left: ${props => props.active ? '32px' : '2px'};
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
`;

const UsernameSection = styled.div`
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const UsernameContainer = styled.div`
  position: relative;
  margin-bottom: ${props => props.theme.spacing.md};
`;

const UsernameInput = styled.input`
  width: 100%;
  padding: ${props => props.theme.spacing.md};
  padding-right: 100px;
  border: 2px solid ${props =>
    props.isValid === false ? props.theme.colors.error :
    props.isValid === true ? props.theme.colors.success :
    props.theme.colors.border.light
  };
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: 1rem;
  transition: all 0.2s ease;

  &:focus {
    outline: none;
    border-color: ${props =>
      props.isValid === false ? props.theme.colors.error :
      props.isValid === true ? props.theme.colors.success :
      props.theme.colors.primary
    };
  }
`;

const UsernameActions = styled.div`
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
`;

const ValidationIcon = styled.div`
  color: ${props =>
    props.isChecking ? props.theme.colors.primary :
    props.isValid === true ? props.theme.colors.success :
    props.isValid === false ? props.theme.colors.error :
    props.theme.colors.text.secondary
  };
  font-size: 1.2rem;

  ${props => props.isChecking && `
    animation: spin 1s linear infinite;
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
  `}
`;

const GenerateButton = styled.button`
  background: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: ${props => props.theme.borderRadius.sm};
  padding: 4px 8px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${props => props.theme.colors.primaryDark};
  }
`;

const UsernameHelp = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  font-size: 0.9rem;
  margin: 0 0 ${props => props.theme.spacing.xs} 0;
`;

const ValidationMessage = styled.p`
  color: ${props =>
    props.type === 'error' ? props.theme.colors.error :
    props.type === 'success' ? props.theme.colors.success :
    props.theme.colors.text.secondary
  };
  font-size: 0.8rem;
  margin: ${props => props.theme.spacing.xs} 0 ${props => props.theme.spacing.md} 0;
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
`;

const SaveButton = styled(Button)`
  margin-top: ${props => props.theme.spacing.lg};
`;

const CollapsibleSection = styled.div`
  border: 1px solid ${props => props.theme.colors.border.light};
  border-radius: ${props => props.theme.borderRadius.lg};
  margin-bottom: ${props => props.theme.spacing.lg};
  overflow: hidden;
`;

const SectionHeader = styled.button`
  width: 100%;
  padding: ${props => props.theme.spacing.lg};
  background: ${props => props.theme.colors.background.primary};
  border: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: ${props => props.theme.colors.background.secondary};
  }
`;

const SectionHeaderTitle = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.md};
  color: ${props => props.theme.colors.text.primary};
  font-size: 1.2rem;
  font-weight: 600;
`;

const SectionContent = styled.div`
  padding: ${props => props.theme.spacing.lg};
  background: ${props => props.theme.colors.background.primary};
  border-top: 1px solid ${props => props.theme.colors.border.light};
  display: ${props => props.isOpen ? 'block' : 'none'};
`;

const DangerZone = styled.div`
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.lg};
  margin-top: ${props => props.theme.spacing.xl};
`;

const DangerTitle = styled.h3`
  color: #e53e3e;
  margin: 0 0 ${props => props.theme.spacing.md} 0;
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const DangerDescription = styled.p`
  color: #744210;
  margin: 0 0 ${props => props.theme.spacing.lg} 0;
  font-size: 0.9rem;
  line-height: 1.5;
`;

const DangerButton = styled(Button)`
  background: #e53e3e;
  border-color: #e53e3e;

  &:hover {
    background: #c53030;
    border-color: #c53030;
  }
`;

const Settings = () => {
  const { user, deleteAccount } = useAuth();
  const { privacySettings, updatePrivacySettings, loading } = useSocial();
  const [localSettings, setLocalSettings] = useState({
    is_public: true,
    allow_care_sharing: true,
    allow_search: true
  });
  const [hasChanges, setHasChanges] = useState(false);
  const [isPrivacyOpen, setIsPrivacyOpen] = useState(false);
  const [isAccountOpen, setIsAccountOpen] = useState(false);
  const [isDataOpen, setIsDataOpen] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);



  useEffect(() => {
    if (privacySettings) {
      setLocalSettings(privacySettings);
    }
  }, [privacySettings]);

  const handleToggle = (setting) => {
    const newSettings = {
      ...localSettings,
      [setting]: !localSettings[setting]
    };
    setLocalSettings(newSettings);
    setHasChanges(true);
  };

  const handleSave = async () => {
    try {
      await updatePrivacySettings(localSettings);
      setHasChanges(false);
      toast.success('Configurações salvas com sucesso!');
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Erro ao salvar configurações');
    }
  };

  const handleDeleteAccount = async () => {
    const confirmed = window.confirm(
      'Tem certeza que deseja excluir sua conta?\n\nEsta ação é IRREVERSÍVEL e removerá permanentemente:\n• Todos os seus dados pessoais\n• Todas as suas plantas e fotos\n• Histórico de cuidados\n• Assinatura ativa (se houver)\n• Conexões sociais (seguidores/seguindo)'
    );

    if (confirmed) {
      const doubleConfirm = window.confirm(
        'ÚLTIMA CONFIRMAÇÃO:\n\nVocê tem CERTEZA ABSOLUTA que deseja excluir sua conta?\n\nEsta ação NÃO PODE ser desfeita!'
      );

      if (doubleConfirm) {
        try {
          await deleteAccount();
          toast.success('Conta excluída com sucesso');
        } catch (error) {
          console.error('Error deleting account:', error);
          toast.error('Erro ao excluir conta');
        }
      }
    }
  };

  const handleDownloadData = async () => {
    setIsDownloading(true);
    try {
      const result = await dataExportService.downloadUserData();
      toast.success(`Dados exportados com sucesso! Arquivo: ${result.filename}`);
    } catch (error) {
      console.error('Error downloading data:', error);
      toast.error(error.message || 'Erro ao baixar dados');
    } finally {
      setIsDownloading(false);
    }
  };

  if (loading && !privacySettings) {
    return (
      <Container>
        <LoadingSpinner />
      </Container>
    );
  }

  return (
    <Container>
      <Header>
        <Title>
          <FaCog /> Preferências
        </Title>
        <Subtitle>
          Gerencie suas preferências de privacidade e conta
        </Subtitle>
      </Header>

      {/* Seção de Configurações de Privacidade */}
      <CollapsibleSection>
        <SectionHeader onClick={() => setIsPrivacyOpen(!isPrivacyOpen)}>
          <SectionHeaderTitle>
            <FaShieldAlt />
            Configurações de Privacidade
          </SectionHeaderTitle>
          {isPrivacyOpen ? <FaChevronUp /> : <FaChevronDown />}
        </SectionHeader>

        <SectionContent isOpen={isPrivacyOpen}>
          <SettingItem>
            <SettingInfo>
              <SettingLabel>Perfil Público</SettingLabel>
              <SettingDescription>
                Permite que outros usuários vejam seu perfil, suas plantas públicas e informações básicas.
                Se desabilitado, seu perfil ficará completamente privado.
              </SettingDescription>
            </SettingInfo>
            <Toggle
              active={localSettings.is_public}
              onClick={() => handleToggle('is_public')}
            />
          </SettingItem>

          <SettingItem>
            <SettingInfo>
              <SettingLabel>Aparecer na Busca</SettingLabel>
              <SettingDescription>
                Permite que outros usuários encontrem você através da busca por nome, username ou email.
              </SettingDescription>
            </SettingInfo>
            <Toggle
              active={localSettings.allow_search}
              onClick={() => handleToggle('allow_search')}
            />
          </SettingItem>

          <SettingItem>
            <SettingInfo>
              <SettingLabel>Permitir Ver Cuidados</SettingLabel>
              <SettingDescription>
                Permite que seus seguidores vejam o histórico de cuidados das suas plantas públicas.
                Se desabilitado, apenas as fotos da galeria serão visíveis.
              </SettingDescription>
            </SettingInfo>
            <Toggle
              active={localSettings.allow_care_sharing}
              onClick={() => handleToggle('allow_care_sharing')}
            />
          </SettingItem>

          <div style={{ textAlign: 'center', marginTop: '1.5rem' }}>
            <SaveButton
              variant="primary"
              onClick={handleSave}
              disabled={!hasChanges || loading}
            >
              {loading ? 'Salvando...' : 'Salvar Configurações de Privacidade'}
            </SaveButton>
          </div>
        </SectionContent>
      </CollapsibleSection>

      {/* Seção de Dados Pessoais */}
      <CollapsibleSection>
        <SectionHeader onClick={() => setIsDataOpen(!isDataOpen)}>
          <SectionHeaderTitle>
            <FaFileAlt />
            Meus Dados Pessoais
          </SectionHeaderTitle>
          {isDataOpen ? <FaChevronUp /> : <FaChevronDown />}
        </SectionHeader>

        <SectionContent isOpen={isDataOpen}>
          <SettingItem>
            <SettingInfo>
              <SettingLabel>Baixar Meus Dados</SettingLabel>
              <SettingDescription>
                Faça o download de todos os seus dados pessoais em formato JSON, incluindo:
                plantas, fotos, cuidados, lembretes, assinaturas e configurações.
                Este arquivo contém todas as informações que temos sobre você.
              </SettingDescription>
            </SettingInfo>
            <Button
              variant="secondary"
              onClick={handleDownloadData}
              disabled={isDownloading}
              style={{ minWidth: '140px' }}
            >
              {isDownloading ? (
                <>
                  <FaSpinner style={{ animation: 'spin 1s linear infinite' }} />
                  Baixando...
                </>
              ) : (
                <>
                  <FaDownload />
                  Baixar Dados
                </>
              )}
            </Button>
          </SettingItem>

          <div style={{
            background: '#e3f2fd',
            border: '1px solid #2196f3',
            borderRadius: '8px',
            padding: '16px',
            marginTop: '16px',
            fontSize: '0.9rem',
            color: '#1565c0'
          }}>
            <strong>📋 Sobre a Portabilidade de Dados (LGPD)</strong>
            <br />
            Você tem o direito de receber seus dados pessoais em formato estruturado e legível.
            O arquivo JSON contém todas as informações que coletamos sobre você durante o uso da plataforma.
          </div>
        </SectionContent>
      </CollapsibleSection>

      {/* Seção de Gerenciamento de Conta */}
      <CollapsibleSection>
        <SectionHeader onClick={() => setIsAccountOpen(!isAccountOpen)}>
          <SectionHeaderTitle>
            <FaUsers />
            Gerenciamento de Conta
          </SectionHeaderTitle>
          {isAccountOpen ? <FaChevronUp /> : <FaChevronDown />}
        </SectionHeader>

        <SectionContent isOpen={isAccountOpen}>
          <SettingItem>
            <SettingInfo>
              <SettingLabel>Política de Privacidade</SettingLabel>
              <SettingDescription>
                Consulte nossa política de privacidade para entender como tratamos seus dados pessoais
                e quais são seus direitos de acordo com a LGPD.
              </SettingDescription>
            </SettingInfo>
            <Button
              variant="secondary"
              onClick={() => window.open('/privacy-policy', '_blank')}
            >
              <FaShieldAlt />
              Ver Política
            </Button>
          </SettingItem>

          <SettingItem>
            <SettingInfo>
              <SettingLabel>Termos de Uso</SettingLabel>
              <SettingDescription>
                Consulte nossos termos de uso para entender as regras e condições
                para utilização da plataforma MeuBonsai.App.
              </SettingDescription>
            </SettingInfo>
            <Button
              variant="secondary"
              onClick={() => window.open('/terms-of-service', '_blank')}
            >
              <FaFileAlt />
              Ver Termos
            </Button>
          </SettingItem>

          <DangerZone>
            <DangerTitle>
              <FaTrashAlt />
              Zona de Perigo
            </DangerTitle>
            <DangerDescription>
              <strong>Excluir Conta:</strong> Esta ação é irreversível e removerá permanentemente:
              <br />• Todos os seus dados pessoais
              <br />• Todas as suas plantas e fotos
              <br />• Histórico de cuidados
              <br />• Assinatura ativa (se houver)
              <br />• Conexões sociais (seguidores/seguindo)
            </DangerDescription>
            <DangerButton
              variant="danger"
              onClick={handleDeleteAccount}
            >
              <FaTrashAlt /> Excluir Minha Conta
            </DangerButton>
          </DangerZone>
        </SectionContent>
      </CollapsibleSection>

      {/* Informações sobre Privacidade */}
      <SettingsCard style={{ marginTop: '2rem', background: '#f8f9fa' }}>
        <SectionTitle style={{ color: '#6c757d' }}>
          <FaShieldAlt /> Como Funciona a Privacidade
        </SectionTitle>

        <div style={{ color: '#6c757d', fontSize: '0.9rem', lineHeight: '1.6' }}>
          <p><strong>Perfil Público:</strong> Outros usuários podem ver suas informações básicas e plantas marcadas como públicas.</p>

          <p><strong>Plantas Públicas:</strong> Cada planta pode ser configurada individualmente como pública ou privada.</p>

          <p><strong>Cuidados:</strong> Apenas seguidores podem ver o histórico de cuidados, e apenas se você permitir.</p>

          <p><strong>Busca:</strong> Você pode escolher se aparece ou não nos resultados de busca de outros usuários.</p>
        </div>
      </SettingsCard>
    </Container>
  );
};

export default Settings;
