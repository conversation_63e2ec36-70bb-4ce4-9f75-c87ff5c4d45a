import React, { useState } from 'react';
import styled from 'styled-components';
import { FaEnvelope, FaBug, FaLightbulb, FaComments, FaPaperPlane, FaArrowLeft } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';
import Button from '../components/UI/Button';
import contactService from '../services/contactService';

const Container = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${props => props.theme.spacing.lg};
`;

const Card = styled.div`
  background: white;
  border-radius: ${props => props.theme.borderRadius.lg};
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: ${props => props.theme.spacing.xl};
  width: 100%;
  max-width: 600px;
  position: relative;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const Icon = styled.div`
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto ${props => props.theme.spacing.lg};
  color: white;
  font-size: 2rem;
`;

const Title = styled.h1`
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing.sm};
  font-size: 2rem;
  font-weight: 600;
`;

const Subtitle = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  font-size: 1.1rem;
  line-height: 1.6;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.lg};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.sm};
`;

const Label = styled.label`
  font-weight: 600;
  color: ${props => props.theme.colors.text.primary};
  font-size: 0.95rem;
`;

const Select = styled.select`
  padding: ${props => props.theme.spacing.md};
  border: 2px solid ${props => props.theme.colors.border.light};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: 1rem;
  transition: border-color 0.2s ease;
  background: white;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }

  &:disabled {
    background-color: ${props => props.theme.colors.background};
    cursor: not-allowed;
  }
`;

const Input = styled.input`
  padding: ${props => props.theme.spacing.md};
  border: 2px solid ${props => props.theme.colors.border.light};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: 1rem;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }

  &:disabled {
    background-color: ${props => props.theme.colors.background};
    cursor: not-allowed;
  }
`;

const TextArea = styled.textarea`
  padding: ${props => props.theme.spacing.md};
  border: 2px solid ${props => props.theme.colors.border.light};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: 1rem;
  min-height: 120px;
  resize: vertical;
  font-family: inherit;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
  }

  &:disabled {
    background-color: ${props => props.theme.colors.background};
    cursor: not-allowed;
  }
`;

const CharacterCount = styled.div`
  text-align: right;
  font-size: 0.85rem;
  color: ${props => props.count > 1000 ? props.theme.colors.error : props.theme.colors.text.secondary};
`;

const TypeInfo = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  padding: ${props => props.theme.spacing.sm};
  background-color: ${props => props.theme.colors.background};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: 0.9rem;
  color: ${props => props.theme.colors.text.secondary};
`;

const BackLink = styled.button`
  position: absolute;
  top: ${props => props.theme.spacing.lg};
  left: ${props => props.theme.spacing.lg};
  background: none;
  border: none;
  color: ${props => props.theme.colors.text.secondary};
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  font-size: 0.9rem;
  transition: color 0.2s ease;

  &:hover {
    color: ${props => props.theme.colors.primary};
  }
`;

const Contact = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    type: '',
    subject: '',
    message: '',
    email: ''
  });
  const [isLoading, setIsLoading] = useState(false);

  const contactTypes = [
    { value: 'bug', label: 'Reportar Bug', icon: FaBug, info: 'Encontrou algo que não está funcionando?' },
    { value: 'suggestion', label: 'Sugestão/Ideia', icon: FaLightbulb, info: 'Tem uma ideia para melhorar o app?' },
    { value: 'support', label: 'Suporte Geral', icon: FaComments, info: 'Precisa de ajuda ou tem dúvidas?' }
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.type || !formData.subject || !formData.message) {
      toast.error('Por favor, preencha todos os campos obrigatórios');
      return;
    }

    if (formData.subject.length < 3) {
      toast.error('O assunto deve ter pelo menos 3 caracteres');
      return;
    }

    if (formData.message.length < 10) {
      toast.error('A mensagem deve ter pelo menos 10 caracteres');
      return;
    }

    if (formData.message.length > 1000) {
      toast.error('Mensagem muito longa (máximo 1000 caracteres)');
      return;
    }

    setIsLoading(true);

    try {
      const response = await contactService.sendContactMessage(formData);

      toast.success(response.message || 'Mensagem enviada com sucesso! Responderemos em breve.');

      // Reset form
      setFormData({
        type: '',
        subject: '',
        message: '',
        email: ''
      });

    } catch (error) {
      console.error('Error sending contact message:', error);

      // Tratar erros de validação (422)
      if (error.response?.status === 422) {
        const validationErrors = error.response?.data?.detail;
        if (Array.isArray(validationErrors)) {
          // Extrair mensagens de erro específicas
          const errorMessages = validationErrors.map(err => {
            if (err.loc?.includes('message') && err.msg?.includes('at least 10 characters')) {
              return 'A mensagem deve ter pelo menos 10 caracteres.';
            }
            if (err.loc?.includes('subject') && err.msg?.includes('at least 3 characters')) {
              return 'O assunto deve ter pelo menos 3 caracteres.';
            }
            if (err.loc?.includes('type')) {
              return 'Selecione um tipo de contato válido.';
            }
            if (err.loc?.includes('email')) {
              return 'Digite um email válido.';
            }
            return err.msg || 'Erro de validação';
          });

          // Mostrar a primeira mensagem de erro
          toast.error(errorMessages[0] || 'Verifique os dados preenchidos.');
        } else {
          toast.error('Verifique os dados preenchidos e tente novamente.');
        }
      } else {
        // Outros erros
        const errorMessage = error.response?.data?.detail || 'Erro ao enviar mensagem. Tente novamente ou envie <NAME_EMAIL>';
        toast.error(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const selectedType = contactTypes.find(type => type.value === formData.type);

  return (
    <Container>
      <Card>
        <BackLink onClick={() => navigate(-1)}>
          <FaArrowLeft />
          Voltar
        </BackLink>

        <Header>
          <Icon>
            <FaEnvelope />
          </Icon>
          <Title>Entre em Contato</Title>
          <Subtitle>
            Tem algum bug para reportar, sugestão ou precisa de ajuda? 
            Estamos aqui para ouvir você!
          </Subtitle>
        </Header>

        <Form onSubmit={handleSubmit}>
          <FormGroup>
            <Label htmlFor="type">Tipo de Contato *</Label>
            <Select
              id="type"
              name="type"
              value={formData.type}
              onChange={handleInputChange}
              disabled={isLoading}
              required
            >
              <option value="">Selecione o tipo...</option>
              {contactTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </Select>
            {selectedType && (
              <TypeInfo>
                <selectedType.icon />
                {selectedType.info}
              </TypeInfo>
            )}
          </FormGroup>

          <FormGroup>
            <Label htmlFor="email">Seu Email (opcional)</Label>
            <Input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
              placeholder="<EMAIL>"
              disabled={isLoading}
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="subject">Assunto *</Label>
            <Input
              id="subject"
              name="subject"
              type="text"
              value={formData.subject}
              onChange={handleInputChange}
              placeholder="Descreva brevemente o assunto"
              disabled={isLoading}
              required
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="message">Mensagem *</Label>
            <TextArea
              id="message"
              name="message"
              value={formData.message}
              onChange={handleInputChange}
              placeholder="Descreva detalhadamente sua mensagem..."
              disabled={isLoading}
              maxLength={1000}
              required
            />
            <CharacterCount count={formData.message.length}>
              {formData.message.length}/1000 caracteres
              {formData.message.length < 10 && formData.message.length > 0 && (
                <span style={{ color: '#e74c3c', fontSize: '12px', marginLeft: '8px' }}>
                  (mínimo 10 caracteres)
                </span>
              )}
            </CharacterCount>
          </FormGroup>

          <Button 
            type="submit" 
            fullWidth 
            disabled={isLoading || !formData.type || !formData.subject || !formData.message}
          >
            {isLoading ? (
              'Enviando...'
            ) : (
              <>
                <FaPaperPlane />
                Enviar Mensagem
              </>
            )}
          </Button>
        </Form>
      </Card>
    </Container>
  );
};

export default Contact;
