import React, { useState, useRef } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { FaArrowLeft, FaCamera, FaCalendarAlt, FaEdit, FaTrash, FaPlus, FaBell, FaHeart, FaArchive, FaCheck, FaChevronLeft, FaChevronRight, FaPlay, FaUser } from 'react-icons/fa';
import { useQuery, useQueryClient } from 'react-query';
import plantsService from '../services/plantsService';
import remindersService from '../services/remindersService';
import subscriptionService from '../services/subscriptionService';
import Button from '../components/UI/Button';
import Loading from '../components/UI/Loading';
import ImageUpload from '../components/ImageUpload';
import ImageGallery from '../components/ImageGallery';
import CareForm from '../components/CareForm';
import CareEditModal from '../components/CareEditModal';
import PlantCalendar from '../components/PlantCalendar';
import ImageModal from '../components/ImageModal';
import PlantEditModal from '../components/PlantEditModal';
import ReminderModal from '../components/ReminderModal';
import PlantPrivacySettings from '../components/PlantPrivacySettings';
import LikeButton from '../components/LikeButton';
import CommentList from '../components/CommentList';
import CultivationPhaseSection from '../components/CultivationPhaseSection';
import { toast } from 'react-toastify';
import { getImageUrlSync as getImageUrl, getUserTimezoneOffset } from '../services/api';
import { formatDateLocal } from '../utils/dateUtils';
import socialInteractionsApi from '../services/socialInteractionsApi';
import { useAuth } from '../contexts/AuthContext';

// Video placeholder SVG
const VIDEO_PLACEHOLDER = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjZjVmNWY1Ii8+Cjxwb2x5Z29uIHBvaW50cz0iODAsNDAgMTIwLDYwIDgwLDgwIiBmaWxsPSIjNjY2Ii8+Cjx0ZXh0IHg9IjEwMCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5IiBmb250LXNpemU9IjEyIj5WaWRlbzwvdGV4dD4KPC9zdmc+";



const PlantDetailContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: ${props => props.theme.spacing.lg};
`;

const SocialViewContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: ${props => props.theme.spacing.lg};
`;

const OwnerInfo = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.md};
  margin-bottom: ${props => props.theme.spacing.lg};
  padding: ${props => props.theme.spacing.md};
  background-color: ${props => props.theme.colors.surface};
  border-radius: ${props => props.theme.borderRadius.lg};
  box-shadow: ${props => props.theme.shadows.sm};
`;

const OwnerAvatar = styled.div`
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: ${props => props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 18px;
`;

const OwnerDetails = styled.div`
  flex: 1;
`;

const OwnerName = styled.div`
  font-weight: bold;
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: 4px;
`;

const OwnerUsername = styled.div`
  color: ${props => props.theme.colors.text.secondary};
  font-size: ${props => props.theme.typography.fontSize.sm};
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.md};
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const BackButton = styled(Link)`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
  color: ${props => props.theme.colors.text.secondary};
  text-decoration: none;
  padding: ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.md};
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${props => props.theme.colors.background};
    color: ${props => props.theme.colors.primary};
  }
`;

const PlantContent = styled.div`
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const PlantSections = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${props => props.theme.spacing.xl};

  @media (max-width: ${props => props.theme.breakpoints.md}) {
    grid-template-columns: 1fr;
  }
`;

const PlantInfo = styled.div`
  background-color: ${props => props.theme.colors.surface};
  padding: ${props => props.theme.spacing.xl};
  border-radius: ${props => props.theme.borderRadius.lg};
  box-shadow: ${props => props.theme.shadows.sm};
`;

const PlantName = styled.h1`
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing.md};
`;

const PlantType = styled.span`
  background-color: ${props => props.theme.colors.primary};
  color: white;
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: ${props => props.theme.typography.fontSize.sm};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  margin-bottom: ${props => props.theme.spacing.lg};
  display: inline-block;
`;

const InfoItem = styled.div`
  margin-bottom: ${props => props.theme.spacing.md};
`;

const InfoLabel = styled.strong`
  color: ${props => props.theme.colors.text.primary};
  display: block;
  margin-bottom: ${props => props.theme.spacing.xs};
`;

const InfoValue = styled.span`
  color: ${props => props.theme.colors.text.secondary};
`;

const PlantImages = styled.div`
  background-color: ${props => props.theme.colors.surface};
  padding: ${props => props.theme.spacing.xl};
  border-radius: ${props => props.theme.borderRadius.lg};
  box-shadow: ${props => props.theme.shadows.sm};
`;

const SectionTitle = styled.h2`
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing.lg};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  width: 100%;

  /* Layout responsivo para telas pequenas */
  @media (max-width: 480px) {
    flex-direction: column;
    align-items: stretch;
    gap: ${props => props.theme.spacing.md};
    text-align: center;

    .section-button {
      margin-left: 0 !important;
      align-self: center;
    }
  }
`;

const SectionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${props => props.theme.spacing.lg};

  ${SectionTitle} {
    margin-bottom: 0;
  }

  /* Layout responsivo para telas pequenas */
  @media (max-width: 480px) {
    flex-direction: column;
    align-items: stretch;
    gap: ${props => props.theme.spacing.md};

    ${SectionTitle} {
      text-align: center;
    }
  }
`;

const WideButton = styled(Button)`
  min-width: 160px;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.lg};

  svg {
    color: inherit !important;
    fill: currentColor !important;
  }

  /* Permitir quebra de linha em telas pequenas */
  @media (max-width: 480px) {
    min-width: auto;
    white-space: normal;
    text-align: center;
    line-height: 1.2;
    padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  }
`;





const CareHistory = styled.div`
  grid-column: 1 / -1;
  background-color: ${props => props.theme.colors.surface};
  padding: ${props => props.theme.spacing.xl};
  border-radius: ${props => props.theme.borderRadius.lg};
  box-shadow: ${props => props.theme.shadows.sm};
  margin-top: ${props => props.theme.spacing.xl};
`;

const CareItem = styled.div`
  padding: ${props => props.theme.spacing.md};
  border-left: 3px solid ${props => props.theme.colors.primary};
  margin-bottom: ${props => props.theme.spacing.md};
  background-color: ${props => props.theme.colors.background};
  border-radius: ${props => props.theme.borderRadius.md};
  position: relative;

  &:hover .care-actions {
    opacity: 1;
  }
`;

const CareActions = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.xs};
  justify-content: flex-end;
  margin-top: ${props => props.theme.spacing.sm};
  padding-top: ${props => props.theme.spacing.sm};
  border-top: 1px solid ${props => props.theme.colors.border};
  opacity: 0;
  transition: opacity 0.2s ease;
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.sm};
  background-color: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text.secondary};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${props => props.variant === 'danger' ? '#dc3545' : props.theme.colors.primary};
    color: white;
    border-color: ${props => props.variant === 'danger' ? '#dc3545' : props.theme.colors.primary};
  }

  &:active {
    transform: scale(0.95);
  }
`;

const PlantActionsSection = styled.div`
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const PlantActionsGrid = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  justify-content: flex-end;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    justify-content: stretch;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: ${props => props.theme.spacing.sm};
  }
`;

const PlantActionButton = styled(Button)`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${props => props.theme.spacing.sm};
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  font-size: ${props => props.theme.typography.fontSize.sm};
  min-width: 160px;

  &.favorite {
    background-color: #e74c3c;
    border-color: #e74c3c;
    color: white;

    &:hover {
      background-color: #c0392b;
      border-color: #a93226;
    }
  }

  &.archive {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;

    &:hover {
      background-color: #5a6268;
      border-color: #545b62;
    }
  }

  &.delete {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;

    &:hover {
      background-color: #c82333;
      border-color: #bd2130;
    }
  }

  @media (max-width: 768px) {
    min-width: auto;
    width: 100%;
  }
`;

const CareType = styled.strong`
  color: ${props => props.theme.colors.primary};
`;

const CareDate = styled.span`
  color: ${props => props.theme.colors.text.secondary};
  font-size: ${props => props.theme.typography.fontSize.sm};
  float: right;
`;

const CarouselContainer = styled.div`
  margin-top: ${props => props.theme.spacing.sm};
`;

const CarouselImageContainer = styled.div`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
`;

const CarouselButton = styled.button`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 2;

  &:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: translateY(-50%) scale(1.1);
  }

  &.prev {
    left: 10px;
  }

  &.next {
    right: 10px;
  }

  @media (max-width: 768px) {
    width: 35px;
    height: 35px;

    &.prev {
      left: 5px;
    }

    &.next {
      right: 5px;
    }
  }
`;

const CarouselInfo = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: ${props => props.theme.spacing.sm};
  padding: 0 ${props => props.theme.spacing.sm};
`;

const CarouselCounter = styled.div`
  font-size: ${props => props.theme.typography.fontSize.sm};
  color: ${props => props.theme.colors.text.secondary};
`;

const CarouselDots = styled.div`
  display: flex;
  gap: 6px;
`;

const CarouselDot = styled.button`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: none;
  background: ${props => props.active ? props.theme.colors.primary : '#ddd'};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${props => props.active ? props.theme.colors.primary : '#bbb'};
    transform: scale(1.2);
  }
`;

const CareImageContainer = styled.div`
  position: relative;
  margin-top: ${props => props.theme.spacing.sm};
  text-align: center;
`;

const CareImage = styled.img`
  width: 100%;
  max-width: 200px;
  height: 120px;
  object-fit: cover;
  border-radius: ${props => props.theme.borderRadius.md};
  border: 1px solid ${props => props.theme.colors.border};
  cursor: pointer;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.05);
  }
`;

const CareVideoElement = styled.video`
  width: 100%;
  max-width: 200px;
  height: 120px;
  object-fit: cover;
  border-radius: ${props => props.theme.borderRadius.md};
  border: 1px solid ${props => props.theme.colors.border};
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.05);
  }
`;

const VideoOverlay = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  pointer-events: none;
`;

const PrimaryBadge = styled.div`
  position: absolute;
  top: 4px;
  right: 4px;
  background: ${props => props.theme.colors.primary};
  color: white;
  padding: 2px 6px;
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: 10px;
  font-weight: bold;
`;

const SocialSeparator = styled.div`
  width: 100%;
  height: 1px;
  background: linear-gradient(
    to right,
    transparent,
    ${props => props.theme.colors.border} 20%,
    ${props => props.theme.colors.border} 80%,
    transparent
  );
  margin: ${props => props.theme.spacing.xl} 0;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 3px;
    background: ${props => props.theme.colors.primary};
    border-radius: 2px;
  }
`;

const SocialSection = styled.div`
  padding: ${props => props.theme.spacing.lg} 0;

  ${SectionTitle} {
    color: ${props => props.theme.colors.primary};
    margin-bottom: ${props => props.theme.spacing.lg};

    svg {
      color: ${props => props.theme.colors.primary};
    }
  }
`;

const PlantDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const calendarRef = useRef();
  const [showImageUpload, setShowImageUpload] = useState(false);
  const [showCareForm, setShowCareForm] = useState(false);
  const [editingCare, setEditingCare] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showImageModal, setShowImageModal] = useState(false);
  const [modalImages, setModalImages] = useState([]);
  const [modalCurrentIndex, setModalCurrentIndex] = useState(0);
  const [socialInfo, setSocialInfo] = useState({
    likes_count: 0,
    comments_count: 0,
    is_liked_by_user: false,
    can_like: false,
    can_comment: false
  });

  // Buscar informações do plano do usuário
  const { data: userPlan } = useQuery(
    'user-plan',
    () => subscriptionService.getCurrentSubscription().then(res => res.data?.plan),
    {
      staleTime: 5 * 60 * 1000, // 5 minutos
      retry: false
    }
  );
  const [showPlantEditModal, setShowPlantEditModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [showFavoriteConfirm, setShowFavoriteConfirm] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [editingReminder, setEditingReminder] = useState(null);
  const [showEditReminderModal, setShowEditReminderModal] = useState(false);

  const { data: plant, isLoading, error } = useQuery(
    ['plant', id],
    () => {
      const timezoneOffset = getUserTimezoneOffset();
      return plantsService.getPlant(id, timezoneOffset).then(res => res.data);
    },
    {
      retry: 1,
    }
  );

  const { data: allImages, isLoading: imagesLoading } = useQuery(
    ['plant-all-images', id],
    () => plantsService.getAllPlantImages(id).then(res => res.data),
    {
      retry: 1,
      enabled: !!id,
    }
  );

  const { data: reminders = [], refetch: refetchReminders } = useQuery(
    ['plant-reminders', id],
    () => plantsService.getPlantReminders(id, true).then(res => res.data),
    {
      retry: 1,
      enabled: !!id && !!plant && plant.owner_id === user?.id, // Only fetch reminders if user owns the plant
    }
  );

  // Buscar informações sociais da planta
  const { data: plantSocialInfo } = useQuery(
    ['plant-social-info', id],
    () => socialInteractionsApi.getPlantSocialInfo(id),
    {
      retry: 1,
      enabled: !!id && !!plant,
      onSuccess: (data) => {
        setSocialInfo(data);
      },
      onError: (error) => {
        // Se der erro (ex: planta privada), manter valores padrão
        console.log('Could not load social info:', error);
      }
    }
  );

  const handleUploadSuccess = () => {
    // Refresh plant data to show new image
    queryClient.invalidateQueries(['plant', id]);
    queryClient.invalidateQueries(['plant-all-images', id]);
    setShowImageUpload(false);
  };

  const handleCareSuccess = () => {
    // Refresh plant data to show new care
    queryClient.invalidateQueries(['plant', id]);
    queryClient.invalidateQueries(['plant-all-images', id]);
    // Refresh reminders to show new reminders created
    queryClient.invalidateQueries(['plant-reminders', id]);
    // Also refresh global reminders for notification bell
    queryClient.invalidateQueries('upcomingReminders');
    queryClient.invalidateQueries('notifications');
    setShowCareForm(false);
  };

  const handleAddReminder = () => {
    if (calendarRef.current) {
      calendarRef.current.openReminderModal();
    }
  };

  const handleCompleteReminder = async (reminderId) => {
    try {
      await remindersService.completeReminder(reminderId);
      toast.success('Lembrete marcado como concluído!');
      refetchReminders();
    } catch (error) {
      console.error('Erro ao completar lembrete:', error);
      toast.error('Erro ao completar lembrete');
    }
  };

  const handleEditReminder = (reminder) => {
    setEditingReminder(reminder);
    setShowEditReminderModal(true);
  };

  const handleDeleteReminder = async (reminderId) => {
    if (window.confirm('Tem certeza que deseja remover este lembrete?')) {
      try {
        await remindersService.deleteReminder(reminderId);
        toast.success('Lembrete removido com sucesso!');
        refetchReminders();
      } catch (error) {
        console.error('Erro ao remover lembrete:', error);
        toast.error('Erro ao remover lembrete');
      }
    }
  };

  const handleEditCare = (care) => {
    setEditingCare(care);
    setShowEditModal(true);
  };

  const handleEditSuccess = async () => {
    // Refresh plant data to show updated care
    await queryClient.invalidateQueries(['plant', id]);
    await queryClient.invalidateQueries(['plant-all-images', id]);

    // Force refetch to ensure data is updated
    setTimeout(() => {
      queryClient.refetchQueries(['plant-all-images', id]);
    }, 100);

    setShowEditModal(false);
    setEditingCare(null);
  };

  const handleDeleteCare = async (careId) => {
    if (window.confirm('Tem certeza que deseja remover este cuidado?')) {
      try {
        await plantsService.deletePlantCare(id, careId);
        toast.success('Cuidado removido com sucesso!');
        queryClient.invalidateQueries(['plant', id]);
        queryClient.invalidateQueries(['plant-all-images', id]);
      } catch (error) {
        console.error('Erro ao remover cuidado:', error);
        toast.error('Erro ao remover cuidado');
      }
    }
  };

  const handleImageClick = (imagesOrSrc, indexOrTitle) => {
    // Se receber um array de imagens (do carrossel), usar navegação múltipla
    if (Array.isArray(imagesOrSrc)) {
      setModalImages(imagesOrSrc);
      setModalCurrentIndex(indexOrTitle || 0);
    } else {
      // Se receber uma única imagem (legacy), criar array com uma imagem
      setModalImages([{
        src: imagesOrSrc,
        title: indexOrTitle || 'Foto do cuidado',
        description: ''
      }]);
      setModalCurrentIndex(0);
    }
    setShowImageModal(true);
  };

  const handlePlantEditSuccess = () => {
    setShowPlantEditModal(false);
    queryClient.invalidateQueries(['plant', id]);
  };

  const handleDeletePlant = async () => {
    setActionLoading(true);
    try {
      await plantsService.deletePlant(plant.id);
      toast.success('Planta removida com sucesso!');
      navigate('/profile');
    } catch (error) {
      console.error('Erro ao remover planta:', error);
      toast.error('Erro ao remover planta');
    } finally {
      setActionLoading(false);
      setShowDeleteConfirm(false);
    }
  };

  const handleArchivePlant = async () => {
    console.log('handleArchivePlant chamado', { plantId: plant.id, currentArchived: plant.archived });
    setActionLoading(true);
    try {
      const isCurrentlyArchived = plant.archived || false;
      console.log('Enviando requisição de arquivar:', { plantId: plant.id, newArchived: !isCurrentlyArchived });

      await plantsService.archivePlant(plant.id, !isCurrentlyArchived);

      if (isCurrentlyArchived) {
        toast.success('Planta desarquivada com sucesso!');
      } else {
        toast.success('Planta arquivada com sucesso!');
      }

      queryClient.invalidateQueries(['plant', id]);
    } catch (error) {
      console.error('Erro detalhado ao arquivar planta:', {
        error,
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });
      toast.error('Erro ao arquivar planta: ' + (error.response?.data?.detail || error.message));
    } finally {
      setActionLoading(false);
      setShowArchiveConfirm(false);
    }
  };

  const handleFavoritePlant = async () => {
    console.log('handleFavoritePlant chamado', { plantId: plant.id, currentFavorite: plant.favorite });
    setActionLoading(true);
    try {
      const isCurrentlyFavorite = plant.favorite || false;
      console.log('Enviando requisição de favoritar:', { plantId: plant.id, newFavorite: !isCurrentlyFavorite });

      await plantsService.favoritePlant(plant.id, !isCurrentlyFavorite);

      if (isCurrentlyFavorite) {
        toast.success('Removido dos favoritos!');
      } else {
        toast.success('Adicionado aos favoritos!');
      }

      queryClient.invalidateQueries(['plant', id]);
    } catch (error) {
      console.error('Erro detalhado ao favoritar planta:', {
        error,
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });
      toast.error('Erro ao favoritar planta: ' + (error.response?.data?.detail || error.message));
    } finally {
      setActionLoading(false);
      setShowFavoriteConfirm(false);
    }
  };

  const handlePlantDelete = () => {
    // Navigate back to profile after successful deletion
    navigate('/profile');
  };

  if (isLoading) return <Loading text="Carregando detalhes da planta..." />;

  if (error) {
    return (
      <PlantDetailContainer>
        <div>Erro ao carregar planta: {error.message}</div>
      </PlantDetailContainer>
    );
  }

  if (!plant) {
    return (
      <PlantDetailContainer>
        <div>Planta não encontrada</div>
      </PlantDetailContainer>
    );
  }

  // Determinar se o usuário atual é o dono da planta
  const isOwner = user && plant.owner_id === user.id;

  // Se não for o dono, usar layout social
  if (!isOwner) {
    return (
      <SocialViewContainer>
        <Header>
          <BackButton to="/search">
            <FaArrowLeft />
            Voltar
          </BackButton>
        </Header>

        {/* Informações do dono da planta */}
        <OwnerInfo>
          <OwnerAvatar>
            <FaUser />
          </OwnerAvatar>
          <OwnerDetails>
            <OwnerName>
              {plant.owner_name || 'Usuário'}
            </OwnerName>
            <OwnerUsername>
              @{plant.owner_username || 'usuario'}
            </OwnerUsername>
          </OwnerDetails>
        </OwnerInfo>

        {/* Informações básicas da planta */}
        <PlantInfo>
          <PlantName>{plant.name}</PlantName>
          <PlantType>{plant.plant_type_display}</PlantType>

          {plant.scientific_name && (
            <InfoItem>
              <InfoLabel>Nome Científico:</InfoLabel>
              <InfoValue>{plant.scientific_name}</InfoValue>
            </InfoItem>
          )}

          {plant.description && (
            <InfoItem>
              <InfoLabel>Descrição:</InfoLabel>
              <InfoValue>{plant.description}</InfoValue>
            </InfoItem>
          )}
        </PlantInfo>

        {/* Galeria de fotos (somente visualização) */}
        {allImages && allImages.length > 0 && (
          <PlantImages>
            <SectionTitle>
              <FaCamera />
              Fotos da Planta ({allImages.length})
            </SectionTitle>
            <ImageGallery
              images={allImages}
              onImageClick={handleImageClick}
              readOnly={true}
            />
          </PlantImages>
        )}

        {/* Histórico de Cuidados (somente visualização se permitido) */}
        {plant.allow_care_sharing && plant.cares && plant.cares.length > 0 && (
          <CareHistory>
            <SectionTitle>
              <FaBell />
              Histórico de Cuidados ({plant.cares.length})
            </SectionTitle>
            {plant.cares.map((care) => (
              <CareItem key={care.id}>
                <div>
                  <CareType>{care.care_type_display}</CareType>
                  <CareDate>{formatDateLocal(care.care_date)}</CareDate>
                </div>
                {care.description && <p>{care.description}</p>}

                {/* Exibir imagens/vídeos do cuidado */}
                {care.images && care.images.length > 0 && (
                  <CarouselContainer>
                    {care.images.map((image, index) => (
                      <CareImageContainer key={image.id}>
                        {image.is_video ? (
                          <div style={{ position: 'relative' }}>
                            <CareImage
                              src={VIDEO_PLACEHOLDER}
                              alt={`Vídeo do cuidado ${index + 1}`}
                              onClick={() => handleImageClick([{
                                src: getImageUrl(image.r2_key),
                                title: `Vídeo do cuidado - ${care.care_type_display}`,
                                description: care.description || '',
                                isVideo: true
                              }], 0)}
                            />
                            <VideoOverlay>
                              <FaPlay />
                            </VideoOverlay>
                          </div>
                        ) : (
                          <CareImage
                            src={getImageUrl(image.r2_key)}
                            alt={`Foto do cuidado ${index + 1}`}
                            onClick={() => handleImageClick([{
                              src: getImageUrl(image.r2_key),
                              title: `Foto do cuidado - ${care.care_type_display}`,
                              description: care.description || ''
                            }], 0)}
                          />
                        )}
                      </CareImageContainer>
                    ))}
                  </CarouselContainer>
                )}
              </CareItem>
            ))}
          </CareHistory>
        )}

        {/* Linha separadora */}
        {plant.is_public && (
          <SocialSeparator />
        )}

        {/* Seção Social - Curtidas e Comentários */}
        {plant.is_public && (
          <SocialSection>
            <div style={{ marginBottom: '20px' }}>
              <LikeButton
                plantId={id}
                initialLikesCount={socialInfo.likes_count}
                initialIsLiked={socialInfo.is_liked_by_user}
                canLike={socialInfo.can_like}
              />
            </div>

            <CommentList
              plantId={id}
              plantOwnerId={plant.owner_id}
              canComment={socialInfo.can_comment}
              initialCommentsCount={socialInfo.comments_count}
            />
          </SocialSection>
        )}

        {/* Modais */}
        {showImageModal && (
          <ImageModal
            images={modalImages}
            currentIndex={modalCurrentIndex}
            onClose={() => setShowImageModal(false)}
            onNext={() => setModalCurrentIndex((prev) => (prev + 1) % modalImages.length)}
            onPrevious={() => setModalCurrentIndex((prev) => (prev - 1 + modalImages.length) % modalImages.length)}
          />
        )}
      </SocialViewContainer>
    );
  }

  // Layout completo para o dono da planta
  return (
    <PlantDetailContainer>
      <Header>
        <BackButton to="/profile">
          <FaArrowLeft />
          Voltar
        </BackButton>
      </Header>

      <PlantContent>
        <PlantActionsSection>
          {showDeleteConfirm && (
            <div style={{
              background: '#f8d7da',
              border: '1px solid #f5c6cb',
              borderRadius: '8px',
              padding: '16px',
              marginBottom: '16px',
              color: '#721c24'
            }}>
              <p style={{ margin: '0 0 12px 0', fontWeight: 'bold' }}>
                Tem certeza que deseja remover esta planta? Esta ação não pode ser desfeita.
                Todas as fotos e registros de cuidados também serão removidos.
              </p>
              <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
                <Button
                  variant="secondary"
                  onClick={() => setShowDeleteConfirm(false)}
                  disabled={actionLoading}
                >
                  Cancelar
                </Button>
                <PlantActionButton
                  className="delete"
                  onClick={handleDeletePlant}
                  disabled={actionLoading}
                >
                  <FaTrash />
                  {actionLoading ? 'Removendo...' : 'Confirmar Remoção'}
                </PlantActionButton>
              </div>
            </div>
          )}

          {showArchiveConfirm && (
            <div style={{
              background: '#d1ecf1',
              border: '1px solid #bee5eb',
              borderRadius: '8px',
              padding: '16px',
              marginBottom: '16px',
              color: '#0c5460'
            }}>
              <p style={{ margin: '0 0 12px 0', fontWeight: 'bold' }}>
                {plant.archived
                  ? 'Tem certeza que deseja desarquivar esta planta? Ela voltará a aparecer na lista principal.'
                  : 'Tem certeza que deseja arquivar esta planta? Ela será movida para a seção de plantas arquivadas.'
                }
              </p>
              <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
                <Button
                  variant="secondary"
                  onClick={() => setShowArchiveConfirm(false)}
                  disabled={actionLoading}
                >
                  Cancelar
                </Button>
                <PlantActionButton
                  className="archive"
                  onClick={handleArchivePlant}
                  disabled={actionLoading}
                >
                  <FaArchive />
                  {actionLoading
                    ? (plant.archived ? 'Desarquivando...' : 'Arquivando...')
                    : (plant.archived ? 'Confirmar Desarquivamento' : 'Confirmar Arquivamento')
                  }
                </PlantActionButton>
              </div>
            </div>
          )}

          {showFavoriteConfirm && (
            <div style={{
              background: '#f8d7da',
              border: '1px solid #f5c6cb',
              borderRadius: '8px',
              padding: '16px',
              marginBottom: '16px',
              color: '#721c24'
            }}>
              <p style={{ margin: '0 0 12px 0', fontWeight: 'bold' }}>
                {plant.favorite
                  ? 'Tem certeza que deseja remover esta planta dos favoritos?'
                  : 'Tem certeza que deseja adicionar esta planta aos favoritos?'
                }
              </p>
              <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
                <Button
                  variant="secondary"
                  onClick={() => setShowFavoriteConfirm(false)}
                  disabled={actionLoading}
                >
                  Cancelar
                </Button>
                <PlantActionButton
                  className="favorite"
                  onClick={handleFavoritePlant}
                  disabled={actionLoading}
                >
                  <FaHeart />
                  {actionLoading
                    ? (plant.favorite ? 'Removendo...' : 'Adicionando...')
                    : (plant.favorite ? 'Confirmar Remoção' : 'Confirmar Adição')
                  }
                </PlantActionButton>
              </div>
            </div>
          )}

          {!showDeleteConfirm && !showArchiveConfirm && !showFavoriteConfirm && (
            <PlantActionsGrid>
              <PlantActionButton
                className="favorite"
                onClick={() => setShowFavoriteConfirm(true)}
                disabled={actionLoading}
              >
                <FaHeart />
                {plant.favorite ? 'Remover dos Favoritos' : 'Adicionar aos Favoritos'}
              </PlantActionButton>

              <PlantActionButton
                className="archive"
                onClick={() => setShowArchiveConfirm(true)}
                disabled={actionLoading}
              >
                <FaArchive />
                {plant.archived ? 'Desarquivar Planta' : 'Arquivar Planta'}
              </PlantActionButton>

              <PlantActionButton
                className="delete"
                onClick={() => setShowDeleteConfirm(true)}
                disabled={actionLoading}
              >
                <FaTrash />
                Remover Planta
              </PlantActionButton>
            </PlantActionsGrid>
          )}
        </PlantActionsSection>

        <PlantSections>
          <PlantInfo>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '16px' }}>
            <div>
              <PlantName>{plant.name}</PlantName>
              <PlantType>{plant.plant_type_display}</PlantType>
            </div>
            <Button
              size="small"
              onClick={() => setShowPlantEditModal(true)}
              style={{ flexShrink: 0 }}
            >
              <FaEdit />
              Editar Planta
            </Button>
          </div>
          
          {plant.scientific_name && (
            <InfoItem>
              <InfoLabel>Nome Científico:</InfoLabel>
              <InfoValue>{plant.scientific_name}</InfoValue>
            </InfoItem>
          )}
          
          {plant.acquisition_date && (
            <InfoItem>
              <InfoLabel>Data de Aquisição:</InfoLabel>
              <InfoValue>{formatDateLocal(plant.acquisition_date)}</InfoValue>
            </InfoItem>
          )}
          
          {plant.location && (
            <InfoItem>
              <InfoLabel>Localização:</InfoLabel>
              <InfoValue>{plant.location}</InfoValue>
            </InfoItem>
          )}

          {plant.style && (
            <InfoItem>
              <InfoLabel>Estilo:</InfoLabel>
              <InfoValue>{plant.style}</InfoValue>
            </InfoItem>
          )}

          {plant.estimated_age && (
            <InfoItem>
              <InfoLabel>Idade Estimada:</InfoLabel>
              <InfoValue>{plant.estimated_age}</InfoValue>
            </InfoItem>
          )}

          {plant.description && (
            <InfoItem>
              <InfoLabel>Descrição:</InfoLabel>
              <InfoValue>{plant.description}</InfoValue>
            </InfoItem>
          )}
        </PlantInfo>

        <PlantImages>
          <SectionTitle>
            <FaCamera />
            {userPlan?.allows_videos ? 'Todas as Fotos e Vídeos' : 'Todas as Fotos'} ({allImages?.length || 0})
            <Button
              size="small"
              onClick={() => setShowImageUpload(!showImageUpload)}
              style={{ marginLeft: 'auto' }}
            >
              <FaCamera />
              {showImageUpload ? 'Cancelar' : (userPlan?.allows_videos ? 'Adicionar Mídia' : 'Adicionar Foto')}
            </Button>
          </SectionTitle>

          {showImageUpload && (
            <ImageUpload
              plantId={id}
              onUploadSuccess={handleUploadSuccess}
              onCancel={() => setShowImageUpload(false)}
              isFirstPhoto={!allImages || allImages.length === 0}
            />
          )}

          {imagesLoading ? (
            <div style={{ textAlign: 'center', padding: '20px' }}>
              Carregando fotos...
            </div>
          ) : allImages && allImages.length > 0 ? (
            <ImageGallery
              images={allImages}
              plantId={id}
              onImagesChange={handleUploadSuccess}
              showCareImages={true}
              compact={true}
              maxImages={3}
            />
          ) : !showImageUpload && (
            <div style={{ textAlign: 'center', color: '#999' }}>
              <FaCamera size={48} />
              <p>Nenhuma foto adicionada</p>
              <Button
                size="small"
                onClick={() => setShowImageUpload(true)}
              >
                {userPlan?.allows_videos ? 'Adicionar Primeira Mídia' : 'Adicionar Primeira Foto'}
              </Button>
            </div>
          )}
        </PlantImages>
        </PlantSections>

        {/* Configurações de Privacidade */}
        <PlantPrivacySettings
          plant={plant}
          onUpdate={(updatedPlant) => {
            // Atualizar cache do React Query
            queryClient.setQueryData(['plant', id], updatedPlant);
          }}
        />

      </PlantContent>

      {/* Fase de Cultivo */}
      <CultivationPhaseSection
        plant={plant}
        onUpdate={(updatedPlant) => {
          // Atualizar cache do React Query
          queryClient.setQueryData(['plant', id], updatedPlant);
        }}
        isOwner={isOwner}
      />

      <CareHistory>
        <SectionTitle>
          <FaCalendarAlt />
          Histórico de Cuidados ({plant.cares?.length || 0})
          <WideButton
            size="small"
            onClick={() => setShowCareForm(!showCareForm)}
            style={{ marginLeft: 'auto' }}
            className="section-button"
          >
            <FaCalendarAlt style={{ marginRight: '6px', color: 'inherit' }} />
            {showCareForm ? 'Cancelar' : 'Adicionar Cuidado'}
          </WideButton>
        </SectionTitle>

        {showCareForm && (
          <CareForm
            plantId={id}
            onSuccess={handleCareSuccess}
            onCancel={() => setShowCareForm(false)}
          />
        )}

        {plant.cares && plant.cares.length > 0 ? (
          plant.cares.map((care) => (
            <CareItem key={care.id}>
              <div>
                <CareType>{care.care_type_display || care.care_type}</CareType>
                <CareDate>{formatDateLocal(care.date)}</CareDate>
                {care.description && <p>{care.description}</p>}
                {care.notes && <p><em>{care.notes}</em></p>}
                {/* Exibir carrossel usando imagens da galeria com URLs corretas */}
                {(() => {
                  // Buscar imagens deste cuidado na galeria (que têm URLs corretas)
                  const careImagesFromGallery = allImages?.filter(img =>
                    img.type === 'care' && img.source_id === care.id
                  ) || [];

                  console.log(`🔍 [DEBUG] Care ${care.id} - careImagesFromGallery:`, careImagesFromGallery);
                  console.log(`🔍 [DEBUG] Care ${care.id} - allImages:`, allImages?.filter(img => img.type === 'care'));

                  if (careImagesFromGallery.length > 0) {
                    return (
                      <CareImageCarousel
                        careImages={careImagesFromGallery}
                        care={care}
                        onImageClick={handleImageClick}
                      />
                    );
                  } else if (care.image) {
                    // Fallback para imagem legacy
                    return (
                      <CareImageContainer>
                        <CareImage
                          src={getImageUrl(care.image)}
                          alt="Foto do cuidado"
                          onClick={() => handleImageClick(getImageUrl(care.image), `${care.care_type_display} - ${formatDateLocal(care.date)}`)}
                        />
                      </CareImageContainer>
                    );
                  }
                  return null;
                })()}
              </div>
              <CareActions className="care-actions">
                <ActionButton
                  onClick={() => handleEditCare(care)}
                  title="Editar cuidado"
                >
                  <FaEdit />
                </ActionButton>
                <ActionButton
                  variant="danger"
                  onClick={() => handleDeleteCare(care.id)}
                  title="Remover cuidado"
                >
                  <FaTrash />
                </ActionButton>
              </CareActions>
            </CareItem>
          ))
        ) : !showCareForm && (
          <div style={{ textAlign: 'center', color: '#999' }}>
            <FaCalendarAlt size={48} />
            <p>Nenhum cuidado registrado</p>
            <Button
              size="small"
              onClick={() => setShowCareForm(true)}
            >
              Adicionar Primeiro Cuidado
            </Button>
          </div>
        )}
      </CareHistory>

      {/* Próximos Cuidados */}
      <CareHistory>
        <SectionHeader>
          <SectionTitle>
            <FaBell />
            Próximos Cuidados ({reminders?.filter(r => !r.is_completed).length || 0})
          </SectionTitle>
          <WideButton size="small" onClick={handleAddReminder}>
            <span style={{
              marginRight: '6px',
              fontSize: '14px',
              filter: 'brightness(0) invert(1)',
              display: 'inline-block'
            }}>🔔</span>
            Adicionar Lembrete
          </WideButton>
        </SectionHeader>

        {reminders && reminders.filter(r => !r.is_completed).length > 0 ? (
          reminders
            .filter(r => !r.is_completed)
            .sort((a, b) => new Date(a.scheduled_date) - new Date(b.scheduled_date))
            .slice(0, 5) // Mostrar apenas os próximos 5
            .map((reminder) => (
              <CareItem key={reminder.id}>
                <div>
                  <CareType style={{ color: reminder.is_overdue ? '#e74c3c' : '#2ecc71' }}>
                    {reminder.care_type_display || reminder.care_type}
                    {reminder.is_overdue && ' (Atrasado)'}
                  </CareType>
                  <CareDate>
                    {formatDateLocal(reminder.scheduled_date)}
                    {reminder.days_until_due !== undefined && (
                      <span style={{
                        marginLeft: '8px',
                        color: reminder.is_overdue ? '#e74c3c' : '#666',
                        fontSize: '12px'
                      }}>
                        {reminder.is_overdue
                          ? `${Math.abs(reminder.days_until_due)} dias atrasado`
                          : reminder.days_until_due === 0
                            ? 'Hoje'
                            : reminder.days_until_due === 1
                              ? 'Amanhã'
                              : `em ${reminder.days_until_due} dias`
                        }
                      </span>
                    )}
                  </CareDate>
                  {reminder.description && <p>{reminder.description}</p>}
                </div>
                <CareActions className="care-actions">
                  <ActionButton
                    onClick={() => handleCompleteReminder(reminder.id)}
                    title="Marcar como concluído"
                    style={{ backgroundColor: '#2ecc71', color: 'white' }}
                  >
                    <FaCheck />
                  </ActionButton>
                  <ActionButton
                    onClick={() => handleEditReminder(reminder)}
                    title="Editar lembrete"
                  >
                    <FaEdit />
                  </ActionButton>
                  <ActionButton
                    variant="danger"
                    onClick={() => handleDeleteReminder(reminder.id)}
                    title="Remover lembrete"
                  >
                    <FaTrash />
                  </ActionButton>
                </CareActions>
              </CareItem>
            ))
        ) : (
          <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
            <FaBell size={48} />
            <p>Nenhum lembrete pendente</p>
            <Button
              size="small"
              onClick={handleAddReminder}
            >
              Criar Primeiro Lembrete
            </Button>
          </div>
        )}
      </CareHistory>

      <CareHistory>
        <SectionTitle>
          <FaCalendarAlt />
          Calendário
        </SectionTitle>
        <PlantCalendar
          ref={calendarRef}
          plant={plant}
          cares={plant.cares || []}
          images={allImages || []}
          reminders={reminders || []}
          onReminderSuccess={refetchReminders}
        />
      </CareHistory>

      {/* Linha separadora */}
      {plant.is_public && (
        <SocialSeparator />
      )}

      {/* Seção Social - Curtidas e Comentários */}
      {plant.is_public && (
        <SocialSection>
          <div style={{ marginBottom: '20px' }}>
            <LikeButton
              plantId={id}
              initialLikesCount={socialInfo.likes_count}
              initialIsLiked={socialInfo.is_liked_by_user}
              canLike={socialInfo.can_like}
            />
          </div>

          <CommentList
            plantId={id}
            plantOwnerId={plant.owner_id}
            canComment={socialInfo.can_comment}
            initialCommentsCount={socialInfo.comments_count}
          />
        </SocialSection>
      )}

      {showEditModal && editingCare && (
        <CareEditModal
          care={editingCare}
          plantId={id}
          allImages={allImages || []}
          onSuccess={handleEditSuccess}
          onCancel={() => {
            setShowEditModal(false);
            setEditingCare(null);
          }}
        />
      )}

      <ImageModal
        isOpen={showImageModal}
        onClose={() => setShowImageModal(false)}
        images={modalImages}
        currentIndex={modalCurrentIndex}
        onIndexChange={setModalCurrentIndex}
      />

      {showPlantEditModal && (
        <PlantEditModal
          plant={plant}
          onSuccess={handlePlantEditSuccess}
          onCancel={() => setShowPlantEditModal(false)}
        />
      )}

      {showEditReminderModal && editingReminder && (
        <ReminderModal
          isOpen={showEditReminderModal}
          onClose={() => {
            setShowEditReminderModal(false);
            setEditingReminder(null);
          }}
          plantId={id}
          onSuccess={() => {
            setShowEditReminderModal(false);
            setEditingReminder(null);
            refetchReminders();
          }}
          mode="edit"
          reminder={editingReminder}
        />
      )}
    </PlantDetailContainer>
  );
};

// Componente Carrossel de Imagens
const CareImageCarousel = ({ careImages, care, onImageClick }) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const nextImage = () => {
    setCurrentIndex((prev) => (prev + 1) % careImages.length);
  };

  const prevImage = () => {
    setCurrentIndex((prev) => (prev - 1 + careImages.length) % careImages.length);
  };

  const handleImageClick = () => {
    const currentItem = careImages[currentIndex];

    // Se for vídeo, não abrir modal (ImageModal não suporta vídeos)
    if (currentItem.is_video) {
      return;
    }

    // Preparar array apenas de imagens (filtrar vídeos) para o modal
    const imageItems = careImages.filter(item => !item.is_video);
    const modalImages = imageItems.map((careImage, index) => ({
      src: careImage.image, // Usar URL completa da galeria
      title: `${care.care_type_display} - ${formatDateLocal(care.date)}`,
      description: `Foto ${index + 1} de ${imageItems.length}${careImage.is_primary ? ' (Principal)' : ''}`
    }));

    // Encontrar o índice correto da imagem atual no array filtrado
    const currentImageIndex = imageItems.findIndex(item => item.id === currentItem.id);

    // Chamar onImageClick com todas as imagens e índice atual
    onImageClick(modalImages, currentImageIndex >= 0 ? currentImageIndex : 0);
  };



  if (!careImages || careImages.length === 0) return null;

  const currentImage = careImages[currentIndex];

  console.log(`🎬 [CareImageCarousel] Care ${care.id} - careImages:`, careImages);
  console.log(`🎬 [CareImageCarousel] Current index: ${currentIndex}, currentImage:`, currentImage);
  console.log(`🎬 [CareImageCarousel] currentImage.is_video:`, currentImage.is_video);
  console.log(`🎬 [CareImageCarousel] Will render:`, currentImage.is_video ? 'VIDEO' : 'IMAGE');
  console.log(`🎬 [CareImageCarousel] Video URL:`, currentImage.image);

  return (
    <CarouselContainer>
      <CarouselImageContainer>
        {currentImage.is_video ? (
          <>
            <video
              src={getImageUrl(currentImage.image)}
              muted
              preload="none"
              controls={false}
              playsInline
              poster={currentImage.thumbnail_url || VIDEO_PLACEHOLDER}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                // Load video on first click, then play/pause
                if (e.target.readyState === 0) {
                  e.target.load();
                  e.target.addEventListener('loadeddata', () => {
                    e.target.play();
                  }, { once: true });
                } else if (e.target.paused) {
                  e.target.play();
                } else {
                  e.target.pause();
                }
              }}
              style={{
                width: '100%',
                maxWidth: '200px',
                height: '120px',
                objectFit: 'cover',
                cursor: 'pointer',
                borderRadius: '8px',
                border: '1px solid #ddd'
              }}
              onError={(e) => {
                console.error('❌ [CareVideo] Error loading:', getImageUrl(currentImage.image), e);
                console.error('❌ [CareVideo] Error details:', e.target.error);
                e.target.style.display = 'none';
              }}
              onLoadedMetadata={(e) => {
                console.log('🎥 [CareVideo] Loaded metadata:', getImageUrl(currentImage.image));
              }}
            />
            <VideoOverlay>
              <FaPlay />
            </VideoOverlay>
          </>
        ) : (
          <CareImage
            src={currentImage.image} // Usar URL completa da galeria
            alt={`Foto do cuidado ${currentIndex + 1}`}
            onClick={handleImageClick}
          />
        )}
        {currentImage.is_primary && (
          <PrimaryBadge>Principal</PrimaryBadge>
        )}

        {careImages.length > 1 && (
          <>
            <CarouselButton className="prev" onClick={prevImage}>
              <FaChevronLeft />
            </CarouselButton>
            <CarouselButton className="next" onClick={nextImage}>
              <FaChevronRight />
            </CarouselButton>
          </>
        )}
      </CarouselImageContainer>

      {careImages.length > 1 && (
        <CarouselInfo>
          <CarouselCounter>
            {currentIndex + 1} de {careImages.length} {careImages.some(img => img.is_video) ? 'itens' : 'fotos'}
          </CarouselCounter>
          <CarouselDots>
            {careImages.map((_, index) => (
              <CarouselDot
                key={index}
                active={index === currentIndex}
                onClick={() => setCurrentIndex(index)}
              />
            ))}
          </CarouselDots>
        </CarouselInfo>
      )}
    </CarouselContainer>
  );
};

export default PlantDetail;
