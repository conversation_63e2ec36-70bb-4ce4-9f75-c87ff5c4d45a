import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { FaUser, FaUserPlus, FaUserMinus, FaMapMarkerAlt, FaCalendarAlt, FaLeaf } from 'react-icons/fa';
import { useSocial } from '../contexts/SocialContext';
import { useAuth } from '../contexts/AuthContext';
import Button from '../components/UI/Button';
import LoadingSpinner from '../components/UI/LoadingSpinner';
import PlantCard from '../components/PlantCard';

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: ${props => props.theme.spacing.xl};
`;

const ProfileHeader = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.xl};
  margin-bottom: ${props => props.theme.spacing.xl};
  padding: ${props => props.theme.spacing.xl};
  background: ${props => props.theme.colors.background.primary};
  border-radius: ${props => props.theme.borderRadius.lg};
  border: 1px solid ${props => props.theme.colors.border.light};
  
  @media (max-width: 768px) {
    flex-direction: column;
    text-align: center;
  }
`;

const Avatar = styled.div`
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: ${props => props.theme.colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 3rem;
  background-image: ${props => props.avatar ? `url(${props.avatar})` : 'none'};
  background-size: cover;
  background-position: center;
  flex-shrink: 0;
`;

const ProfileInfo = styled.div`
  flex: 1;
`;

const ProfileName = styled.h1`
  margin: 0 0 ${props => props.theme.spacing.sm} 0;
  color: ${props => props.theme.colors.text.primary};
  font-size: 2rem;
`;

const Username = styled.p`
  margin: 0 0 ${props => props.theme.spacing.md} 0;
  color: ${props => props.theme.colors.text.secondary};
  font-size: 1.1rem;
`;

const Bio = styled.p`
  margin: 0 0 ${props => props.theme.spacing.md} 0;
  color: ${props => props.theme.colors.text.primary};
  line-height: 1.5;
`;

const ProfileMeta = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.lg};
  margin-bottom: ${props => props.theme.spacing.md};
  color: ${props => props.theme.colors.text.secondary};
  font-size: 0.9rem;
  
  @media (max-width: 768px) {
    justify-content: center;
  }
`;

const Stats = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.lg};
  margin-bottom: ${props => props.theme.spacing.md};
  
  @media (max-width: 768px) {
    justify-content: center;
  }
`;

const StatItem = styled.div`
  text-align: center;
  cursor: pointer;
  padding: ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.md};
  transition: background 0.2s ease;
  
  &:hover {
    background: ${props => props.theme.colors.background.secondary};
  }
`;

const StatNumber = styled.div`
  font-size: 1.5rem;
  font-weight: bold;
  color: ${props => props.theme.colors.text.primary};
`;

const StatLabel = styled.div`
  font-size: 0.9rem;
  color: ${props => props.theme.colors.text.secondary};
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  
  @media (max-width: 768px) {
    justify-content: center;
  }
`;

const TabContainer = styled.div`
  border-bottom: 1px solid ${props => props.theme.colors.border.light};
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const TabList = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.lg};
`;

const Tab = styled.button`
  background: none;
  border: none;
  padding: ${props => props.theme.spacing.md} 0;
  color: ${props => props.active ? props.theme.colors.primary : props.theme.colors.text.secondary};
  border-bottom: 2px solid ${props => props.active ? props.theme.colors.primary : 'transparent'};
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.2s ease;
  
  &:hover {
    color: ${props => props.theme.colors.primary};
  }
`;

const PlantsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: ${props => props.theme.spacing.lg};
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${props => props.theme.spacing.xl};
  color: ${props => props.theme.colors.text.secondary};
`;

const ErrorState = styled.div`
  text-align: center;
  padding: ${props => props.theme.spacing.xl};
  color: ${props => props.theme.colors.error};
`;

const PublicProfile = () => {
  const { username, userId } = useParams();
  const navigate = useNavigate();
  const { user: currentUser } = useAuth();
  const { getPublicProfile, getPublicProfileByUsername, getPublicPlants, getPublicPlantsByUsername, followUser, unfollowUser, getUserFollowers, getUserFollowing } = useSocial();
  
  const [profile, setProfile] = useState(null);
  const [plants, setPlants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('plants');
  const [isFollowing, setIsFollowing] = useState(false);

  useEffect(() => {
    loadProfile();
  }, [username, userId]);

  const loadProfile = async () => {
    try {
      setLoading(true);
      setError(null);
      
      let profileData;
      if (username) {
        profileData = await getPublicProfileByUsername(username);
      } else if (userId) {
        profileData = await getPublicProfile(userId);
      } else {
        throw new Error('No username or userId provided');
      }
      
      setProfile(profileData);

      // Set following status from API response
      if (currentUser && profileData.id !== currentUser.id) {
        setIsFollowing(profileData.is_following || false);
      }
      
      // Load plants
      try {
        let plantsData;
        if (username) {
          plantsData = await getPublicPlantsByUsername(username);
        } else if (userId) {
          plantsData = await getPublicPlants(userId);
        }
        setPlants(plantsData?.plants || []);
      } catch (error) {
        console.error('Error loading plants:', error);
        setPlants([]);
      }
      
    } catch (error) {
      console.error('Error loading profile:', error);
      setError('Perfil não encontrado ou privado');
    } finally {
      setLoading(false);
    }
  };

  const handleFollowToggle = async () => {
    if (!currentUser || !profile) return;
    
    try {
      if (isFollowing) {
        await unfollowUser(profile.id);
        setIsFollowing(false);
        setProfile(prev => ({
          ...prev,
          followers_count: Math.max(0, prev.followers_count - 1)
        }));
      } else {
        await followUser(profile.id);
        setIsFollowing(true);
        setProfile(prev => ({
          ...prev,
          followers_count: prev.followers_count + 1
        }));
      }
    } catch (error) {
      console.error('Error toggling follow:', error);
    }
  };

  const handleShowFollowers = () => {
    if (username) {
      navigate(`/profile/${username}/followers`);
    } else if (userId) {
      navigate(`/profile/user/${userId}/followers`);
    }
  };

  const handleShowFollowing = () => {
    if (username) {
      navigate(`/profile/${username}/following`);
    } else if (userId) {
      navigate(`/profile/user/${userId}/following`);
    }
  };

  if (loading) {
    return (
      <Container>
        <LoadingSpinner />
      </Container>
    );
  }

  if (error) {
    return (
      <Container>
        <ErrorState>
          <h2>😔 {error}</h2>
          <Button onClick={() => navigate('/search')}>
            Buscar outros usuários
          </Button>
        </ErrorState>
      </Container>
    );
  }

  if (!profile) {
    return (
      <Container>
        <ErrorState>
          <h2>😔 Perfil não encontrado</h2>
        </ErrorState>
      </Container>
    );
  }

  const isOwnProfile = currentUser && profile.id === currentUser.id;
  const joinDate = new Date(profile.created_at).toLocaleDateString('pt-BR', {
    month: 'long',
    year: 'numeric'
  });

  return (
    <Container>
      <ProfileHeader>
        <Avatar avatar={profile.avatar}>
          {!profile.avatar && <FaUser />}
        </Avatar>
        
        <ProfileInfo>
          <ProfileName>
            {profile.first_name} {profile.last_name}
          </ProfileName>
          
          {profile.username && (
            <Username>@{profile.username}</Username>
          )}
          
          {profile.bio && (
            <Bio>{profile.bio}</Bio>
          )}
          
          <ProfileMeta>
            {(profile.city || profile.state) && (
              <span>
                <FaMapMarkerAlt /> {profile.city}{profile.city && profile.state && ', '}{profile.state}
              </span>
            )}
            <span>
              <FaCalendarAlt /> Membro desde {joinDate}
            </span>
          </ProfileMeta>
          
          <Stats>
            <StatItem onClick={handleShowFollowers}>
              <StatNumber>{profile.followers_count}</StatNumber>
              <StatLabel>Seguidores</StatLabel>
            </StatItem>
            <StatItem onClick={handleShowFollowing}>
              <StatNumber>{profile.following_count}</StatNumber>
              <StatLabel>Seguindo</StatLabel>
            </StatItem>
            <StatItem>
              <StatNumber>{plants.length}</StatNumber>
              <StatLabel>Plantas</StatLabel>
            </StatItem>
          </Stats>
          
          {!isOwnProfile && currentUser && (
            <ActionButtons>
              <Button
                variant={isFollowing ? "outline" : "primary"}
                onClick={handleFollowToggle}
              >
                {isFollowing ? (
                  <>
                    <FaUserMinus /> Deixar de seguir
                  </>
                ) : (
                  <>
                    <FaUserPlus /> Seguir
                  </>
                )}
              </Button>
            </ActionButtons>
          )}
        </ProfileInfo>
      </ProfileHeader>

      <TabContainer>
        <TabList>
          <Tab 
            active={activeTab === 'plants'} 
            onClick={() => setActiveTab('plants')}
          >
            <FaLeaf /> Plantas
          </Tab>
        </TabList>
      </TabContainer>

      {activeTab === 'plants' && (
        <>
          {plants.length === 0 ? (
            <EmptyState>
              <FaLeaf size={48} />
              <p>Nenhuma planta pública encontrada</p>
            </EmptyState>
          ) : (
            <PlantsGrid>
              {plants.map(plant => (
                <PlantCard key={plant.id} plant={plant} />
              ))}
            </PlantsGrid>
          )}
        </>
      )}
    </Container>
  );
};

export default PublicProfile;
