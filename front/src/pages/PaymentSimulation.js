import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { toast } from 'react-toastify';

const Container = styled.div`
  max-width: 600px;
  margin: 2rem auto;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;

const Title = styled.h1`
  color: #2d5016;
  text-align: center;
  margin-bottom: 2rem;
`;

const SimulationCard = styled.div`
  background: #f8f9fa;
  border: 2px dashed #28a745;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  margin-bottom: 2rem;
`;

const PlanInfo = styled.div`
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid #e9ecef;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
`;

const Button = styled.button`
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;

  &.success {
    background: #28a745;
    color: white;
    
    &:hover {
      background: #218838;
    }
  }

  &.danger {
    background: #dc3545;
    color: white;
    
    &:hover {
      background: #c82333;
    }
  }

  &.secondary {
    background: #6c757d;
    color: white;
    
    &:hover {
      background: #5a6268;
    }
  }
`;

const PaymentSimulation = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [planInfo, setPlanInfo] = useState(null);

  useEffect(() => {
    // Extrair informações do plano da URL
    const prefId = searchParams.get('pref_id');
    if (prefId && prefId.startsWith('test-')) {
      // Simular informações do plano
      setPlanInfo({
        name: 'Plano Selecionado',
        price: 'R$ 19,90',
        description: 'Simulação de pagamento para desenvolvimento'
      });
    }
  }, [searchParams]);

  const handlePaymentSuccess = () => {
    toast.success('Pagamento simulado com sucesso!');
    // Redirecionar para página de sucesso
    navigate('/pricing?payment=success');
  };

  const handlePaymentFailure = () => {
    toast.error('Pagamento simulado como falha!');
    // Redirecionar para página de falha
    navigate('/pricing?payment=failure');
  };

  const handleCancel = () => {
    navigate('/pricing');
  };

  if (!planInfo) {
    return (
      <Container>
        <Title>Carregando...</Title>
      </Container>
    );
  }

  return (
    <Container>
      <Title>🧪 Simulação de Pagamento</Title>
      
      <SimulationCard>
        <h3>⚠️ MODO DE DESENVOLVIMENTO</h3>
        <p>Esta é uma simulação de pagamento para testes.</p>
        <p>Nenhum pagamento real será processado.</p>
      </SimulationCard>

      <PlanInfo>
        <h4>{planInfo.name}</h4>
        <p><strong>Valor:</strong> {planInfo.price}</p>
        <p>{planInfo.description}</p>
      </PlanInfo>

      <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
        <h4>Escolha o resultado da simulação:</h4>
      </div>

      <ButtonGroup>
        <Button className="success" onClick={handlePaymentSuccess}>
          ✅ Simular Sucesso
        </Button>
        <Button className="danger" onClick={handlePaymentFailure}>
          ❌ Simular Falha
        </Button>
        <Button className="secondary" onClick={handleCancel}>
          🔙 Cancelar
        </Button>
      </ButtonGroup>

      <div style={{ marginTop: '2rem', padding: '1rem', background: '#e9ecef', borderRadius: '6px', fontSize: '0.9rem' }}>
        <strong>💡 Dica:</strong> Em produção, esta página será substituída pelo checkout real do Mercado Pago.
      </div>
    </Container>
  );
};

export default PaymentSimulation;
