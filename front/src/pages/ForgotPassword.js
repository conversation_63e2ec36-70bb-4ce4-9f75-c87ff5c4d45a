import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { FaEnvelope, FaArrowLeft, FaCheckCircle } from 'react-icons/fa';
import { toast } from 'react-toastify';
import api from '../services/api';
import Button from '../components/UI/Button';

const Container = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: ${props => props.theme.spacing.md};
`;

const Card = styled.div`
  background: white;
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.xl};
  box-shadow: ${props => props.theme.shadows.lg};
  max-width: 450px;
  width: 100%;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const Icon = styled.div`
  font-size: 3rem;
  color: #007bff;
  margin-bottom: ${props => props.theme.spacing.md};
  
  &.success {
    color: #28a745;
  }
`;

const Title = styled.h1`
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing.sm};
  font-size: 1.8rem;
`;

const Subtitle = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  line-height: 1.5;
`;

const Form = styled.form`
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const FormGroup = styled.div`
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const Label = styled.label`
  display: block;
  margin-bottom: ${props => props.theme.spacing.sm};
  color: ${props => props.theme.colors.text.primary};
  font-weight: 500;
`;

const Input = styled.input`
  width: 100%;
  padding: ${props => props.theme.spacing.md};
  border: 2px solid ${props => props.theme.colors.border.light};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: 1rem;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #007bff;
  }

  &:disabled {
    background-color: #f8f9fa;
    cursor: not-allowed;
  }
`;

const SuccessMessage = styled.div`
  text-align: center;
  color: #28a745;
  
  h3 {
    margin-bottom: ${props => props.theme.spacing.md};
    color: #28a745;
  }
  
  p {
    line-height: 1.6;
    margin-bottom: ${props => props.theme.spacing.lg};
  }
`;

const BackLink = styled(Link)`
  display: inline-flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  color: #666;
  text-decoration: none;
  font-size: 0.9rem;
  margin-top: ${props => props.theme.spacing.lg};
  padding-top: ${props => props.theme.spacing.lg};
  border-top: 1px solid #eee;

  &:hover {
    color: #007bff;
  }
`;

const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!email.trim()) {
      toast.error('Por favor, digite seu email.');
      return;
    }

    setIsLoading(true);

    try {
      const response = await api.post('/api/v1/auth/forgot-password', {
        email: email.trim()
      });

      setIsSuccess(true);
      toast.success('Instruções enviadas para seu email!');

    } catch (error) {
      console.error('Erro ao solicitar reset de senha:', error);
      
      const errorMessage = error.response?.data?.detail || 
                          error.response?.data?.message || 
                          'Erro ao enviar email. Tente novamente.';
      
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  if (isSuccess) {
    return (
      <Container>
        <Card>
          <Header>
            <Icon className="success">
              <FaCheckCircle />
            </Icon>
            <SuccessMessage>
              <h3>Email Enviado!</h3>
              <p>
                Se o email <strong>{email}</strong> estiver cadastrado em nossa base, 
                você receberá instruções para redefinir sua senha em alguns minutos.
              </p>
              <p>
                Verifique também sua caixa de spam caso não encontre o email.
              </p>
            </SuccessMessage>
          </Header>
          
          <div style={{ textAlign: 'center' }}>
            <Button as={Link} to="/login">
              Voltar ao Login
            </Button>
          </div>
          
          <BackLink to="/">
            <FaArrowLeft />
            Voltar ao MeuBonsai
          </BackLink>
        </Card>
      </Container>
    );
  }

  return (
    <Container>
      <Card>
        <Header>
          <Icon>
            <FaEnvelope />
          </Icon>
          <Title>Esqueci minha senha</Title>
          <Subtitle>
            Digite seu email e enviaremos instruções para redefinir sua senha.
          </Subtitle>
        </Header>

        <Form onSubmit={handleSubmit}>
          <FormGroup>
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              disabled={isLoading}
              required
            />
          </FormGroup>

          <Button 
            type="submit" 
            fullWidth 
            disabled={isLoading}
          >
            {isLoading ? 'Enviando...' : 'Enviar Instruções'}
          </Button>
        </Form>

        <BackLink to="/">
          <FaArrowLeft />
          Voltar ao MeuBonsai
        </BackLink>
      </Card>
    </Container>
  );
};

export default ForgotPassword;
