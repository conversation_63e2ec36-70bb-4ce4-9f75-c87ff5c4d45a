import React from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { FaClock, FaInfoCircle } from 'react-icons/fa';
import Button from '../components/UI/Button';

const Container = styled.div`
  max-width: 600px;
  margin: 0 auto;
  padding: ${props => props.theme.spacing.xl};
  text-align: center;
`;

const IconContainer = styled.div`
  font-size: 4rem;
  color: #ffc107;
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const Title = styled.h1`
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing.md};
  font-size: 2rem;
`;

const Message = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  justify-content: center;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const InfoBox = styled.div`
  background: #e7f3ff;
  border: 1px solid #b3d9ff;
  border-radius: ${props => props.theme.borderRadius.md};
  padding: ${props => props.theme.spacing.md};
  margin-bottom: ${props => props.theme.spacing.lg};
  color: #0056b3;
  text-align: left;
`;

const InfoItem = styled.div`
  display: flex;
  align-items: flex-start;
  gap: ${props => props.theme.spacing.sm};
  margin-bottom: ${props => props.theme.spacing.sm};

  &:last-child {
    margin-bottom: 0;
  }
`;

const SubscriptionPending = () => {
  const navigate = useNavigate();

  const handleGoToProfile = () => {
    navigate('/profile');
  };

  const handleGoToPlants = () => {
    navigate('/profile');
  };

  const handleGoToPricing = () => {
    navigate('/pricing');
  };

  return (
    <Container>
      <IconContainer>
        <FaClock />
      </IconContainer>
      
      <Title>Pagamento Pendente</Title>
      
      <Message>
        Seu pagamento está sendo processado. Você receberá uma confirmação 
        por email assim que for aprovado.
      </Message>

      <InfoBox>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '12px' }}>
          <FaInfoCircle />
          <strong>O que acontece agora?</strong>
        </div>
        
        <InfoItem>
          <span>•</span>
          <span>Para PIX: O pagamento é processado instantaneamente</span>
        </InfoItem>
        
        <InfoItem>
          <span>•</span>
          <span>Para cartão: Pode levar até 2 dias úteis</span>
        </InfoItem>
        
        <InfoItem>
          <span>•</span>
          <span>Para boleto: Pode levar até 3 dias úteis após o pagamento</span>
        </InfoItem>
        
        <InfoItem>
          <span>•</span>
          <span>Você receberá um email de confirmação quando aprovado</span>
        </InfoItem>
      </InfoBox>

      <Message>
        Enquanto isso, você pode continuar usando o plano gratuito normalmente.
      </Message>

      <ButtonGroup>
        <Button onClick={handleGoToProfile}>
          Ver Perfil
        </Button>
        <Button variant="secondary" onClick={handleGoToPlants}>
          Ver Meus Bonsais
        </Button>
        <Button variant="outline" onClick={handleGoToPricing}>
          Ver Planos
        </Button>
      </ButtonGroup>
    </Container>
  );
};

export default SubscriptionPending;
