import React from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { FaLeaf, FaCamera, FaCalendarAlt, FaUsers, FaCheck, FaCrown, FaStar, FaBell } from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';
import Button from '../components/UI/Button';
import SEOHead from '../components/SEO/SEOHead';
import { HomePageStructuredData } from '../components/SEO/StructuredData';
import { ScrollReveal, FloatingCard, AnimatedBackground, AnimatedButton } from '../components/Animations';

const HomeContainer = styled.div`
  min-height: calc(100vh - 80px);
  display: flex;
  flex-direction: column;
`;

const HeroSection = styled.section`
  position: relative;
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
  color: #2c3e50;
  padding: ${props => props.theme.spacing.xxl} 0;
  text-align: center;
  min-height: 60vh;
  display: flex;
  align-items: center;
  overflow: hidden;

  @media (max-width: 768px) {
    min-height: 50vh;
    padding: ${props => props.theme.spacing.xl} 0;
  }
`;

const HeroContent = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 0 ${props => props.theme.spacing.md};
  position: relative;
  z-index: 2;
`;

const HeroTitle = styled.h1`
  font-size: ${props => props.theme.typography.fontSize.xxxl};
  margin-bottom: ${props => props.theme.spacing.lg};
  font-weight: ${props => props.theme.typography.fontWeight.bold};
`;

const HeroLogo = styled.img`
  max-width: 400px;
  width: 100%;
  height: auto;
  margin-bottom: ${props => props.theme.spacing.lg};
  object-fit: contain;

  @media (max-width: 768px) {
    max-width: 320px;
  }

  @media (max-width: 480px) {
    max-width: 280px;
  }
`;

const HeroSubtitle = styled.p`
  font-size: ${props => props.theme.typography.fontSize.lg};
  margin-bottom: ${props => props.theme.spacing.xl};
  opacity: 0.9;
`;

const HeroButtons = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  justify-content: center;
  flex-wrap: wrap;
`;

const FeaturesSection = styled.section`
  padding: ${props => props.theme.spacing.xxl} 0;
  background-color: ${props => props.theme.colors.surface};
`;

const FeaturesContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${props => props.theme.spacing.md};
`;

const FeaturesTitle = styled.h2`
  text-align: center;
  margin-bottom: ${props => props.theme.spacing.xxl};
  color: ${props => props.theme.colors.text.primary};
`;

const FeaturesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: ${props => props.theme.spacing.xl};

  @media (min-width: 1200px) {
    grid-template-columns: repeat(3, 1fr);

    /* Para 5 cards: 3 na primeira linha, 2 na segunda centralizados */
    & > div:nth-child(4),
    & > div:nth-child(5) {
      grid-column: span 1;
    }

    & > div:nth-child(4) {
      margin-left: auto;
      margin-right: ${props => props.theme.spacing.md};
    }

    & > div:nth-child(5) {
      margin-left: ${props => props.theme.spacing.md};
      margin-right: auto;
    }
  }

  @media (min-width: 1400px) {
    grid-template-columns: repeat(5, 1fr);

    /* Reset margins para telas muito grandes */
    & > div:nth-child(4),
    & > div:nth-child(5) {
      margin-left: 0;
      margin-right: 0;
    }
  }
`;

const FeatureCard = styled.div`
  text-align: center;
  padding: ${props => props.theme.spacing.xl};
  border-radius: ${props => props.theme.borderRadius.lg};
  background-color: ${props => props.theme.colors.background};
  border: 1px solid #e8f5e8;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    border-color: #4caf50;
  }
`;

const FeatureIcon = styled.div`
  color: ${props => props.theme.colors.primary};
  margin-bottom: ${props => props.theme.spacing.md};
  display: flex;
  justify-content: center;
`;

const FeatureTitle = styled.h3`
  margin-bottom: ${props => props.theme.spacing.sm};
  color: ${props => props.theme.colors.text.primary};
`;

const FeatureDescription = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  margin-bottom: 0;
`;

// Pricing Section Styles
const PricingSection = styled.section`
  padding: ${props => props.theme.spacing.xxl} 0;
  background: linear-gradient(135deg, #f1f8e9 0%, #e8f5e8 100%);
`;

const PricingContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${props => props.theme.spacing.md};
`;

const PricingTitle = styled.h2`
  text-align: center;
  margin-bottom: ${props => props.theme.spacing.lg};
  color: ${props => props.theme.colors.text.primary};
  font-size: ${props => props.theme.typography.fontSize.xxl};
`;

const PricingSubtitle = styled.p`
  text-align: center;
  margin-bottom: ${props => props.theme.spacing.xxl};
  color: ${props => props.theme.colors.text.secondary};
  font-size: ${props => props.theme.typography.fontSize.lg};
`;

const PlansGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: ${props => props.theme.spacing.xl};
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const PlanCard = styled.div`
  background: ${props => props.theme.colors.background.primary};
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.xl};
  border: 2px solid ${props => props.isPopular ? props.theme.colors.primary : 'rgba(0, 0, 0, 0.1)'};
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  height: 100%;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: ${props => props.theme.colors.primary};
  }
`;

const PopularBadge = styled.div`
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: ${props => props.theme.colors.primary};
  color: white;
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.md};
  border-radius: ${props => props.theme.borderRadius.full};
  font-size: ${props => props.theme.typography.fontSize.sm};
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
`;

const PlanName = styled.h3`
  font-size: ${props => props.theme.typography.fontSize.xl};
  color: ${props => props.theme.colors.text.primary};
  margin: 0 0 ${props => props.theme.spacing.sm} 0;
  text-align: center;
`;

const PlanPrice = styled.div`
  font-size: ${props => props.theme.typography.fontSize.xxl};
  font-weight: 700;
  color: ${props => props.theme.colors.primary};
  margin-bottom: ${props => props.theme.spacing.xs};
  text-align: center;
`;

const PlanBilling = styled.div`
  font-size: ${props => props.theme.typography.fontSize.sm};
  color: ${props => props.theme.colors.text.secondary};
  margin-bottom: ${props => props.theme.spacing.lg};
  text-align: center;
`;

const PlanDescription = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  margin-bottom: ${props => props.theme.spacing.lg};
  text-align: center;
  font-style: italic;
`;

const FeaturesList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0 0 ${props => props.theme.spacing.xl} 0;
  flex: 1;
`;

const Feature = styled.li`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  margin-bottom: ${props => props.theme.spacing.sm};
  color: ${props => props.theme.colors.text.primary};

  svg {
    color: ${props => props.theme.colors.success};
    flex-shrink: 0;
  }
`;

const PlanButton = styled(Button)`
  width: 100%;
  margin-top: auto;
  flex-shrink: 0;
`;

const Home = () => {
  const { isAuthenticated } = useAuth();

  const features = [
    {
      icon: <FaLeaf size={48} />,
      title: 'Gerencie suas Plantas',
      description: 'Cadastre e organize todas as suas plantas em um só lugar.'
    },
    {
      icon: <FaCamera size={48} />,
      title: 'Galeria de Fotos',
      description: 'Acompanhe o crescimento das suas plantas com uma galeria de imagens.'
    },
    {
      icon: <FaCalendarAlt size={48} />,
      title: 'Histórico de Cuidados',
      description: 'Registre todos os cuidados: adubação, poda, transplante e muito mais.'
    },
    {
      icon: <FaBell size={48} />,
      title: 'Lembretes Inteligentes',
      description: 'Nunca mais esqueça de cuidar das suas plantas com notificações personalizadas.'
    },
    {
      icon: <FaUsers size={48} />,
      title: 'Comunidade',
      description: 'Compartilhe experiências e aprenda com outros entusiastas.'
    }
  ];

  const plans = [
    {
      name: 'Gratuito',
      price: 'Gratuito',
      billing: 'Para sempre',
      description: 'Perfeito para começar sua coleção',
      features: [
        'Até 10 plantas',
        'Até 30 fotos por planta',
        'Histórico de cuidados',
        '1 foto por cuidado',
        'Lembretes e calendário de cuidados',
        'Galeria pública',
        'Conecte-se com outros bonsaístas'
      ],
      isPopular: false,
      buttonText: 'Começar Grátis',
      buttonVariant: 'outline'
    },
    {
      name: 'Premium',
      price: 'R$ 18,00',
      billing: 'por mês',
      description: 'Para colecionadores sérios e profissionais',
      features: [
        'Sem limites de plantas',
        'Sem limites de fotos',
        'Carregamento de vídeos',
        'Histórico de cuidados',
        'Até 10 fotos ou vídeos por cuidado',
        'Lembretes e calendário de cuidados',
        'Galeria pública',
        'Conecte-se com outros bonsaístas',
        'Plano anual: R$ 180,00 (economize R$ 36,00)'
      ],
      isPopular: true,
      buttonText: 'Assinar Premium',
      buttonVariant: 'primary'
    }
  ];

  return (
    <>
      <SEOHead
        title="MeuBonsai.App - Gerenciamento Profissional de Bonsai e Plantas"
        description="Plataforma completa para cuidar dos seus bonsais e plantas. Controle de rega, poda, adubação, galeria de fotos e lembretes inteligentes. Ideal para iniciantes e especialistas em bonsai."
        keywords="bonsai, plantas, jardinagem, cuidados com plantas, rega, poda, adubação, galeria de plantas, lembretes de cuidados, cultivo de bonsai, plantas ornamentais, jardinagem urbana, app plantas, aplicativo bonsai"
        url="https://meubonsai.app"
      />
      <HomePageStructuredData />
      <HomeContainer>
      <HeroSection>
        <HeroContent>
          <ScrollReveal direction="up" delay={0.2}>
            <HeroLogo src="/logo_meubonsai.png" alt="MeuBonsai.App" />
          </ScrollReveal>
          <ScrollReveal direction="up" delay={0.4}>
            <HeroSubtitle>
              O lugar perfeito para gerenciar suas plantas com carinho e dedicação.
            </HeroSubtitle>
          </ScrollReveal>
          <ScrollReveal direction="up" delay={0.6}>
            <HeroButtons>
              {isAuthenticated ? (
                <Button as={Link} to="/profile" size="large">
                  Ver Minhas Plantas
                </Button>
              ) : (
                <>
                  <Button as={Link} to="/register" size="large">
                    Começar Agora
                  </Button>
                  <Button as={Link} to="/login" variant="outline" size="large">
                    Fazer Login
                  </Button>
                </>
              )}
            </HeroButtons>
          </ScrollReveal>
        </HeroContent>
      </HeroSection>

      <FeaturesSection>
        <FeaturesContainer>
          <ScrollReveal direction="up" delay={0.2}>
            <FeaturesTitle>Por que escolher o MeuBonsai.App?</FeaturesTitle>
          </ScrollReveal>
          <FeaturesGrid>
            {features.map((feature, index) => (
              <ScrollReveal key={index} direction="up" delay={0.3 + index * 0.1}>
                <FeatureCard>
                  <FeatureIcon>{feature.icon}</FeatureIcon>
                  <FeatureTitle>{feature.title}</FeatureTitle>
                  <FeatureDescription>{feature.description}</FeatureDescription>
                </FeatureCard>
              </ScrollReveal>
            ))}
          </FeaturesGrid>
        </FeaturesContainer>
      </FeaturesSection>

      <PricingSection>
        <PricingContainer>
          <ScrollReveal direction="up" delay={0.2}>
            <PricingTitle>Escolha o Plano Ideal</PricingTitle>
            <PricingSubtitle>
              Gerencie suas plantas com as ferramentas certas para cada necessidade
            </PricingSubtitle>
          </ScrollReveal>
          <PlansGrid>
            {plans.map((plan, index) => (
              <ScrollReveal key={index} direction="up" delay={0.4 + index * 0.2}>
                <PlanCard isPopular={plan.isPopular}>
                  {plan.isPopular && (
                    <PopularBadge>
                      <FaStar size={12} />
                      Mais Popular
                    </PopularBadge>
                  )}

                  <PlanName>{plan.name}</PlanName>
                  <PlanPrice>{plan.price}</PlanPrice>
                  <PlanBilling>{plan.billing}</PlanBilling>
                  <PlanDescription>{plan.description}</PlanDescription>

                  <FeaturesList>
                    {plan.features.map((feature, featureIndex) => (
                      <Feature key={featureIndex}>
                        <FaCheck size={14} />
                        {feature}
                      </Feature>
                    ))}
                  </FeaturesList>

                  <Button
                    as={Link}
                    to={isAuthenticated ? "/pricing" : "/register"}
                    variant={plan.buttonVariant}
                    size="large"
                  >
                    {plan.buttonText}
                  </Button>
                </PlanCard>
              </ScrollReveal>
            ))}
          </PlansGrid>

          {isAuthenticated && (
            <ScrollReveal direction="up" delay={0.8}>
              <div style={{ textAlign: 'center' }}>
                <Button as={Link} to="/pricing" variant="outline" size="large">
                  Ver Detalhes dos Planos
                </Button>
              </div>
            </ScrollReveal>
          )}
        </PricingContainer>
      </PricingSection>
    </HomeContainer>
    </>
  );
};

export default Home;
