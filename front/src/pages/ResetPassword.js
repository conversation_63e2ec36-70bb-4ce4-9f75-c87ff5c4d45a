import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate, Link } from 'react-router-dom';
import styled from 'styled-components';
import { FaLock, FaEye, FaEyeSlash, FaCheckCircle, FaTimesCircle } from 'react-icons/fa';
import { toast } from 'react-toastify';
import api from '../services/api';
import Button from '../components/UI/Button';

const Container = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: ${props => props.theme.spacing.md};
`;

const Card = styled.div`
  background: white;
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.xl};
  box-shadow: ${props => props.theme.shadows.lg};
  max-width: 450px;
  width: 100%;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const Icon = styled.div`
  font-size: 3rem;
  margin-bottom: ${props => props.theme.spacing.md};
  
  &.primary {
    color: #007bff;
  }
  
  &.success {
    color: #28a745;
  }
  
  &.error {
    color: #dc3545;
  }
`;

const Title = styled.h1`
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing.sm};
  font-size: 1.8rem;
`;

const Subtitle = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  line-height: 1.5;
`;

const Form = styled.form`
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const FormGroup = styled.div`
  margin-bottom: ${props => props.theme.spacing.lg};
  position: relative;
`;

const Label = styled.label`
  display: block;
  margin-bottom: ${props => props.theme.spacing.sm};
  color: ${props => props.theme.colors.text.primary};
  font-weight: 500;
`;

const InputWrapper = styled.div`
  position: relative;
`;

const Input = styled.input`
  width: 100%;
  padding: ${props => props.theme.spacing.md};
  padding-right: 3rem;
  border: 2px solid ${props => props.theme.colors.border.light};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: 1rem;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #007bff;
  }

  &:disabled {
    background-color: #f8f9fa;
    cursor: not-allowed;
  }
`;

const ToggleButton = styled.button`
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 1.1rem;

  &:hover {
    color: #007bff;
  }
`;

const PasswordHint = styled.div`
  margin-top: ${props => props.theme.spacing.sm};
  font-size: 0.85rem;
  color: #666;
`;

const SuccessMessage = styled.div`
  text-align: center;
  color: #28a745;
  
  h3 {
    margin-bottom: ${props => props.theme.spacing.md};
    color: #28a745;
  }
  
  p {
    line-height: 1.6;
    margin-bottom: ${props => props.theme.spacing.lg};
  }
`;

const ErrorMessage = styled.div`
  text-align: center;
  color: #dc3545;
  
  h3 {
    margin-bottom: ${props => props.theme.spacing.md};
    color: #dc3545;
  }
  
  p {
    line-height: 1.6;
    margin-bottom: ${props => props.theme.spacing.lg};
  }
`;

const ResetPassword = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [status, setStatus] = useState('form'); // form, success, error
  const [message, setMessage] = useState('');

  const token = searchParams.get('token');

  useEffect(() => {
    if (!token) {
      setStatus('error');
      setMessage('Token de redefinição não encontrado na URL.');
    }
  }, [token]);

  const validatePassword = (password) => {
    return password.length >= 8;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!password.trim()) {
      toast.error('Por favor, digite uma nova senha.');
      return;
    }

    if (!validatePassword(password)) {
      toast.error('A senha deve ter pelo menos 8 caracteres.');
      return;
    }

    if (password !== confirmPassword) {
      toast.error('As senhas não coincidem.');
      return;
    }

    setIsLoading(true);

    try {
      const response = await api.post('/api/v1/auth/reset-password', {
        token: token,
        new_password: password
      });

      setStatus('success');
      setMessage(response.data.message || 'Senha redefinida com sucesso!');
      toast.success('Senha redefinida com sucesso!');

    } catch (error) {
      console.error('Erro ao redefinir senha:', error);
      
      const errorMessage = error.response?.data?.detail || 
                          error.response?.data?.message || 
                          'Erro ao redefinir senha. O token pode estar expirado.';
      
      setStatus('error');
      setMessage(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  if (status === 'success') {
    return (
      <Container>
        <Card>
          <Header>
            <Icon className="success">
              <FaCheckCircle />
            </Icon>
            <SuccessMessage>
              <h3>Senha Redefinida!</h3>
              <p>{message}</p>
            </SuccessMessage>
          </Header>
          
          <div style={{ textAlign: 'center' }}>
            <Button onClick={() => navigate('/login')}>
              Fazer Login
            </Button>
          </div>
        </Card>
      </Container>
    );
  }

  if (status === 'error') {
    return (
      <Container>
        <Card>
          <Header>
            <Icon className="error">
              <FaTimesCircle />
            </Icon>
            <ErrorMessage>
              <h3>Erro na Redefinição</h3>
              <p>{message}</p>
            </ErrorMessage>
          </Header>
          
          <div style={{ textAlign: 'center' }}>
            <Button 
              variant="secondary" 
              onClick={() => navigate('/forgot-password')}
              style={{ marginRight: '1rem' }}
            >
              Solicitar Novo Link
            </Button>
            <Button onClick={() => navigate('/login')}>
              Voltar ao Login
            </Button>
          </div>
        </Card>
      </Container>
    );
  }

  return (
    <Container>
      <Card>
        <Header>
          <Icon className="primary">
            <FaLock />
          </Icon>
          <Title>Redefinir Senha</Title>
          <Subtitle>
            Digite sua nova senha abaixo.
          </Subtitle>
        </Header>

        <Form onSubmit={handleSubmit}>
          <FormGroup>
            <Label htmlFor="password">Nova Senha</Label>
            <InputWrapper>
              <Input
                id="password"
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Digite sua nova senha"
                disabled={isLoading}
                required
              />
              <ToggleButton
                type="button"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <FaEyeSlash /> : <FaEye />}
              </ToggleButton>
            </InputWrapper>
            <PasswordHint>
              A senha deve ter pelo menos 8 caracteres.
            </PasswordHint>
          </FormGroup>

          <FormGroup>
            <Label htmlFor="confirmPassword">Confirmar Nova Senha</Label>
            <InputWrapper>
              <Input
                id="confirmPassword"
                type={showConfirmPassword ? 'text' : 'password'}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Confirme sua nova senha"
                disabled={isLoading}
                required
              />
              <ToggleButton
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
              </ToggleButton>
            </InputWrapper>
          </FormGroup>

          <Button 
            type="submit" 
            fullWidth 
            disabled={isLoading}
          >
            {isLoading ? 'Redefinindo...' : 'Redefinir Senha'}
          </Button>
        </Form>

        <div style={{ textAlign: 'center' }}>
          <Link 
            to="/login" 
            style={{ 
              color: '#007bff', 
              textDecoration: 'none',
              fontSize: '0.9rem'
            }}
          >
            Voltar ao Login
          </Link>
        </div>
      </Card>
    </Container>
  );
};

export default ResetPassword;
