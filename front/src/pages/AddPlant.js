import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import styled from 'styled-components';
import { FaLeaf, FaArrowLeft } from 'react-icons/fa';
import { toast } from 'react-toastify';
import Button from '../components/UI/Button';
import { plantsService } from '../services/plantsService';

const AddPlantContainer = styled.div`
  min-height: calc(100vh - 80px);
  padding: ${props => props.theme.spacing.lg};
  max-width: 800px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.md};
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const BackButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.text.secondary};
  cursor: pointer;
  padding: ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.md};
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background-color: ${props => props.theme.colors.background};
    color: ${props => props.theme.colors.primary};
  }
`;

const Title = styled.h1`
  color: ${props => props.theme.colors.text.primary};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  margin: 0;
`;

const Form = styled.form`
  background-color: ${props => props.theme.colors.surface};
  padding: ${props => props.theme.spacing.xl};
  border-radius: ${props => props.theme.borderRadius.lg};
  box-shadow: ${props => props.theme.shadows.md};
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.lg};
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${props => props.theme.spacing.md};

  @media (max-width: ${props => props.theme.breakpoints.sm}) {
    grid-template-columns: 1fr;
  }
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.xs};
`;

const Label = styled.label`
  color: ${props => props.theme.colors.text.primary};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
`;

const Input = styled.input`
  width: 100%;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.error ? props.theme.colors.error : props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.md};
  
  &:focus {
    border-color: ${props => props.error ? props.theme.colors.error : props.theme.colors.primary};
    outline: none;
  }
`;

const Select = styled.select`
  width: 100%;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.error ? props.theme.colors.error : props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.md};
  background-color: white;
  
  &:focus {
    border-color: ${props => props.error ? props.theme.colors.error : props.theme.colors.primary};
    outline: none;
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.error ? props.theme.colors.error : props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.md};
  min-height: 100px;
  resize: vertical;
  
  &:focus {
    border-color: ${props => props.error ? props.theme.colors.error : props.theme.colors.primary};
    outline: none;
  }
`;

const ErrorMessage = styled.span`
  color: ${props => props.theme.colors.error};
  font-size: ${props => props.theme.typography.fontSize.sm};
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  justify-content: flex-end;
  margin-top: ${props => props.theme.spacing.lg};
`;

const AddPlant = () => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    setError
  } = useForm();

  const onSubmit = async (data) => {
    setLoading(true);
    try {
      // Transform frontend data to match backend schema
      const plantData = {
        name: data.name,
        scientific_name: data.species || null,
        plant_type: mapPlantTypeToBackend(data.plant_type),
        plant_category: data.plant_type, // Enviar a categoria específica
        description: data.description || null,
        acquisition_date: data.acquisition_date || null,
        location: data.location || null,
        style: data.bonsai_style || null,
        estimated_age: data.estimated_age || null,
      };

      console.log('Sending plant data:', plantData);
      const response = await plantsService.createPlant(plantData);
      toast.success('Bonsai adicionado com sucesso!');

      // Redirect to plant detail page for immediate photo upload and editing
      if (response.data && response.data.id) {
        navigate(`/plants/${response.data.id}`);
      } else {
        // Fallback to profile if no ID returned
        navigate('/profile');
      }
    } catch (error) {
      console.error('Erro ao adicionar bonsai:', error);
      if (error.response?.data?.detail) {
        if (typeof error.response.data.detail === 'object') {
          Object.keys(error.response.data.detail).forEach(field => {
            setError(field, { message: error.response.data.detail[field][0] });
          });
        } else {
          toast.error(error.response.data.detail);
        }
      } else {
        toast.error('Erro ao adicionar bonsai');
      }
    } finally {
      setLoading(false);
    }
  };

  // Map frontend plant types to backend enum values
  const mapPlantTypeToBackend = (frontendType) => {
    const mapping = {
      'conifera': 'bonsai',
      'caducifolia': 'bonsai',
      'perene_folhosa': 'bonsai',
      'frutifera': 'bonsai',
      'florifera': 'bonsai',
      'suculenta': 'suculenta',
      'outro': 'outro'
    };
    return mapping[frontendType] || 'bonsai';
  };

  return (
    <AddPlantContainer>
      <Header>
        <BackButton onClick={() => navigate('/profile')}>
          <FaArrowLeft size={20} />
        </BackButton>
        <Title>
          <FaLeaf />
          Adicionar Nova Planta
        </Title>
      </Header>

      <Form onSubmit={handleSubmit(onSubmit)}>
        <FormRow>
          <FormGroup>
            <Label htmlFor="name">Nome da Planta *</Label>
            <Input
              id="name"
              type="text"
              placeholder="Ex: Meu Bonsai de Ficus"
              error={errors.name}
              {...register('name', {
                required: 'Nome é obrigatório'
              })}
            />
            {errors.name && <ErrorMessage>{errors.name.message}</ErrorMessage>}
          </FormGroup>

          <FormGroup>
            <Label htmlFor="plant_type">Categoria da Planta *</Label>
            <Select
              id="plant_type"
              error={errors.plant_type}
              {...register('plant_type', {
                required: 'Categoria é obrigatória'
              })}
            >
              <option value="">Selecione a categoria</option>
              <option value="conifera">Conífera (Pinheiros, Juníperos)</option>
              <option value="caducifolia">Caducifólia (Perde folhas no outono)</option>
              <option value="perene_folhosa">Perene Folhosa (Ficus, Carmona)</option>
              <option value="frutifera">Frutífera (Jabuticaba, Pitanga, Romã)</option>
              <option value="florifera">Florífera (Azaléia, Cerejeira, Bougainvillea)</option>
              <option value="suculenta">Suculenta (Jade, Portulacaria)</option>
              <option value="outro">Outro</option>
            </Select>
            {errors.plant_type && <ErrorMessage>{errors.plant_type.message}</ErrorMessage>}
          </FormGroup>
        </FormRow>

        <FormRow>
          <FormGroup>
            <Label htmlFor="species">Espécie/Nome Científico</Label>
            <Input
              id="species"
              type="text"
              placeholder="Ex: Ficus benjamina, Juniperus chinensis, Acer palmatum"
              error={errors.species}
              {...register('species')}
            />
            {errors.species && <ErrorMessage>{errors.species.message}</ErrorMessage>}
            <small style={{ color: '#666', fontSize: '12px', marginTop: '4px' }}>
              Exemplos: Ficus retusa, Juniperus rigida, Pinus thunbergii, Eugenia uniflora (Pitanga)
            </small>
          </FormGroup>

          <FormGroup>
            <Label htmlFor="acquisition_date">Data de Aquisição</Label>
            <Input
              id="acquisition_date"
              type="date"
              error={errors.acquisition_date}
              {...register('acquisition_date')}
            />
            {errors.acquisition_date && <ErrorMessage>{errors.acquisition_date.message}</ErrorMessage>}
            <small style={{ color: '#666', fontSize: '12px', marginTop: '4px' }}>
              Opcional - deixe em branco se não souber a data exata
            </small>
          </FormGroup>
        </FormRow>

        <FormRow>
          <FormGroup>
            <Label htmlFor="location">Localização</Label>
            <Input
              id="location"
              type="text"
              placeholder="Ex: Varanda, Jardim, Bancada externa"
              error={errors.location}
              {...register('location')}
            />
            {errors.location && <ErrorMessage>{errors.location.message}</ErrorMessage>}
          </FormGroup>

          <FormGroup>
            <Label htmlFor="estimated_age">Idade Estimada</Label>
            <Input
              id="estimated_age"
              type="text"
              placeholder="Ex: 5 anos, 2-3 anos, Muda"
              error={errors.estimated_age}
              {...register('estimated_age')}
            />
            {errors.estimated_age && <ErrorMessage>{errors.estimated_age.message}</ErrorMessage>}
            <small style={{ color: '#666', fontSize: '12px', marginTop: '4px' }}>
              Informação adicional (será incluída na descrição)
            </small>
          </FormGroup>
        </FormRow>

        <FormRow>
          <FormGroup>
            <Label htmlFor="bonsai_style">Estilo da Planta</Label>
            <Select
              id="bonsai_style"
              error={errors.bonsai_style}
              {...register('bonsai_style')}
            >
              <option value="">Selecione o estilo (opcional)</option>
              <option value="Chokkan (Formal Ereto)">Chokkan (Formal Ereto)</option>
              <option value="Moyogi (Informal Ereto)">Moyogi (Informal Ereto)</option>
              <option value="Shakan (Inclinado)">Shakan (Inclinado)</option>
              <option value="Kengai (Cascata)">Kengai (Cascata)</option>
              <option value="Han-Kengai (Semi-cascata)">Han-Kengai (Semi-cascata)</option>
              <option value="Bunjingi (Literati)">Bunjingi (Literati)</option>
              <option value="Hokidachi (Vassoura)">Hokidachi (Vassoura)</option>
              <option value="Kabudachi (Múltiplos troncos)">Kabudachi (Múltiplos troncos)</option>
              <option value="Yose-ue (Floresta)">Yose-ue (Floresta)</option>
              <option value="Ishitsuki (Sobre rocha)">Ishitsuki (Sobre rocha)</option>
              <option value="Neagari (Raízes expostas)">Neagari (Raízes expostas)</option>
              <option value="Outro/Em desenvolvimento">Outro/Em desenvolvimento</option>
            </Select>
            {errors.bonsai_style && <ErrorMessage>{errors.bonsai_style.message}</ErrorMessage>}
            <small style={{ color: '#666', fontSize: '12px', marginTop: '4px' }}>
              Informação adicional (será incluída na descrição)
            </small>
          </FormGroup>
        </FormRow>

        <FormGroup>
          <Label htmlFor="description">Descrição e História</Label>
          <TextArea
            id="description"
            placeholder="Conte a história do seu bonsai: como adquiriu, técnicas aplicadas, características especiais, objetivos futuros, etc."
            error={errors.description}
            maxLength={2000}
            {...register('description')}
          />
          {errors.description && <ErrorMessage>{errors.description.message}</ErrorMessage>}
          <small style={{ color: '#666', fontSize: '12px', marginTop: '4px' }}>
            Máximo 2000 caracteres
          </small>
        </FormGroup>

        <ButtonGroup>
          <Button
            type="button"
            variant="secondary"
            onClick={() => navigate('/profile')}
            disabled={loading}
          >
            Cancelar
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? 'Adicionando...' : 'Adicionar Bonsai'}
          </Button>
        </ButtonGroup>
      </Form>
    </AddPlantContainer>
  );
};

export default AddPlant;
