import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { FaCheckCircle, FaSpinner } from 'react-icons/fa';
import { toast } from 'react-toastify';
import Button from '../components/UI/Button';
import subscriptionService from '../services/subscriptionService';

const Container = styled.div`
  max-width: 600px;
  margin: 0 auto;
  padding: ${props => props.theme.spacing.xl};
  text-align: center;
`;

const IconContainer = styled.div`
  font-size: 4rem;
  color: ${props => props.theme.colors.success};
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const Title = styled.h1`
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing.md};
  font-size: 2rem;
`;

const Message = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  justify-content: center;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${props => props.theme.spacing.md};
  color: ${props => props.theme.colors.text.secondary};
`;

const SubscriptionSuccess = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [paymentStatus, setPaymentStatus] = useState(null);

  useEffect(() => {
    // Verificar status do pagamento
    const checkPaymentStatus = async () => {
      try {
        // Aguardar um pouco para o webhook processar
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Verificar assinatura atual
        const response = await subscriptionService.getCurrentSubscription();
        
        if (response.subscription && response.subscription.status === 'active') {
          setPaymentStatus('success');
          toast.success('Pagamento aprovado! Sua assinatura está ativa.');
        } else {
          setPaymentStatus('pending');
        }
      } catch (error) {
        console.error('Error checking payment status:', error);
        setPaymentStatus('error');
      } finally {
        setLoading(false);
      }
    };

    checkPaymentStatus();
  }, []);

  const handleGoToProfile = () => {
    navigate('/profile');
  };

  const handleGoToPlants = () => {
    navigate('/profile');
  };

  const handleGoToPricing = () => {
    navigate('/pricing');
  };

  if (loading) {
    return (
      <Container>
        <LoadingContainer>
          <FaSpinner size={48} className="fa-spin" />
          <Title>Verificando pagamento...</Title>
          <Message>
            Aguarde enquanto confirmamos seu pagamento. Isso pode levar alguns segundos.
          </Message>
        </LoadingContainer>
      </Container>
    );
  }

  if (paymentStatus === 'success') {
    return (
      <Container>
        <IconContainer>
          <FaCheckCircle />
        </IconContainer>
        
        <Title>Pagamento Aprovado!</Title>
        
        <Message>
          Parabéns! Sua assinatura foi ativada com sucesso. 
          Agora você pode aproveitar todos os recursos do seu novo plano.
        </Message>

        <ButtonGroup>
          <Button onClick={handleGoToPlants}>
            Ver Meus Bonsais
          </Button>
          <Button variant="secondary" onClick={handleGoToProfile}>
            Ver Perfil
          </Button>
        </ButtonGroup>
      </Container>
    );
  }

  if (paymentStatus === 'pending') {
    return (
      <Container>
        <IconContainer>
          <FaSpinner size={48} style={{ color: '#ffc107' }} />
        </IconContainer>
        
        <Title>Pagamento Pendente</Title>
        
        <Message>
          Seu pagamento está sendo processado. Você receberá uma confirmação 
          por email assim que for aprovado. Isso pode levar alguns minutos.
        </Message>

        <ButtonGroup>
          <Button onClick={handleGoToProfile}>
            Ver Perfil
          </Button>
          <Button variant="secondary" onClick={handleGoToPricing}>
            Ver Planos
          </Button>
        </ButtonGroup>
      </Container>
    );
  }

  // Error case
  return (
    <Container>
      <IconContainer style={{ color: '#dc3545' }}>
        <FaSpinner size={48} />
      </IconContainer>
      
      <Title>Erro na Verificação</Title>
      
      <Message>
        Não foi possível verificar o status do seu pagamento. 
        Por favor, verifique sua assinatura no perfil ou entre em contato conosco.
      </Message>

      <ButtonGroup>
        <Button onClick={handleGoToProfile}>
          Ver Perfil
        </Button>
        <Button variant="secondary" onClick={handleGoToPricing}>
          Tentar Novamente
        </Button>
      </ButtonGroup>
    </Container>
  );
};

export default SubscriptionSuccess;
