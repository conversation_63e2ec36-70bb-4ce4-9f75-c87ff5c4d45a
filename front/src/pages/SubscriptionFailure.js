import React from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import styled from 'styled-components';
import { FaTimesCircle, FaExclamationTriangle } from 'react-icons/fa';
import Button from '../components/UI/Button';

const Container = styled.div`
  max-width: 600px;
  margin: 0 auto;
  padding: ${props => props.theme.spacing.xl};
  text-align: center;
`;

const IconContainer = styled.div`
  font-size: 4rem;
  color: ${props => props.theme.colors.error};
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const Title = styled.h1`
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing.md};
  font-size: 2rem;
`;

const Message = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  justify-content: center;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const ReasonBox = styled.div`
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: ${props => props.theme.borderRadius.md};
  padding: ${props => props.theme.spacing.md};
  margin-bottom: ${props => props.theme.spacing.lg};
  color: #856404;
`;

const SubscriptionFailure = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  // Obter parâmetros do Mercado Pago
  const paymentStatus = searchParams.get('payment_status');
  const statusDetail = searchParams.get('status_detail');

  const handleTryAgain = () => {
    navigate('/pricing');
  };

  const handleGoToProfile = () => {
    navigate('/profile');
  };

  const handleGoToPlants = () => {
    navigate('/profile');
  };

  const getFailureReason = () => {
    switch (statusDetail) {
      case 'cc_rejected_insufficient_amount':
        return 'Saldo insuficiente no cartão';
      case 'cc_rejected_bad_filled_card_number':
        return 'Número do cartão inválido';
      case 'cc_rejected_bad_filled_date':
        return 'Data de vencimento inválida';
      case 'cc_rejected_bad_filled_security_code':
        return 'Código de segurança inválido';
      case 'cc_rejected_call_for_authorize':
        return 'Pagamento rejeitado pelo banco. Entre em contato com seu banco.';
      case 'cc_rejected_card_disabled':
        return 'Cartão desabilitado';
      case 'cc_rejected_duplicated_payment':
        return 'Pagamento duplicado';
      case 'cc_rejected_high_risk':
        return 'Pagamento rejeitado por segurança';
      default:
        return 'Pagamento não pôde ser processado';
    }
  };

  return (
    <Container>
      <IconContainer>
        <FaTimesCircle />
      </IconContainer>
      
      <Title>Pagamento Não Aprovado</Title>
      
      <Message>
        Infelizmente, não foi possível processar seu pagamento. 
        Verifique os dados e tente novamente.
      </Message>

      {statusDetail && (
        <ReasonBox>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
            <FaExclamationTriangle />
            <strong>Motivo:</strong>
          </div>
          {getFailureReason()}
        </ReasonBox>
      )}

      <Message>
        Você pode tentar novamente com outro método de pagamento ou 
        continuar usando o plano gratuito.
      </Message>

      <ButtonGroup>
        <Button onClick={handleTryAgain}>
          Tentar Novamente
        </Button>
        <Button variant="secondary" onClick={handleGoToProfile}>
          Ver Perfil
        </Button>
        <Button variant="outline" onClick={handleGoToPlants}>
          Continuar com Plano Gratuito
        </Button>
      </ButtonGroup>
    </Container>
  );
};

export default SubscriptionFailure;
