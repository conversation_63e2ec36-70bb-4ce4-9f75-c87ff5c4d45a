import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FaCheck, FaStar, FaLeaf, FaCreditCard, FaCalendarAlt, FaExclamationTriangle, FaHistory } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { useAuth } from '../contexts/AuthContext';
import subscriptionService from '../services/subscriptionService';
import Button from '../components/UI/Button';
import CheckoutModal from '../components/CheckoutModal';
import CancelSubscriptionModal from '../components/CancelSubscriptionModal';
import PlanSelectionModal from '../components/PlanSelectionModal';
import GracePeriodAlert from '../components/GracePeriodAlert';
import SubscriptionHistory from '../components/SubscriptionHistory';
import SEOHead from '../components/SEO/SEOHead';
import { PricingPageStructuredData } from '../components/SEO/StructuredData';
import { getBasePlanDisplayName } from '../utils/planUtils';



const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: ${props => props.theme.spacing.xl};
`;

const CurrentSubscriptionCard = styled.div`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 3rem;
  color: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
`;

const CurrentSubscriptionHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;

  h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
  }
`;

const CurrentSubscriptionInfo = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
`;

const CurrentSubscriptionDetail = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const CurrentSubscriptionLabel = styled.span`
  font-size: 0.9rem;
  opacity: 0.8;
  font-weight: 500;
`;

const CurrentSubscriptionValue = styled.span`
  font-size: 1.1rem;
  font-weight: 600;
`;

const CurrentSubscriptionStatus = styled.span`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  width: fit-content;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
  }
`;

const CurrentSubscriptionActions = styled.div`
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
`;

const CancelButton = styled(Button)`
  background: rgba(220, 53, 69, 0.9);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);

  &:hover {
    background: rgba(200, 35, 51, 1);
  }
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const BillingToggle = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin: 2rem 0;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 12px;
  max-width: 400px;
  margin: 2rem auto;
`;

const ToggleButton = styled.button`
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  background: ${props => props.active ? '#28a745' : 'transparent'};
  color: ${props => props.active ? 'white' : '#666'};

  &:hover {
    background: ${props => props.active ? '#218838' : '#e9ecef'};
  }
`;

const SavingsLabel = styled.span`
  background: #28a745;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  margin-left: 0.5rem;
`;

const Title = styled.h1`
  font-size: ${props => props.theme.typography.fontSize.xxl};
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing.md};
`;

const Subtitle = styled.p`
  font-size: ${props => props.theme.typography.fontSize.lg};
  color: ${props => props.theme.colors.text.secondary};
  margin: 0;
`;

const PlansGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: ${props => props.theme.spacing.xl};
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const PlanCard = styled.div`
  background: ${props => props.theme.colors.background.secondary};
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.xl};
  border: 2px solid ${props => {
    if (props.isCurrent) return props.theme.colors.success;
    if (props.isPopular) return props.theme.colors.primary;
    return '#e0e0e0';
  }};
  position: relative;
  transition: all 0.3s ease;

  ${props => props.isCurrent && `
    background: ${props.theme.colors.success}08;
  `}

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: ${props => props.theme.colors.primary};
  }
`;

const PopularBadge = styled.div`
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: ${props => props.theme.colors.primary};
  color: white;
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.md};
  border-radius: ${props => props.theme.borderRadius.full};
  font-size: ${props => props.theme.typography.fontSize.sm};
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
`;

const CurrentBadge = styled.div`
  position: absolute;
  top: -12px;
  right: 20px;
  background: ${props => props.theme.colors.success};
  color: white;
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.md};
  border-radius: ${props => props.theme.borderRadius.full};
  font-size: ${props => props.theme.typography.fontSize.sm};
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xs};
`;

const PlanName = styled.h3`
  font-size: ${props => props.theme.typography.fontSize.xl};
  color: ${props => props.theme.colors.text.primary};
  margin: 0 0 ${props => props.theme.spacing.sm} 0;
`;

const PlanPrice = styled.div`
  font-size: ${props => props.theme.typography.fontSize.xxl};
  font-weight: 700;
  color: ${props => props.theme.colors.primary};
  margin-bottom: ${props => props.theme.spacing.xs};
`;

const PlanBilling = styled.div`
  font-size: ${props => props.theme.typography.fontSize.sm};
  color: ${props => props.theme.colors.text.secondary};
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const PlanDescription = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const FeaturesList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0 0 ${props => props.theme.spacing.xl} 0;
`;

const Feature = styled.li`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  margin-bottom: ${props => props.theme.spacing.sm};
  color: ${props => props.theme.colors.text.primary};

  svg {
    color: ${props => props.theme.colors.success};
    flex-shrink: 0;
  }
`;

const UsageSection = styled.div`
  background: ${props => props.theme.colors.background.secondary};
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.xl};
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const UsageTitle = styled.h3`
  color: ${props => props.theme.colors.text.primary};
  margin: 0 0 ${props => props.theme.spacing.lg} 0;
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const UsageGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${props => props.theme.spacing.lg};
`;

const UsageCard = styled.div`
  text-align: center;
  padding: ${props => props.theme.spacing.lg};
  background: ${props => props.theme.colors.background.primary};
  border-radius: ${props => props.theme.borderRadius.md};
`;

const UsageNumber = styled.div`
  font-size: ${props => props.theme.typography.fontSize.xl};
  font-weight: 700;
  color: ${props => props.theme.colors.primary};
  margin-bottom: ${props => props.theme.spacing.xs};
`;

const UsageLabel = styled.div`
  color: ${props => props.theme.colors.text.secondary};
  font-size: ${props => props.theme.typography.fontSize.sm};
`;

const UsageLimit = styled.div`
  color: ${props => props.theme.colors.text.tertiary};
  font-size: ${props => props.theme.typography.fontSize.xs};
  margin-top: ${props => props.theme.spacing.xs};
`;

const CurrentPlanSection = styled.div`
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border-radius: ${props => props.theme.borderRadius.lg};
  padding: ${props => props.theme.spacing.xl};
  margin-bottom: ${props => props.theme.spacing.xl};
  color: white;
  text-align: center;
`;

const CurrentPlanTitle = styled.h3`
  color: white;
  margin: 0 0 ${props => props.theme.spacing.sm} 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${props => props.theme.spacing.sm};
`;

const CurrentPlanName = styled.div`
  font-size: ${props => props.theme.typography.fontSize.xl};
  font-weight: 700;
  margin-bottom: ${props => props.theme.spacing.xs};
`;

const CurrentPlanDetails = styled.div`
  opacity: 0.9;
  font-size: ${props => props.theme.typography.fontSize.sm};
`;

const Pricing = () => {
  const { user } = useAuth();
  const [pricingData, setPricingData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [processingPlan, setProcessingPlan] = useState(null);
  const [checkoutPlan, setCheckoutPlan] = useState(null);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [showPlanSelection, setShowPlanSelection] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [currentSubscription, setCurrentSubscription] = useState(null);
  const [subscriptionData, setSubscriptionData] = useState(null);
  const [showHistoryModal, setShowHistoryModal] = useState(false);


  useEffect(() => {
    loadPricingData();
    loadCurrentSubscription();
  }, []);

  const loadCurrentSubscription = async () => {
    // Só tentar carregar assinatura se o usuário estiver logado
    if (!user) {
      console.log('🔍 Usuário não logado - pulando carregamento de assinatura');
      setCurrentSubscription(null);
      setSubscriptionData(null);
      return;
    }

    try {
      console.log('🔍 Carregando assinatura atual...');
      const response = await subscriptionService.getCurrentSubscription();
      console.log('🔍 Response completa:', response);
      console.log('🔍 Response.data:', response.data);

      // Salvar dados completos para período de graça
      setSubscriptionData(response.data);

      // O endpoint /current retorna { subscription: {...} } ou { plan: {...}, subscription: {...} }
      const subscription = response.data?.subscription;
      console.log('🔍 Subscription extraída:', subscription);
      setCurrentSubscription(subscription);
    } catch (error) {
      console.error('❌ Error loading current subscription:', error);
      // Se for erro de autenticação (401), não é um erro real para usuários não logados
      if (error.response?.status === 401) {
        console.log('🔍 Erro 401 - usuário não autenticado');
      }
      setCurrentSubscription(null);
      setSubscriptionData(null);
    }
  };

  const loadPricingData = async () => {
    try {
      console.log('🔍 [PRICING] Loading pricing data...');
      const response = await subscriptionService.getPricingData();
      console.log('📊 [PRICING] Response received:', response);
      console.log('📊 [PRICING] Response data:', response.data);

      if (response.data) {
        console.log('📊 [PRICING] Plans:', response.data.plans);
        console.log('📊 [PRICING] Current plan info:', response.data.current_plan_info);
        console.log('📊 [PRICING] Usage:', response.data.usage);
      }

      setPricingData(response.data);
    } catch (error) {
      console.error('❌ [PRICING] Error loading pricing data:', error);
      toast.error('Erro ao carregar planos');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelSubscription = () => {
    console.log('🔍 handleCancelSubscription chamado');
    console.log('🔍 currentSubscription:', currentSubscription);
    console.log('🔍 current_plan_info:', pricingData?.current_plan_info);

    // Verificar se há uma assinatura ativa antes de abrir o modal
    if (!currentSubscription || !currentSubscription.id) {
      console.log('❌ Nenhuma assinatura ativa encontrada!');
      toast.error('Nenhuma assinatura ativa encontrada');
      return;
    }

    setShowCancelModal(true);
  };

  const handleCancelSuccess = () => {
    setShowCancelModal(false);
    loadPricingData(); // Recarregar dados de pricing
    loadCurrentSubscription(); // Recarregar assinatura atual
    toast.success('Assinatura cancelada com sucesso!');
  };

  const handleSelectPlan = async (plan) => {
    if (!user) {
      toast.error('Faça login para assinar um plano');
      return;
    }

    // Permitir seleção do plano atual se estiver em período de graça
    if (plan.is_current && current_plan_info?.status !== 'expired_grace_period') {
      toast.info('Você já está neste plano');
      return;
    }

    if (plan.is_free) {
      toast.info('Você já tem acesso ao plano gratuito');
      return;
    }

    // 🎯 NOVO: Impedir downgrade de planos
    if (current_plan_info && current_plan_info.name !== 'free') {
      console.log('🔍 [DOWNGRADE] Validando downgrade...');
      console.log('🔍 [DOWNGRADE] current_plan_info.name:', current_plan_info.name);
      console.log('🔍 [DOWNGRADE] plan.name:', plan.name);

      const currentPlanHierarchy = {
        'free': 0,
        'basic': 1,
        'premium': 2
      };

      const currentPlanLevel = currentPlanHierarchy[current_plan_info.name] || 0;
      const selectedPlanLevel = currentPlanHierarchy[plan.name] || 0;

      console.log('🔍 [DOWNGRADE] currentPlanLevel:', currentPlanLevel);
      console.log('🔍 [DOWNGRADE] selectedPlanLevel:', selectedPlanLevel);

      if (selectedPlanLevel < currentPlanLevel) {
        console.log('🚨 [DOWNGRADE] Bloqueando downgrade!');
        toast.error('Não é possível fazer downgrade para um plano inferior. Entre em contato com o suporte se desejar cancelar sua assinatura.');
        return;
      }
    }

    // Show plan selection modal for paid plans
    setSelectedPlan(plan);
    setShowPlanSelection(true);
  };

  const handlePlanSelected = (selectedPlanWithCycle) => {
    setShowPlanSelection(false);
    setCheckoutPlan(selectedPlanWithCycle);
  };

  const handleCheckoutSuccess = () => {
    setCheckoutPlan(null);
    loadPricingData(); // Recarregar dados de pricing
    loadCurrentSubscription(); // Recarregar assinatura atual
  };

  const handleCheckoutClose = () => {
    setCheckoutPlan(null);
  };

  if (loading) {
    return (
      <Container>
        <div style={{ textAlign: 'center', padding: '40px' }}>
          Carregando planos...
        </div>
      </Container>
    );
  }

  if (!pricingData) {
    return (
      <Container>
        <div style={{ textAlign: 'center', padding: '40px' }}>
          Erro ao carregar planos
        </div>
      </Container>
    );
  }

  const { plans = [], usage, current_plan_info } = pricingData;

  // DEBUG: Mostrar informações dos planos
  console.log('🔍 [PRICING] Planos recebidos:', plans);
  console.log('🔍 [PRICING] current_plan_info:', current_plan_info);
  console.log('🔍 [PRICING] current_plan_info.name:', current_plan_info?.name);
  console.log('🔍 [PRICING] currentSubscription:', currentSubscription);
  console.log('🔍 [PRICING] expires_at:', current_plan_info?.expires_at);

  // Mostrar todos os planos que o backend enviar (já filtrados)
  const filteredPlans = plans
    .sort((a, b) => {
      // Ordem: Gratuito → Premium
      const order = { 'free': 1, 'premium_monthly': 2 };
      return (order[a.name] || 999) - (order[b.name] || 999);
    });

  // DEBUG: Mostrar planos filtrados
  console.log('🔍 [PRICING] Planos filtrados:', filteredPlans);

  return (
    <>
      <SEOHead
        title="Planos e Preços - Escolha o melhor para seus bonsais"
        description="Conheça nossos planos para gerenciamento de bonsai e plantas. Plano gratuito, básico e premium com recursos avançados para cuidar das suas plantas."
        keywords="planos, preços, assinatura, bonsai premium, plantas premium, gerenciamento avançado, fotos ilimitadas"
        url="https://meubonsai.app/pricing"
      />
      <PricingPageStructuredData />
      <Container>
      <Header>
        <Title>Escolha o Plano Ideal</Title>
        <Subtitle>
          Gerencie suas plantas com as ferramentas certas para cada necessidade
        </Subtitle>


      </Header>

      {/* Alerta do período de graça - sem botão de renovar */}
      <GracePeriodAlert
        gracePeriodInfo={subscriptionData?.grace_period_info}
        onRenewClick={null} // Sem botão de renovar na página de pricing
      />

      {current_plan_info && current_plan_info.name !== 'free' && (
        <CurrentSubscriptionCard>
          <CurrentSubscriptionHeader>
            <FaCreditCard />
            <h2>Minha Assinatura</h2>
          </CurrentSubscriptionHeader>

          <CurrentSubscriptionInfo>
            <CurrentSubscriptionDetail>
              <CurrentSubscriptionLabel>Plano Atual</CurrentSubscriptionLabel>
              <CurrentSubscriptionValue>{getBasePlanDisplayName(current_plan_info)}</CurrentSubscriptionValue>
            </CurrentSubscriptionDetail>

            <CurrentSubscriptionDetail>
              <CurrentSubscriptionLabel>Ciclo de Cobrança</CurrentSubscriptionLabel>
              <CurrentSubscriptionValue>
                {current_plan_info.billing_cycle === 'monthly' ? 'Mensal' : 'Anual'}
              </CurrentSubscriptionValue>
            </CurrentSubscriptionDetail>

            <CurrentSubscriptionDetail>
              <CurrentSubscriptionLabel>Status</CurrentSubscriptionLabel>
              {current_plan_info.status === 'cancelled_pending_expiration' ? (
                <CurrentSubscriptionStatus
                  style={{ cursor: 'not-allowed', opacity: 0.6 }}
                  title="Plano cancelado - expira em breve"
                >
                  ⏳ Cancelado
                </CurrentSubscriptionStatus>
              ) : current_plan_info.status === 'expired_grace_period' ? (
                <CurrentSubscriptionStatus
                  style={{ cursor: 'not-allowed', opacity: 0.6 }}
                  title="Assinatura expirada - período de graça ativo"
                >
                  ⚠️ Não renovado
                </CurrentSubscriptionStatus>
              ) : (
                <CurrentSubscriptionStatus
                  onClick={() => {
                    console.log('🔍 Status clicado!');
                    console.log('🔍 currentSubscription:', currentSubscription);
                    handleCancelSubscription();
                  }}
                  title="Gerenciar assinatura"
                >
                  ✅ Ativo
                </CurrentSubscriptionStatus>
              )}
            </CurrentSubscriptionDetail>

            <CurrentSubscriptionDetail>
              <CurrentSubscriptionLabel>
                <FaCalendarAlt style={{ marginRight: '0.5rem' }} />
                {current_plan_info.status === 'cancelled_pending_expiration' ? 'Data de Expiração' : 'Próxima Cobrança'}
              </CurrentSubscriptionLabel>
              <CurrentSubscriptionValue>
                {current_plan_info.status === 'cancelled_pending_expiration'
                  ? (current_plan_info.expires_at
                      ? new Date(current_plan_info.expires_at).toLocaleDateString('pt-BR')
                      : 'Data não disponível'
                    )
                  : (current_plan_info.billing_cycle === 'yearly'
                      ? new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toLocaleDateString('pt-BR')
                      : new Date(new Date().setMonth(new Date().getMonth() + 1)).toLocaleDateString('pt-BR')
                    )
                }
              </CurrentSubscriptionValue>
            </CurrentSubscriptionDetail>
          </CurrentSubscriptionInfo>

          <CurrentSubscriptionActions>
            <Button
              variant="outline"
              size="small"
              onClick={() => setShowHistoryModal(true)}
              style={{
                fontSize: '0.9rem',
                padding: '8px 16px',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                background: 'white',
                color: '#6c5ce7',
                border: '1px solid #6c5ce7',
                borderRadius: '6px',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.target.style.background = '#6c5ce7';
                e.target.style.color = 'white';
              }}
              onMouseLeave={(e) => {
                e.target.style.background = 'white';
                e.target.style.color = '#6c5ce7';
              }}
            >
              <FaHistory />
              Histórico de Assinaturas
            </Button>
          </CurrentSubscriptionActions>
        </CurrentSubscriptionCard>
      )}

      {current_plan_info && current_plan_info.name === 'free' && (
        <CurrentPlanSection>
          <CurrentPlanTitle>
            <FaCheck />
            Seu Plano Atual
          </CurrentPlanTitle>
          <CurrentPlanName>{getBasePlanDisplayName(current_plan_info)}</CurrentPlanName>
          <CurrentPlanDetails>Plano gratuito ativo</CurrentPlanDetails>
        </CurrentPlanSection>
      )}

      {usage && (
        <UsageSection>
          <UsageTitle>
            <FaLeaf />
            Seu Uso Atual
          </UsageTitle>
          <UsageGrid>
            <UsageCard>
              <UsageNumber>{usage.plants_count}</UsageNumber>
              <UsageLabel>Plantas</UsageLabel>
              <UsageLimit>
                {usage.plants_limit ? `Limite: ${usage.plants_limit}` : 'Ilimitado'}
              </UsageLimit>
            </UsageCard>
            <UsageCard>
              <UsageNumber>{usage.total_photos_count}</UsageNumber>
              <UsageLabel>Fotos Total</UsageLabel>
              <UsageLimit>Todas as suas fotos</UsageLimit>
            </UsageCard>
            <UsageCard>
              <UsageNumber>{usage.max_photos_per_plant}</UsageNumber>
              <UsageLabel>Máx. Fotos/Planta</UsageLabel>
              <UsageLimit>
                {usage.photos_per_plant_limit ? `Limite: ${usage.photos_per_plant_limit}` : 'Ilimitado'}
              </UsageLimit>
            </UsageCard>
          </UsageGrid>
        </UsageSection>
      )}

      <PlansGrid>
        {filteredPlans.map((plan) => (
          <PlanCard key={plan.id} isPopular={plan.is_popular} isCurrent={plan.is_current}>
            {plan.is_popular && (
              <PopularBadge>
                <FaStar size={12} />
                Mais Popular
              </PopularBadge>
            )}
            {plan.is_current && (
              <CurrentBadge>
                <FaCheck size={12} />
                Plano Atual
              </CurrentBadge>
            )}
            
            <PlanName>{plan.display_name}</PlanName>
            <PlanPrice>
              {plan.is_free ? 'Gratuito' : plan.price_display}
            </PlanPrice>
            <PlanBilling>
              {plan.is_free ? 'Para sempre' : 'por mês'}
            </PlanBilling>
            
            {plan.description && (
              <PlanDescription>{plan.description}</PlanDescription>
            )}

            <FeaturesList>
              {plan.features.map((feature, index) => (
                <Feature key={index}>
                  <FaCheck size={14} />
                  {feature}
                </Feature>
              ))}
            </FeaturesList>

            <Button
              variant={plan.is_popular ? 'primary' : 'secondary'}
              size="large"
              fullWidth
              disabled={
                (plan.is_current && current_plan_info?.status !== 'expired_grace_period') ||
                processingPlan === plan.id ||
                // 🎯 NOVO: Desabilitar planos inferiores (downgrade)
                (() => {
                  if (!current_plan_info || current_plan_info.name === 'free') return false;

                  const planHierarchy = {
                    'free': 0,
                    'basic': 1,
                    'premium': 2
                  };

                  const currentLevel = planHierarchy[current_plan_info.name] || 0;
                  const planLevel = planHierarchy[plan.name] || 0;

                  return planLevel < currentLevel;
                })()
              }
              onClick={() => handleSelectPlan(plan)}
            >
              {processingPlan === plan.id ? 'Processando...' :
               (plan.is_current && current_plan_info?.status !== 'expired_grace_period') ? 'Plano Atual' :
               (() => {
                 // 🎯 NOVO: Mostrar "Plano Inferior" para downgrades
                 if (current_plan_info && current_plan_info.name !== 'free') {
                   const planHierarchy = {
                     'free': 0,
                     'basic': 1,
                     'premium': 2
                   };

                   const currentLevel = planHierarchy[current_plan_info.name] || 0;
                   const planLevel = planHierarchy[plan.name] || 0;

                   if (planLevel < currentLevel) {
                     return 'Plano Inferior';
                   }
                 }

                 return plan.is_free ? 'Gratuito' : 'Assinar Agora';
               })()}
            </Button>
          </PlanCard>
        ))}
      </PlansGrid>

      {/* Histórico removido - agora está dentro da seção "Minha Assinatura" */}

      {checkoutPlan && (
        <CheckoutModal
          plan={checkoutPlan}
          onClose={handleCheckoutClose}
          onSuccess={handleCheckoutSuccess}
        />
      )}

      {showCancelModal && currentSubscription?.id && (
        <CancelSubscriptionModal
          subscriptionId={currentSubscription.id}
          onClose={() => setShowCancelModal(false)}
          onSuccess={handleCancelSuccess}
        />
      )}

      {showPlanSelection && selectedPlan && (
        <PlanSelectionModal
          plan={selectedPlan}
          onClose={() => setShowPlanSelection(false)}
          onSelectPlan={handlePlanSelected}
        />
      )}

      {showHistoryModal && (
        <SubscriptionHistory
          onClose={() => setShowHistoryModal(false)}
        />
      )}
    </Container>
    </>
  );
};

export default Pricing;
