import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import styled from 'styled-components';
import { FaLeaf, FaEye, FaEyeSlash } from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';
import Button from '../components/UI/Button';
import SocialLoginButtons from '../components/SocialLoginButtons';
import SEOHead from '../components/SEO/SEOHead';
import { LoginPageStructuredData } from '../components/SEO/StructuredData';

const LoginContainer = styled.div`
  min-height: calc(100vh - 80px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${props => props.theme.spacing.md};
`;

const LoginCard = styled.div`
  background-color: ${props => props.theme.colors.surface};
  padding: ${props => props.theme.spacing.xxl};
  border-radius: ${props => props.theme.borderRadius.lg};
  box-shadow: ${props => props.theme.shadows.lg};
  width: 100%;
  max-width: 400px;
`;

const LoginHeader = styled.div`
  text-align: center;
  margin-bottom: ${props => props.theme.spacing.xl};
`;

const Logo = styled.div`
  color: ${props => props.theme.colors.primary};
  margin-bottom: ${props => props.theme.spacing.md};
  display: flex;
  justify-content: center;
`;

const Title = styled.h1`
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing.sm};
`;

const Subtitle = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  margin-bottom: 0;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.lg};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing.xs};
`;

const Label = styled.label`
  color: ${props => props.theme.colors.text.primary};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
`;

const InputContainer = styled.div`
  position: relative;
`;

const Input = styled.input`
  width: 100%;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  border: 1px solid ${props => props.error ? props.theme.colors.error : props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.md};
  font-size: ${props => props.theme.typography.fontSize.md};
  
  &:focus {
    border-color: ${props => props.error ? props.theme.colors.error : props.theme.colors.primary};
    outline: none;
  }
`;

const PasswordToggle = styled.button`
  position: absolute;
  right: ${props => props.theme.spacing.sm};
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: ${props => props.theme.colors.text.secondary};
  cursor: pointer;
  padding: ${props => props.theme.spacing.xs};
`;

const ErrorMessage = styled.span`
  color: ${props => props.theme.colors.error};
  font-size: ${props => props.theme.typography.fontSize.sm};
`;

const LoginFooter = styled.div`
  text-align: center;
  margin-top: ${props => props.theme.spacing.lg};
  color: ${props => props.theme.colors.text.secondary};
`;

const Login = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();
  const navigate = useNavigate();
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    setError
  } = useForm();

  const onSubmit = async (data) => {
    setLoading(true);
    const result = await login(data.email, data.password);

    if (result.success) {
      // Aguardar um pouco para garantir que os dados foram carregados
      setTimeout(() => {
        navigate('/profile');
      }, 500);
    } else {
      setError('root', { message: result.error });
    }
    setLoading(false);
  };

  const handleSocialLoginSuccess = async (result) => {
    // Aguardar um pouco para garantir que os dados foram carregados
    setTimeout(() => {
      navigate('/profile');
    }, 500);
  };

  const handleSocialLoginError = (error) => {
    console.error('Social login error:', error);
  };

  return (
    <>
      <SEOHead
        title="Login - Acesse sua conta"
        description="Faça login no MeuBonsai.App e gerencie seus bonsais e plantas. Acesse sua galeria de fotos, histórico de cuidados e lembretes personalizados."
        keywords="login, entrar, conta, bonsai, plantas, acesso, gerenciamento de plantas"
        url="https://meubonsai.app/login"
      />
      <LoginPageStructuredData />
      <LoginContainer>
        <LoginCard>
        <LoginHeader>
          <Logo>
            <FaLeaf size={48} />
          </Logo>
          <Title>Entrar</Title>
          <Subtitle>Acesse sua conta do MeuBonsai.App</Subtitle>
        </LoginHeader>

        <Form onSubmit={handleSubmit(onSubmit)}>
          <FormGroup>
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              error={errors.email}
              {...register('email', {
                required: 'Email é obrigatório',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Email inválido'
                }
              })}
            />
            {errors.email && <ErrorMessage>{errors.email.message}</ErrorMessage>}
          </FormGroup>

          <FormGroup>
            <Label htmlFor="password">Senha</Label>
            <InputContainer>
              <Input
                id="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="Sua senha"
                error={errors.password}
                {...register('password', {
                  required: 'Senha é obrigatória'
                })}
              />
              <PasswordToggle
                type="button"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <FaEyeSlash /> : <FaEye />}
              </PasswordToggle>
            </InputContainer>
            {errors.password && <ErrorMessage>{errors.password.message}</ErrorMessage>}
          </FormGroup>

          {errors.root && <ErrorMessage>{errors.root.message}</ErrorMessage>}

          <Button type="submit" fullWidth disabled={loading}>
            {loading ? 'Entrando...' : 'Entrar'}
          </Button>
        </Form>

        <SocialLoginButtons
          onSuccess={handleSocialLoginSuccess}
          onError={handleSocialLoginError}
          dividerText="ou entre com"
        />

        <div style={{ textAlign: 'center', marginBottom: '1rem' }}>
          <Link
            to="/forgot-password"
            style={{
              color: '#007bff',
              textDecoration: 'none',
              fontSize: '0.9rem'
            }}
          >
            Esqueci minha senha
          </Link>
        </div>

        <LoginFooter>
          Não tem uma conta?{' '}
          <Link to="/register">Cadastre-se aqui</Link>
        </LoginFooter>
      </LoginCard>
    </LoginContainer>
    </>
  );
};

export default Login;
