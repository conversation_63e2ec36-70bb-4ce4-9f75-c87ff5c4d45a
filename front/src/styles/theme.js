const theme = {
  colors: {
    primary: '#2E7D32',      // Verde escuro
    primaryLight: '#4CAF50', // Verde médio
    primaryDark: '#1B5E20',  // Verde muito escuro
    secondary: '#8BC34A',    // Verde claro
    accent: '#FFC107',       // Amarelo/dourado
    
    background: '#F5F5F5',   // Cinza muito claro
    surface: '#FFFFFF',      // Branco
    
    text: {
      primary: '#212121',    // Preto suave
      secondary: '#757575',  // Cinza médio
      disabled: '#BDBDBD',   // Cinza claro
      hint: '#9E9E9E',       // Cinza
    },
    
    error: '#F44336',        // Vermelho
    warning: '#FF9800',      // Laranja
    info: '#2196F3',         // Azul
    success: '#4CAF50',      // Verde
    
    border: '#E0E0E0',       // Cinza claro para bordas
    divider: '#EEEEEE',      // Cinza muito claro para divisores
  },
  
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
    xxl: '48px',
  },
  
  borderRadius: {
    sm: '4px',
    md: '8px',
    lg: '12px',
    xl: '16px',
    round: '50%',
  },
  
  shadows: {
    sm: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
    md: '0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23)',
    lg: '0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23)',
    xl: '0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22)',
  },
  
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    fontSize: {
      xs: '12px',
      sm: '14px',
      md: '16px',
      lg: '18px',
      xl: '20px',
      xxl: '24px',
      xxxl: '32px',
    },
    fontWeight: {
      light: 300,
      regular: 400,
      medium: 500,
      bold: 700,
    },
  },
  
  breakpoints: {
    xs: '0px',
    sm: '600px',
    md: '960px',
    lg: '1280px',
    xl: '1920px',
  },
  
  zIndex: {
    drawer: 1200,
    modal: 1300,
    snackbar: 1400,
    tooltip: 1500,
  },
};

export default theme;
