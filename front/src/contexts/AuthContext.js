import React, { createContext, useContext, useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import api from '../services/api';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showCompleteProfileModal, setShowCompleteProfileModal] = useState(false);

  // Cache para dados do usuário
  const USER_CACHE_KEY = 'meubonsai_user_cache';
  const CACHE_DURATION = 5 * 60 * 1000; // 5 minutos

  // Funções de cache
  const saveUserToCache = (userData) => {
    try {
      const cacheData = {
        user: userData,
        timestamp: Date.now()
      };
      localStorage.setItem(USER_CACHE_KEY, JSON.stringify(cacheData));
    } catch (error) {
      console.error('Error saving user to cache:', error);
    }
  };

  const getUserFromCache = () => {
    try {
      const cached = localStorage.getItem(USER_CACHE_KEY);
      if (!cached) return null;

      const cacheData = JSON.parse(cached);
      const isExpired = Date.now() - cacheData.timestamp > CACHE_DURATION;

      if (isExpired) {
        localStorage.removeItem(USER_CACHE_KEY);
        return null;
      }

      return cacheData.user;
    } catch (error) {
      console.error('Error reading user from cache:', error);
      localStorage.removeItem(USER_CACHE_KEY);
      return null;
    }
  };

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkProfileComplete = (userData, loginResponse = null) => {
    // Verificar se usuário precisa completar o perfil
    const needsUsername = loginResponse?.needs_username ||
                         !userData?.username ||
                         (userData?.username && (
                           userData.username.startsWith('user_') ||
                           userData.username.startsWith('googleuser') ||
                           userData.username.startsWith('appleuser')
                         ));

    // Verificar se falta informações básicas
    const needsBasicInfo = !userData?.first_name || !userData?.last_name;

    if (needsUsername || needsBasicInfo) {
      setShowCompleteProfileModal(true);
      return true;
    }
    return false;
  };

  const checkAuthStatus = async () => {
    try {
      const token = localStorage.getItem('token');
      if (token) {
        api.defaults.headers.common['Authorization'] = `Bearer ${token}`;

        // Tentar carregar do cache primeiro
        const cachedUser = getUserFromCache();
        if (cachedUser) {
          console.log('Loading user from cache');
          setUser(cachedUser);
          setLoading(false);

          // Verificar se perfil precisa ser completado
          checkProfileComplete(cachedUser);

          // Tentar atualizar em background
          try {
            const response = await api.get('/api/v1/users/me');
            setUser(response.data);
            saveUserToCache(response.data);
            checkProfileComplete(response.data);
          } catch (error) {
            console.log('Background refresh failed, using cached data');
          }
          return;
        }

        // Se não há cache, tentar carregar da API
        try {
          const response = await api.get('/api/v1/users/me');
          setUser(response.data);
          saveUserToCache(response.data);
          checkProfileComplete(response.data);
        } catch (error) {
          console.log('API failed, user not authenticated');
          localStorage.removeItem('token');
          delete api.defaults.headers.common['Authorization'];
          setUser(null);
        }
      } else {
        setUser(null);
      }
    } catch (error) {
      console.log('Error checking auth status:', error);
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const login = async (email, password) => {
    try {
      // FastAPI OAuth2PasswordRequestForm expects form-data with username field
      const formData = new FormData();
      formData.append('username', email); // OAuth2 uses 'username' field for email
      formData.append('password', password);

      const response = await api.post('/api/v1/auth/login', formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      // Store the token
      const token = response.data.access_token;
      localStorage.setItem('token', token);

      // Set authorization header for future requests
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;

      // Get user profile with retry
      console.log('🔄 Starting user profile fetch after login...');
      let userResponse;
      let retries = 3;

      while (retries > 0) {
        try {
          console.log(`🌐 Attempting to fetch user profile... (${4-retries}/3)`);
          userResponse = await api.get('/api/v1/users/me');
          console.log('✅ User profile fetch successful:', userResponse.data);
          break;
        } catch (error) {
          retries--;
          console.error(`❌ User profile fetch failed:`, error);
          if (retries === 0) throw error;
          console.log(`🔄 Retrying user fetch after login... ${retries} attempts left`);
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      setUser(userResponse.data);
      saveUserToCache(userResponse.data);

      // Verificar se perfil precisa ser completado (para login social, usar resposta do login)
      if (response.data.needs_username !== undefined) {
        checkProfileComplete(userResponse.data, response.data);
      } else {
        checkProfileComplete(userResponse.data);
      }

      toast.success('Login realizado com sucesso!');
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.detail ||
                     error.response?.data?.message ||
                     'Erro ao fazer login';
      toast.error(message);
      return { success: false, error: message };
    }
  };

  const register = async (userData) => {
    try {
      const response = await api.post('/api/v1/auth/register', userData);
      
      toast.success('Conta criada com sucesso! Faça login para continuar.');
      return { success: true, data: response.data };
    } catch (error) {
      const message = error.response?.data?.detail || 
                     error.response?.data?.message || 
                     'Erro ao criar conta';
      toast.error(message);
      return { success: false, error: error.response?.data };
    }
  };

  const loginWithToken = async (token, userData = null, socialLoginData = null) => {
    try {
      // Store the token
      localStorage.setItem('token', token);

      // Set authorization header for future requests
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;

      // Get user profile if not provided
      let finalUserData;
      if (userData) {
        setUser(userData);
        finalUserData = userData;
      } else {
        const userResponse = await api.get('/api/v1/users/me');
        setUser(userResponse.data);
        finalUserData = userResponse.data;
      }

      // Verificar se perfil precisa ser completado (especialmente para login social)
      if (socialLoginData) {
        checkProfileComplete(finalUserData, socialLoginData);
      }

      return { success: true };
    } catch (error) {
      const message = error.response?.data?.detail ||
                     error.response?.data?.message ||
                     'Erro ao fazer login';
      toast.error(message);
      return { success: false, error: message };
    }
  };

  const logout = async () => {
    try {
      await api.post('/api/v1/auth/logout');
    } catch (error) {
      console.log('Erro ao fazer logout:', error);
    } finally {
      localStorage.removeItem('token');
      localStorage.removeItem(USER_CACHE_KEY);
      delete api.defaults.headers.common['Authorization'];
      setUser(null);
      toast.success('Logout realizado com sucesso!');
    }
  };

  const deleteAccount = async () => {
    try {
      await api.delete('/api/v1/users/me');

      // Limpar dados locais
      localStorage.removeItem('token');
      localStorage.removeItem(USER_CACHE_KEY);
      delete api.defaults.headers.common['Authorization'];
      setUser(null);

      toast.success('Conta excluída com sucesso');

      // Redirecionar para home
      window.location.href = '/';
    } catch (error) {
      console.error('Error deleting account:', error);
      throw error;
    }
  };

  const updateProfile = async (userData) => {
    try {

      // Se há arquivo (avatar), usar FormData
      if (userData.avatar instanceof File) {
        const formData = new FormData();
        Object.keys(userData).forEach(key => {
          if (userData[key] !== null && userData[key] !== undefined) {
            formData.append(key, userData[key]);
          }
        });

        const response = await api.put('/api/v1/users/me', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
        setUser(response.data);
        saveUserToCache(response.data);
        toast.success('Perfil atualizado com sucesso!');
        return { success: true, data: response.data };
      } else {
        // Para dados simples, usar JSON no endpoint correto
        const response = await api.put('/api/v1/users/me/profile', userData);
        setUser(response.data);
        saveUserToCache(response.data);
        toast.success('Perfil atualizado com sucesso!');
        return { success: true, data: response.data };
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      const message = error.response?.data?.detail ||
                     error.response?.data?.message ||
                     'Erro ao atualizar perfil';
      toast.error(message);
      return { success: false, error: error.response?.data };
    }
  };

  const refreshUser = async (retries = 3) => {
    try {
      const response = await api.get('/api/v1/users/me');
      setUser(response.data);
      saveUserToCache(response.data);
      return response.data;
    } catch (error) {
      console.error('Error refreshing user:', error);

      // Retry logic for timeout errors
      if (retries > 0 && (error.code === 'ECONNABORTED' || error.response?.status >= 500)) {
        console.log(`Retrying user refresh... ${retries} attempts left`);
        await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
        return refreshUser(retries - 1);
      }

      // Se falhar, tentar usar cache como fallback
      const cachedUser = getUserFromCache();
      if (cachedUser) {
        console.log('Using cached user as fallback');
        setUser(cachedUser);
        return cachedUser;
      }

      return null;
    }
  };

  const handleCompleteProfileSuccess = async () => {
    setShowCompleteProfileModal(false);

    // Refresh user data to get updated profile
    await refreshUser();

    // Aguardar um pouco para garantir que os dados foram atualizados
    setTimeout(() => {
      window.location.href = '/profile';
    }, 1000);
  };

  const value = {
    user,
    loading,
    showCompleteProfileModal,
    setShowCompleteProfileModal,
    handleCompleteProfileSuccess,
    checkProfileComplete,
    login,
    loginWithToken,
    register,
    logout,
    deleteAccount,
    updateProfile,
    refreshUser,
    isAuthenticated: !!user,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
