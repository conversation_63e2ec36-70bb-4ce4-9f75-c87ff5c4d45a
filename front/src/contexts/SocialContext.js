import React, { createContext, useContext, useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import socialApi from '../services/socialApi';
import { useAuth } from './AuthContext';

const SocialContext = createContext();

export const useSocial = () => {
  const context = useContext(SocialContext);
  if (!context) {
    throw new Error('useSocial must be used within a SocialProvider');
  }
  return context;
};

export const SocialProvider = ({ children }) => {
  const { user, refreshUser } = useAuth();
  const [socialStats, setSocialStats] = useState({
    followers_count: 0,
    following_count: 0,
    public_plants_count: 0,
    total_plants_count: 0
  });
  const [privacySettings, setPrivacySettings] = useState({
    is_public: true,
    allow_care_sharing: true,
    allow_search: true
  });
  const [loading, setLoading] = useState(false);
  const [lastStatsUpdate, setLastStatsUpdate] = useState(null);

  // Cache duration: 30 seconds
  const CACHE_DURATION = 30 * 1000;

  // Load social stats and privacy settings when user is available
  useEffect(() => {
    if (user) {
      loadSocialData();
    }
  }, [user]);

  const loadSocialData = async (forceRefresh = false) => {
    // Check cache first
    const now = Date.now();
    if (!forceRefresh && lastStatsUpdate && (now - lastStatsUpdate) < CACHE_DURATION) {
      console.log('Using cached social stats');
      return;
    }

    try {
      setLoading(true);
      const [stats, privacy] = await Promise.all([
        socialApi.getSocialStats(),
        socialApi.getPrivacySettings()
      ]);
      setSocialStats(stats);
      setPrivacySettings(privacy);
      setLastStatsUpdate(now);
      console.log('Social stats updated from API');
    } catch (error) {
      console.error('Error loading social data:', error);
    } finally {
      setLoading(false);
    }
  };

  const searchUsers = async (query, page = 1) => {
    try {
      setLoading(true);
      const result = await socialApi.searchUsers(query, page);
      return result;
    } catch (error) {
      console.error('Error searching users:', error);
      toast.error('Erro ao buscar usuários');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const followUser = async (userId) => {
    try {
      console.log('🔄 Following user, current stats:', socialStats);

      const result = await socialApi.followUser(userId);

      // Force immediate refresh from API to get accurate count
      console.log('✅ Follow successful, refreshing stats...');
      await loadSocialData(true);

      toast.success('Usuário seguido com sucesso!');
      return result;
    } catch (error) {
      console.error('❌ Error following user:', error);
      toast.error('Erro ao seguir usuário');
      throw error;
    }
  };

  const unfollowUser = async (userId) => {
    try {
      console.log('🔄 Unfollowing user, current stats:', socialStats);

      const result = await socialApi.unfollowUser(userId);

      // Force immediate refresh from API to get accurate count
      console.log('✅ Unfollow successful, refreshing stats...');
      await loadSocialData(true);

      toast.success('Usuário deixou de ser seguido');
      return result;
    } catch (error) {
      console.error('❌ Error unfollowing user:', error);
      toast.error('Erro ao deixar de seguir usuário');
      throw error;
    }
  };

  const updatePrivacySettings = async (newSettings) => {
    try {
      setLoading(true);
      const result = await socialApi.updatePrivacySettings(newSettings);
      setPrivacySettings(result);
      toast.success('Configurações de privacidade atualizadas!');
      return result;
    } catch (error) {
      console.error('Error updating privacy settings:', error);
      toast.error('Erro ao atualizar configurações de privacidade');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const checkUsernameAvailability = async (username) => {
    try {
      const result = await socialApi.checkUsernameAvailability(username);
      return result;
    } catch (error) {
      console.error('Error checking username availability:', error);
      return { available: false, reason: 'error' };
    }
  };

  const updateUsername = async (username) => {
    try {
      setLoading(true);
      const result = await socialApi.updateUsername(username);

      // Atualizar dados do usuário no AuthContext
      await refreshUser();

      toast.success('Nome de usuário atualizado!');
      return result;
    } catch (error) {
      console.error('Error updating username:', error);
      if (error.response?.status === 400) {
        toast.error('Nome de usuário já está em uso');
      } else {
        toast.error('Erro ao atualizar nome de usuário');
      }
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const getPublicProfile = async (userId) => {
    try {
      return await socialApi.getPublicProfile(userId);
    } catch (error) {
      console.error('Error getting public profile:', error);
      if (error.response?.status === 404) {
        toast.error('Usuário não encontrado ou perfil privado');
      } else {
        toast.error('Erro ao carregar perfil');
      }
      throw error;
    }
  };

  const getPublicProfileByUsername = async (username) => {
    try {
      return await socialApi.getPublicProfileByUsername(username);
    } catch (error) {
      console.error('Error getting public profile by username:', error);
      if (error.response?.status === 404) {
        toast.error('Usuário não encontrado ou perfil privado');
      } else {
        toast.error('Erro ao carregar perfil');
      }
      throw error;
    }
  };

  const getPublicPlants = async (userId, page = 1, limit = 20) => {
    try {
      return await socialApi.getPublicPlants(userId, page, limit);
    } catch (error) {
      console.error('Error getting public plants:', error);
      throw error;
    }
  };

  const getPublicPlantsByUsername = async (username, page = 1, limit = 20) => {
    try {
      return await socialApi.getPublicPlantsByUsername(username, page, limit);
    } catch (error) {
      console.error('Error getting public plants by username:', error);
      throw error;
    }
  };

  const getUserFollowers = async (userId, page = 1) => {
    try {
      return await socialApi.getUserFollowers(userId, page);
    } catch (error) {
      console.error('Error getting followers:', error);
      toast.error('Erro ao carregar seguidores');
      throw error;
    }
  };

  const getUserFollowing = async (userId, page = 1) => {
    try {
      return await socialApi.getUserFollowing(userId, page);
    } catch (error) {
      console.error('Error getting following:', error);
      toast.error('Erro ao carregar seguindo');
      throw error;
    }
  };

  const refreshSocialStats = (forceRefresh = false) => {
    if (user) {
      loadSocialData(forceRefresh);
    }
  };

  const value = {
    // State
    socialStats,
    privacySettings,
    loading,
    
    // Actions
    searchUsers,
    followUser,
    unfollowUser,
    updatePrivacySettings,
    checkUsernameAvailability,
    updateUsername,
    getPublicProfile,
    getPublicProfileByUsername,
    getPublicPlants,
    getPublicPlantsByUsername,
    getUserFollowers,
    getUserFollowing,
    refreshSocialStats,
    
    // Utils
    loadSocialData
  };

  return (
    <SocialContext.Provider value={value}>
      {children}
    </SocialContext.Provider>
  );
};
