import React, { createContext, useContext } from 'react';
import { useQueryClient } from 'react-query';

const ReminderContext = createContext();

export const useReminder = () => {
  const context = useContext(ReminderContext);
  if (!context) {
    throw new Error('useReminder must be used within a ReminderProvider');
  }
  return context;
};

export const ReminderProvider = ({ children }) => {
  const queryClient = useQueryClient();

  const invalidateReminders = async () => {
    // Invalidate all reminder-related queries
    await Promise.all([
      queryClient.invalidateQueries('upcomingReminders'),
      queryClient.invalidateQueries('overdueReminders'),
      queryClient.invalidateQueries(['plantReminders']),
    ]);
  };

  const refetchReminders = async () => {
    // Force refetch all reminder queries
    await Promise.all([
      queryClient.refetchQueries('upcomingReminders'),
      queryClient.refetchQueries('overdueReminders'),
      queryClient.refetchQueries(['plantReminders']),
    ]);
  };

  return (
    <ReminderContext.Provider
      value={{
        invalidateReminders,
        refetchReminders,
      }}
    >
      {children}
    </ReminderContext.Provider>
  );
};
