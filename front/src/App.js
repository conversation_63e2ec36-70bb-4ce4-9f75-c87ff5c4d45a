import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import { ToastContainer } from 'react-toastify';
import { ThemeProvider } from 'styled-components';
import { HelmetProvider } from 'react-helmet-async';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { SocialProvider } from './contexts/SocialContext';
import { ReminderProvider } from './contexts/ReminderContext';
import api from './services/api';
import GlobalStyle from './styles/GlobalStyle';
import theme from './styles/theme';
import Header from './components/Layout/Header';
import Footer from './components/Layout/Footer';
import PrivateRoute from './components/Auth/PrivateRoute';
import SEOHead from './components/SEO/SEOHead';
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';
import ForgotPassword from './pages/ForgotPassword';
import ResetPassword from './pages/ResetPassword';
import ActivateAccount from './pages/ActivateAccount';
import ConfirmPasswordChange from './pages/ConfirmPasswordChange';

import AddPlant from './pages/AddPlant';
import PlantDetail from './pages/PlantDetail';
import PlantGallery from './pages/PlantGallery';
import Pricing from './pages/Pricing';
import Profile from './pages/NewProfile';
import SubscriptionSuccess from './pages/SubscriptionSuccess';
import SubscriptionFailure from './pages/SubscriptionFailure';
import SubscriptionPending from './pages/SubscriptionPending';
import PaymentSimulation from './pages/PaymentSimulation';
import PaymentTest from './pages/PaymentTest';
import Admin from './pages/Admin';
import UserSearch from './pages/UserSearch';
import PublicProfile from './pages/PublicProfile';
import Settings from './pages/Settings';
import PrivacyPolicy from './pages/PrivacyPolicy';
import TermsOfService from './pages/TermsOfService';
import Contact from './pages/Contact';
import CompleteProfileModal from './components/CompleteProfileModal';
import CookieBanner from './components/CookieBanner';

import 'react-toastify/dist/ReactToastify.css';

// Componente wrapper para o modal de completar perfil
const CompleteProfileModalWrapper = () => {
  const { showCompleteProfileModal, setShowCompleteProfileModal, handleCompleteProfileSuccess } = useAuth();

  return (
    <CompleteProfileModal
      isOpen={showCompleteProfileModal}
      onClose={() => setShowCompleteProfileModal(false)}
      onSuccess={handleCompleteProfileSuccess}
    />
  );
};

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  // Initialize R2 URL cache on app start
  React.useEffect(() => {
    const initializeR2Cache = async () => {
      try {
        const response = await fetch(`${process.env.REACT_APP_API_URL || 'https://api.meubonsai.app'}/api/v1/config/public`);
        const config = await response.json();
        // This will populate the cache for sync functions
        console.log('R2 URL cache initialized:', config.r2_public_url);
      } catch (error) {
        console.warn('Failed to initialize R2 URL cache:', error);
      }
    };

    initializeR2Cache();
  }, []);

  return (
    <HelmetProvider>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider theme={theme}>
          <AuthProvider>
            <SocialProvider>
              <ReminderProvider>
                <Router>
              <GlobalStyle />
              <SEOHead />
              <div className="App">
                <Header />
                <CompleteProfileModalWrapper />
                <CookieBanner />
                <main>
                <Routes>
                  <Route path="/" element={<Home />} />
                  <Route path="/login" element={<Login />} />
                  <Route path="/register" element={<Register />} />
                  <Route path="/forgot-password" element={<ForgotPassword />} />
                  <Route path="/reset-password" element={<ResetPassword />} />
                  <Route path="/activate-account" element={<ActivateAccount />} />
                  <Route path="/confirm-password-change" element={<ConfirmPasswordChange />} />
                  <Route path="/privacy-policy" element={<PrivacyPolicy />} />
                  <Route path="/terms-of-service" element={<TermsOfService />} />
                  <Route path="/contact" element={<Contact />} />

                  <Route
                    path="/plants/new"
                    element={
                      <PrivateRoute>
                        <AddPlant />
                      </PrivateRoute>
                    }
                  />
                  <Route
                    path="/plants/add"
                    element={
                      <PrivateRoute>
                        <AddPlant />
                      </PrivateRoute>
                    }
                  />
                  <Route
                    path="/plants/:id"
                    element={
                      <PrivateRoute>
                        <PlantDetail />
                      </PrivateRoute>
                    }
                  />
                  <Route
                    path="/plants/:id/gallery"
                    element={
                      <PrivateRoute>
                        <PlantGallery />
                      </PrivateRoute>
                    }
                  />
                  <Route
                    path="/pricing"
                    element={
                      <PrivateRoute>
                        <Pricing />
                      </PrivateRoute>
                    }
                  />
                  <Route
                    path="/profile"
                    element={
                      <PrivateRoute>
                        <Profile />
                      </PrivateRoute>
                    }
                  />
                  <Route
                    path="/subscription/success"
                    element={
                      <PrivateRoute>
                        <SubscriptionSuccess />
                      </PrivateRoute>
                    }
                  />
                  <Route
                    path="/subscription/failure"
                    element={
                      <PrivateRoute>
                        <SubscriptionFailure />
                      </PrivateRoute>
                    }
                  />
                  <Route
                    path="/subscription/pending"
                    element={
                      <PrivateRoute>
                        <SubscriptionPending />
                      </PrivateRoute>
                    }
                  />
                  <Route
                    path="/payment-simulation"
                    element={
                      <PrivateRoute>
                        <PaymentSimulation />
                      </PrivateRoute>
                    }
                  />
                  <Route
                    path="/payment-test"
                    element={
                      <PrivateRoute>
                        <PaymentTest />
                      </PrivateRoute>
                    }
                  />
                  <Route
                    path="/admin"
                    element={
                      <PrivateRoute>
                        <Admin />
                      </PrivateRoute>
                    }
                  />
                  <Route
                    path="/search"
                    element={
                      <PrivateRoute>
                        <UserSearch />
                      </PrivateRoute>
                    }
                  />
                  <Route
                    path="/profile/:username"
                    element={<PublicProfile />}
                  />
                  <Route
                    path="/profile/user/:userId"
                    element={<PublicProfile />}
                  />
                  <Route
                    path="/settings"
                    element={
                      <PrivateRoute>
                        <Settings />
                      </PrivateRoute>
                    }
                  />

                </Routes>
              </main>
              <Footer />
              <ToastContainer
                position="top-right"
                autoClose={3000}
                hideProgressBar={false}
                newestOnTop={false}
                closeOnClick
                rtl={false}
                pauseOnFocusLoss
                draggable
                pauseOnHover
              />
            </div>
                </Router>
              </ReminderProvider>
            </SocialProvider>
        </AuthProvider>
      </ThemeProvider>
    </QueryClientProvider>
    </HelmetProvider>
  );
}

export default App;
