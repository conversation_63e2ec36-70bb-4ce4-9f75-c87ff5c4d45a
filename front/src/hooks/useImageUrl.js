import { useState, useEffect } from 'react';
import { getImageUrl } from '../services/api';

export const useImageUrl = (imagePath, size = 'medium', plantId = null) => {
  const [imageUrl, setImageUrl] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadImageUrl = async () => {
      if (!imagePath) {
        setImageUrl(null);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const url = await getImageUrl(imagePath, size, plantId);
        setImageUrl(url);
      } catch (err) {
        console.error('Error loading image URL:', err);
        setError(err);
        setImageUrl(null);
      } finally {
        setLoading(false);
      }
    };

    loadImageUrl();
  }, [imagePath, size, plantId]);

  return { imageUrl, loading, error };
};
