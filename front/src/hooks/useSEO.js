import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { trackPageView, updatePageTitle, updateMetaDescription } from '../utils/analytics';

const useSEO = ({ 
  title, 
  description, 
  keywords,
  canonical,
  noindex = false 
}) => {
  const location = useLocation();

  useEffect(() => {
    // Update page title
    if (title) {
      updatePageTitle(title);
    }

    // Update meta description
    if (description) {
      updateMetaDescription(description);
    }

    // Update canonical URL
    if (canonical && typeof document !== 'undefined') {
      let canonicalLink = document.querySelector('link[rel="canonical"]');
      if (!canonicalLink) {
        canonicalLink = document.createElement('link');
        canonicalLink.rel = 'canonical';
        document.head.appendChild(canonicalLink);
      }
      canonicalLink.href = canonical;
    }

    // Update robots meta tag
    if (typeof document !== 'undefined') {
      let robotsMeta = document.querySelector('meta[name="robots"]');
      if (!robotsMeta) {
        robotsMeta = document.createElement('meta');
        robotsMeta.name = 'robots';
        document.head.appendChild(robotsMeta);
      }
      robotsMeta.content = noindex ? 'noindex, nofollow' : 'index, follow';
    }

    // Update keywords meta tag
    if (keywords && typeof document !== 'undefined') {
      let keywordsMeta = document.querySelector('meta[name="keywords"]');
      if (!keywordsMeta) {
        keywordsMeta = document.createElement('meta');
        keywordsMeta.name = 'keywords';
        document.head.appendChild(keywordsMeta);
      }
      keywordsMeta.content = keywords;
    }

    // Track page view for analytics
    const fullUrl = `${window.location.origin}${location.pathname}${location.search}`;
    trackPageView(fullUrl, title || document.title);

  }, [title, description, keywords, canonical, noindex, location]);

  return {
    updateTitle: updatePageTitle,
    updateDescription: updateMetaDescription
  };
};

export default useSEO;
