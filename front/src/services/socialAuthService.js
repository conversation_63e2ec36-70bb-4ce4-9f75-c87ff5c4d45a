import api from './api';

const socialAuthService = {
  // Configurações do Google OAuth
  getGoogleConfig: async () => {
    try {
      const response = await api.get('/api/v1/auth/social/config');
      return response.data.google;
    } catch (error) {
      console.error('<PERSON>rror getting Google config:', error);
      return { enabled: false };
    }
  },

  // Login com Google
  loginWithGoogle: async (idToken, accessToken = null) => {
    try {
      const response = await api.post('/api/v1/auth/social/google', {
        id_token: idToken,
        access_token: accessToken
      });
      return response.data;
    } catch (error) {
      console.error('Error during Google login:', error);
      throw error;
    }
  },

  // Login com Facebook
  loginWithFacebook: async (accessToken) => {
    try {
      const response = await api.post('/api/v1/auth/social/facebook', {
        access_token: accessToken
      });
      return response.data;
    } catch (error) {
      console.error('Error during Facebook login:', error);
      throw error;
    }
  },

  // Login com Apple
  loginWithApple: async (idToken, authorizationCode = null, userInfo = null) => {
    try {
      const response = await api.post('/api/v1/auth/social/apple', {
        id_token: idToken,
        authorization_code: authorizationCode,
        user_info: userInfo
      });
      return response.data;
    } catch (error) {
      console.error('Error during Apple login:', error);
      throw error;
    }
  },

  // Login social genérico
  socialLogin: async (provider, idToken, accessToken = null, authorizationCode = null, userInfo = null) => {
    try {
      const response = await api.post('/api/v1/auth/social/login', {
        provider,
        id_token: idToken,
        access_token: accessToken,
        authorization_code: authorizationCode,
        user_info: userInfo
      });
      return response.data;
    } catch (error) {
      console.error(`Error during ${provider} login:`, error);
      throw error;
    }
  },

  // Vincular conta social
  linkSocialAccount: async (provider, idToken, accessToken = null) => {
    try {
      const response = await api.post('/api/v1/auth/social/link', {
        provider,
        id_token: idToken,
        access_token: accessToken
      });
      return response.data;
    } catch (error) {
      console.error(`Error linking ${provider} account:`, error);
      throw error;
    }
  },

  // Desvincular conta social
  unlinkSocialAccount: async (provider) => {
    try {
      const response = await api.post('/api/v1/auth/social/unlink', {
        provider
      });
      return response.data;
    } catch (error) {
      console.error(`Error unlinking ${provider} account:`, error);
      throw error;
    }
  },

  // Listar contas vinculadas
  getLinkedAccounts: async () => {
    try {
      const response = await api.get('/api/v1/auth/social/accounts');
      return response.data.linked_accounts;
    } catch (error) {
      console.error('Error getting linked accounts:', error);
      throw error;
    }
  },

  // Configuração completa de autenticação social
  getSocialAuthConfig: async () => {
    try {
      const response = await api.get('/api/v1/auth/social/config');
      return response.data;
    } catch (error) {
      console.error('Error getting social auth config:', error);
      return {
        google: { enabled: false },
        facebook: { enabled: false },
        apple: { enabled: false }
      };
    }
  },

  // Inicializar Google OAuth
  initializeGoogleAuth: async () => {
    try {
      const config = await socialAuthService.getGoogleConfig();
      
      if (!config.enabled || !config.client_id) {
        throw new Error('Google OAuth not configured');
      }

      // Carrega o script do Google OAuth se não estiver carregado
      if (!window.google) {
        await socialAuthService.loadGoogleScript();
      }

      // Inicializa o Google OAuth
      await new Promise((resolve, reject) => {
        window.google.accounts.id.initialize({
          client_id: config.client_id,
          callback: resolve,
          auto_select: false,
          cancel_on_tap_outside: true
        });
        resolve();
      });

      return config;
    } catch (error) {
      console.error('Error initializing Google Auth:', error);
      throw error;
    }
  },

  // Carregar script do Google OAuth
  loadGoogleScript: () => {
    return new Promise((resolve, reject) => {
      if (window.google && window.google.accounts) {
        resolve();
        return;
      }

      // Remove script existente se houver
      const existingScript = document.querySelector('script[src*="accounts.google.com"]');
      if (existingScript) {
        existingScript.remove();
      }

      const script = document.createElement('script');
      script.src = 'https://accounts.google.com/gsi/client';
      script.async = true;
      script.defer = true;
      script.onload = () => {
        // Aguarda um pouco para garantir que o objeto está disponível
        setTimeout(() => {
          if (window.google && window.google.accounts) {
            resolve();
          } else {
            reject(new Error('Google script carregado mas objeto não disponível'));
          }
        }, 500);
      };
      script.onerror = () => reject(new Error('Falha ao carregar script do Google'));
      document.head.appendChild(script);
    });
  },

  // Prompt de login do Google - usando apenas popup OAuth2
  promptGoogleLogin: async () => {
    try {
      const config = await socialAuthService.getGoogleConfig();

      if (!config.enabled || !config.client_id) {
        throw new Error('Google OAuth não está configurado');
      }

      // Carrega o script do Google OAuth se não estiver carregado
      await socialAuthService.loadGoogleScript();

      // Usa diretamente o popup OAuth2 que é mais confiável
      return await socialAuthService.showGooglePopup();

    } catch (error) {
      console.error('Error prompting Google login:', error);
      throw error;
    }
  },

  // Popup de login do Google usando OAuth2
  showGooglePopup: async () => {
    try {
      const config = await socialAuthService.getGoogleConfig();

      console.log('Iniciando Google popup com config:', config);

      if (!window.google || !window.google.accounts || !window.google.accounts.oauth2) {
        throw new Error('Google OAuth2 não está disponível');
      }

      return new Promise((resolve, reject) => {
        let resolved = false;

        // Timeout de segurança
        const timeout = setTimeout(() => {
          if (!resolved) {
            resolved = true;
            reject(new Error('Timeout: Popup do Google não respondeu'));
          }
        }, 60000); // 1 minuto

        try {
          const tokenClient = window.google.accounts.oauth2.initTokenClient({
            client_id: config.client_id,
            scope: 'https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile openid',
            callback: async (response) => {
              if (resolved) return;

              try {
                console.log('Google popup response:', response);
                clearTimeout(timeout);
                resolved = true;

                if (response && response.access_token) {
                  console.log('Google access token recebido');

                  // Usar o access token diretamente no nosso backend
                  const result = await socialAuthService.loginWithGoogle(null, response.access_token);
                  resolve(result);
                } else if (response && response.error) {
                  reject(new Error(`Google OAuth error: ${response.error}`));
                } else {
                  reject(new Error('Nenhum token de acesso recebido do Google'));
                }
              } catch (error) {
                console.error('Erro no callback do popup:', error);
                reject(error);
              }
            },
            error_callback: (error) => {
              if (resolved) return;

              clearTimeout(timeout);
              resolved = true;
              console.error('Erro no Google OAuth:', error);

              // Não mostrar erro se o usuário cancelou
              if (error.type !== 'popup_closed' && error.type !== 'popup_closed_by_user') {
                reject(new Error(`Erro no Google OAuth: ${error.type || 'unknown'}`));
              } else {
                reject(new Error('Login cancelado pelo usuário'));
              }
            }
          });

          if (tokenClient) {
            console.log('Abrindo popup do Google...');
            tokenClient.requestAccessToken({
              prompt: 'consent'
            });
          } else {
            clearTimeout(timeout);
            if (!resolved) {
              resolved = true;
              reject(new Error('Falha ao inicializar cliente Google OAuth'));
            }
          }
        } catch (initError) {
          clearTimeout(timeout);
          if (!resolved) {
            resolved = true;
            console.error('Erro ao inicializar popup:', initError);
            reject(new Error('Erro ao inicializar popup: ' + initError.message));
          }
        }
      });
    } catch (error) {
      console.error('Error showing Google popup:', error);
      throw error;
    }
  },

  // Inicializar Apple Sign In
  initializeAppleAuth: async () => {
    try {
      const config = await socialAuthService.getSocialAuthConfig();
      
      if (!config.apple.enabled || !config.apple.client_id) {
        throw new Error('Apple Sign In not configured');
      }

      // Carrega o script do Apple Sign In se não estiver carregado
      if (!window.AppleID) {
        await socialAuthService.loadAppleScript();
      }

      // Inicializa o Apple Sign In
      await window.AppleID.auth.init({
        clientId: config.apple.client_id,
        scope: 'name email',
        redirectURI: window.location.origin,
        usePopup: true
      });

      return config.apple;
    } catch (error) {
      console.error('Error initializing Apple Auth:', error);
      throw error;
    }
  },

  // Carregar script do Apple Sign In
  loadAppleScript: () => {
    return new Promise((resolve, reject) => {
      if (window.AppleID) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js';
      script.async = true;
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  },

  // Login com Apple
  promptAppleLogin: async () => {
    try {
      await socialAuthService.initializeAppleAuth();
      
      const response = await window.AppleID.auth.signIn();
      
      const result = await socialAuthService.loginWithApple(
        response.authorization.id_token,
        response.authorization.code,
        response.user
      );
      
      return result;
    } catch (error) {
      console.error('Error prompting Apple login:', error);
      throw error;
    }
  }
};

export default socialAuthService;
