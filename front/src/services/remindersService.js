import api, { getUserTimezoneOffset } from './api';

export const remindersService = {
  // Listar lembretes
  getReminders: (includeCompleted = false) => {
    return api.get('/api/v1/reminders/', {
      params: { include_completed: includeCompleted }
    });
  },

  // Lembretes próximos (para notificações)
  getUpcomingReminders: (daysAhead = 7) => {
    const timezoneOffset = getUserTimezoneOffset();
    return api.get('/api/v1/reminders/upcoming', {
      params: { days_ahead: daysAhead, timezone_offset: timezoneOffset }
    });
  },

  // Lembretes atrasados
  getOverdueReminders: () => {
    const timezoneOffset = getUserTimezoneOffset();
    return api.get('/api/v1/reminders/overdue', {
      params: { timezone_offset: timezoneOffset }
    });
  },

  // Criar lembrete manual
  createReminder: (plantId, data) => {
    // Use new endpoint that accepts plant_id in body
    const requestData = {
      plant_id: plantId,
      ...data
    };
    return api.post('/api/v1/reminders/create', requestData);
  },

  // Obter lembrete específico
  getReminder: (id) => {
    return api.get(`/api/v1/reminders/${id}`);
  },

  // Atualizar lembrete
  updateReminder: (id, data) => {
    return api.put(`/api/v1/reminders/${id}`, data);
  },

  // Marcar como concluído
  completeReminder: (id) => {
    return api.post(`/api/v1/reminders/${id}/complete`);
  },

  // Deletar lembrete
  deleteReminder: (id) => {
    return api.delete(`/api/v1/reminders/${id}`);
  },
};

export default remindersService;
