import api from './api';

// User Search
export const searchUsers = async (query, page = 1, limit = 20) => {
  const response = await api.get('/api/v1/social/search', {
    params: {
      q: query,
      skip: (page - 1) * limit,
      limit
    }
  });
  return response.data;
};

// Follow/Unfollow
export const followUser = async (userId) => {
  const response = await api.post(`/api/v1/social/follow/${userId}`);
  return response.data;
};

export const unfollowUser = async (userId) => {
  const response = await api.delete(`/api/v1/social/follow/${userId}`);
  return response.data;
};

// Public Profiles
export const getPublicProfile = async (userId) => {
  const response = await api.get(`/api/v1/social/profile/${userId}`);
  return response.data;
};

export const getPublicProfileByUsername = async (username) => {
  const response = await api.get(`/api/v1/social/profile/username/${username}`);
  return response.data;
};

// Public Plants
export const getPublicPlants = async (userId, page = 1, limit = 20) => {
  const response = await api.get(`/api/v1/social/profile/${userId}/plants`, {
    params: {
      skip: (page - 1) * limit,
      limit
    }
  });
  return response.data;
};

export const getPublicPlantsByUsername = async (username, page = 1, limit = 20) => {
  const response = await api.get(`/api/v1/social/profile/username/${username}/plants`, {
    params: {
      skip: (page - 1) * limit,
      limit
    }
  });
  return response.data;
};

// Followers/Following
export const getUserFollowers = async (userId, page = 1, limit = 20) => {
  const response = await api.get(`/api/v1/social/${userId}/followers`, {
    params: {
      skip: (page - 1) * limit,
      limit
    }
  });
  return response.data;
};

export const getUserFollowing = async (userId, page = 1, limit = 20) => {
  const response = await api.get(`/api/v1/social/${userId}/following`, {
    params: {
      skip: (page - 1) * limit,
      limit
    }
  });
  return response.data;
};

// Privacy Settings
export const getPrivacySettings = async () => {
  const response = await api.get('/api/v1/social/privacy');
  return response.data;
};

export const updatePrivacySettings = async (settings) => {
  const response = await api.put('/api/v1/social/privacy', settings);
  return response.data;
};

// Username
export const checkUsernameAvailability = async (username) => {
  const response = await api.get(`/api/v1/social/username/check/${username}`);
  return response.data;
};

export const checkUsernameAvailabilityPublic = async (username) => {
  const response = await api.get(`/api/v1/social/username/check-public/${username}`);
  return response.data;
};

export const updateUsername = async (username) => {
  const response = await api.put('/api/v1/social/username', { username });
  return response.data;
};

// Social Stats
export const getSocialStats = async () => {
  const response = await api.get('/api/v1/social/stats');
  return response.data;
};

// Plant Privacy (will be added to plant API)
export const updatePlantPrivacy = async (plantId, privacySettings) => {
  const response = await api.put(`/api/v1/plants/${plantId}/privacy`, privacySettings);
  return response.data;
};

export default {
  searchUsers,
  followUser,
  unfollowUser,
  getPublicProfile,
  getPublicProfileByUsername,
  getPublicPlants,
  getPublicPlantsByUsername,
  getUserFollowers,
  getUserFollowing,
  getPrivacySettings,
  updatePrivacySettings,
  checkUsernameAvailability,
  checkUsernameAvailabilityPublic,
  updateUsername,
  getSocialStats,
  updatePlantPrivacy
};
