import api from './api';

// Get all notifications
export const getNotifications = async (page = 1, limit = 20, unreadOnly = false) => {
  const response = await api.get('/api/v1/notifications/', {
    params: {
      skip: (page - 1) * limit,
      limit,
      unread_only: unreadOnly
    }
  });
  return response.data;
};

// Get notification statistics
export const getNotificationStats = async () => {
  const response = await api.get('/api/v1/notifications/stats');
  return response.data;
};

// Mark notification as read
export const markAsRead = async (notificationId) => {
  const response = await api.put(`/api/v1/notifications/${notificationId}/read`);
  return response.data;
};

// Mark all notifications as read
export const markAllAsRead = async () => {
  const response = await api.put('/api/v1/notifications/read-all');
  return response.data;
};

// Mark all notifications as seen (clears badge but keeps notifications)
export const markAllAsSeen = async () => {
  const response = await api.put('/api/v1/notifications/seen-all');
  return response.data;
};

export default {
  getNotifications,
  getNotificationStats,
  markAsRead,
  markAllAsRead,
  markAllAsSeen
};
