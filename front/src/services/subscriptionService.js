import api from './api';

const subscriptionService = {
  // Get all subscription plans
  getPlans: () => api.get('/api/v1/subscriptions/plans'),

  // Get pricing page data (plans + current user data)
  getPricingData: async () => {
    console.log('🔍 [SUBSCRIPTION_SERVICE] Making request to /api/v1/subscriptions/pricing');
    try {
      const response = await api.get('/api/v1/subscriptions/pricing');
      console.log('✅ [SUBSCRIPTION_SERVICE] Response received:', response);
      console.log('📊 [SUBSCRIPTION_SERVICE] Response status:', response.status);
      console.log('📊 [SUBSCRIPTION_SERVICE] Response data:', response.data);
      return response;
    } catch (error) {
      console.error('❌ [SUBSCRIPTION_SERVICE] Error in getPricingData:', error);
      console.error('❌ [SUBSCRIPTION_SERVICE] Error response:', error.response);
      throw error;
    }
  },

  // Get current user's subscription
  getCurrentSubscription: () => api.get('/api/v1/subscriptions/current'),

  // Removed getPlanOptions - no longer needed with simplified plans

  // Create new subscription
  createSubscription: (plan, couponCode = null) => {
    const requestData = {
      plan_id: plan.id
    };

    // Se for plano anual customizado, enviar dados adicionais
    if (plan.billing_cycle === 'yearly' && plan.price_cents) {
      requestData.custom_price_cents = plan.price_cents;
      requestData.custom_billing_cycle = 'yearly';
      requestData.custom_description = plan.description || `${plan.display_name} - Plano Anual`;
    }

    // Adicionar cupom se fornecido
    if (couponCode) {
      requestData.coupon_code = couponCode;
    }

    return api.post('/api/v1/subscriptions/create', requestData);
  },

  // Cancel subscription
  cancelSubscription: (subscriptionId) =>
    api.post(`/api/v1/subscriptions/cancel/${subscriptionId}`),

  // Calculate upgrade cost with proportional credit
  calculateUpgradeCost: (plan) => {
    const requestData = {
      plan_id: plan.id
    };

    // Se for plano anual customizado, enviar dados adicionais
    if (plan.billing_cycle === 'yearly' && plan.price_cents) {
      requestData.custom_price_cents = plan.price_cents;
      requestData.custom_billing_cycle = 'yearly';
    }

    return api.post('/api/v1/subscriptions/calculate-upgrade-cost', requestData);
  },

  // Validate coupon
  validateCoupon: (couponCode, plan) => {
    const requestData = {
      coupon_code: couponCode,
      plan_id: plan.id
    };

    // Se for plano anual customizado, enviar dados adicionais
    if (plan.billing_cycle === 'yearly' && plan.price_cents) {
      requestData.custom_price_cents = plan.price_cents;
      requestData.custom_billing_cycle = 'yearly';
    }

    return api.post('/api/v1/subscriptions/validate-coupon', requestData);
  },

  // Get cancellation information
  getCancellationInfo: (subscriptionId) =>
    api.get(`/api/v1/subscriptions/cancel-info/${subscriptionId}`),

  // Cancel subscription (keeps active until expiration)
  cancelSubscriptionWithGracePeriod: (subscriptionId) =>
    api.post(`/api/v1/subscriptions/cancel/${subscriptionId}`),

  // Redirect to Asaas payment (opens in new tab)
  redirectToPayment: (initPoint, sandboxInitPoint, isTest = true) => {
    const paymentUrl = isTest ? sandboxInitPoint : initPoint;
    if (paymentUrl) {
      // Se for uma URL de simulação (desenvolvimento), redirecionar para nossa página
      if (paymentUrl.includes('pref_id=test-')) {
        const urlParams = new URLSearchParams(paymentUrl.split('?')[1]);
        const prefId = urlParams.get('pref_id');
        window.open(`/payment-simulation?pref_id=${prefId}`, '_blank');
      } else {
        // URL real do Asaas - abrir em nova aba
        window.open(paymentUrl, '_blank');
      }
    } else {
      throw new Error('URL de pagamento não encontrada');
    }
  },

  // Check if user can perform action
  checkLimits: (action, plantId = null) => {
    const params = plantId ? `?plant_id=${plantId}` : '';
    return api.get(`/api/v1/subscriptions/check-limits/${action}${params}`);
  },

  // Helper functions for limit checking
  canCreatePlant: async () => {
    try {
      const response = await subscriptionService.checkLimits('create_plant');
      return response.data.can_perform;
    } catch (error) {
      console.error('Error checking plant creation limit:', error);
      return false;
    }
  },

  canAddPlantPhoto: async (plantId) => {
    try {
      const response = await subscriptionService.checkLimits('add_plant_photo', plantId);
      return response.data.can_perform;
    } catch (error) {
      console.error('Error checking plant photo limit:', error);
      return false;
    }
  },

  canAddCarePhoto: async () => {
    try {
      const response = await subscriptionService.checkLimits('add_care_photo');
      return response.data.can_perform;
    } catch (error) {
      console.error('Error checking care photo limit:', error);
      return false;
    }
  },

  // Format price for display
  formatPrice: (cents) => {
    if (cents === 0) return 'Gratuito';
    return `R$ ${(cents / 100).toFixed(2).replace('.', ',')}`;
  },

  // Get plan features for display
  getPlanFeatures: (plan) => {
    const features = [];
    
    if (plan.max_plants) {
      features.push(`Até ${plan.max_plants} plantas`);
    } else {
      features.push('Plantas ilimitadas');
    }
    
    if (plan.max_photos_per_plant) {
      features.push(`Até ${plan.max_photos_per_plant} fotos por planta`);
    } else {
      features.push('Fotos ilimitadas por planta');
    }
    
    features.push(`Até ${plan.max_photos_per_care} foto${plan.max_photos_per_care > 1 ? 's' : ''} por cuidado`);
    features.push('Histórico completo de cuidados');
    features.push('Lembretes e notificações');
    features.push('Galeria organizada por data');
    
    return features;
  },

  // Check if user is on free plan
  isFreePlan: (plan) => {
    return plan && (plan.name === 'free' || plan.price_cents === 0);
  },

  // Get upgrade suggestions based on usage
  getUpgradeSuggestions: (usage, currentPlan) => {
    const suggestions = [];
    
    if (!currentPlan) return suggestions;
    
    // Check if approaching plant limit
    if (currentPlan.max_plants && usage.plants_count >= currentPlan.max_plants * 0.8) {
      suggestions.push({
        type: 'plants',
        message: `Você está usando ${usage.plants_count} de ${currentPlan.max_plants} plantas. Considere fazer upgrade para ter mais espaço.`,
        urgency: usage.plants_count >= currentPlan.max_plants ? 'high' : 'medium'
      });
    }
    
    // Check if approaching photo limit
    if (currentPlan.max_photos_per_plant && usage.max_photos_per_plant >= currentPlan.max_photos_per_plant * 0.8) {
      suggestions.push({
        type: 'photos',
        message: `Algumas plantas estão próximas do limite de ${currentPlan.max_photos_per_plant} fotos. Upgrade para fotos ilimitadas.`,
        urgency: usage.max_photos_per_plant >= currentPlan.max_photos_per_plant ? 'high' : 'medium'
      });
    }
    
    return suggestions;
  },

  // Get subscription history and invoices
  getSubscriptionHistory: () => api.get('/api/v1/subscriptions/subscription-history')
};

export default subscriptionService;
