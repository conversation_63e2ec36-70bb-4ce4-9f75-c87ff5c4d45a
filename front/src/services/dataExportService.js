import api from './api';

export const dataExportService = {
  /**
   * Download all user data for LGPD compliance
   */
  async downloadUserData() {
    try {
      console.log('🔄 [DataExport] Starting user data export...');
      
      const response = await api.get('/api/v1/users/me/data-export');
      
      if (response.data) {
        // Create a blob with the JSON data
        const jsonData = JSON.stringify(response.data, null, 2);
        const blob = new Blob([jsonData], { type: 'application/json' });
        
        // Create download link
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        
        // Generate filename with timestamp
        const timestamp = new Date().toISOString().split('T')[0];
        const filename = `meubonsai-dados-${timestamp}.json`;
        link.download = filename;
        
        // Trigger download
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Clean up
        window.URL.revokeObjectURL(url);
        
        console.log('✅ [DataExport] User data exported successfully');
        return { success: true, filename };
      }
      
      throw new Error('No data received');
      
    } catch (error) {
      console.error('❌ [DataExport] Error exporting user data:', error);
      
      if (error.response?.status === 401) {
        throw new Error('Sessão expirada. Faça login novamente.');
      }
      
      if (error.response?.status === 429) {
        throw new Error('Muitas tentativas. Tente novamente em alguns minutos.');
      }
      
      throw new Error('Erro ao exportar dados. Tente novamente.');
    }
  },

  /**
   * Get export info without downloading
   */
  async getExportInfo() {
    try {
      console.log('🔄 [DataExport] Getting export info...');
      
      const response = await api.get('/api/v1/users/me/data-export');
      
      if (response.data?.export_info) {
        return {
          success: true,
          info: response.data.export_info
        };
      }
      
      throw new Error('No export info available');
      
    } catch (error) {
      console.error('❌ [DataExport] Error getting export info:', error);
      throw new Error('Erro ao obter informações de exportação.');
    }
  }
};

export default dataExportService;
