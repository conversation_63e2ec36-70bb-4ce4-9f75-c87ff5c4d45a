import api from './api';

const adminService = {
  // Dashboard
  getDashboard: async () => {
    const response = await api.get('/api/v1/admin/dashboard');
    return response.data;
  },

  // User Management
  getUsers: async (skip = 0, limit = 100) => {
    const response = await api.get(`/api/v1/admin/users?skip=${skip}&limit=${limit}`);
    return response.data;
  },

  updateUser: async (userId, userData) => {
    const response = await api.put(`/api/v1/admin/users/${userId}`, userData);
    return response.data;
  },

  changeUserPlan: async (userId, planData) => {
    const response = await api.post(`/api/v1/admin/users/${userId}/change-plan`, planData);
    return response.data;
  },

  deactivateUserPlan: async (userId) => {
    const response = await api.post(`/api/v1/admin/users/${userId}/deactivate-plan`);
    return response.data;
  },

  // Subscription Plans
  getPlans: async () => {
    try {
      // Try admin endpoint first
      const response = await api.get('/api/v1/admin/plans');
      return response.data;
    } catch (error) {
      // Fallback to public endpoint if admin fails
      console.warn('Admin plans endpoint failed, using public endpoint:', error);
      const response = await api.get('/api/v1/subscriptions/pricing');
      return response.data.plans || [];
    }
  },

  // Coupon Management
  getCoupons: async (skip = 0, limit = 100, activeOnly = false) => {
    const response = await api.get(`/api/v1/admin/coupons?skip=${skip}&limit=${limit}&active_only=${activeOnly}`);
    return response.data;
  },

  createCoupon: async (couponData) => {
    const response = await api.post('/api/v1/admin/coupons', couponData);
    return response.data;
  },

  updateCoupon: async (couponId, couponData) => {
    const response = await api.put(`/api/v1/admin/coupons/${couponId}`, couponData);
    return response.data;
  },

  deleteCoupon: async (couponId) => {
    const response = await api.delete(`/api/v1/admin/coupons/${couponId}`);
    return response.data;
  },

  getCouponUses: async (couponId, skip = 0, limit = 100) => {
    const response = await api.get(`/api/v1/admin/coupons/${couponId}/uses?skip=${skip}&limit=${limit}`);
    return response.data;
  }
};

export default adminService;
