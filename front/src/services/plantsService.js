import api from './api';

export const plantsService = {
  // Plantas
  getPlants: (archived = false, favorite = null) => {
    const params = { archived };
    if (favorite !== null) params.favorite = favorite;
    return api.get('/api/v1/plants/', { params });
  },
  getPlant: (id, timezoneOffset = -3) => api.get(`/api/v1/plants/${id}`, { params: { timezone_offset: timezoneOffset } }),
  createPlant: (data) => api.post('/api/v1/plants/', data),
  updatePlant: (id, data) => api.put(`/api/v1/plants/${id}`, data),
  deletePlant: (id) => api.delete(`/api/v1/plants/${id}`),
  archivePlant: (id, archived = true) => api.put(`/api/v1/plants/${id}/archive`, null, { params: { archived } }),
  favoritePlant: (id, favorite = true) => api.put(`/api/v1/plants/${id}/favorite`, null, { params: { favorite } }),
  updatePlantPrivacy: (id, privacySettings) => api.put(`/api/v1/plants/${id}/privacy`, null, { params: privacySettings }),

  // Imagens
  getPlantImages: (plantId) => api.get(`/api/v1/plants/${plantId}/images`),
  getAllPlantImages: (plantId) => api.get(`/api/v1/plants/${plantId}/all-images`),

  // Vídeos
  getPlantVideos: (plantId) => api.get(`/api/v1/plants/${plantId}/videos`),
  addPlantVideo: (plantId, videoData) => {
    const formData = new FormData();
    formData.append('file', videoData.video);  // Backend espera 'file', não 'video'
    if (videoData.caption) formData.append('caption', videoData.caption);
    if (videoData.video_date) formData.append('video_date', videoData.video_date);

    return api.post(`/api/v1/plants/${plantId}/videos`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  addPlantImage: (plantId, data) => {
    const formData = new FormData();
    formData.append('file', data.image);
    if (data.caption) formData.append('caption', data.caption);
    if (data.is_primary) formData.append('is_primary', data.is_primary);
    if (data.photo_date) formData.append('photo_date', data.photo_date);

    return api.post(`/api/v1/plants/${plantId}/images`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  updatePlantImage: (plantId, imageId, data) => {
    const formData = new FormData();
    if (data.caption !== undefined) formData.append('caption', data.caption);
    if (data.is_primary !== undefined) formData.append('is_primary', data.is_primary);
    if (data.photo_date !== undefined) formData.append('photo_date', data.photo_date);
    if (data.rotation !== undefined) formData.append('rotation', data.rotation);

    return api.put(`/api/v1/plants/${plantId}/images/${imageId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  updatePlantImageWithFile: (plantId, imageId, data) => {
    const formData = new FormData();
    if (data.file) formData.append('file', data.file);
    if (data.caption !== undefined) formData.append('caption', data.caption);
    if (data.is_primary !== undefined) formData.append('is_primary', data.is_primary);
    if (data.photo_date !== undefined) formData.append('photo_date', data.photo_date);

    return api.put(`/api/v1/plants/${plantId}/images/${imageId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  rotatePlantImage: (plantId, imageId, rotation = 90) => {
    return api.post(`/api/v1/plants/${plantId}/images/${imageId}/rotate`, null, {
      params: { rotation }
    });
  },

  // Care image management
  updateCareImage: (plantId, careId, imageId, data) => {
    const formData = new FormData();
    if (data.caption !== undefined) formData.append('caption', data.caption);
    if (data.is_primary !== undefined) formData.append('is_primary', data.is_primary);

    return api.put(`/api/v1/plants/${plantId}/cares/${careId}/images/${imageId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  rotateCareImage: (plantId, careId, imageId, rotation = 90) => {
    return api.post(`/api/v1/plants/${plantId}/cares/${careId}/images/${imageId}/rotate`, null, {
      params: { rotation }
    });
  },

  deleteCareImage: (plantId, careId, imageId) => {
    return api.delete(`/api/v1/plants/${plantId}/cares/${careId}/images/${imageId}`);
  },

  deleteCareVideo: (plantId, careId, videoId) => {
    return api.delete(`/api/v1/plants/${plantId}/cares/${careId}/videos/${videoId}`);
  },

  // Lembretes
  getPlantReminders: (plantId, includeCompleted = false) => {
    return api.get(`/api/v1/plants/${plantId}/reminders`, {
      params: { include_completed: includeCompleted }
    });
  },



  createReminderFromCare: (plantId, careId, data) => {
    const formData = new FormData();
    formData.append('interval_amount', data.interval_amount);
    formData.append('interval_unit', data.interval_unit);
    if (data.description) formData.append('description', data.description);

    return api.post(`/api/v1/plants/${plantId}/cares/${careId}/create-reminder`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  deletePlantImage: (plantId, imageId) => api.delete(`/api/v1/plants/${plantId}/images/${imageId}`),
  deletePlantVideo: (plantId, videoId) => api.delete(`/api/v1/plants/${plantId}/videos/${videoId}`),

  // Cuidados
  getPlantCares: (plantId) => api.get(`/api/v1/plants/${plantId}/cares`),
  addPlantCare: (plantId, data) => api.post(`/api/v1/plants/${plantId}/cares`, data),
  addPlantCareWithFile: (plantId, formData) => {
    return api.post(`/api/v1/plants/${plantId}/cares`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  updatePlantCare: (plantId, careId, data) =>
    api.put(`/api/v1/plants/${plantId}/cares/${careId}`, data),
  updatePlantCareWithFile: (plantId, careId, formData) => {
    return api.put(`/api/v1/plants/${plantId}/cares/${careId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  deletePlantCare: (plantId, careId) =>
    api.delete(`/api/v1/plants/${plantId}/cares/${careId}`),
};

export default plantsService;
