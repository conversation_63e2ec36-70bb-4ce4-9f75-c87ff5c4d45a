import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'https://api.meubonsai.app';

// Cache for R2 public URL
let R2_PUBLIC_URL_CACHE = null;

// Function to get R2 public URL from backend
const getR2PublicUrl = async () => {
  if (R2_PUBLIC_URL_CACHE) {
    return R2_PUBLIC_URL_CACHE;
  }

  try {
    const response = await fetch(`${API_URL}/api/v1/config/public`);
    const config = await response.json();
    R2_PUBLIC_URL_CACHE = config.r2_public_url;
    return R2_PUBLIC_URL_CACHE;
  } catch (error) {
    console.error('Failed to get R2 public URL from backend:', error);
    // Fallback to dev URL if config fails
    return 'https://pub-a1a37eb812e943939ba2baaf2a81cec1.r2.dev';
  }
};

// Synchronous version for backward compatibility (uses cached R2 URL)
export const getImageUrlSync = (imagePath, size = 'medium', plantId = null) => {
  if (!imagePath) return null;

  // If it's already a complete URL (from backend), return as-is
  if (imagePath.startsWith('http')) {
    console.log('getImageUrlSync (complete URL):', imagePath);
    return imagePath;
  }

  // If it's an API endpoint (like video-stream), return full API URL
  if (imagePath.startsWith('/api/')) {
    const url = `${API_URL}${imagePath}`;
    console.log('getImageUrlSync (API endpoint):', imagePath, '->', url);
    return url;
  }

  // Check if it's an R2 filename (UUID format)
  const filename = imagePath.split('/').pop();
  const filenameWithoutExt = filename.split('.')[0];

  if (filenameWithoutExt.length === 32) {
    // R2 image - use cached URL or fallback
    const R2_PUBLIC_URL = R2_PUBLIC_URL_CACHE || 'https://pub-a1a37eb812e943939ba2baaf2a81cec1.r2.dev';

    // Try to extract plant_id from path or use provided plantId
    let extractedPlantId = plantId;
    if (!extractedPlantId) {
      const pathParts = imagePath.split('/');
      if (pathParts.length > 1 && pathParts[0] === 'plants') {
        // Path format: plants/plant_id/filename
        extractedPlantId = pathParts[1];
      }
    }

    if (extractedPlantId) {
      const url = `${R2_PUBLIC_URL}/plants/${extractedPlantId}/${size}_${filename}`;
      console.log('getImageUrlSync (R2):', imagePath, '->', url);
      return url;
    }
  }

  // Legacy local file
  let fullPath = imagePath;
  if (!fullPath.startsWith('/media/') && !fullPath.startsWith('media/')) {
    fullPath = `/media/${fullPath}`;
  }

  const url = `${API_URL}${fullPath.startsWith('/') ? '' : '/'}${fullPath}`;
  console.log('getImageUrlSync (local):', imagePath, '->', url);
  return url;
};

// Async helper function to build image URLs
export const getImageUrl = async (imagePath, size = 'medium', plantId = null) => {
  if (!imagePath) return null;

  // If it's already a complete URL (from backend), return as-is
  if (imagePath.startsWith('http')) {
    console.log('getImageUrl (complete URL):', imagePath);
    return imagePath;
  }

  // If it's an API endpoint (like video-stream), return full API URL
  if (imagePath.startsWith('/api/')) {
    const url = `${API_URL}${imagePath}`;
    console.log('getImageUrl (API endpoint):', imagePath, '->', url);
    return url;
  }

  // Check if it's an R2 filename (UUID format)
  const filename = imagePath.split('/').pop();
  const filenameWithoutExt = filename.split('.')[0];

  if (filenameWithoutExt.length === 32) {
    // R2 image - get URL from backend config
    const R2_PUBLIC_URL = await getR2PublicUrl();

    // Try to extract plant_id from path or use provided plantId
    let extractedPlantId = plantId;
    if (!extractedPlantId) {
      const pathParts = imagePath.split('/');
      if (pathParts.length > 1 && pathParts[0] === 'plants') {
        // Path format: plants/plant_id/filename
        extractedPlantId = pathParts[1];
      }
    }

    if (extractedPlantId) {
      const url = `${R2_PUBLIC_URL}/plants/${extractedPlantId}/${size}_${filename}`;
      console.log('getImageUrl (R2):', imagePath, '->', url);
      return url;
    }
  }

  // Legacy local file
  let fullPath = imagePath;
  if (!fullPath.startsWith('/media/') && !fullPath.startsWith('media/')) {
    fullPath = `/media/${fullPath}`;
  }

  const url = `${API_URL}${fullPath.startsWith('/') ? '' : '/'}${fullPath}`;
  console.log('getImageUrl (local):', imagePath, '->', url);
  return url;
};

const api = axios.create({
  baseURL: API_URL,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor para adicionar token de autorização
api.interceptors.request.use(
  (config) => {
    console.log('🔍 [API] Making request to:', config.url);
    console.log('🔍 [API] Base URL:', config.baseURL);
    console.log('🔍 [API] Full URL:', `${config.baseURL}${config.url}`);

    // Adicionar token de autorização se disponível
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log('🔑 [API] Token added to request');
    } else {
      console.log('⚠️ [API] No token found in localStorage');
    }

    console.log('📤 [API] Request headers:', config.headers);
    return config;
  },
  (error) => {
    console.error('❌ [API] Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Interceptor para tratar respostas
api.interceptors.response.use(
  (response) => {
    console.log('✅ [API] Response received:', response.status, response.config.url);
    console.log('📊 [API] Response data:', response.data);
    return response;
  },
  (error) => {
    console.error('❌ [API] Response error:', error);
    console.error('❌ [API] Error response:', error.response);
    console.error('❌ [API] Error status:', error.response?.status);
    console.error('❌ [API] Error data:', error.response?.data);

    if (error.response?.status === 401) {
      console.log('🚪 [API] Unauthorized - redirecting to login');
      // Token expirado ou usuário não autenticado
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Function to get user timezone offset in hours
export const getUserTimezoneOffset = () => {
  const offset = new Date().getTimezoneOffset();
  return -Math.floor(offset / 60); // Convert minutes to hours and invert sign
};

export default api;
