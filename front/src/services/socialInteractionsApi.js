import api from './api';

// Feed API
export const getFeed = async (page = 1, limit = 20) => {
  const skip = (page - 1) * limit;
  const response = await api.get(`/api/v1/social/feed?skip=${skip}&limit=${limit}`);
  return response.data;
};

// Plant Likes API
export const likePlant = async (plantId) => {
  const response = await api.post(`/api/v1/social/plants/${plantId}/like`);
  return response.data;
};

export const unlikePlant = async (plantId) => {
  const response = await api.delete(`/api/v1/social/plants/${plantId}/like`);
  return response.data;
};

export const getPlantLikes = async (plantId, page = 1, limit = 20) => {
  const skip = (page - 1) * limit;
  const response = await api.get(`/api/v1/social/plants/${plantId}/likes?skip=${skip}&limit=${limit}`);
  return response.data;
};

// Plant Comments API
export const createPlantComment = async (plantId, content) => {
  const response = await api.post(`/api/v1/social/plants/${plantId}/comments`, {
    plant_id: plantId,
    content: content
  });
  return response.data;
};

export const getPlantComments = async (plantId, page = 1, limit = 20) => {
  const skip = (page - 1) * limit;
  const response = await api.get(`/api/v1/social/plants/${plantId}/comments?skip=${skip}&limit=${limit}`);
  return response.data;
};

export const deletePlantComment = async (commentId) => {
  const response = await api.delete(`/api/v1/social/comments/${commentId}`);
  return response.data;
};

// Plant Social Info API
export const getPlantSocialInfo = async (plantId) => {
  const response = await api.get(`/api/v1/social/plants/${plantId}/social-info`);
  return response.data;
};

export default {
  getFeed,
  likePlant,
  unlikePlant,
  getPlantLikes,
  createPlantComment,
  getPlantComments,
  deletePlantComment,
  getPlantSocialInfo
};
