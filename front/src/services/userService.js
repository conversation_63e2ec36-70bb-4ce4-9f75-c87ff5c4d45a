import api from './api';

export const userService = {
  // Obter perfil do usuário
  getProfile: () => api.get('/api/v1/users/me'),

  // Atualizar perfil do usuário (FormData para upload de avatar)
  updateProfile: (formData) => {
    return api.put('/api/v1/users/me', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // Atualizar perfil do usuário (JSON para dados simples)
  updateProfileData: (data) => {
    return api.put('/api/v1/users/me/profile', data);
  },

  // Excluir conta do usuário
  deleteAccount: () => {
    return api.delete('/api/v1/users/me');
  },

  // Solicitar alteração de senha
  requestPasswordChange: (currentPassword, newPassword) => {
    return api.post('/api/v1/auth/request-password-change', {
      current_password: currentPassword,
      new_password: newPassword
    });
  },

  // Confirmar alteração de senha
  confirmPasswordChange: (token) => {
    return api.post('/api/v1/auth/confirm-password-change', {
      token: token
    });
  },
};

export default userService;
