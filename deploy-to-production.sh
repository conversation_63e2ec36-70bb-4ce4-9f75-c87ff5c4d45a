#!/bin/bash

# 🚀 Script de Deploy Seguro para Produção
# Este script automatiza o deploy mantendo as configurações de SEO protegidas

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Diretórios
PROD_DIR="/root/meubonsai-app"
DEV_DIR="/root/meubonsai-app-dev"

echo -e "${BLUE}🚀 Deploy Seguro para Produção - MeuBonsai.App${NC}"
echo "============================================================"

# Função para verificar se estamos no diretório correto
check_environment() {
    if [ ! -d "$PROD_DIR" ]; then
        echo -e "${RED}❌ Diretório de produção não encontrado: $PROD_DIR${NC}"
        exit 1
    fi
    
    if [ "$(pwd)" != "$PROD_DIR" ]; then
        echo -e "${YELLOW}⚠️  Mudando para diretório de produção...${NC}"
        cd "$PROD_DIR"
    fi
}

# Função para verificar status do git
check_git_status() {
    echo -e "${YELLOW}🔍 Verificando status do Git...${NC}"
    
    # Verificar se há mudanças não commitadas
    if ! git diff --quiet; then
        echo -e "${RED}❌ Há mudanças não commitadas no ambiente de produção!${NC}"
        echo "Execute 'git status' para ver as mudanças."
        exit 1
    fi
    
    # Verificar branch atual
    current_branch=$(git branch --show-current)
    if [ "$current_branch" != "main" ]; then
        echo -e "${YELLOW}⚠️  Branch atual: $current_branch (recomendado: main)${NC}"
        read -p "Continuar mesmo assim? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    echo -e "${GREEN}✅ Git status OK${NC}"
}

# Função para fazer backup das configurações
backup_production_configs() {
    echo -e "${YELLOW}📦 Fazendo backup das configurações de produção...${NC}"
    
    if [ -f "protect-production-config.sh" ]; then
        ./protect-production-config.sh backup
    else
        echo -e "${RED}❌ Script de proteção não encontrado!${NC}"
        exit 1
    fi
}

# Função para fazer pull do git
pull_latest_changes() {
    echo -e "${YELLOW}⬇️  Fazendo pull das últimas mudanças...${NC}"
    
    git fetch origin
    git pull origin main
    
    echo -e "${GREEN}✅ Pull concluído${NC}"
}

# Função para restaurar configurações de produção
restore_production_configs() {
    echo -e "${YELLOW}🔄 Restaurando configurações de produção...${NC}"
    
    ./protect-production-config.sh restore
}

# Função para rebuild dos containers
rebuild_containers() {
    echo -e "${YELLOW}🔨 Reconstruindo containers...${NC}"
    
    ./start.sh prod down
    ./start.sh prod build
    
    echo -e "${GREEN}✅ Containers reconstruídos e iniciados${NC}"
}

# Função para validar deploy
validate_deploy() {
    echo -e "${YELLOW}🔍 Validando deploy...${NC}"
    
    # Validar configurações
    ./protect-production-config.sh validate
    
    # Verificar se os containers estão rodando
    echo -e "${YELLOW}🐳 Verificando status dos containers...${NC}"
    ./start.sh prod status
    
    # Testar conectividade
    echo -e "${YELLOW}🌐 Testando conectividade...${NC}"
    
    sleep 10  # Aguardar containers iniciarem
    
    if curl -s -o /dev/null -w "%{http_code}" https://meubonsai.app/ | grep -q "200"; then
        echo -e "${GREEN}✅ Frontend acessível${NC}"
    else
        echo -e "${RED}❌ Frontend não acessível${NC}"
    fi
    
    if curl -s -o /dev/null -w "%{http_code}" https://api.meubonsai.app/health | grep -q "200"; then
        echo -e "${GREEN}✅ Backend acessível${NC}"
    else
        echo -e "${RED}❌ Backend não acessível${NC}"
    fi
    
    # Verificar SEO
    echo -e "${YELLOW}🔍 Verificando configurações de SEO...${NC}"
    
    if curl -s https://meubonsai.app/robots.txt | grep -q "Allow: /"; then
        echo -e "${GREEN}✅ robots.txt permite indexação${NC}"
    else
        echo -e "${RED}❌ robots.txt não permite indexação${NC}"
    fi
    
    if curl -s https://meubonsai.app/ | grep -q 'content="index, follow"'; then
        echo -e "${GREEN}✅ Meta tags permitem indexação${NC}"
    else
        echo -e "${RED}❌ Meta tags não permitem indexação${NC}"
    fi
}

# Função para mostrar resumo
show_summary() {
    echo ""
    echo -e "${BLUE}📋 Resumo do Deploy${NC}"
    echo "===================="
    echo -e "🌐 Frontend: ${GREEN}https://meubonsai.app${NC}"
    echo -e "🔧 Backend:  ${GREEN}https://api.meubonsai.app${NC}"
    echo -e "🗄️  PgAdmin:  ${GREEN}https://pg.meubonsai.app${NC}"
    echo ""
    echo -e "${GREEN}✅ Deploy concluído com sucesso!${NC}"
    echo ""
    echo -e "${YELLOW}📝 Próximos passos recomendados:${NC}"
    echo "1. Testar funcionalidades críticas no site"
    echo "2. Verificar logs se necessário: ./start.sh prod logs"
    echo "3. Monitorar métricas de performance"
    echo ""
}

# Função para rollback em caso de erro
rollback() {
    echo -e "${RED}❌ Erro detectado! Iniciando rollback...${NC}"
    
    # Tentar restaurar último backup
    if [ -d ".production-backup-"* ]; then
        latest_backup=$(ls -td .production-backup-* | head -1)
        echo -e "${YELLOW}🔄 Restaurando backup: $latest_backup${NC}"
        
        # Restaurar arquivos críticos
        cp "$latest_backup"/* front/public/ 2>/dev/null || true
        
        # Reiniciar containers
        ./start.sh prod restart
        
        echo -e "${YELLOW}⚠️  Rollback concluído. Verifique o status manualmente.${NC}"
    else
        echo -e "${RED}❌ Nenhum backup encontrado para rollback automático!${NC}"
        echo "Execute rollback manual se necessário."
    fi
}

# Função principal
main() {
    # Trap para rollback em caso de erro
    trap rollback ERR
    
    echo -e "${BLUE}Iniciando deploy seguro...${NC}"
    
    # Verificações iniciais
    check_environment
    check_git_status
    
    # Backup das configurações atuais
    backup_production_configs
    
    # Pull das mudanças
    pull_latest_changes
    
    # Restaurar configurações de produção
    restore_production_configs
    
    # Rebuild dos containers
    rebuild_containers
    
    # Validar deploy
    validate_deploy
    
    # Mostrar resumo
    show_summary
    
    # Remover trap de erro (deploy bem-sucedido)
    trap - ERR
}

# Verificar se foi chamado com parâmetro de ajuda
if [[ "${1:-}" == "help" || "${1:-}" == "--help" || "${1:-}" == "-h" ]]; then
    echo -e "${BLUE}🚀 Script de Deploy Seguro para Produção${NC}"
    echo ""
    echo "Este script automatiza o deploy mantendo as configurações de SEO protegidas."
    echo ""
    echo "Uso: ./deploy-to-production.sh"
    echo ""
    echo "O script executa automaticamente:"
    echo "1. ✅ Verificação do status do Git"
    echo "2. 📦 Backup das configurações de produção"
    echo "3. ⬇️  Pull das últimas mudanças do Git"
    echo "4. 🔄 Restauração das configurações de produção"
    echo "5. 🔨 Rebuild dos containers Docker"
    echo "6. 🔍 Validação do deploy"
    echo "7. 📋 Resumo e próximos passos"
    echo ""
    echo -e "${YELLOW}⚠️  IMPORTANTE: Execute este script apenas no ambiente de produção!${NC}"
    exit 0
fi

# Executar deploy
main
