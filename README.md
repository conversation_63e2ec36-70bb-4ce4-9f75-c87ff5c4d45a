# 🌱 MeuBonsai.App

Plataforma completa para gerenciamento de coleções de bonsai com sistema social, lembretes e galeria de fotos.

## 🏗️ Arquitetura

- **Frontend**: React 18 + PWA
- **Backend**: FastAPI + Python
- **Database**: PostgreSQL + pgVector
- **Storage**: Cloudflare R2
- **Proxy**: Traefik

## 🚀 Início Rápido

### Pré-requisitos

- Docker e Docker Compose
- Rede `my-network` (criada automaticamente)

### Usando o Script de Conveniência

```bash
# Iniciar toda a aplicação
./start.sh up

# Reconstruir e iniciar
./start.sh build

# Ver logs em tempo real
./start.sh logs

# Parar aplicação
./start.sh down

# Ver ajuda completa
./start.sh help
```

### Usando Docker Compose Diretamente

```bash
# Iniciar todos os containers
docker compose up -d

# Reconstruir imagens
docker compose build

# Ver logs
docker compose logs -f

# Parar containers
docker compose down
```

## 🌐 URLs de Acesso

- **Frontend**: https://meubonsai.app
- **Backend API**: https://api.meubonsai.app
- **PgAdmin**: https://pg.meubonsai.app
- **API Docs**: https://api.meubonsai.app/docs

## 📁 Estrutura do Projeto

```
meubonsai/
├── docker-compose.yml     # Configuração unificada
├── start.sh              # Script de conveniência
├── .env                  # Variáveis de ambiente
├── back/                 # Backend FastAPI
│   ├── Dockerfile
│   ├── requirements.txt
│   └── app/
├── front/                # Frontend React
│   ├── Dockerfile
│   ├── package.json
│   └── src/
└── db/                   # Database
    └── init/             # Scripts de inicialização
```

## 🔧 Desenvolvimento

### Alterações no Frontend
```bash
# Apenas rebuild do frontend
docker compose up -d --build frontend
```

### Alterações no Backend
```bash
# Apenas rebuild do backend
docker compose up -d --build backend
```

### Alterações no Banco
```bash
# Reiniciar banco (CUIDADO: perde dados)
docker compose down database
docker compose up -d database
```

## 📊 Monitoramento

### Ver Status dos Containers
```bash
./start.sh status
# ou
docker compose ps
```

### Logs Específicos
```bash
# Frontend
docker compose logs -f frontend

# Backend
docker compose logs -f backend

# Database
docker compose logs -f database

# PgAdmin
docker compose logs -f pgadmin
```

## 🛠️ Troubleshooting

### Problemas Comuns

1. **Erro de rede**: Certifique-se que a rede `my-network` existe
   ```bash
   docker network create my-network
   ```

2. **Porta ocupada**: Verifique se as portas 5432 estão livres
   ```bash
   sudo netstat -tulpn | grep :5432
   ```

3. **Problemas de build**: Limpe o cache do Docker
   ```bash
   docker system prune -f
   docker compose build --no-cache
   ```

### Reset Completo
```bash
# ATENÇÃO: Remove todos os dados!
./start.sh clean
```

## 🔐 Variáveis de Ambiente

As principais variáveis estão no arquivo `.env`:

- `POSTGRES_*`: Configurações do banco
- `R2_*`: Cloudflare R2 para imagens
- `GOOGLE_*`: OAuth Google
- `SMTP_*`: Configurações de email
- `ASAAS_*`: Gateway de pagamento

## 📝 Comandos Úteis

```bash
# Backup do banco
docker exec meubonsai_postgres pg_dump -U dbadmin meubonsai_db > backup.sql

# Restaurar backup
docker exec -i meubonsai_postgres psql -U dbadmin meubonsai_db < backup.sql

# Acessar banco via CLI
docker exec -it meubonsai_postgres psql -U dbadmin meubonsai_db

# Shell no container backend
docker exec -it meubonsai_backend bash

# Shell no container frontend
docker exec -it meubonsai_frontend bash
```

## 🎯 Próximos Passos

1. Configure o arquivo `.env` com suas credenciais
2. Execute `./start.sh up` para iniciar
3. Acesse https://meubonsai.app para testar
4. Configure o Traefik para SSL em produção

## 📞 Suporte

Para problemas ou dúvidas, verifique:
- Logs dos containers: `./start.sh logs`
- Status dos serviços: `./start.sh status`
- Documentação da API: https://api.meubonsai.app/docs
