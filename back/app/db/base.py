from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from app.core.config import settings

# Configurações mais conservadoras para estabilidade
engine = create_engine(
    settings.SQLALCHEMY_DATABASE_URI,
    pool_size=5,  # Pool size padrão
    max_overflow=10,  # Overflow padrão
    pool_timeout=30,  # Timeout padrão
    pool_recycle=1800,  # Reciclar conexões a cada 30 min
    pool_pre_ping=True,  # Verificar conexões antes de usar
    echo=False  # Desabilitar logs SQL em produção
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()
