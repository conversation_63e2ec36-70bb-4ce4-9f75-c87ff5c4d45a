from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import text, and_, or_
from datetime import datetime, timedelta
from uuid import UUID

from app.crud.base import CRUDBase
from app.models.subscription import SubscriptionPlan, UserSubscription, Payment, UsageTracking
from app.models.subscription import SubscriptionStatus, PaymentStatus
from app.schemas.subscription import (
    SubscriptionPlanCreate, SubscriptionPlanUpdate,
    UserSubscriptionCreate, UserSubscriptionUpdate,
    PaymentCreate, PaymentUpdate,
    UsageTrackingCreate
)


class CRUDSubscriptionPlan(CRUDBase[SubscriptionPlan, SubscriptionPlanCreate, SubscriptionPlanUpdate]):
    def get_by_name(self, db: Session, *, name: str) -> Optional[SubscriptionPlan]:
        return db.query(SubscriptionPlan).filter(SubscriptionPlan.name == name).first()

    def get_active_plans(self, db: Session) -> List[SubscriptionPlan]:
        return db.query(SubscriptionPlan).filter(SubscriptionPlan.is_active == True).all()

    def get_free_plan(self, db: Session) -> Optional[SubscriptionPlan]:
        return self.get_by_name(db, name="free")


class CRUDUserSubscription(CRUDBase[UserSubscription, UserSubscriptionCreate, UserSubscriptionUpdate]):
    def get_by_user(self, db: Session, *, user_id: int) -> List[UserSubscription]:
        return db.query(UserSubscription).filter(UserSubscription.user_id == user_id).all()

    def get_active_by_user(self, db: Session, *, user_id: int) -> Optional[UserSubscription]:
        return db.query(UserSubscription).filter(
            and_(
                UserSubscription.user_id == user_id,
                UserSubscription.status == SubscriptionStatus.active,
                or_(
                    UserSubscription.expires_at.is_(None),
                    UserSubscription.expires_at > datetime.utcnow()
                )
            )
        ).first()

    def create_subscription(
        self, 
        db: Session, 
        *, 
        user_id: int, 
        plan_id: UUID,
        expires_at: Optional[datetime] = None
    ) -> UserSubscription:
        # Cancel any existing active subscriptions
        existing = self.get_active_by_user(db, user_id=user_id)
        if existing:
            existing.status = SubscriptionStatus.cancelled
            existing.cancelled_at = datetime.utcnow()
            db.add(existing)

        # Create new subscription
        subscription_data = UserSubscriptionCreate(
            user_id=user_id,
            plan_id=plan_id,
            expires_at=expires_at
        )
        subscription = UserSubscription(**subscription_data.dict())
        db.add(subscription)
        db.commit()
        db.refresh(subscription)
        return subscription

    def cancel_subscription(
        self, 
        db: Session, 
        *, 
        subscription_id: UUID,
        cancel_immediately: bool = False
    ) -> UserSubscription:
        subscription = db.query(UserSubscription).filter(UserSubscription.id == subscription_id).first()
        if not subscription:
            return None

        subscription.cancelled_at = datetime.utcnow()
        subscription.auto_renew = False
        
        if cancel_immediately:
            subscription.status = SubscriptionStatus.cancelled
            subscription.expires_at = datetime.utcnow()
        
        db.add(subscription)
        db.commit()
        db.refresh(subscription)
        return subscription


class CRUDPayment(CRUDBase[Payment, PaymentCreate, PaymentUpdate]):
    def get_by_user(self, db: Session, *, user_id: int) -> List[Payment]:
        return db.query(Payment).filter(Payment.user_id == user_id).order_by(Payment.created_at.desc()).all()

    def get_by_subscription(self, db: Session, *, subscription_id: UUID) -> List[Payment]:
        return db.query(Payment).filter(Payment.subscription_id == subscription_id).all()

    def create_payment(
        self, 
        db: Session, 
        *, 
        user_id: int,
        amount_cents: int,
        subscription_id: Optional[UUID] = None,
        payment_method: Optional[str] = None
    ) -> Payment:
        payment_data = PaymentCreate(
            user_id=user_id,
            amount_cents=amount_cents,
            subscription_id=subscription_id,
            payment_method=payment_method
        )
        payment = Payment(**payment_data.dict())
        db.add(payment)
        db.commit()
        db.refresh(payment)
        return payment

    def mark_as_completed(
        self, 
        db: Session, 
        *, 
        payment_id: UUID,
        external_payment_id: Optional[str] = None,
        payment_data: Optional[dict] = None
    ) -> Payment:
        payment = db.query(Payment).filter(Payment.id == payment_id).first()
        if not payment:
            return None

        payment.status = PaymentStatus.completed
        payment.processed_at = datetime.utcnow()
        if external_payment_id:
            payment.external_payment_id = external_payment_id
        if payment_data:
            payment.payment_data = payment_data

        db.add(payment)
        db.commit()
        db.refresh(payment)
        return payment


class CRUDUsageTracking(CRUDBase[UsageTracking, UsageTrackingCreate, None]):
    def track_action(
        self,
        db: Session,
        *,
        user_id: int,
        resource_type: str,
        action: str,
        resource_id: Optional[UUID] = None,
        tracking_metadata: Optional[dict] = None
    ) -> UsageTracking:
        tracking_data = UsageTrackingCreate(
            user_id=user_id,
            resource_type=resource_type,
            resource_id=resource_id,
            action=action,
            tracking_metadata=tracking_metadata
        )
        tracking = UsageTracking(**tracking_data.dict())
        db.add(tracking)
        db.commit()
        db.refresh(tracking)
        return tracking


class CRUDSubscriptionService:
    """Service class for subscription-related operations"""
    
    def __init__(self):
        self.plan = CRUDSubscriptionPlan(SubscriptionPlan)
        self.subscription = CRUDUserSubscription(UserSubscription)
        self.payment = CRUDPayment(Payment)
        self.usage = CRUDUsageTracking(UsageTracking)

    def get_user_usage_stats(self, db: Session, *, user_id: int) -> Dict[str, Any]:
        """Get user's current usage statistics"""
        result = db.execute(text("SELECT * FROM get_user_usage(:user_id)"), {"user_id": user_id}).fetchone()
        
        if not result:
            return {
                "plants_count": 0,
                "total_photos_count": 0,
                "max_photos_per_plant": 0
            }
        
        return {
            "plants_count": result.plants_count or 0,
            "total_photos_count": result.total_photos_count or 0,
            "max_photos_per_plant": result.max_photos_per_plant or 0
        }

    def can_user_perform_action(
        self, 
        db: Session, 
        *, 
        user_id: int,
        action_type: str,
        plant_id: Optional[UUID] = None
    ) -> bool:
        """Check if user can perform a specific action based on their plan limits"""
        result = db.execute(
            text("SELECT can_user_perform_action(:user_id, :action_type, :plant_id)"),
            {
                "user_id": user_id,
                "action_type": action_type,
                "plant_id": plant_id
            }
        ).fetchone()
        
        return result[0] if result else False

    def get_user_plan_with_usage(self, db: Session, *, user_id: int) -> Dict[str, Any]:
        """Get user's current plan and usage statistics"""
        from app.models.user import User
        
        user = db.query(User).filter(User.id == user_id).first()
        if not user or not user.current_plan:
            return None

        usage_stats = self.get_user_usage_stats(db, user_id=user_id)
        
        return {
            "plan": user.current_plan,
            "usage": usage_stats,
            "limits": {
                "max_plants": user.current_plan.max_plants,
                "max_photos_per_plant": user.current_plan.max_photos_per_plant,
                "max_photos_per_care": user.current_plan.max_photos_per_care
            },
            "can_create_plant": self.can_user_perform_action(db, user_id=user_id, action_type="create_plant"),
            "subscription": self.subscription.get_active_by_user(db, user_id=user_id)
        }


# Create instances
subscription_plan = CRUDSubscriptionPlan(SubscriptionPlan)
user_subscription = CRUDUserSubscription(UserSubscription)
payment = CRUDPayment(Payment)
usage_tracking = CRUDUsageTracking(UsageTracking)
subscription_service = CRUDSubscriptionService()
