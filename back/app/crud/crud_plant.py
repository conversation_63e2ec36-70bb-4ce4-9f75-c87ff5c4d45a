from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func
from app.crud.base import CRUDBase
from app.models.plant import Plant, PlantImage, Care, Reminder
from app.schemas.plant import PlantCreate, PlantUpdate, PlantImageCreate, CareCreate, ReminderCreate


class CRUDPlant(CRUDBase[Plant, PlantCreate, PlantUpdate]):
    def get_by_owner(
        self, db: Session, *, owner_id: int, skip: int = 0, limit: int = 100, archived: bool = False, favorite: bool = None
    ) -> List[Plant]:
        query = (
            db.query(Plant)
            .filter(Plant.owner_id == owner_id)
            .filter(Plant.archived == archived)
        )

        if favorite is not None:
            query = query.filter(Plant.favorite == favorite)

        return query.offset(skip).limit(limit).all()

    def create_with_owner(
        self, db: Session, *, obj_in: PlantCreate, owner_id: int
    ) -> Plant:
        obj_in_data = obj_in.dict()
        db_obj = Plant(**obj_in_data, owner_id=owner_id)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_with_details(self, db: Session, *, id: int) -> Optional[Plant]:
        return (
            db.query(Plant)
            .filter(Plant.id == id)
            .first()
        )


class CRUDPlantImage(CRUDBase[PlantImage, PlantImageCreate, PlantImageCreate]):
    def get_by_plant(self, db: Session, *, plant_id: int) -> List[PlantImage]:
        return (
            db.query(PlantImage)
            .filter(PlantImage.plant_id == plant_id)
            .order_by(PlantImage.photo_date.desc().nullslast(), PlantImage.created_at.desc())
            .all()
        )

    def create_with_plant(
        self, db: Session, *, obj_in: PlantImageCreate, plant_id: int, image_path: str, original_filename: str = None
    ) -> PlantImage:
        db_obj = PlantImage(
            plant_id=plant_id,
            image=image_path,
            original_filename=original_filename,
            caption=obj_in.caption,
            is_primary=obj_in.is_primary,
            photo_date=obj_in.photo_date,
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def set_primary(self, db: Session, *, plant_id: int, image_id: int) -> PlantImage:
        # Remove primary from all images of this plant
        db.query(PlantImage).filter(PlantImage.plant_id == plant_id).update(
            {"is_primary": False}
        )
        # Set new primary
        image = db.query(PlantImage).filter(PlantImage.id == image_id).first()
        if image:
            image.is_primary = True
            db.commit()
            db.refresh(image)
        return image


class CRUDCare(CRUDBase[Care, CareCreate, CareCreate]):
    def get_by_plant(
        self, db: Session, *, plant_id: int, skip: int = 0, limit: int = 100
    ) -> List[Care]:
        from sqlalchemy.orm import joinedload
        return (
            db.query(Care)
            .options(joinedload(Care.images))
            .filter(Care.plant_id == plant_id)
            .order_by(Care.date.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def create_with_plant(
        self, db: Session, *, obj_in: CareCreate, plant_id: int
    ) -> Care:
        obj_in_data = obj_in.dict()

        # Handle multiple care types
        if obj_in_data.get('care_types'):
            # Use new multiple types field
            obj_in_data['care_types'] = obj_in_data['care_types']
            # Clear single care_type for consistency
            obj_in_data['care_type'] = None
        elif obj_in_data.get('care_type'):
            # Backward compatibility: convert single type to array
            obj_in_data['care_types'] = [obj_in_data['care_type']]

        db_obj = Care(**obj_in_data, plant_id=plant_id)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj


class CRUDReminder(CRUDBase[Reminder, ReminderCreate, ReminderCreate]):
    def get_by_plant(
        self, db: Session, *, plant_id: int, skip: int = 0, limit: int = 100, include_completed: bool = False
    ) -> List[Reminder]:
        query = (
            db.query(Reminder)
            .filter(Reminder.plant_id == plant_id)
        )

        if not include_completed:
            query = query.filter(Reminder.is_completed == False)

        return (
            query.order_by(Reminder.scheduled_date.asc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_upcoming_reminders(
        self, db: Session, *, owner_id: int, days_ahead: int = 7
    ) -> List[Reminder]:
        """Get reminders for the next X days for a specific user"""
        from datetime import date, timedelta

        end_date = date.today() + timedelta(days=days_ahead)

        return (
            db.query(Reminder)
            .join(Plant)
            .filter(Plant.owner_id == owner_id)
            .filter(Reminder.is_completed == False)
            .filter(Reminder.scheduled_date <= end_date)
            .order_by(Reminder.scheduled_date.asc())
            .all()
        )

    def get_overdue_reminders(
        self, db: Session, *, owner_id: int
    ) -> List[Reminder]:
        """Get overdue reminders for a specific user"""
        from datetime import date

        return (
            db.query(Reminder)
            .join(Plant)
            .filter(Plant.owner_id == owner_id)
            .filter(Reminder.is_completed == False)
            .filter(Reminder.scheduled_date < date.today())
            .order_by(Reminder.scheduled_date.asc())
            .all()
        )

    def create_with_plant(
        self, db: Session, *, obj_in: ReminderCreate, plant_id: int
    ) -> Reminder:
        obj_in_data = obj_in.dict()
        db_obj = Reminder(**obj_in_data, plant_id=plant_id)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def mark_as_completed(
        self, db: Session, *, reminder_id: int
    ) -> Reminder:
        from datetime import datetime

        reminder = db.query(Reminder).filter(Reminder.id == reminder_id).first()
        if reminder:
            reminder.is_completed = True
            reminder.completed_at = datetime.utcnow()
            db.commit()
            db.refresh(reminder)
        return reminder


plant = CRUDPlant(Plant)
plant_image = CRUDPlantImage(PlantImage)
care = CRUDCare(Care)
reminder = CRUDReminder(Reminder)
