from typing import Optional
from sqlalchemy.orm import Session
from uuid import UUID
from datetime import datetime

from app.crud.base import CRUDBase
from app.models.auth_token import AuthToken, TokenType
from app.schemas.auth_token import AuthTokenCreate, AuthTokenUpdate


class CRUDAuthToken(CRUDBase[AuthToken, AuthTokenCreate, AuthTokenUpdate]):
    def get_by_token(self, db: Session, *, token: str) -> Optional[AuthToken]:
        """Busca token por string"""
        return db.query(AuthToken).filter(AuthToken.token == token).first()

    def get_valid_token(self, db: Session, *, token: str, token_type: TokenType) -> Optional[AuthToken]:
        """Busca token válido por string e tipo"""
        from datetime import timezone

        # Usar datetime com timezone UTC para comparação
        now_utc = datetime.now(timezone.utc)

        auth_token = db.query(AuthToken).filter(
            AuthToken.token == token,
            AuthToken.token_type == token_type,
            AuthToken.is_active == True,
            AuthToken.used_at.is_(None),
            AuthToken.expires_at > now_utc
        ).first()

        return auth_token

    def invalidate_user_tokens(self, db: Session, *, user_id: UUID, token_type: TokenType) -> int:
        """Invalida todos os tokens de um usuário de um tipo específico"""
        count = db.query(AuthToken).filter(
            AuthToken.user_id == user_id,
            AuthToken.token_type == token_type,
            AuthToken.is_active == True
        ).update({
            "is_active": False,
            "used_at": datetime.utcnow()
        })
        db.commit()
        return count

    def create_activation_token(self, db: Session, *, user_id: UUID) -> AuthToken:
        """Cria token de ativação para usuário"""
        # Invalida tokens de ativação existentes
        self.invalidate_user_tokens(db, user_id=user_id, token_type=TokenType.ACTIVATION)
        
        # Cria novo token
        token = AuthToken.create_activation_token(user_id)
        db.add(token)
        db.commit()
        db.refresh(token)
        return token

    def create_password_reset_token(self, db: Session, *, user_id: UUID) -> AuthToken:
        """Cria token de reset de senha para usuário"""
        # Invalida tokens de reset existentes
        self.invalidate_user_tokens(db, user_id=user_id, token_type=TokenType.PASSWORD_RESET)

        # Cria novo token
        token = AuthToken.create_password_reset_token(user_id)
        db.add(token)
        db.commit()
        db.refresh(token)
        return token

    def create_password_change_token(self, db: Session, *, user_id: UUID) -> AuthToken:
        """Cria token de alteração de senha para usuário"""
        # Invalida tokens de alteração existentes
        self.invalidate_user_tokens(db, user_id=user_id, token_type=TokenType.PASSWORD_CHANGE)

        # Cria novo token
        token = AuthToken.create_password_change_token(user_id)
        db.add(token)
        db.commit()
        db.refresh(token)
        return token

    def use_token(self, db: Session, *, token: AuthToken) -> AuthToken:
        """Marca token como usado"""
        token.mark_as_used()
        db.commit()
        db.refresh(token)
        return token

    def cleanup_expired_tokens(self, db: Session) -> int:
        """Remove tokens expirados"""
        count = db.query(AuthToken).filter(
            AuthToken.expires_at < datetime.utcnow()
        ).delete()
        db.commit()
        return count


auth_token = CRUDAuthToken(AuthToken)
