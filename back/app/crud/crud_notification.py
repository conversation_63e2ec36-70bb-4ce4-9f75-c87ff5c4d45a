from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc
from datetime import datetime, timedelta

from app.crud.base import CRUDBase
from app.models.notification import Notification, NotificationType
from app.schemas.notification import NotificationCreate, NotificationUpdate


class CRUDNotification(CRUDBase[Notification, NotificationCreate, NotificationUpdate]):
    def get_user_notifications(
        self, 
        db: Session, 
        user_id: str, 
        skip: int = 0, 
        limit: int = 20,
        unread_only: bool = False
    ) -> List[Notification]:
        """Get notifications for a user"""
        query = db.query(self.model).filter(self.model.user_id == user_id)
        
        if unread_only:
            query = query.filter(self.model.is_read == False)
        
        return query.order_by(desc(self.model.created_at)).offset(skip).limit(limit).all()

    def get_unread_count(self, db: Session, user_id: str) -> int:
        """Get count of unread notifications for a user"""
        return db.query(self.model).filter(
            and_(
                self.model.user_id == user_id,
                self.model.is_read == False
            )
        ).count()

    def get_unseen_count(self, db: Session, user_id: str) -> int:
        """Get count of unseen notifications for a user (for badge)"""
        return db.query(self.model).filter(
            and_(
                self.model.user_id == user_id,
                self.model.seen_at.is_(None)
            )
        ).count()

    def mark_as_read(self, db: Session, notification_id: str, user_id: str) -> Optional[Notification]:
        """Mark a notification as read"""
        notification = db.query(self.model).filter(
            and_(
                self.model.id == notification_id,
                self.model.user_id == user_id
            )
        ).first()
        
        if notification and not notification.is_read:
            notification.is_read = True
            notification.read_at = datetime.utcnow()
            db.commit()
            db.refresh(notification)
        
        return notification

    def mark_all_as_read(self, db: Session, user_id: str) -> int:
        """Mark all notifications as read for a user"""
        count = db.query(self.model).filter(
            and_(
                self.model.user_id == user_id,
                self.model.is_read == False
            )
        ).update({
            "is_read": True,
            "read_at": datetime.utcnow()
        })
        db.commit()
        return count

    def mark_all_as_seen(self, db: Session, user_id: str) -> int:
        """Mark all notifications as seen for a user (clears badge but keeps notifications)"""
        count = db.query(self.model).filter(
            and_(
                self.model.user_id == user_id,
                self.model.seen_at.is_(None)
            )
        ).update({
            "seen_at": datetime.utcnow()
        })
        db.commit()
        return count

    def cleanup_old_notifications(self, db: Session, days_old: int = 30) -> int:
        """Delete notifications older than specified days"""
        cutoff_date = datetime.utcnow() - timedelta(days=days_old)

        count = db.query(self.model).filter(
            self.model.created_at < cutoff_date
        ).delete()

        db.commit()
        return count

    def cleanup_user_old_notifications(self, db: Session, user_id: str, days_old: int = 30) -> int:
        """Delete user's notifications older than specified days"""
        cutoff_date = datetime.utcnow() - timedelta(days=days_old)

        count = db.query(self.model).filter(
            and_(
                self.model.user_id == user_id,
                self.model.created_at < cutoff_date
            )
        ).delete()

        db.commit()
        return count

    def create_follower_notification(
        self, 
        db: Session, 
        followed_user_id: str, 
        follower_id: str,
        follower_name: str
    ) -> Notification:
        """Create a new follower notification"""
        notification_data = NotificationCreate(
            user_id=followed_user_id,
            type=NotificationType.new_follower,
            title="Novo seguidor",
            message=f"{follower_name} começou a seguir você",
            follower_id=follower_id,
            data={
                "follower_id": follower_id,
                "follower_name": follower_name
            }
        )
        return self.create(db=db, obj_in=notification_data)

    def create_subscription_notification(
        self, 
        db: Session, 
        user_id: str, 
        notification_type: NotificationType,
        subscription_id: str,
        title: str,
        message: str,
        data: dict = None
    ) -> Notification:
        """Create a subscription-related notification"""
        notification_data = NotificationCreate(
            user_id=user_id,
            type=notification_type,
            title=title,
            message=message,
            subscription_id=subscription_id,
            data=data or {}
        )
        return self.create(db=db, obj_in=notification_data)

    def create_reminder_notification(
        self,
        db: Session,
        user_id: str,
        reminder_id: str,
        plant_name: str,
        care_type: str
    ) -> Notification:
        """Create a reminder notification"""
        notification_data = NotificationCreate(
            user_id=user_id,
            type=NotificationType.reminder,
            title="Lembrete de cuidado",
            message=f"Hora de cuidar da sua planta {plant_name}: {care_type}",
            reminder_id=reminder_id,
            data={
                "plant_name": plant_name,
                "care_type": care_type,
                "reminder_id": reminder_id
            }
        )
        return self.create(db=db, obj_in=notification_data)

    def create_plant_like_notification(
        self,
        db: Session,
        plant_owner_id: str,
        liker_id: str,
        liker_name: str,
        plant_id: str,
        plant_name: str,
        like_id: str
    ) -> Notification:
        """Create a plant like notification"""
        notification_data = NotificationCreate(
            user_id=plant_owner_id,
            type=NotificationType.plant_like,
            title="Nova curtida!",
            message=f"{liker_name} curtiu sua planta '{plant_name}'",
            follower_id=liker_id,
            plant_id=plant_id,
            like_id=like_id,
            data={
                "liker_id": liker_id,
                "liker_name": liker_name,
                "plant_id": plant_id,
                "plant_name": plant_name,
                "like_id": like_id
            }
        )
        return self.create(db=db, obj_in=notification_data)

    def create_plant_comment_notification(
        self,
        db: Session,
        plant_owner_id: str,
        commenter_id: str,
        commenter_name: str,
        plant_id: str,
        plant_name: str,
        comment_id: str,
        comment_preview: str
    ) -> Notification:
        """Create a plant comment notification"""
        # Truncate comment preview if too long
        if len(comment_preview) > 100:
            comment_preview = comment_preview[:97] + "..."

        notification_data = NotificationCreate(
            user_id=plant_owner_id,
            type=NotificationType.plant_comment,
            title="Novo comentário!",
            message=f"{commenter_name} comentou na sua planta '{plant_name}': {comment_preview}",
            follower_id=commenter_id,
            plant_id=plant_id,
            comment_id=comment_id,
            data={
                "commenter_id": commenter_id,
                "commenter_name": commenter_name,
                "plant_id": plant_id,
                "plant_name": plant_name,
                "comment_id": comment_id,
                "comment_preview": comment_preview
            }
        )
        return self.create(db=db, obj_in=notification_data)


notification = CRUDNotification(Notification)
