from typing import List, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc
from uuid import UUID

from app.crud.base import CRUDBase
from app.models.social import UserFollow, UserBlock
from app.models.user import User
from app.models.plant import Plant
from app.schemas.social import UserFollowCreate


class CRUDUserFollow(CRUDBase[UserFollow, UserFollowCreate, None]):
    
    def follow_user(self, db: Session, *, follower_id: UUID, followed_id: UUID) -> UserFollow:
        """Create a follow relationship"""
        # Check if already following
        existing = db.query(UserFollow).filter(
            and_(
                UserFollow.follower_id == follower_id,
                UserFollow.followed_id == followed_id
            )
        ).first()

        if existing:
            return existing

        # Create follow relationship (triggers will update counters automatically)
        follow = UserFollow(follower_id=follower_id, followed_id=followed_id)
        db.add(follow)
        db.commit()
        db.refresh(follow)
        return follow
    
    def unfollow_user(self, db: Session, *, follower_id: UUID, followed_id: UUID) -> bool:
        """Remove a follow relationship"""
        follow = db.query(UserFollow).filter(
            and_(
                UserFollow.follower_id == follower_id,
                UserFollow.followed_id == followed_id
            )
        ).first()

        if follow:
            # Delete follow relationship (triggers will update counters automatically)
            db.delete(follow)
            db.commit()
            return True
        return False
    
    def is_following(self, db: Session, *, follower_id: UUID, followed_id: UUID) -> bool:
        """Check if user is following another user"""
        return db.query(UserFollow).filter(
            and_(
                UserFollow.follower_id == follower_id,
                UserFollow.followed_id == followed_id
            )
        ).first() is not None
    
    def get_followers(self, db: Session, *, user_id: UUID, skip: int = 0, limit: int = 20) -> Tuple[List[User], int]:
        """Get list of followers for a user"""
        query = db.query(User).join(
            UserFollow, User.id == UserFollow.follower_id
        ).filter(UserFollow.followed_id == user_id).order_by(desc(UserFollow.created_at))
        
        total = query.count()
        followers = query.offset(skip).limit(limit).all()
        
        return followers, total
    
    def get_following(self, db: Session, *, user_id: UUID, skip: int = 0, limit: int = 20) -> Tuple[List[User], int]:
        """Get list of users that a user is following"""
        query = db.query(User).join(
            UserFollow, User.id == UserFollow.followed_id
        ).filter(UserFollow.follower_id == user_id).order_by(desc(UserFollow.created_at))
        
        total = query.count()
        following = query.offset(skip).limit(limit).all()
        
        return following, total
    



class CRUDSocial:
    """CRUD operations for social features"""

    def fix_all_counters(self, db: Session):
        """Fix all follow counters by recalculating from actual data"""
        users = db.query(User).all()

        for user in users:
            # Recalculate following count
            following_count = db.query(UserFollow).filter(UserFollow.follower_id == user.id).count()

            # Recalculate followers count
            followers_count = db.query(UserFollow).filter(UserFollow.followed_id == user.id).count()

            # Update user
            user.following_count = following_count
            user.followers_count = followers_count

            print(f"Fixed user {user.email}: following={following_count}, followers={followers_count}")

        db.commit()
        print("All counters fixed!")
    
    def search_users(self, db: Session, *, query: str, current_user_id: UUID, skip: int = 0, limit: int = 20) -> Tuple[List[User], int]:
        """Search for users by name, username or email"""
        search_filter = or_(
            User.first_name.ilike(f"%{query}%"),
            User.last_name.ilike(f"%{query}%"),
            User.username.ilike(f"%{query}%"),
            User.email.ilike(f"%{query}%")
        )
        
        # Only search users that allow search and are not the current user
        base_query = db.query(User).filter(
            and_(
                search_filter,
                User.allow_search == True,
                User.is_active == True,
                User.id != current_user_id
            )
        ).order_by(User.first_name, User.last_name)
        
        total = base_query.count()
        users = base_query.offset(skip).limit(limit).all()
        
        return users, total
    
    def get_public_profile(self, db: Session, *, user_id: UUID, current_user_id: Optional[UUID] = None) -> Optional[User]:
        """Get public profile of a user"""
        user = db.query(User).filter(
            and_(
                User.id == user_id,
                User.is_public == True,
                User.is_active == True
            )
        ).first()
        
        return user
    
    def get_public_profile_by_username(self, db: Session, *, username: str, current_user_id: Optional[UUID] = None) -> Optional[User]:
        """Get public profile of a user by username"""
        user = db.query(User).filter(
            and_(
                User.username == username.lower(),
                User.is_public == True,
                User.is_active == True
            )
        ).first()
        
        return user
    
    def get_public_plants(self, db: Session, *, user_id: UUID, current_user_id: Optional[UUID] = None, skip: int = 0, limit: int = 20) -> Tuple[List[Plant], int]:
        """Get public plants of a user"""
        # Check if current user is following the plant owner
        is_following = False
        if current_user_id:
            is_following = db.query(UserFollow).filter(
                and_(
                    UserFollow.follower_id == current_user_id,
                    UserFollow.followed_id == user_id
                )
            ).first() is not None
        
        # Get user to check their privacy settings
        user = db.query(User).filter(User.id == user_id).first()
        if not user or not user.is_public:
            return [], 0
        
        # Base query for public plants
        query = db.query(Plant).filter(
            and_(
                Plant.owner_id == user_id,
                Plant.is_public == True,
                Plant.archived == False
            )
        ).order_by(desc(Plant.created_at))
        
        total = query.count()
        plants = query.offset(skip).limit(limit).all()
        
        return plants, total
    
    def can_view_plant_care(self, db: Session, *, plant_id: UUID, viewer_id: Optional[UUID] = None) -> bool:
        """Check if user can view care records of a plant"""
        plant = db.query(Plant).filter(Plant.id == plant_id).first()
        if not plant:
            return False
        
        # Owner can always see their own plant care
        if viewer_id and plant.owner_id == viewer_id:
            return True
        
        # Check if plant allows care sharing
        if not plant.allow_care_sharing:
            return False
        
        # Check if plant owner allows care sharing globally
        owner = db.query(User).filter(User.id == plant.owner_id).first()
        if not owner or not owner.allow_care_sharing:
            return False
        
        # Check if viewer is following the plant owner
        if viewer_id:
            is_following = db.query(UserFollow).filter(
                and_(
                    UserFollow.follower_id == viewer_id,
                    UserFollow.followed_id == plant.owner_id
                )
            ).first() is not None
            return is_following
        
        return False
    
    def update_username(self, db: Session, *, user_id: UUID, username: str) -> bool:
        """Update user's username"""
        # Check if username is already taken
        existing = db.query(User).filter(
            and_(
                User.username == username.lower(),
                User.id != user_id
            )
        ).first()
        
        if existing:
            return False
        
        # Update username
        db.query(User).filter(User.id == user_id).update({
            User.username: username.lower()
        })
        db.commit()
        return True


# Create instances
user_follow = CRUDUserFollow(UserFollow)
social = CRUDSocial()
