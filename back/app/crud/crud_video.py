from typing import List, Optional
from uuid import UUID
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.plant import PlantVideo, CareVideo
from app.schemas.video import PlantVideoCreate, PlantVideoUpdate, CareVideoCreate, CareVideoUpdate


class CRUDPlantVideo(CRUDBase[PlantVideo, PlantVideoCreate, PlantVideoUpdate]):
    def create_with_plant(
        self,
        db: Session,
        *,
        obj_in: PlantVideoCreate,
        plant_id: UUID,
        video_path: str,
        thumbnail_url: Optional[str] = None,
        original_filename: Optional[str] = None,
        file_size_bytes: Optional[int] = None
    ) -> PlantVideo:
        obj_in_data = obj_in.dict()
        db_obj = self.model(
            **obj_in_data,
            plant_id=plant_id,
            video=video_path,
            thumbnail_url=thumbnail_url,
            original_filename=original_filename,
            file_size_bytes=file_size_bytes
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_by_plant(self, db: Session, *, plant_id: UUID) -> List[PlantVideo]:
        return db.query(self.model).filter(
            PlantVideo.plant_id == plant_id
        ).order_by(PlantVideo.created_at.desc()).all()


class CRUDCareVideo(CRUDBase[CareVideo, CareVideoCreate, CareVideoUpdate]):
    def create_with_care(
        self,
        db: Session,
        *,
        obj_in: CareVideoCreate,
        care_id: UUID,
        video_path: str,
        thumbnail_url: Optional[str] = None,
        original_filename: Optional[str] = None,
        file_size_bytes: Optional[int] = None
    ) -> CareVideo:
        obj_in_data = obj_in.dict()
        db_obj = self.model(
            **obj_in_data,
            care_id=care_id,
            video_path=video_path,
            thumbnail_url=thumbnail_url,
            original_filename=original_filename,
            file_size_bytes=file_size_bytes
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_by_care(self, db: Session, *, care_id: UUID) -> List[CareVideo]:
        return db.query(self.model).filter(
            CareVideo.care_id == care_id
        ).order_by(CareVideo.created_at.desc()).all()


plant_video = CRUDPlantVideo(PlantVideo)
care_video = CRUDCareVideo(CareVideo)
