from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_
from datetime import datetime, timedelta, timezone
from uuid import UUID

from app.crud.base import CRUDBase
from app.models.coupon import Coupon, CouponUse
from app.models.user import User
from app.models.subscription import UserSubscription, SubscriptionPlan, SubscriptionStatus
from app.models.plant import Plant, PlantImage
from app.schemas.admin import CouponCreate, CouponUpdate, AdminUserUpdate
from app.schemas.admin import AdminUserList, AdminDashboardStats


class CRUDAdmin:
    
    def get_users_list(self, db: Session, skip: int = 0, limit: int = 100) -> List[AdminUserList]:
        """Get paginated list of users with subscription info"""
        
        # Query users with their subscription data
        users_query = db.query(User).offset(skip).limit(limit).all()
        
        result = []
        for user in users_query:
            # Get active subscription
            active_subscription = db.query(UserSubscription).filter(
                UserSubscription.user_id == user.id,
                UserSubscription.status == SubscriptionStatus.active
            ).first()
            
            # Get usage stats
            plants_count = db.query(Plant).filter(Plant.owner_id == user.id).count()
            photos_count = db.query(PlantImage).join(Plant).filter(Plant.owner_id == user.id).count()
            
            user_data = AdminUserList(
                id=user.id,
                email=user.email,
                full_name=user.full_name,
                is_active=user.is_active,
                email_verified=user.email_verified,
                created_at=user.created_at,
                subscription_status=user.subscription_status,
                current_plan_name=active_subscription.plan.name if active_subscription else "free",
                current_plan_display_name=active_subscription.plan.display_name if active_subscription else "Gratuito",
                subscription_expires_at=active_subscription.expires_at if active_subscription else None,
                plants_count=plants_count,
                total_photos_count=photos_count
            )
            result.append(user_data)
        
        return result
    
    def update_user(self, db: Session, user_id: UUID, user_update: AdminUserUpdate) -> User:
        """Update user data (admin only)"""
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            return None
        
        update_data = user_update.dict(exclude_unset=True)
        
        for field, value in update_data.items():
            setattr(user, field, value)
        
        db.commit()
        db.refresh(user)
        return user
    
    def change_user_plan(self, db: Session, user_id: UUID, new_plan_id: UUID, expires_at: Optional[datetime] = None) -> bool:
        """Change user's subscription plan"""
        user = db.query(User).filter(User.id == user_id).first()
        plan = db.query(SubscriptionPlan).filter(SubscriptionPlan.id == new_plan_id).first()
        
        if not user or not plan:
            return False
        
        # Update user's current plan
        user.current_plan_id = new_plan_id
        user.subscription_status = "active" if not plan.is_free else "free"
        user.subscription_expires_at = expires_at
        
        # Deactivate existing subscriptions
        db.query(UserSubscription).filter(
            UserSubscription.user_id == user_id,
            UserSubscription.status == SubscriptionStatus.active
        ).update({"status": SubscriptionStatus.cancelled})
        
        # Create new subscription if not free plan
        if not plan.is_free:
            new_subscription = UserSubscription(
                user_id=user_id,
                plan_id=new_plan_id,
                status=SubscriptionStatus.active,
                started_at=datetime.now(timezone.utc),
                expires_at=expires_at,
                auto_renew=False
            )
            db.add(new_subscription)
        
        db.commit()
        return True
    
    def deactivate_user_plan(self, db: Session, user_id: UUID) -> bool:
        """Deactivate user's subscription plan"""
        # Get free plan
        free_plan = db.query(SubscriptionPlan).filter(SubscriptionPlan.name == "free").first()
        if not free_plan:
            return False
        
        return self.change_user_plan(db, user_id, free_plan.id)
    
    def get_dashboard_stats(self, db: Session) -> AdminDashboardStats:
        """Get admin dashboard statistics"""
        
        # Basic counts
        total_users = db.query(User).count()
        active_users = db.query(User).filter(User.is_active == True).count()
        verified_users = db.query(User).filter(User.email_verified == True).count()
        
        total_subscriptions = db.query(UserSubscription).count()
        active_subscriptions = db.query(UserSubscription).filter(UserSubscription.status == SubscriptionStatus.active).count()
        
        total_plants = db.query(Plant).count()
        total_photos = db.query(PlantImage).count()
        
        total_coupons = db.query(Coupon).count()
        active_coupons = db.query(Coupon).filter(Coupon.is_active == True).count()
        
        # Coupon uses today
        today = datetime.now(timezone.utc).date()
        coupon_uses_today = db.query(CouponUse).filter(
            func.date(CouponUse.used_at) == today
        ).count()
        
        # Plan distribution - include free users
        # First get active subscriptions by plan
        plan_distribution = db.query(
            SubscriptionPlan.display_name,
            func.count(UserSubscription.id).label('count')
        ).join(UserSubscription).filter(
            UserSubscription.status == SubscriptionStatus.active
        ).group_by(SubscriptionPlan.display_name).all()

        plan_dist_dict = {plan: count for plan, count in plan_distribution}

        # Count users without active subscriptions (free users)
        users_with_active_subs = db.query(UserSubscription.user_id).filter(
            UserSubscription.status == SubscriptionStatus.active
        ).subquery()

        free_users_count = db.query(User).filter(
            ~User.id.in_(users_with_active_subs)
        ).count()

        # Add free users to distribution
        if free_users_count > 0:
            plan_dist_dict["Gratuito"] = free_users_count

        plan_dist_list = [{"plan": plan, "count": count} for plan, count in plan_dist_dict.items()]
        
        # Recent signups (last 10)
        recent_users = db.query(User).order_by(desc(User.created_at)).limit(10).all()
        recent_signups = []
        for user in recent_users:
            active_subscription = db.query(UserSubscription).filter(
                UserSubscription.user_id == user.id,
                UserSubscription.status == SubscriptionStatus.active
            ).first()
            
            user_data = AdminUserList(
                id=user.id,
                email=user.email,
                full_name=user.full_name,
                is_active=user.is_active,
                email_verified=user.email_verified,
                created_at=user.created_at,
                subscription_status=user.subscription_status,
                current_plan_name=active_subscription.plan.name if active_subscription else "free",
                current_plan_display_name=active_subscription.plan.display_name if active_subscription else "Gratuito",
                subscription_expires_at=active_subscription.expires_at if active_subscription else None,
                plants_count=0,
                total_photos_count=0
            )
            recent_signups.append(user_data)
        
        # Recent subscriptions
        recent_subs = db.query(UserSubscription).order_by(desc(UserSubscription.created_at)).limit(10).all()
        recent_subscriptions = []
        for sub in recent_subs:
            recent_subscriptions.append({
                "id": str(sub.id),
                "user_email": sub.user.email,
                "plan_name": sub.plan.display_name,
                "status": str(sub.status.value) if sub.status else "unknown",
                "created_at": sub.created_at.isoformat()
            })
        
        # Recent coupon uses
        recent_coupon_uses_query = db.query(CouponUse).order_by(desc(CouponUse.used_at)).limit(10).all()
        recent_coupon_uses = []
        for use in recent_coupon_uses_query:
            recent_coupon_uses.append({
                "id": use.id,
                "coupon_id": use.coupon_id,
                "user_id": use.user_id,
                "subscription_id": use.subscription_id,
                "discount_applied": use.discount_applied,
                "original_price": use.original_price,
                "final_price": use.final_price,
                "used_at": use.used_at,
                "user_email": use.user.email,
                "coupon_code": use.coupon.code
            })
        
        return AdminDashboardStats(
            total_users=total_users,
            active_users=active_users,
            verified_users=verified_users,
            total_subscriptions=total_subscriptions,
            active_subscriptions=active_subscriptions,
            total_plants=total_plants,
            total_photos=total_photos,
            total_coupons=total_coupons,
            active_coupons=active_coupons,
            coupon_uses_today=coupon_uses_today,
            revenue_this_month=0.0,  # TODO: Calculate from payments
            plan_distribution=plan_dist_list,
            recent_signups=recent_signups,
            recent_subscriptions=recent_subscriptions,
            recent_coupon_uses=recent_coupon_uses
        )

    def get_payments_list(self, db: Session, skip: int = 0, limit: int = 100):
        """Get paginated list of payments with user and subscription info"""
        from app.schemas.admin import AdminPaymentList
        from app.models.subscription import Payment, UserSubscription, SubscriptionPlan

        # Query payments with joins
        payments_query = db.query(
            Payment,
            User.email.label('user_email'),
            func.concat(User.first_name, ' ', User.last_name).label('user_name'),
            SubscriptionPlan.name.label('plan_name'),
            SubscriptionPlan.display_name.label('plan_display_name')
        ).join(
            User, Payment.user_id == User.id
        ).outerjoin(
            UserSubscription, Payment.subscription_id == UserSubscription.id
        ).outerjoin(
            SubscriptionPlan, UserSubscription.plan_id == SubscriptionPlan.id
        ).order_by(
            Payment.created_at.desc()
        ).offset(skip).limit(limit)

        payments_list = []
        for payment, user_email, user_name, plan_name, plan_display_name in payments_query:
            payment_data = AdminPaymentList(
                id=payment.id,
                user_id=payment.user_id,
                user_email=user_email,
                user_name=user_name or "N/A",
                subscription_id=payment.subscription_id,
                plan_name=plan_name,
                plan_display_name=plan_display_name,
                amount_cents=payment.amount_cents,
                amount_display=payment.amount_display,
                currency=payment.currency,
                status=payment.status.value if hasattr(payment.status, 'value') else payment.status,
                payment_method=payment.payment_method.value if payment.payment_method and hasattr(payment.payment_method, 'value') else payment.payment_method,
                external_payment_id=payment.external_payment_id,
                processed_at=payment.processed_at,
                created_at=payment.created_at
            )
            payments_list.append(payment_data)

        return payments_list

    def get_user_history(self, db: Session, user_id: UUID):
        """Get complete history of plans and payments for a specific user"""
        from app.models.subscription import Payment, UserSubscription, SubscriptionPlan

        # Get user info
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            return None

        # Get all user subscriptions (including cancelled/expired)
        subscriptions = db.query(UserSubscription).filter(
            UserSubscription.user_id == user_id
        ).order_by(UserSubscription.created_at.desc()).all()

        # Get all user payments
        payments = db.query(Payment).filter(
            Payment.user_id == user_id
        ).order_by(Payment.created_at.desc()).all()

        # Format subscriptions
        subscription_history = []
        for sub in subscriptions:
            subscription_history.append({
                "id": str(sub.id),
                "plan_name": sub.plan.name,
                "plan_display_name": sub.plan.display_name,
                "status": sub.status.value if hasattr(sub.status, 'value') else sub.status,
                "started_at": sub.started_at.isoformat() if sub.started_at else None,
                "expires_at": sub.expires_at.isoformat() if sub.expires_at else None,
                "cancelled_at": sub.cancelled_at.isoformat() if sub.cancelled_at else None,
                "auto_renew": sub.auto_renew,
                "billing_cycle": sub.billing_cycle.value if hasattr(sub.billing_cycle, 'value') else sub.billing_cycle,
                "created_at": sub.created_at.isoformat()
            })

        # Format payments
        payment_history = []
        for payment in payments:
            # Get subscription info if exists
            subscription_info = None
            if payment.subscription_id:
                sub = db.query(UserSubscription).filter(UserSubscription.id == payment.subscription_id).first()
                if sub:
                    subscription_info = {
                        "plan_name": sub.plan.name,
                        "plan_display_name": sub.plan.display_name
                    }

            payment_history.append({
                "id": str(payment.id),
                "amount_cents": payment.amount_cents,
                "amount_display": payment.amount_display,
                "currency": payment.currency,
                "status": payment.status.value if hasattr(payment.status, 'value') else payment.status,
                "payment_method": payment.payment_method.value if payment.payment_method and hasattr(payment.payment_method, 'value') else payment.payment_method,
                "external_payment_id": payment.external_payment_id,
                "processed_at": payment.processed_at.isoformat() if payment.processed_at else None,
                "created_at": payment.created_at.isoformat(),
                "subscription_info": subscription_info
            })

        return {
            "user": {
                "id": str(user.id),
                "email": user.email,
                "full_name": user.full_name,
                "current_plan": user.current_plan.display_name if user.current_plan else "Gratuito",
                "subscription_status": user.subscription_status,
                "subscription_expires_at": user.subscription_expires_at.isoformat() if user.subscription_expires_at else None
            },
            "subscriptions": subscription_history,
            "payments": payment_history
        }


class CRUDCoupon(CRUDBase[Coupon, CouponCreate, CouponUpdate]):
    
    def create_with_creator(self, db: Session, *, obj_in: CouponCreate, creator_id: UUID) -> Coupon:
        """Create coupon with creator"""
        obj_in_data = obj_in.dict()
        obj_in_data["created_by"] = creator_id
        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def get_by_code(self, db: Session, *, code: str) -> Optional[Coupon]:
        """Get coupon by code"""
        return db.query(Coupon).filter(Coupon.code == code).first()
    
    def get_active_coupons(self, db: Session, skip: int = 0, limit: int = 100) -> List[Coupon]:
        """Get active coupons"""
        return db.query(Coupon).filter(Coupon.is_active == True).offset(skip).limit(limit).all()


# Create instances
admin = CRUDAdmin()
coupon = CRUDCoupon(Coupon)
