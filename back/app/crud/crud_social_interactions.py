from typing import List, Optional, Tuple, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, text
from uuid import UUID

from app.crud.base import CRUDBase
from app.models.social import PlantLike, PlantComment, FeedActivity, FeedActivityType, UserFollow
from app.models.plant import Plant, Care, PlantImage
from app.models.user import User
from app.schemas.social_interactions import (
    PlantLikeCreate, PlantCommentCreate, PlantCommentUpdate, 
    FeedActivityCreate
)


class CRUDPlantLike(CRUDBase[PlantLike, PlantLikeCreate, None]):
    
    def like_plant(self, db: Session, *, user_id: UUID, plant_id: UUID) -> PlantLike:
        """Like a plant (idempotent - won't create duplicate)"""
        # Check if already liked
        existing = db.query(PlantLike).filter(
            and_(
                PlantLike.user_id == user_id,
                PlantLike.plant_id == plant_id
            )
        ).first()

        if existing:
            return existing

        # Create like
        like = PlantLike(user_id=user_id, plant_id=plant_id)
        db.add(like)
        db.commit()
        db.refresh(like)
        return like

    def unlike_plant(self, db: Session, *, user_id: UUID, plant_id: UUID) -> bool:
        """Unlike a plant - returns True if like was removed, False if not found"""
        existing = db.query(PlantLike).filter(
            and_(
                PlantLike.user_id == user_id,
                PlantLike.plant_id == plant_id
            )
        ).first()

        if existing:
            db.delete(existing)
            db.commit()
            return True

        return False
    
    def get_plant_likes_count(self, db: Session, *, plant_id: UUID) -> int:
        """Get total likes count for a plant"""
        return db.query(PlantLike).filter(PlantLike.plant_id == plant_id).count()
    
    def is_plant_liked_by_user(self, db: Session, *, plant_id: UUID, user_id: UUID) -> bool:
        """Check if a plant is liked by a specific user"""
        return db.query(PlantLike).filter(
            and_(
                PlantLike.plant_id == plant_id,
                PlantLike.user_id == user_id
            )
        ).first() is not None
    
    def get_plant_likes(self, db: Session, *, plant_id: UUID, skip: int = 0, limit: int = 20) -> Tuple[List[PlantLike], int]:
        """Get likes for a plant with pagination"""
        query = db.query(PlantLike).filter(PlantLike.plant_id == plant_id)
        total = query.count()
        likes = query.options(joinedload(PlantLike.user)).offset(skip).limit(limit).all()
        return likes, total


class CRUDPlantComment(CRUDBase[PlantComment, PlantCommentCreate, PlantCommentUpdate]):
    
    def create_comment(self, db: Session, *, user_id: UUID, plant_id: UUID, content: str) -> PlantComment:
        """Create a comment on a plant"""
        comment = PlantComment(user_id=user_id, plant_id=plant_id, content=content)
        db.add(comment)
        db.commit()
        db.refresh(comment)
        return comment
    
    def get_plant_comments_count(self, db: Session, *, plant_id: UUID) -> int:
        """Get total comments count for a plant"""
        return db.query(PlantComment).filter(PlantComment.plant_id == plant_id).count()
    
    def get_plant_comments(self, db: Session, *, plant_id: UUID, skip: int = 0, limit: int = 20) -> Tuple[List[Dict[str, Any]], int]:
        """Get comments for a plant with user info"""
        query = db.query(PlantComment).filter(PlantComment.plant_id == plant_id)
        total = query.count()
        
        # Get comments with user info
        comments_query = db.query(
            PlantComment,
            User.first_name,
            User.last_name,
            User.username,
            User.avatar
        ).join(User, PlantComment.user_id == User.id).filter(
            PlantComment.plant_id == plant_id
        ).order_by(desc(PlantComment.created_at)).offset(skip).limit(limit)
        
        comments_with_user = []
        for comment, first_name, last_name, username, avatar in comments_query.all():
            comment_dict = {
                "id": comment.id,
                "user_id": comment.user_id,
                "plant_id": comment.plant_id,
                "content": comment.content,
                "created_at": comment.created_at,
                "updated_at": comment.updated_at,
                "user_first_name": first_name,
                "user_last_name": last_name,
                "user_username": username,
                "user_avatar": avatar
            }
            comments_with_user.append(comment_dict)
        
        return comments_with_user, total
    
    def delete_comment(self, db: Session, *, comment_id: UUID, user_id: UUID) -> bool:
        """Delete a comment (only by owner or plant owner)"""
        comment = db.query(PlantComment).filter(PlantComment.id == comment_id).first()
        if not comment:
            return False
        
        # Check if user can delete (comment owner or plant owner)
        plant = db.query(Plant).filter(Plant.id == comment.plant_id).first()
        if comment.user_id != user_id and plant.owner_id != user_id:
            return False
        
        db.delete(comment)
        db.commit()
        return True


class CRUDFeedActivity(CRUDBase[FeedActivity, FeedActivityCreate, None]):
    
    def create_activity(self, db: Session, *, user_id: UUID, activity_type: FeedActivityType, 
                       plant_id: Optional[UUID] = None, care_id: Optional[UUID] = None) -> FeedActivity:
        """Create a feed activity"""
        activity = FeedActivity(
            user_id=user_id,
            activity_type=activity_type,
            plant_id=plant_id,
            care_id=care_id
        )
        db.add(activity)
        db.commit()
        db.refresh(activity)
        return activity
    
    def get_user_feed(self, db: Session, *, user_id: UUID, skip: int = 0, limit: int = 20) -> Tuple[List[Dict[str, Any]], int]:
        """Get feed activities from followed users"""
        # Get users that current user follows
        followed_users_subquery = db.query(UserFollow.followed_id).filter(
            UserFollow.follower_id == user_id
        ).subquery()
        
        # Base query for feed activities from followed users
        base_query = db.query(FeedActivity).filter(
            FeedActivity.user_id.in_(followed_users_subquery)
        )
        
        total = base_query.count()
        
        # Get activities with all related info including likes count and primary image
        activities_query = db.query(
            FeedActivity,
            User.first_name,
            User.last_name,
            User.username,
            User.avatar,
            Plant.name.label('plant_name'),
            PlantImage.image.label('plant_primary_image'),
            Care.description.label('care_description'),
            Care.care_type.label('care_type'),
            func.count(PlantLike.id).label('likes_count')
        ).join(User, FeedActivity.user_id == User.id)\
         .join(Plant, FeedActivity.plant_id == Plant.id)\
         .outerjoin(Care, FeedActivity.care_id == Care.id)\
         .outerjoin(PlantImage, and_(Plant.id == PlantImage.plant_id, PlantImage.is_primary == True))\
         .outerjoin(PlantLike, Plant.id == PlantLike.plant_id)\
         .filter(FeedActivity.user_id.in_(followed_users_subquery))\
         .group_by(FeedActivity.id, User.first_name, User.last_name, User.username, User.avatar,
                   Plant.name, PlantImage.image, Care.description, Care.care_type)\
         .order_by(desc(FeedActivity.created_at))\
         .offset(skip).limit(limit)
        
        activities_with_info = []
        for result in activities_query.all():
            activity, first_name, last_name, username, avatar, plant_name, plant_primary_image, care_description, care_type, likes_count = result

            # Check if current user liked this plant
            is_liked_by_user = False
            if activity.plant_id:
                is_liked_by_user = db.query(PlantLike).filter(
                    PlantLike.plant_id == activity.plant_id,
                    PlantLike.user_id == user_id
                ).first() is not None
            
            # Get care type display if available
            care_type_display = None
            if care_type:
                care_type_map = {
                    "poda": "Poda",
                    "adubacao": "Adubação", 
                    "transplante": "Transplante",
                    "aramacao": "Aramação",
                    "limpeza": "Limpeza",
                    "tratamento": "Tratamento",
                    "desfolha": "Desfolha",
                    "outro": "Outro"
                }
                care_type_display = care_type_map.get(str(care_type), str(care_type))
            
            activity_dict = {
                "id": activity.id,
                "user_id": activity.user_id,
                "activity_type": activity.activity_type,
                "plant_id": activity.plant_id,
                "care_id": activity.care_id,
                "created_at": activity.created_at,
                "user_first_name": first_name,
                "user_last_name": last_name,
                "user_username": username,
                "user_avatar": avatar,
                "plant_name": plant_name,
                "plant_primary_image": plant_primary_image,
                "care_description": care_description,
                "care_type_display": care_type_display,
                "likes_count": likes_count or 0,
                "is_liked_by_user": is_liked_by_user
            }
            activities_with_info.append(activity_dict)
        
        return activities_with_info, total
    
    def cleanup_old_activities(self, db: Session, days_old: int = 30) -> int:
        """Clean up old feed activities to keep database size manageable"""
        from datetime import datetime, timedelta
        
        cutoff_date = datetime.utcnow() - timedelta(days=days_old)
        deleted_count = db.query(FeedActivity).filter(
            FeedActivity.created_at < cutoff_date
        ).delete()
        db.commit()
        return deleted_count


# Create instances
plant_like = CRUDPlantLike(PlantLike)
plant_comment = CRUDPlantComment(PlantComment)
feed_activity = CRUDFeedActivity(FeedActivity)


def fix_all_counters(db: Session):
    """Fix follow counters for all users"""
    try:
        # Get all users
        users = db.query(User).all()

        for user in users:
            # Count actual followers
            followers_count = db.query(UserFollow).filter(
                UserFollow.followed_id == user.id
            ).count()

            # Count actual following
            following_count = db.query(UserFollow).filter(
                UserFollow.follower_id == user.id
            ).count()

            # Update user counters
            user.followers_count = followers_count
            user.following_count = following_count

            print(f"Fixed counters for user {user.id}: followers={followers_count}, following={following_count}")

        db.commit()
        print("All follow counters fixed successfully")

    except Exception as e:
        print(f"Error fixing all counters: {e}")
        db.rollback()
        raise
