from typing import List, Optional
from sqlalchemy.orm import Session
from uuid import UUID

from app.crud.base import CRUDBase
from app.models.plant import CareImage
from app.schemas.plant import CareImageCreate, CareImageUpdate


class CRUDCareImage(CRUDBase[CareImage, CareImageCreate, CareImageUpdate]):
    def get_by_care(
        self, db: Session, *, care_id: UUID, skip: int = 0, limit: int = 100
    ) -> List[CareImage]:
        return (
            db.query(self.model)
            .filter(CareImage.care_id == care_id)
            .order_by(CareImage.is_primary.desc(), CareImage.created_at.asc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def create_with_care(
        self, db: Session, *, obj_in: CareImageCreate, care_id: UUID
    ) -> CareImage:
        obj_in_data = obj_in.dict()
        db_obj = self.model(**obj_in_data, care_id=care_id)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_primary_by_care(self, db: Session, *, care_id: UUID) -> Optional[CareImage]:
        return (
            db.query(self.model)
            .filter(CareImage.care_id == care_id, CareImage.is_primary == True)
            .first()
        )

    def set_primary(self, db: Session, *, care_id: UUID, image_id: UUID) -> CareImage:
        # First, unset all primary flags for this care
        db.query(self.model).filter(CareImage.care_id == care_id).update(
            {"is_primary": False}
        )
        
        # Then set the specified image as primary
        db_obj = db.query(self.model).filter(CareImage.id == image_id).first()
        if db_obj:
            db_obj.is_primary = True
            db.commit()
            db.refresh(db_obj)
        
        return db_obj


care_image = CRUDCareImage(CareImage)
