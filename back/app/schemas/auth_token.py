from typing import Optional
from pydantic import BaseModel
from datetime import datetime
from uuid import UUID
from app.models.auth_token import TokenType


# Shared properties
class AuthTokenBase(BaseModel):
    token_type: TokenType
    expires_at: datetime


# Properties to receive on token creation
class AuthTokenCreate(AuthTokenBase):
    user_id: UUID
    token: str


# Properties to receive on token update
class AuthTokenUpdate(BaseModel):
    is_active: Optional[bool] = None
    used_at: Optional[datetime] = None


# Properties shared by models stored in DB
class AuthTokenInDBBase(AuthTokenBase):
    id: UUID
    user_id: UUID
    token: str
    used_at: Optional[datetime] = None
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# Properties to return to client
class AuthToken(AuthTokenInDBBase):
    pass


# Properties stored in DB
class AuthTokenInDB(AuthTokenInDBBase):
    pass
