from typing import Optional, List, Any
from pydantic import BaseModel, validator
from datetime import datetime
from uuid import UUID


# User Management Schemas
class AdminUserList(BaseModel):
    id: UUID
    email: str
    full_name: str
    is_active: bool
    email_verified: bool
    created_at: datetime
    
    # Subscription info
    subscription_status: Optional[str] = None
    current_plan_name: Optional[str] = None
    current_plan_display_name: Optional[str] = None
    subscription_expires_at: Optional[datetime] = None
    
    # Usage stats
    plants_count: int = 0
    total_photos_count: int = 0
    
    class Config:
        from_attributes = True


class AdminUserUpdate(BaseModel):
    is_active: Optional[bool] = None
    email_verified: Optional[bool] = None
    subscription_status: Optional[str] = None
    current_plan_id: Optional[UUID] = None
    subscription_expires_at: Optional[datetime] = None


# Coupon Schemas
class CouponBase(BaseModel):
    code: str
    name: str
    description: Optional[str] = None
    coupon_type: str  # 'percentage' or 'fixed'
    discount_value: float
    max_uses: Optional[int] = None
    max_uses_per_user: int = 1
    valid_from: Optional[datetime] = None
    valid_until: Optional[datetime] = None
    is_active: bool = True
    applicable_plans: Optional[str] = None  # JSON string

    @validator('coupon_type')
    def validate_coupon_type(cls, v):
        if v not in ['percentage', 'fixed']:
            raise ValueError('coupon_type must be "percentage" or "fixed"')
        return v

    @validator('discount_value')
    def validate_discount_value(cls, v, values):
        if v <= 0:
            raise ValueError('discount_value must be positive')
        
        coupon_type = values.get('coupon_type')
        if coupon_type == 'percentage' and v > 100:
            raise ValueError('percentage discount cannot exceed 100%')
        
        return v


class CouponCreate(CouponBase):
    pass


class CouponUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    discount_value: Optional[float] = None
    max_uses: Optional[int] = None
    max_uses_per_user: Optional[int] = None
    valid_from: Optional[datetime] = None
    valid_until: Optional[datetime] = None
    is_active: Optional[bool] = None
    applicable_plans: Optional[str] = None


class Coupon(CouponBase):
    id: UUID
    current_uses: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    created_by: UUID
    is_valid: bool
    usage_percentage: float
    
    class Config:
        from_attributes = True


class CouponUse(BaseModel):
    id: UUID
    coupon_id: UUID
    user_id: UUID
    subscription_id: Optional[UUID] = None
    discount_applied: float
    original_price: float
    final_price: float
    used_at: datetime
    
    # Related data
    user_email: Optional[str] = None
    coupon_code: Optional[str] = None
    
    class Config:
        from_attributes = True


# Dashboard Stats
class AdminDashboardStats(BaseModel):
    total_users: int
    active_users: int
    verified_users: int
    total_subscriptions: int
    active_subscriptions: int
    total_plants: int
    total_photos: int
    total_coupons: int
    active_coupons: int
    coupon_uses_today: int
    revenue_this_month: float
    
    # Plan distribution
    plan_distribution: List[dict]
    
    # Recent activity
    recent_signups: List[AdminUserList]
    recent_subscriptions: List[dict]
    recent_coupon_uses: List[CouponUse]


# Plan Change Schema
class PlanChangeRequest(BaseModel):
    user_id: UUID
    new_plan_id: UUID
    expires_at: Optional[datetime] = None
    reason: Optional[str] = None


# Payment/Invoice schemas
class AdminPaymentList(BaseModel):
    id: UUID
    user_id: UUID
    user_email: str
    user_name: str
    subscription_id: Optional[UUID] = None
    plan_name: Optional[str] = None
    plan_display_name: Optional[str] = None
    amount_cents: int
    amount_display: str
    currency: str
    status: str
    payment_method: Optional[str] = None
    external_payment_id: Optional[str] = None
    processed_at: Optional[datetime] = None
    created_at: datetime

    class Config:
        from_attributes = True
