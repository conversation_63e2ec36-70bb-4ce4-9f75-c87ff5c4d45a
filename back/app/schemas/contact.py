from typing import Optional
from pydantic import BaseModel, EmailStr, validator
from datetime import datetime


class ContactRequest(BaseModel):
    type: str  # bug, suggestion, support
    subject: str
    message: str
    email: Optional[EmailStr] = None
    
    @validator('type')
    def validate_type(cls, v):
        allowed_types = ['bug', 'suggestion', 'support']
        if v not in allowed_types:
            raise ValueError(f'Type must be one of: {", ".join(allowed_types)}')
        return v
    
    @validator('subject')
    def validate_subject(cls, v):
        if not v or len(v.strip()) < 3:
            raise ValueError('Subject must have at least 3 characters')
        if len(v) > 200:
            raise ValueError('Subject must have at most 200 characters')
        return v.strip()
    
    @validator('message')
    def validate_message(cls, v):
        if not v or len(v.strip()) < 10:
            raise ValueError('Message must have at least 10 characters')
        if len(v) > 1000:
            raise ValueError('Message must have at most 1000 characters')
        return v.strip()


class ContactResponse(BaseModel):
    success: bool
    message: str
