from typing import Optional, List
from pydantic import BaseModel, validator
from datetime import datetime
from uuid import UUID
from app.models.subscription import SubscriptionStatus, PaymentStatus, PaymentMethod, BillingCycle


# Subscription Plan schemas
class SubscriptionPlanBase(BaseModel):
    name: str
    display_name: str
    description: Optional[str] = None
    price_cents: int = 0
    currency: str = "BRL"
    billing_cycle: BillingCycle = BillingCycle.monthly
    max_plants: Optional[int] = None
    max_photos_per_plant: Optional[int] = None
    max_photos_per_care: int = 1
    is_active: bool = True


class SubscriptionPlanCreate(SubscriptionPlanBase):
    pass


class SubscriptionPlanUpdate(BaseModel):
    display_name: Optional[str] = None
    description: Optional[str] = None
    price_cents: Optional[int] = None
    max_plants: Optional[int] = None
    max_photos_per_plant: Optional[int] = None
    max_photos_per_care: Optional[int] = None
    is_active: Optional[bool] = None


class SubscriptionPlan(SubscriptionPlanBase):
    id: UUID
    created_at: datetime
    updated_at: datetime
    price_display: str
    is_free: bool
    plants_limit_display: str
    photos_per_plant_limit_display: str

    class Config:
        from_attributes = True


# User Subscription schemas
class UserSubscriptionBase(BaseModel):
    plan_id: UUID
    status: SubscriptionStatus = SubscriptionStatus.active
    expires_at: Optional[datetime] = None
    auto_renew: bool = True


class UserSubscriptionCreate(UserSubscriptionBase):
    user_id: UUID


class UserSubscriptionUpdate(BaseModel):
    status: Optional[SubscriptionStatus] = None
    expires_at: Optional[datetime] = None
    cancelled_at: Optional[datetime] = None
    auto_renew: Optional[bool] = None


class UserSubscription(UserSubscriptionBase):
    id: UUID
    user_id: UUID
    started_at: datetime
    cancelled_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    is_active: bool
    days_until_expiry: int
    plan: SubscriptionPlan

    class Config:
        from_attributes = True


# Payment schemas
class PaymentBase(BaseModel):
    amount_cents: int
    currency: str = "BRL"
    payment_method: Optional[PaymentMethod] = None


class PaymentCreate(PaymentBase):
    user_id: UUID
    subscription_id: Optional[UUID] = None


class PaymentUpdate(BaseModel):
    status: Optional[PaymentStatus] = None
    external_payment_id: Optional[str] = None
    payment_data: Optional[dict] = None
    processed_at: Optional[datetime] = None


class Payment(PaymentBase):
    id: UUID
    user_id: UUID
    subscription_id: Optional[UUID] = None
    status: PaymentStatus
    external_payment_id: Optional[str] = None
    payment_data: Optional[dict] = None
    processed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    amount_display: str

    class Config:
        from_attributes = True


# Usage schemas
class UsageStats(BaseModel):
    plants_count: int
    total_photos_count: int
    max_photos_per_plant: int
    plants_limit: Optional[int] = None
    photos_per_plant_limit: Optional[int] = None
    photos_per_care_limit: int = 1
    can_create_plant: bool = True
    can_add_photos: bool = True


class UsageTrackingCreate(BaseModel):
    user_id: UUID
    resource_type: str
    resource_id: Optional[UUID] = None
    action: str
    tracking_metadata: Optional[dict] = None


class UsageTracking(BaseModel):
    id: UUID
    user_id: UUID
    resource_type: str
    resource_id: Optional[UUID] = None
    action: str
    tracked_at: datetime
    tracking_metadata: Optional[dict] = None

    class Config:
        from_attributes = True


# Subscription management schemas
class SubscriptionUpgrade(BaseModel):
    plan_id: UUID
    payment_method: Optional[PaymentMethod] = None


class SubscriptionCancel(BaseModel):
    reason: Optional[str] = None
    cancel_immediately: bool = False


# Pricing page schema
class PricingPlan(BaseModel):
    id: UUID
    name: str
    display_name: str
    description: Optional[str] = None
    price_display: str
    is_free: bool
    billing_cycle: BillingCycle
    features: List[str]
    limits: dict
    is_popular: bool = False
    is_current: bool = False


class PricingResponse(BaseModel):
    plans: List[PricingPlan]
    current_plan: Optional[SubscriptionPlan] = None
    usage: dict


# Mercado Pago schemas
class CreateSubscriptionRequest(BaseModel):
    plan_id: UUID
    # Campos opcionais para planos customizados (ex: anual)
    custom_price_cents: Optional[int] = None
    custom_billing_cycle: Optional[BillingCycle] = None
    custom_description: Optional[str] = None
    # Cupom de desconto
    coupon_code: Optional[str] = None


class CreateSubscriptionResponse(BaseModel):
    success: bool
    subscription_id: Optional[UUID] = None
    preference_id: Optional[str] = None
    init_point: Optional[str] = None
    sandbox_init_point: Optional[str] = None
    error: Optional[str] = None


class WebhookNotification(BaseModel):
    id: Optional[str] = None
    live_mode: Optional[bool] = None
    type: Optional[str] = None
    date_created: Optional[str] = None
    application_id: Optional[str] = None
    user_id: Optional[str] = None
    version: Optional[str] = None
    api_version: Optional[str] = None
    action: Optional[str] = None
    data: Optional[dict] = None
