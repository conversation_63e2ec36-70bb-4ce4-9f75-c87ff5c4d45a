from typing import Optional, List
from pydantic import BaseModel, validator
from datetime import datetime
from uuid import UUID

# User Follow Schemas
class UserFollowBase(BaseModel):
    pass

class UserFollowCreate(UserFollowBase):
    followed_id: UUID

class UserFollowResponse(UserFollowBase):
    id: UUID
    follower_id: UUID
    followed_id: UUID
    created_at: datetime

    class Config:
        from_attributes = True

# Public User Profile Schemas
class PublicUserProfile(BaseModel):
    id: UUID
    username: Optional[str] = None
    first_name: str
    last_name: str
    bio: Optional[str] = None
    avatar: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    country: Optional[str] = None
    followers_count: int = 0
    following_count: int = 0
    is_public: bool = True
    is_following: bool = False  # Se o usuário atual está seguindo este perfil
    created_at: datetime

    class Config:
        from_attributes = True

# User Search Schemas
class UserSearchResult(BaseModel):
    id: UUID
    username: Optional[str] = None
    first_name: str
    last_name: str
    bio: Optional[str] = None
    avatar: Optional[str] = None
    followers_count: int = 0
    following_count: int = 0
    is_following: bool = False  # Se o usuário atual está seguindo este usuário

    class Config:
        from_attributes = True

class UserSearchResponse(BaseModel):
    users: List[UserSearchResult]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool

# Follow/Unfollow Response
class FollowActionResponse(BaseModel):
    success: bool
    is_following: bool
    followers_count: int
    message: str

# Privacy Settings Schemas
class UserPrivacySettings(BaseModel):
    is_public: bool = True
    allow_care_sharing: bool = True
    allow_search: bool = True

class PlantPrivacySettings(BaseModel):
    is_public: bool = True
    allow_care_sharing: bool = True

# Social Stats Schema
class SocialStats(BaseModel):
    followers_count: int
    following_count: int
    public_plants_count: int
    total_plants_count: int

# Username Update Schema
class UsernameUpdate(BaseModel):
    username: str

    @validator('username')
    def validate_username(cls, v):
        if not v:
            raise ValueError('Username cannot be empty')
        if len(v) < 3:
            raise ValueError('Username must be at least 3 characters long')
        if len(v) > 50:
            raise ValueError('Username must be at most 50 characters long')
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('Username can only contain letters, numbers, underscores and hyphens')
        return v.lower()

# Follower/Following List Schemas
class FollowerUser(BaseModel):
    id: UUID
    username: Optional[str] = None
    first_name: str
    last_name: str
    avatar: Optional[str] = None
    bio: Optional[str] = None
    followers_count: int = 0
    is_following: bool = False  # Se o usuário atual está seguindo este usuário
    followed_at: datetime

    class Config:
        from_attributes = True

class FollowingUser(BaseModel):
    id: UUID
    username: Optional[str] = None
    first_name: str
    last_name: str
    avatar: Optional[str] = None
    bio: Optional[str] = None
    followers_count: int = 0
    is_following: bool = True  # Sempre True para lista de seguindo
    followed_at: datetime

    class Config:
        from_attributes = True

class FollowersResponse(BaseModel):
    followers: List[FollowerUser]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool

class FollowingResponse(BaseModel):
    following: List[FollowingUser]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool
