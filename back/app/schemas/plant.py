from typing import Optional, List
from pydantic import BaseModel, validator, Field
from datetime import datetime, date
from uuid import UUID
from app.models.plant import PlantType, CareType, PlantCategory


# Plant Image schemas
class PlantImageBase(BaseModel):
    caption: Optional[str] = None
    is_primary: Optional[bool] = False
    photo_date: Optional[datetime] = None


class PlantImageCreate(PlantImageBase):
    pass


class PlantImageUpdate(PlantImageBase):
    pass


class PlantImage(PlantImageBase):
    id: UUID
    plant_id: UUID
    image: str
    created_at: datetime

    class Config:
        from_attributes = True

    @validator('image')
    def format_image_url(cls, v, values):
        """Return R2 URL or fallback to local media path"""
        if v and not v.startswith('http'):
            # Check if it's an R2 filename (UUID format without hyphens)
            filename_without_ext = v.split('.')[0]
            if len(filename_without_ext) == 32:  # R2 filename
                from app.core.config import settings
                plant_id = values.get('plant_id')
                if plant_id:
                    return f"{settings.R2_PUBLIC_URL}/plants/{plant_id}/medium_{v}"
                else:
                    # Fallback if no plant_id
                    return f"{settings.R2_PUBLIC_URL}/plants/unknown/medium_{v}"
            else:
                # Legacy local file
                return f"/media/{v}"
        return v

    @property
    def image_url(self) -> str:
        """Get image URL - R2 or local fallback"""
        return self.image


# CareImage schemas
class CareImageBase(BaseModel):
    image_path: str
    caption: Optional[str] = None
    is_primary: bool = False


class CareImageCreate(CareImageBase):
    pass


class CareImageUpdate(BaseModel):
    caption: Optional[str] = None
    is_primary: Optional[bool] = None


class CareImageInDBBase(CareImageBase):
    id: UUID
    care_id: UUID
    created_at: datetime

    class Config:
        from_attributes = True


class CareImage(CareImageInDBBase):
    pass


# Care schemas
class CareBase(BaseModel):
    care_type: Optional[CareType] = None  # Keep for backward compatibility
    care_types: Optional[List[CareType]] = None  # New field for multiple types
    description: Optional[str] = None
    date: datetime
    notes: Optional[str] = None
    image: Optional[str] = None


class CareCreate(CareBase):
    pass


class CareUpdate(CareBase):
    care_type: Optional[CareType] = None
    care_types: Optional[List[CareType]] = None
    date: Optional[datetime] = None


class Care(CareBase):
    id: UUID
    plant_id: UUID
    created_at: datetime
    care_type_display: Optional[str] = None
    care_types_list: Optional[List[str]] = None
    images: List["CareImage"] = []

    class Config:
        from_attributes = True


# Plant schemas
class PlantBase(BaseModel):
    name: str
    scientific_name: Optional[str] = None
    plant_type: PlantType = PlantType.bonsai
    plant_category: Optional[PlantCategory] = None
    description: Optional[str] = Field(None, max_length=2000)
    acquisition_date: Optional[date] = None
    location: Optional[str] = None
    style: Optional[str] = None
    estimated_age: Optional[str] = None
    archived: bool = False
    favorite: bool = False
    # Privacy settings
    is_public: bool = True
    allow_care_sharing: bool = True


class PlantCreate(PlantBase):
    @validator('acquisition_date', pre=True)
    def validate_acquisition_date(cls, v):
        """Convert empty string to None for optional date field"""
        if v == "" or v is None:
            return None
        return v

    @validator('scientific_name', 'location', 'style', 'estimated_age', 'description', pre=True)
    def validate_optional_strings(cls, v):
        """Convert empty strings to None for optional string fields"""
        if v == "" or v is None:
            return None
        return v


class PlantUpdate(PlantBase):
    name: Optional[str] = None
    plant_type: Optional[PlantType] = None
    plant_category: Optional[PlantCategory] = None
    scientific_name: Optional[str] = None
    description: Optional[str] = Field(None, max_length=2000)
    acquisition_date: Optional[date] = None
    location: Optional[str] = None
    style: Optional[str] = None
    estimated_age: Optional[str] = None
    archived: Optional[bool] = None
    favorite: Optional[bool] = None
    # Privacy settings
    is_public: Optional[bool] = None
    allow_care_sharing: Optional[bool] = None

    @validator('acquisition_date', pre=True)
    def validate_acquisition_date(cls, v):
        """Convert empty string to None for optional date field"""
        if v == "" or v is None:
            return None
        return v

    @validator('scientific_name', 'location', 'style', 'estimated_age', 'description', pre=True)
    def validate_optional_strings(cls, v):
        """Convert empty strings to None for optional string fields"""
        if v == "" or v is None:
            return None
        return v


class PlantInDBBase(PlantBase):
    id: UUID
    owner_id: UUID
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class Plant(PlantInDBBase):
    images: List[PlantImage] = []
    cares: List[Care] = []
    plant_type_display: Optional[str] = None
    # Owner information (only included for public plants viewed by others)
    owner_name: Optional[str] = None
    owner_username: Optional[str] = None
    owner_avatar: Optional[str] = None

    class Config:
        from_attributes = True


class PlantList(PlantInDBBase):
    primary_image: Optional[str] = None
    care_count: int = 0
    plant_type_display: Optional[str] = None

    class Config:
        from_attributes = True

    @validator('primary_image')
    def format_primary_image_url(cls, v, values):
        """Return R2 URL for primary image or fallback to local media path"""
        if v and not v.startswith('http'):
            # Check if it's an R2 filename (UUID format without hyphens)
            filename_without_ext = v.split('.')[0]
            if len(filename_without_ext) == 32:  # R2 filename
                from app.core.config import settings
                plant_id = values.get('id')
                if plant_id:
                    return f"{settings.R2_PUBLIC_URL}/plants/{plant_id}/medium_{v}"
                else:
                    # Fallback if no plant_id
                    return f"{settings.R2_PUBLIC_URL}/plants/unknown/medium_{v}"
            else:
                # Legacy local file
                return f"/media/{v}"
        return v


# Reminder schemas
class ReminderBase(BaseModel):
    care_type: CareType
    scheduled_date: date
    description: Optional[str] = None


class ReminderCreate(ReminderBase):
    pass


class ReminderUpdate(BaseModel):
    care_type: Optional[CareType] = None
    scheduled_date: Optional[date] = None
    description: Optional[str] = None
    is_completed: Optional[bool] = None


class ReminderInDBBase(ReminderBase):
    id: UUID
    plant_id: UUID
    is_completed: bool
    is_notified: bool
    created_at: datetime
    completed_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class Reminder(ReminderInDBBase):
    care_type_display: str
    days_until_due: int
    is_overdue: bool


class ReminderList(ReminderInDBBase):
    care_type_display: str
    days_until_due: int
    is_overdue: bool
    plant_name: Optional[str] = None  # Para notificações
