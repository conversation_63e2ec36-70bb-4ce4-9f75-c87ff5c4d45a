from typing import Optional
from pydantic import BaseModel, EmailStr
from datetime import datetime
from uuid import UUID


# Shared properties
class UserBase(BaseModel):
    email: Optional[EmailStr] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    bio: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    country: Optional[str] = None
    cpf_cnpj: Optional[str] = None
    document_type: Optional[str] = None
    # Social fields
    username: Optional[str] = None
    is_public: Optional[bool] = True
    allow_care_sharing: Optional[bool] = True
    allow_search: Optional[bool] = True
    is_active: Optional[bool] = True


# Properties to receive via API on creation
class UserCreate(UserBase):
    email: EmailStr
    first_name: str
    last_name: str
    password: str
    username: str


# Properties to receive via API on update
class UserUpdate(UserBase):
    password: Optional[str] = None
    avatar: Optional[str] = None


class UserInDBBase(UserBase):
    id: Optional[UUID] = None
    avatar: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    full_name: Optional[str] = None
    # Social fields
    followers_count: Optional[int] = 0
    following_count: Optional[int] = 0
    # Subscription fields
    current_plan_id: Optional[UUID] = None
    subscription_status: Optional[str] = None
    subscription_expires_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# Additional properties to return via API
class User(UserInDBBase):
    pass


# Additional properties stored in DB
class UserInDB(UserInDBBase):
    hashed_password: str


# Token schemas
class Token(BaseModel):
    access_token: str
    token_type: str


class TokenPayload(BaseModel):
    sub: Optional[UUID] = None


# Password change schemas
class PasswordChangeRequest(BaseModel):
    current_password: str
    new_password: str


class PasswordChangeConfirm(BaseModel):
    token: str
