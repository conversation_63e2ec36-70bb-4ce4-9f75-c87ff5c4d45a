from typing import Optional, Dict, Any
from pydantic import BaseModel
from datetime import datetime
from uuid import UUID

from app.models.notification import NotificationType


class NotificationBase(BaseModel):
    type: NotificationType
    title: str
    message: str
    data: Optional[Dict[str, Any]] = None


class NotificationCreate(NotificationBase):
    user_id: UUID
    reminder_id: Optional[UUID] = None
    follower_id: Optional[UUID] = None
    subscription_id: Optional[UUID] = None


class NotificationUpdate(BaseModel):
    is_read: Optional[bool] = None
    read_at: Optional[datetime] = None
    seen_at: Optional[datetime] = None


class NotificationInDB(NotificationBase):
    id: UUID
    user_id: UUID
    is_read: bool
    reminder_id: Optional[UUID] = None
    follower_id: Optional[UUID] = None
    subscription_id: Optional[UUID] = None
    created_at: datetime
    read_at: Optional[datetime] = None
    seen_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class Notification(NotificationInDB):
    # Dados adicionais para o frontend
    follower_name: Optional[str] = None
    follower_avatar: Optional[str] = None
    plant_name: Optional[str] = None
    
    class Config:
        from_attributes = True


class NotificationStats(BaseModel):
    total_count: int
    unread_count: int
    unseen_count: int
