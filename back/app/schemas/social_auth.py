from typing import Optional
from pydantic import BaseModel, EmailStr, model_validator
from datetime import datetime
from app.models.social_auth import SocialProvider


# Social Login Request Schemas
class GoogleLoginRequest(BaseModel):
    id_token: Optional[str] = None
    access_token: Optional[str] = None

    @model_validator(mode='after')
    def validate_tokens(self):
        if not self.id_token and not self.access_token:
            raise ValueError('Either id_token or access_token must be provided')
        return self


class FacebookLoginRequest(BaseModel):
    access_token: str


class AppleLoginRequest(BaseModel):
    id_token: str
    authorization_code: Optional[str] = None
    user_info: Optional[dict] = None  # Apple pode enviar info do usuário na primeira vez


class SocialLoginRequest(BaseModel):
    provider: SocialProvider
    id_token: str
    access_token: Optional[str] = None
    authorization_code: Optional[str] = None
    user_info: Optional[dict] = None


# Social Account Schemas
class SocialAccountBase(BaseModel):
    provider: SocialProvider
    provider_user_id: str
    email: Optional[EmailStr] = None
    name: Optional[str] = None
    picture: Optional[str] = None


class SocialAccountCreate(SocialAccountBase):
    user_id: int
    access_token: Optional[str] = None
    refresh_token: Optional[str] = None
    token_expires_at: Optional[datetime] = None


class SocialAccountUpdate(BaseModel):
    email: Optional[EmailStr] = None
    name: Optional[str] = None
    picture: Optional[str] = None
    access_token: Optional[str] = None
    refresh_token: Optional[str] = None
    token_expires_at: Optional[datetime] = None
    is_active: Optional[bool] = None


class SocialAccount(SocialAccountBase):
    id: int
    user_id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Response Schemas
class SocialLoginResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    user: dict
    is_new_user: bool = False
    linked_accounts: list[str] = []


class SocialUserInfo(BaseModel):
    """Informações padronizadas do usuário vindas de provedores sociais"""
    provider: SocialProvider
    provider_user_id: str
    email: Optional[EmailStr] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    name: Optional[str] = None
    picture: Optional[str] = None
    verified_email: bool = False


# Link/Unlink Account Schemas
class LinkSocialAccountRequest(BaseModel):
    provider: SocialProvider
    id_token: str
    access_token: Optional[str] = None


class UnlinkSocialAccountRequest(BaseModel):
    provider: SocialProvider
