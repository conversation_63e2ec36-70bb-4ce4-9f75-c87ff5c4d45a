from typing import Optional, List
from pydantic import BaseModel
from datetime import datetime
from uuid import UUID

from app.models.social import FeedActivityType


# Plant Like Schemas
class PlantLikeBase(BaseModel):
    plant_id: UUID


class PlantLikeCreate(PlantLikeBase):
    pass


class PlantLike(PlantLikeBase):
    id: UUID
    user_id: UUID
    created_at: datetime

    class Config:
        from_attributes = True


# Plant Comment Schemas
class PlantCommentBase(BaseModel):
    content: str


class PlantCommentCreate(PlantCommentBase):
    plant_id: UUID


class PlantCommentUpdate(BaseModel):
    content: str


class PlantComment(PlantCommentBase):
    id: UUID
    user_id: UUID
    plant_id: UUID
    created_at: datetime
    updated_at: datetime
    
    # User info for display
    user_first_name: Optional[str] = None
    user_last_name: Optional[str] = None
    user_username: Optional[str] = None
    user_avatar: Optional[str] = None

    class Config:
        from_attributes = True


# Feed Activity Schemas
class FeedActivityBase(BaseModel):
    activity_type: FeedActivityType


class FeedActivityCreate(FeedActivityBase):
    user_id: UUID
    plant_id: Optional[UUID] = None
    care_id: Optional[UUID] = None


class FeedActivity(FeedActivityBase):
    id: UUID
    user_id: UUID
    plant_id: Optional[UUID] = None
    care_id: Optional[UUID] = None
    created_at: datetime

    # User info for display
    user_first_name: str
    user_last_name: str
    user_username: Optional[str] = None
    user_avatar: Optional[str] = None

    # Plant info for display
    plant_name: Optional[str] = None
    plant_primary_image: Optional[str] = None

    # Care info for display (if applicable)
    care_description: Optional[str] = None
    care_type_display: Optional[str] = None

    # Social info for display
    likes_count: int = 0
    is_liked_by_user: bool = False

    class Config:
        from_attributes = True


# Plant with social info
class PlantWithSocialInfo(BaseModel):
    id: UUID
    name: str
    scientific_name: Optional[str] = None
    plant_type_display: Optional[str] = None
    description: Optional[str] = None
    primary_image: Optional[str] = None
    owner_id: UUID
    created_at: datetime
    
    # Owner info
    owner_first_name: str
    owner_last_name: str
    owner_username: Optional[str] = None
    owner_avatar: Optional[str] = None
    
    # Social info
    likes_count: int = 0
    comments_count: int = 0
    is_liked_by_user: bool = False

    class Config:
        from_attributes = True


# Feed Response
class FeedResponse(BaseModel):
    activities: List[FeedActivity]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


# Like Action Response
class LikeActionResponse(BaseModel):
    success: bool
    is_liked: bool
    likes_count: int
    message: str


# Comment List Response
class CommentListResponse(BaseModel):
    comments: List[PlantComment]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool
