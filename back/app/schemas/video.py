from typing import Optional
from datetime import datetime
from pydantic import BaseModel
from uuid import UUID


# Plant Video Schemas
class PlantVideoBase(BaseModel):
    caption: Optional[str] = None
    video_date: Optional[datetime] = None


class PlantVideoCreate(PlantVideoBase):
    pass


class PlantVideoUpdate(PlantVideoBase):
    pass


class PlantVideo(PlantVideoBase):
    id: UUID
    plant_id: UUID
    video: str
    thumbnail_url: Optional[str] = None
    original_filename: Optional[str] = None
    duration_seconds: Optional[int] = None
    file_size_bytes: Optional[int] = None
    created_at: datetime

    class Config:
        from_attributes = True


# Care Video Schemas
class CareVideoBase(BaseModel):
    caption: Optional[str] = None
    is_primary: bool = False


class CareVideoCreate(CareVideoBase):
    pass


class CareVideoUpdate(CareVideoBase):
    pass


class CareVideo(CareVideoBase):
    id: UUID
    care_id: UUID
    video_path: str
    thumbnail_url: Optional[str] = None
    original_filename: Optional[str] = None
    duration_seconds: Optional[int] = None
    file_size_bytes: Optional[int] = None
    created_at: datetime

    class Config:
        from_attributes = True
