from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.db.session import SessionLocal
from app.models.user import User
from app.models.subscription import UserSubscription, SubscriptionStatus
from app.models.notification import NotificationType
from app import crud
import logging

logger = logging.getLogger(__name__)


def check_expiring_subscriptions():
    """
    Check for subscriptions expiring in 3 days and send notifications
    """
    db = SessionLocal()
    try:
        # Calculate date 3 days from now
        three_days_from_now = datetime.utcnow() + timedelta(days=3)
        
        # Find subscriptions expiring in 3 days
        expiring_subscriptions = db.query(UserSubscription).join(User).filter(
            and_(
                UserSubscription.status == SubscriptionStatus.active,
                UserSubscription.expires_at <= three_days_from_now,
                UserSubscription.expires_at > datetime.utcnow()
            )
        ).all()
        
        logger.info(f"Found {len(expiring_subscriptions)} subscriptions expiring in 3 days")
        
        for subscription in expiring_subscriptions:
            try:
                # Check if we already sent a notification for this subscription
                existing_notification = db.query(crud.notification.model).filter(
                    and_(
                        crud.notification.model.user_id == subscription.user_id,
                        crud.notification.model.subscription_id == subscription.id,
                        crud.notification.model.type == NotificationType.subscription_expiring,
                        crud.notification.model.created_at >= datetime.utcnow() - timedelta(days=7)
                    )
                ).first()
                
                if not existing_notification:
                    plan_name = subscription.plan.display_name if subscription.plan else "Premium"
                    days_until_expiry = (subscription.expires_at - datetime.utcnow()).days
                    
                    crud.notification.create_subscription_notification(
                        db=db,
                        user_id=str(subscription.user_id),
                        notification_type=NotificationType.subscription_expiring,
                        subscription_id=str(subscription.id),
                        title="Assinatura Expirando",
                        message=f"Seu plano {plan_name} expira em {days_until_expiry} dias. Renove para continuar aproveitando todos os recursos.",
                        data={
                            "plan_name": plan_name,
                            "days_until_expiry": days_until_expiry,
                            "expires_at": subscription.expires_at.isoformat()
                        }
                    )
                    
                    logger.info(f"Expiring subscription notification sent to user {subscription.user.email}")
                else:
                    logger.info(f"Notification already sent for subscription {subscription.id}")
                    
            except Exception as e:
                logger.error(f"Error sending expiring notification for subscription {subscription.id}: {e}")
                continue
        
        db.commit()
        
    except Exception as e:
        logger.error(f"Error checking expiring subscriptions: {e}")
        db.rollback()
    finally:
        db.close()


def check_grace_period_ending():
    """
    Check for subscriptions in grace period ending soon
    """
    db = SessionLocal()
    try:
        # Find subscriptions in grace period ending in 2 days
        two_days_from_now = datetime.utcnow() + timedelta(days=2)
        
        grace_period_subscriptions = db.query(UserSubscription).join(User).filter(
            and_(
                UserSubscription.status == SubscriptionStatus.expired_grace_period,
                UserSubscription.expires_at + timedelta(days=15) <= two_days_from_now,
                UserSubscription.expires_at + timedelta(days=15) > datetime.utcnow()
            )
        ).all()
        
        logger.info(f"Found {len(grace_period_subscriptions)} subscriptions with grace period ending in 2 days")
        
        for subscription in grace_period_subscriptions:
            try:
                # Check if we already sent a notification
                existing_notification = db.query(crud.notification.model).filter(
                    and_(
                        crud.notification.model.user_id == subscription.user_id,
                        crud.notification.model.subscription_id == subscription.id,
                        crud.notification.model.type == NotificationType.subscription_expiring,
                        crud.notification.model.created_at >= datetime.utcnow() - timedelta(days=3)
                    )
                ).first()
                
                if not existing_notification:
                    plan_name = subscription.plan.display_name if subscription.plan else "Premium"
                    grace_end_date = subscription.expires_at + timedelta(days=15)
                    days_until_final_expiry = (grace_end_date - datetime.utcnow()).days
                    
                    crud.notification.create_subscription_notification(
                        db=db,
                        user_id=str(subscription.user_id),
                        notification_type=NotificationType.subscription_expiring,
                        subscription_id=str(subscription.id),
                        title="Período de Graça Terminando",
                        message=f"Seu período de graça termina em {days_until_final_expiry} dias. Renove seu plano {plan_name} para não perder o acesso.",
                        data={
                            "plan_name": plan_name,
                            "days_until_final_expiry": days_until_final_expiry,
                            "grace_end_date": grace_end_date.isoformat(),
                            "is_grace_period": True
                        }
                    )
                    
                    logger.info(f"Grace period ending notification sent to user {subscription.user.email}")
                    
            except Exception as e:
                logger.error(f"Error sending grace period notification for subscription {subscription.id}: {e}")
                continue
        
        db.commit()
        
    except Exception as e:
        logger.error(f"Error checking grace period subscriptions: {e}")
        db.rollback()
    finally:
        db.close()


def send_renewal_notifications():
    """
    Main function to send all renewal-related notifications
    """
    logger.info("Starting renewal notifications check...")
    check_expiring_subscriptions()
    check_grace_period_ending()
    logger.info("Renewal notifications check completed.")


if __name__ == "__main__":
    # For testing
    send_renewal_notifications()
