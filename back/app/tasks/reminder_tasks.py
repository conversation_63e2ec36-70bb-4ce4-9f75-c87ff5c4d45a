from datetime import datetime, timedelta, date
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.db.session import <PERSON><PERSON>ocal
from app.models.user import User
from app.models.plant import Plant, Reminder
from app.models.notification import NotificationType
from app import crud
import logging

logger = logging.getLogger(__name__)


def check_due_reminders():
    """
    Check for reminders that are due today or overdue and send notifications
    """
    db = SessionLocal()
    try:
        today = date.today()
        
        # Find reminders that are due today or overdue and not completed
        due_reminders = db.query(Reminder).join(Plant).join(User).filter(
            and_(
                Reminder.is_completed == False,
                Reminder.scheduled_date <= today,
                Reminder.is_notified == False  # Only send notification once
            )
        ).all()
        
        logger.info(f"Found {len(due_reminders)} due reminders to notify")
        
        for reminder in due_reminders:
            try:
                plant = reminder.plant
                user = plant.owner
                
                # Check if we already sent a notification for this reminder
                existing_notification = db.query(crud.notification.model).filter(
                    and_(
                        crud.notification.model.user_id == user.id,
                        crud.notification.model.reminder_id == reminder.id,
                        crud.notification.model.type == NotificationType.reminder
                    )
                ).first()
                
                if not existing_notification:
                    # Create notification
                    crud.notification.create_reminder_notification(
                        db=db,
                        user_id=str(user.id),
                        reminder_id=str(reminder.id),
                        plant_name=plant.name,
                        care_type=reminder.care_type_display
                    )
                    
                    # Mark reminder as notified
                    reminder.is_notified = True
                    db.add(reminder)
                    
                    logger.info(f"Reminder notification sent to user {user.email} for plant {plant.name}")
                else:
                    logger.info(f"Notification already exists for reminder {reminder.id}")
                    
            except Exception as e:
                logger.error(f"Error sending reminder notification for reminder {reminder.id}: {e}")
                continue
        
        db.commit()
        
    except Exception as e:
        logger.error(f"Error checking due reminders: {e}")
        db.rollback()
    finally:
        db.close()


def check_upcoming_reminders():
    """
    Check for reminders due in the next 2 days and send advance notifications
    """
    db = SessionLocal()
    try:
        tomorrow = date.today() + timedelta(days=1)
        day_after_tomorrow = date.today() + timedelta(days=2)

        # Find reminders due in the next 2 days
        upcoming_reminders = db.query(Reminder).join(Plant).join(User).filter(
            and_(
                Reminder.is_completed == False,
                Reminder.scheduled_date.in_([tomorrow, day_after_tomorrow]),
                Reminder.is_notified == False  # Only send notification once
            )
        ).all()
        
        logger.info(f"Found {len(upcoming_reminders)} reminders due in the next 2 days")
        
        for reminder in upcoming_reminders:
            try:
                plant = reminder.plant
                user = plant.owner
                
                # Check if we already sent a notification for this reminder
                existing_notification = db.query(crud.notification.model).filter(
                    and_(
                        crud.notification.model.user_id == user.id,
                        crud.notification.model.reminder_id == reminder.id,
                        crud.notification.model.type == NotificationType.reminder
                    )
                ).first()
                
                if not existing_notification:
                    # Calculate days until due
                    days_until = (reminder.scheduled_date - date.today()).days
                    time_text = "hoje" if days_until == 0 else f"em {days_until} dia{'s' if days_until > 1 else ''}"

                    # Create advance notification
                    crud.notification.create_reminder_notification(
                        db=db,
                        user_id=str(user.id),
                        reminder_id=str(reminder.id),
                        plant_name=plant.name,
                        care_type=f"{reminder.care_type_display} ({time_text})"
                    )

                    # Mark reminder as notified for advance notifications
                    reminder.is_notified = True
                    db.add(reminder)

                    logger.info(f"Advance reminder notification sent to user {user.email} for plant {plant.name} - due {time_text}")
                else:
                    logger.info(f"Notification already exists for reminder {reminder.id}")
                    
            except Exception as e:
                logger.error(f"Error sending advance reminder notification for reminder {reminder.id}: {e}")
                continue
        
        db.commit()
        
    except Exception as e:
        logger.error(f"Error checking upcoming reminders: {e}")
        db.rollback()
    finally:
        db.close()


def send_reminder_notifications():
    """
    Main function to send all reminder-related notifications
    """
    logger.info("Starting reminder notifications check...")
    check_due_reminders()
    check_upcoming_reminders()
    logger.info("Reminder notifications check completed.")


if __name__ == "__main__":
    # For testing
    send_reminder_notifications()
