from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Integer, String, DateTime, Text, Date, ForeignKey, Enum
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
import uuid
from app.db.base import Base


class PlantType(str, enum.Enum):
    bonsai = "bonsai"
    suculenta = "suculenta"
    arvore = "arvore"
    arbusto = "arbusto"
    flor = "flor"
    erva = "erva"
    outro = "outro"


class PlantCategory(str, enum.Enum):
    conifera = "conifera"
    caducifolia = "caducifolia"
    perene_folhosa = "perene_folhosa"
    frutifera = "frutifera"
    florifera = "florifera"
    suculenta = "suculenta"
    outro = "outro"


class CareType(str, enum.Enum):
    poda = "poda"
    adubacao = "adubacao"
    transplante = "transplante"
    aramacao = "aramacao"
    limpeza = "limpeza"
    tratamento = "tratamento"
    desfolha = "desfolha"
    outro = "outro"


class Plant(Base):
    __tablename__ = "plants"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String, nullable=False, index=True)
    scientific_name = Column(String, nullable=True)
    plant_type = Column(Enum(PlantType), default=PlantType.bonsai)
    plant_category = Column(Enum(PlantCategory), nullable=True)  # Categoria específica
    description = Column(Text, nullable=True)
    acquisition_date = Column(Date, nullable=True)
    location = Column(String, nullable=True)
    style = Column(String, nullable=True)
    estimated_age = Column(String, nullable=True)
    archived = Column(Boolean, default=False, nullable=False)
    favorite = Column(Boolean, default=False, nullable=False)
    # Store R2 filename instead of local path
    primary_image_r2 = Column(String, nullable=True)

    # Privacy settings
    is_public = Column(Boolean, default=True, nullable=False)  # Planta visível no perfil público
    allow_care_sharing = Column(Boolean, default=True, nullable=False)  # Cuidados visíveis para seguidores

    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    owner = relationship("User", back_populates="plants")
    images = relationship("PlantImage", back_populates="plant", cascade="all, delete-orphan")
    videos = relationship("PlantVideo", back_populates="plant", cascade="all, delete-orphan")
    cares = relationship("Care", back_populates="plant", cascade="all, delete-orphan", order_by="Care.date.desc()")
    reminders = relationship("Reminder", back_populates="plant", cascade="all, delete-orphan")

    @property
    def plant_type_display(self) -> str:
        """Return plant category in Portuguese"""
        # Se tem categoria específica, usa ela
        if self.plant_category:
            category_map = {
                PlantCategory.conifera: "Conífera",
                PlantCategory.caducifolia: "Caducifólia",
                PlantCategory.perene_folhosa: "Perene Folhosa",
                PlantCategory.frutifera: "Frutífera",
                PlantCategory.florifera: "Florífera",
                PlantCategory.suculenta: "Suculenta",
                PlantCategory.outro: "Outro"
            }
            return category_map.get(self.plant_category, self.plant_category.value)

        # Fallback para o tipo geral
        plant_type_map = {
            PlantType.bonsai: "Bonsai",
            PlantType.suculenta: "Suculenta",
            PlantType.arvore: "Árvore",
            PlantType.arbusto: "Arbusto",
            PlantType.flor: "Flor",
            PlantType.erva: "Erva",
            PlantType.outro: "Outro"
        }
        return plant_type_map.get(self.plant_type, self.plant_type.value)


class PlantImage(Base):
    __tablename__ = "plant_images"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    plant_id = Column(UUID(as_uuid=True), ForeignKey("plants.id"), nullable=False)
    image = Column(String, nullable=False)  # R2 filename
    original_filename = Column(String, nullable=True)  # Original upload name
    caption = Column(String, nullable=True)
    is_primary = Column(Boolean, default=False)
    photo_date = Column(DateTime(timezone=True), nullable=True)  # Data da foto (EXIF ou manual)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    plant = relationship("Plant", back_populates="images")


class PlantVideo(Base):
    __tablename__ = "plant_videos"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    plant_id = Column(UUID(as_uuid=True), ForeignKey("plants.id"), nullable=False)
    video = Column(String, nullable=False)  # R2 filename
    thumbnail_url = Column(String(500), nullable=True)  # Thumbnail image URL
    original_filename = Column(String, nullable=True)  # Original upload name
    caption = Column(String, nullable=True)
    duration_seconds = Column(Integer, nullable=True)  # Duração do vídeo em segundos
    file_size_bytes = Column(Integer, nullable=True)  # Tamanho do arquivo em bytes
    video_date = Column(DateTime(timezone=True), nullable=True)  # Data do vídeo (manual)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    plant = relationship("Plant", back_populates="videos")


class Care(Base):
    __tablename__ = "cares"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    plant_id = Column(UUID(as_uuid=True), ForeignKey("plants.id"), nullable=False)
    care_type = Column(Enum(CareType), nullable=True)  # Keep for backward compatibility
    care_types = Column(ARRAY(Enum(CareType)), nullable=True)  # New field for multiple types
    description = Column(Text, nullable=True)
    date = Column(DateTime(timezone=True), nullable=False)
    notes = Column(Text, nullable=True)
    image = Column(String, nullable=True)  # Path to care image (legacy, for backward compatibility)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    plant = relationship("Plant", back_populates="cares")
    images = relationship("CareImage", back_populates="care", cascade="all, delete-orphan")
    videos = relationship("CareVideo", back_populates="care", cascade="all, delete-orphan")

    @property
    def care_type_display(self) -> str:
        """Return care type in Portuguese"""
        care_type_map = {
            CareType.poda: "Poda",
            CareType.adubacao: "Adubação",
            CareType.transplante: "Transplante",
            CareType.aramacao: "Aramação",
            CareType.limpeza: "Limpeza",
            CareType.tratamento: "Tratamento",
            CareType.desfolha: "Desfolha",
            CareType.outro: "Outro"
        }

        # Support multiple care types
        if self.care_types:
            display_names = [care_type_map.get(ct, str(ct)) for ct in self.care_types]
            return ", ".join(display_names)

        # Fallback to single care_type for backward compatibility
        if self.care_type:
            return care_type_map.get(self.care_type, str(self.care_type))

        return "Não especificado"

    @property
    def care_types_list(self) -> list:
        """Return list of care types (for API responses)"""
        if self.care_types:
            return [str(ct.value) for ct in self.care_types]
        elif self.care_type:
            return [str(self.care_type.value)]
        return []


class CareImage(Base):
    __tablename__ = "care_images"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    care_id = Column(UUID(as_uuid=True), ForeignKey("cares.id"), nullable=False)
    image_path = Column(String, nullable=False)
    caption = Column(Text, nullable=True)
    is_primary = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    care = relationship("Care", back_populates="images")


class CareVideo(Base):
    __tablename__ = "care_videos"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    care_id = Column(UUID(as_uuid=True), ForeignKey("cares.id"), nullable=False)
    video_path = Column(String, nullable=False)  # R2 filename
    thumbnail_url = Column(String(500), nullable=True)  # Thumbnail image URL
    original_filename = Column(String, nullable=True)  # Original upload name
    caption = Column(Text, nullable=True)
    duration_seconds = Column(Integer, nullable=True)  # Duração do vídeo em segundos
    file_size_bytes = Column(Integer, nullable=True)  # Tamanho do arquivo em bytes
    is_primary = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    care = relationship("Care", back_populates="videos")


class Reminder(Base):
    __tablename__ = "reminders"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    plant_id = Column(UUID(as_uuid=True), ForeignKey("plants.id"), nullable=False)
    care_type = Column(Enum(CareType), nullable=False)
    scheduled_date = Column(Date, nullable=False)
    description = Column(Text, nullable=True)
    is_completed = Column(Boolean, default=False, nullable=False)
    is_notified = Column(Boolean, default=False, nullable=False)  # Para controlar se já foi notificado
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    plant = relationship("Plant", back_populates="reminders")

    @property
    def care_type_display(self) -> str:
        """Return care type in Portuguese (Reminder only has single care_type)"""
        care_type_map = {
            CareType.poda: "Poda",
            CareType.adubacao: "Adubação",
            CareType.transplante: "Transplante",
            CareType.aramacao: "Aramação",
            CareType.limpeza: "Limpeza",
            CareType.tratamento: "Tratamento",
            CareType.desfolha: "Desfolha",
            CareType.outro: "Outro"
        }

        # Reminder only has single care_type
        if self.care_type:
            return care_type_map.get(self.care_type, str(self.care_type))

        return "Não especificado"



    def get_days_until_due(self, user_timezone_offset: int = -3) -> int:
        """Retorna quantos dias faltam para o lembrete baseado no timezone do usuário"""
        from datetime import datetime, timezone, timedelta
        user_tz = timezone(timedelta(hours=user_timezone_offset))
        today = datetime.now(user_tz).date()
        return (self.scheduled_date - today).days

    def get_is_overdue(self, user_timezone_offset: int = -3) -> bool:
        """Verifica se o lembrete está atrasado baseado no timezone do usuário"""
        from datetime import datetime, timezone, timedelta
        user_tz = timezone(timedelta(hours=user_timezone_offset))
        today = datetime.now(user_tz).date()
        return self.scheduled_date < today and not self.is_completed

    @property
    def days_until_due(self) -> int:
        """Retorna quantos dias faltam para o lembrete (fallback para UTC-3)"""
        return self.get_days_until_due(-3)

    @property
    def is_overdue(self) -> bool:
        """Verifica se o lembrete está atrasado (fallback para UTC-3)"""
        return self.get_is_overdue(-3)


