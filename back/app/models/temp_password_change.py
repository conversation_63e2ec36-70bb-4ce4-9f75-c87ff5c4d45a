from sqlalchemy import Column, String, ForeignKey, DateTime
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base import Base
import uuid


class TempPasswordChange(Base):
    __tablename__ = "temp_password_changes"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    token_id = Column(UUID(as_uuid=True), ForeignKey("auth_tokens.id", ondelete="CASCADE"), nullable=False, unique=True)
    new_password_hash = Column(String, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    token = relationship("AuthToken")
