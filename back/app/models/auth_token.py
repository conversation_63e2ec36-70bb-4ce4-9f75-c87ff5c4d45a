from sqlalchemy import Column, String, DateTime, <PERSON>olean, <PERSON><PERSON><PERSON>, Enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base import Base
import uuid
import enum
from datetime import datetime, timedelta


class TokenType(str, enum.Enum):
    ACTIVATION = "activation"
    PASSWORD_RESET = "password_reset"
    PASSWORD_CHANGE = "password_change"


class AuthToken(Base):
    __tablename__ = "auth_tokens"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    token = Column(String(255), unique=True, nullable=False, index=True)
    token_type = Column(Enum(TokenType), nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    used_at = Column(DateTime(timezone=True), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="auth_tokens")

    @property
    def is_expired(self) -> bool:
        """Verifica se o token está expirado"""
        from datetime import timezone

        # Usar datetime com timezone UTC para comparação consistente
        now_utc = datetime.now(timezone.utc)
        expires_at = self.expires_at

        # Se expires_at não tem timezone, assumir que é UTC
        if expires_at.tzinfo is None:
            expires_at = expires_at.replace(tzinfo=timezone.utc)

        return now_utc > expires_at

    @property
    def is_valid(self) -> bool:
        """Verifica se o token é válido (não usado, não expirado, ativo)"""
        return (
            self.is_active and 
            not self.used_at and 
            not self.is_expired
        )

    def mark_as_used(self):
        """Marca o token como usado"""
        from datetime import timezone

        self.used_at = datetime.now(timezone.utc)
        self.is_active = False

    @classmethod
    def create_activation_token(cls, user_id: uuid.UUID) -> 'AuthToken':
        """Cria um token de ativação (válido por 24 horas)"""
        from datetime import timezone

        return cls(
            user_id=user_id,
            token=str(uuid.uuid4()),
            token_type=TokenType.ACTIVATION,
            expires_at=datetime.now(timezone.utc) + timedelta(hours=24),
            is_active=True,
            used_at=None
        )

    @classmethod
    def create_password_reset_token(cls, user_id: uuid.UUID) -> 'AuthToken':
        """Cria um token de reset de senha (válido por 24 horas)"""
        from datetime import timezone

        return cls(
            user_id=user_id,
            token=str(uuid.uuid4()),
            token_type=TokenType.PASSWORD_RESET,
            expires_at=datetime.now(timezone.utc) + timedelta(hours=24),
            is_active=True,
            used_at=None
        )

    @classmethod
    def create_password_change_token(cls, user_id: uuid.UUID) -> 'AuthToken':
        """Cria um token de mudança de senha (válido por 2 horas)"""
        from datetime import timezone

        return cls(
            user_id=user_id,
            token=str(uuid.uuid4()),
            token_type=TokenType.PASSWORD_CHANGE,
            expires_at=datetime.now(timezone.utc) + timedelta(hours=2),
            is_active=True,
            used_at=None
        )
