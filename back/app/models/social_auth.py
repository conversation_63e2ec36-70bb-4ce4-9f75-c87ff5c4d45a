from sqlalchemy import Column, Integer, String, DateTime, <PERSON><PERSON><PERSON>, Boolean, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from app.db.base import Base


class SocialProvider(str, enum.Enum):
    google = "google"
    apple = "apple"
    facebook = "facebook"
    github = "github"


class SocialAccount(Base):
    __tablename__ = "social_accounts"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    provider = Column(String(50), nullable=False)  # google, apple, etc.
    provider_user_id = Column(String(255), nullable=False)  # ID do usuário no provedor
    email = Column(String(255), nullable=True)
    name = Column(String(255), nullable=True)
    picture = Column(Text, nullable=True)  # URL da foto do perfil
    access_token = Column(Text, nullable=True)
    refresh_token = Column(Text, nullable=True)
    token_expires_at = Column(DateTime(timezone=True), nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="social_accounts")

    def __repr__(self):
        return f"<SocialAccount(user_id={self.user_id}, provider={self.provider})>"
