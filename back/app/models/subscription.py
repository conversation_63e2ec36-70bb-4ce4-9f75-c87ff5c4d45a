from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Integer, String, DateTime, Text, ForeignKey, Enum, DECIMAL
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
import uuid
from datetime import datetime, timedelta
from app.db.base import Base


class SubscriptionStatus(str, enum.Enum):
    active = "active"
    cancelled = "cancelled"
    cancelled_pending_expiration = "cancelled_pending_expiration"  # Cancelada mas ativa até expirar
    expired = "expired"
    expired_grace_period = "expired_grace_period"  # Expirada mas em período de graça
    pending = "pending"
    overdue = "overdue"


class PaymentStatus(str, enum.Enum):
    pending = "pending"
    completed = "completed"
    failed = "failed"
    refunded = "refunded"


class PaymentMethod(str, enum.Enum):
    credit_card = "credit_card"
    pix = "pix"
    boleto = "boleto"


class BillingCycle(str, enum.Enum):
    monthly = "monthly"
    yearly = "yearly"


class SubscriptionPlan(Base):
    __tablename__ = "subscription_plans"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String(50), unique=True, nullable=False, index=True)
    display_name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    price_cents = Column(Integer, nullable=False, default=0)
    currency = Column(String(3), nullable=False, default="BRL")
    billing_cycle = Column(Enum(BillingCycle), nullable=False, default=BillingCycle.monthly)
    max_plants = Column(Integer, nullable=True)  # NULL = unlimited
    max_photos_per_plant = Column(Integer, nullable=True)  # NULL = unlimited
    max_photos_per_care = Column(Integer, nullable=False, default=1)
    max_videos_per_plant = Column(Integer, nullable=True)  # NULL = unlimited, 0 = not allowed
    max_videos_per_care = Column(Integer, nullable=False, default=0)  # 0 = not allowed
    allows_videos = Column(Boolean, nullable=False, default=False)  # Premium feature
    is_active = Column(Boolean, nullable=False, default=True)
    allows_auto_renewal = Column(Boolean, nullable=False, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    user_subscriptions = relationship("UserSubscription", back_populates="plan")
    users = relationship("User", back_populates="current_plan")

    @property
    def price_display(self) -> str:
        """Format price for display (e.g., 'R$ 19,90')"""
        if self.price_cents == 0:
            return "Gratuito"
        return f"R$ {self.price_cents / 100:.2f}".replace(".", ",")

    @property
    def is_free(self) -> bool:
        return self.price_cents == 0

    @property
    def plants_limit_display(self) -> str:
        return "Ilimitado" if self.max_plants is None else str(self.max_plants)

    @property
    def photos_per_plant_limit_display(self) -> str:
        return "Ilimitado" if self.max_photos_per_plant is None else str(self.max_photos_per_plant)


class UserSubscription(Base):
    __tablename__ = "user_subscriptions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    plan_id = Column(UUID(as_uuid=True), ForeignKey("subscription_plans.id"), nullable=False)
    status = Column(Enum(SubscriptionStatus), nullable=False, default=SubscriptionStatus.active)
    asaas_subscription_id = Column(String(255), nullable=True)  # ID da assinatura no ASAAS
    billing_cycle = Column(Enum(BillingCycle), nullable=False, default=BillingCycle.monthly)  # Ciclo real da assinatura
    started_at = Column(DateTime(timezone=True), nullable=False, default=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=True)
    cancelled_at = Column(DateTime(timezone=True), nullable=True)
    auto_renew = Column(Boolean, nullable=False, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="subscriptions")
    plan = relationship("SubscriptionPlan", back_populates="user_subscriptions")
    payments = relationship("Payment", back_populates="subscription")

    @property
    def is_active(self) -> bool:
        # Considerar ativo se está em um dos status que permitem acesso
        active_statuses = [
            SubscriptionStatus.active,
            SubscriptionStatus.cancelled_pending_expiration,
            SubscriptionStatus.expired_grace_period
        ]

        if self.status not in active_statuses:
            return False

        if self.expires_at:
            # Ensure both datetimes are timezone-aware
            now = datetime.now(self.expires_at.tzinfo) if self.expires_at.tzinfo else datetime.utcnow()

            # Se está em período de graça, permitir até 15 dias após expiração
            if self.status == SubscriptionStatus.expired_grace_period:
                grace_period_end = self.expires_at + timedelta(days=15)
                return now <= grace_period_end

            # Para outros status, verificar se não expirou
            if self.expires_at < now:
                return False

        return True

    @property
    def days_until_expiry(self) -> int:
        if not self.expires_at:
            return 999999  # Never expires
        # Ensure both datetimes are timezone-aware
        now = datetime.now(self.expires_at.tzinfo) if self.expires_at.tzinfo else datetime.utcnow()
        delta = self.expires_at - now
        return max(0, delta.days)

    @property
    def grace_period_days_remaining(self) -> int:
        """Calcular dias restantes do período de graça (15 dias após expiração)"""
        if self.status != SubscriptionStatus.expired_grace_period or not self.expires_at:
            return 0

        # Ensure both datetimes are timezone-aware
        now = datetime.now(self.expires_at.tzinfo) if self.expires_at.tzinfo else datetime.utcnow()
        grace_period_end = self.expires_at + timedelta(days=15)
        delta = grace_period_end - now
        return max(0, delta.days)

    @property
    def is_in_grace_period(self) -> bool:
        """Verificar se está em período de graça"""
        return (
            self.status == SubscriptionStatus.expired_grace_period and
            self.grace_period_days_remaining > 0
        )


class Payment(Base):
    __tablename__ = "payments"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    subscription_id = Column(UUID(as_uuid=True), ForeignKey("user_subscriptions.id"), nullable=True)
    amount_cents = Column(Integer, nullable=False)
    currency = Column(String(3), nullable=False, default="BRL")
    status = Column(Enum(PaymentStatus), nullable=False, default=PaymentStatus.pending)
    payment_method = Column(Enum(PaymentMethod), nullable=True)
    external_payment_id = Column(String(255), nullable=True)
    payment_data = Column(JSONB, nullable=True)
    processed_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="payments")
    subscription = relationship("UserSubscription", back_populates="payments")

    @property
    def amount_display(self) -> str:
        """Format amount for display"""
        return f"R$ {self.amount_cents / 100:.2f}".replace(".", ",")


class UsageTracking(Base):
    __tablename__ = "usage_tracking"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    resource_type = Column(String(50), nullable=False)  # plants, photos, care_photos
    resource_id = Column(UUID(as_uuid=True), nullable=True)
    action = Column(String(20), nullable=False)  # created, deleted
    tracked_at = Column(DateTime(timezone=True), server_default=func.now())
    tracking_metadata = Column("metadata", JSONB, nullable=True)

    # Relationships
    user = relationship("User", back_populates="usage_tracking")
