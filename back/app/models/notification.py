from sqlalchemy import Column, String, Text, Boolean, DateTime, ForeignKey, Enum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
import enum

from app.db.base import Base


class NotificationType(str, enum.Enum):
    reminder = "reminder"
    new_follower = "new_follower"
    plant_like = "plant_like"
    plant_comment = "plant_comment"
    subscription_renewal = "subscription_renewal"
    subscription_expiring = "subscription_expiring"
    subscription_expired = "subscription_expired"
    payment_success = "payment_success"
    payment_failed = "payment_failed"


class Notification(Base):
    __tablename__ = "notifications"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    type = Column(Enum(NotificationType), nullable=False)
    title = Column(String(255), nullable=False)
    message = Column(Text, nullable=False)
    is_read = Column(<PERSON>olean, default=False, nullable=False)
    
    # Dados específicos da notificação (JSON)
    data = Column(JSONB, nullable=True)
    
    # Para notificações de lembrete
    reminder_id = Column(UUID(as_uuid=True), ForeignKey("reminders.id"), nullable=True)
    
    # Para notificações de seguidor
    follower_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # Para notificações de curtidas e comentários
    plant_id = Column(UUID(as_uuid=True), ForeignKey("plants.id"), nullable=True)
    like_id = Column(UUID(as_uuid=True), ForeignKey("plant_likes.id"), nullable=True)
    comment_id = Column(UUID(as_uuid=True), ForeignKey("plant_comments.id"), nullable=True)

    # Para notificações de assinatura
    subscription_id = Column(UUID(as_uuid=True), ForeignKey("user_subscriptions.id"), nullable=True)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    read_at = Column(DateTime(timezone=True), nullable=True)
    seen_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    user = relationship("User", foreign_keys=[user_id], back_populates="notifications")
    follower = relationship("User", foreign_keys=[follower_id])
    plant = relationship("Plant")
    like = relationship("PlantLike")
    comment = relationship("PlantComment")
    reminder = relationship("Reminder")
    subscription = relationship("UserSubscription")
