from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String, DateTime, Text, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base import Base
import uuid


class User(Base):
    __tablename__ = "users"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    first_name = Column(String, nullable=False)
    last_name = Column(String, nullable=False)
    hashed_password = Column(String, nullable=False)
    bio = Column(Text, nullable=True)
    avatar = Column(String, nullable=True)
    city = Column(String, nullable=True)
    state = Column(String, nullable=True)
    country = Column(String, nullable=True)

    # Documentos para pagamento
    cpf_cnpj = Column(String(20), nullable=True, index=True)
    document_type = Column(String(10), nullable=True)  # 'CPF' or 'CNPJ'

    # Social fields
    username = Column(String(50), nullable=True, unique=True, index=True)
    is_public = Column(Boolean, default=True, nullable=False)  # Perfil público
    allow_care_sharing = Column(Boolean, default=True, nullable=False)  # Permitir ver cuidados
    allow_search = Column(Boolean, default=True, nullable=False)  # Aparecer na busca
    followers_count = Column(Integer, default=0, nullable=False)
    following_count = Column(Integer, default=0, nullable=False)

    is_active = Column(Boolean, default=False)  # Usuário inativo até ativar por email
    email_verified = Column(Boolean, default=False)  # Verificação de email
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Subscription fields
    current_plan_id = Column(UUID(as_uuid=True), ForeignKey("subscription_plans.id"), nullable=True)
    subscription_status = Column(String(20), default="free")
    subscription_expires_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    plants = relationship("Plant", back_populates="owner", cascade="all, delete-orphan")
    current_plan = relationship("SubscriptionPlan", back_populates="users")
    subscriptions = relationship("UserSubscription", back_populates="user", cascade="all, delete-orphan")
    payments = relationship("Payment", back_populates="user", cascade="all, delete-orphan")
    usage_tracking = relationship("UsageTracking", back_populates="user", cascade="all, delete-orphan")
    social_accounts = relationship("SocialAccount", back_populates="user", cascade="all, delete-orphan")
    auth_tokens = relationship("AuthToken", back_populates="user", cascade="all, delete-orphan")
    notifications = relationship("Notification", foreign_keys="Notification.user_id", back_populates="user", cascade="all, delete-orphan")

    # Social relationships
    following_relationships = relationship("UserFollow", foreign_keys="UserFollow.follower_id", back_populates="follower", cascade="all, delete-orphan")
    follower_relationships = relationship("UserFollow", foreign_keys="UserFollow.followed_id", back_populates="followed", cascade="all, delete-orphan")
    blocking_relationships = relationship("UserBlock", foreign_keys="UserBlock.blocker_id", back_populates="blocker", cascade="all, delete-orphan")
    blocked_relationships = relationship("UserBlock", foreign_keys="UserBlock.blocked_id", back_populates="blocked", cascade="all, delete-orphan")

    @property
    def full_name(self) -> str:
        return f"{self.first_name} {self.last_name}".strip()
