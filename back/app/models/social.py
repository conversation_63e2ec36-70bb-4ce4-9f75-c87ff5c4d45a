from sqlalchemy import <PERSON>um<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateTime, ForeignKey, UniqueConstraint, Text, Enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
import enum

from app.db.base import Base


class UserFollow(Base):
    """Model for user follow relationships"""
    __tablename__ = "user_follows"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    follower_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    followed_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    follower = relationship("User", foreign_keys=[follower_id], back_populates="following_relationships")
    followed = relationship("User", foreign_keys=[followed_id], back_populates="follower_relationships")

    # Constraints
    __table_args__ = (
        UniqueConstraint('follower_id', 'followed_id', name='unique_follow_relationship'),
    )

    def __repr__(self):
        return f"<UserFollow(follower_id={self.follower_id}, followed_id={self.followed_id})>"


class UserBlock(Base):
    """Model for user block relationships (for future use)"""
    __tablename__ = "user_blocks"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    blocker_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    blocked_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)

    # Relationships
    blocker = relationship("User", foreign_keys=[blocker_id], back_populates="blocking_relationships")
    blocked = relationship("User", foreign_keys=[blocked_id], back_populates="blocked_relationships")

    # Constraints
    __table_args__ = (
        UniqueConstraint('blocker_id', 'blocked_id', name='unique_block_relationship'),
    )

    def __repr__(self):
        return f"<UserBlock(blocker_id={self.blocker_id}, blocked_id={self.blocked_id})>"


class FeedActivityType(str, enum.Enum):
    """Types of activities that appear in the social feed"""
    new_plant = "new_plant"
    plant_care = "plant_care"


class PlantLike(Base):
    """Model for plant likes"""
    __tablename__ = "plant_likes"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    plant_id = Column(UUID(as_uuid=True), ForeignKey("plants.id", ondelete="CASCADE"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)

    # Relationships
    user = relationship("User")
    plant = relationship("Plant")

    # Constraints
    __table_args__ = (
        UniqueConstraint('user_id', 'plant_id', name='unique_plant_like'),
    )

    def __repr__(self):
        return f"<PlantLike(user_id={self.user_id}, plant_id={self.plant_id})>"


class PlantComment(Base):
    """Model for plant comments"""
    __tablename__ = "plant_comments"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    plant_id = Column(UUID(as_uuid=True), ForeignKey("plants.id", ondelete="CASCADE"), nullable=False)
    content = Column(Text, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    user = relationship("User")
    plant = relationship("Plant")

    def __repr__(self):
        return f"<PlantComment(user_id={self.user_id}, plant_id={self.plant_id})>"


class FeedActivity(Base):
    """Model for social feed activities"""
    __tablename__ = "feed_activities"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    activity_type = Column(Enum(FeedActivityType), nullable=False)

    # References to related objects
    plant_id = Column(UUID(as_uuid=True), ForeignKey("plants.id", ondelete="CASCADE"), nullable=True)
    care_id = Column(UUID(as_uuid=True), ForeignKey("cares.id", ondelete="CASCADE"), nullable=True)

    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)

    # Relationships
    user = relationship("User")
    plant = relationship("Plant")
    care = relationship("Care")

    def __repr__(self):
        return f"<FeedActivity(user_id={self.user_id}, activity_type={self.activity_type})>"
