from sqlalchemy import <PERSON><PERSON><PERSON>, Column, String, DateTime, Integer, Float, Text, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base import Base
import uuid
import enum


class CouponType(str, enum.Enum):
    percentage = "percentage"  # Desconto em porcentagem
    fixed = "fixed"  # Desconto fixo em reais


class Coupon(Base):
    __tablename__ = "coupons"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    code = Column(String(50), unique=True, index=True, nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    
    # Tipo e valor do desconto
    coupon_type = Column(String(20), nullable=False)  # 'percentage' ou 'fixed'
    discount_value = Column(Float, nullable=False)  # Valor do desconto
    
    # Limites de uso
    max_uses = Column(Integer, nullable=True)  # Máximo de usos (NULL = ilimitado)
    current_uses = Column(Integer, default=0)  # Usos atuais
    max_uses_per_user = Column(Integer, default=1)  # Máximo por usuário
    
    # Validade
    valid_from = Column(DateTime(timezone=True), nullable=True)
    valid_until = Column(DateTime(timezone=True), nullable=True)
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Planos aplicáveis (NULL = todos os planos)
    applicable_plans = Column(String(500), nullable=True)  # JSON string com IDs dos planos
    
    # Metadados
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Relationships
    creator = relationship("User", foreign_keys=[created_by])
    coupon_uses = relationship("CouponUse", back_populates="coupon", cascade="all, delete-orphan")

    @property
    def is_valid(self) -> bool:
        """Check if coupon is currently valid"""
        from datetime import datetime, timezone
        now = datetime.now(timezone.utc)
        
        if not self.is_active:
            return False
            
        if self.valid_from and now < self.valid_from:
            return False
            
        if self.valid_until and now > self.valid_until:
            return False
            
        if self.max_uses and self.current_uses >= self.max_uses:
            return False
            
        return True

    @property
    def usage_percentage(self) -> float:
        """Get usage percentage"""
        if not self.max_uses:
            return 0.0
        return (self.current_uses / self.max_uses) * 100


class CouponUse(Base):
    __tablename__ = "coupon_uses"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    coupon_id = Column(UUID(as_uuid=True), ForeignKey("coupons.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    subscription_id = Column(UUID(as_uuid=True), ForeignKey("user_subscriptions.id"), nullable=True)
    
    # Valor do desconto aplicado
    discount_applied = Column(Float, nullable=False)
    original_price = Column(Float, nullable=False)
    final_price = Column(Float, nullable=False)

    # Status de confirmação (para casos onde o pagamento ainda não foi processado)
    is_confirmed = Column(Boolean, default=True, nullable=False)

    used_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    coupon = relationship("Coupon", back_populates="coupon_uses")
    user = relationship("User")
    subscription = relationship("UserSubscription")
