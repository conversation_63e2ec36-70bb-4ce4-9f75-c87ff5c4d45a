from typing import List, Union
from pydantic import AnyHttpUrl, validator
from pydantic_settings import BaseSettings
import os


class Settings(BaseSettings):
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-here")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days
    
    # Database
    POSTGRES_SERVER: str = os.getenv("POSTGRES_HOST", "localhost")
    POSTGRES_USER: str = os.getenv("POSTGRES_USER", "postgres")
    POSTGRES_PASSWORD: str = os.getenv("POSTGRES_PASSWORD", "")
    POSTGRES_DB: str = os.getenv("POSTGRES_DB", "meubonsai_db")
    POSTGRES_PORT: str = os.getenv("POSTGRES_PORT", "5432")
    
    @property
    def SQLALCHEMY_DATABASE_URI(self) -> str:
        return f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_SERVER}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"

    # Cloudflare R2
    R2_ACCESS_KEY_ID: str = os.getenv("R2_ACCESS_KEY_ID", "")
    R2_SECRET_ACCESS_KEY: str = os.getenv("R2_SECRET_ACCESS_KEY", "")
    R2_ENDPOINT_URL: str = os.getenv("R2_ENDPOINT_URL", "")
    R2_BUCKET_NAME: str = os.getenv("R2_BUCKET_NAME", "")
    R2_PUBLIC_URL: str = os.getenv("R2_PUBLIC_URL", "")

    # Social Authentication
    GOOGLE_CLIENT_ID: str = os.getenv("GOOGLE_CLIENT_ID", "")
    GOOGLE_CLIENT_SECRET: str = os.getenv("GOOGLE_CLIENT_SECRET", "")
    FACEBOOK_APP_ID: str = os.getenv("FACEBOOK_APP_ID", "")
    FACEBOOK_APP_SECRET: str = os.getenv("FACEBOOK_APP_SECRET", "")
    APPLE_CLIENT_ID: str = os.getenv("APPLE_CLIENT_ID", "")
    APPLE_TEAM_ID: str = os.getenv("APPLE_TEAM_ID", "")
    APPLE_KEY_ID: str = os.getenv("APPLE_KEY_ID", "")
    APPLE_PRIVATE_KEY: str = os.getenv("APPLE_PRIVATE_KEY", "")

    # Email Configuration
    SMTP_SERVER: str = os.getenv("SMTP_SERVER", "")
    SMTP_PORT: int = int(os.getenv("SMTP_PORT", "587"))
    SMTP_USERNAME: str = os.getenv("SMTP_USERNAME", "")
    SMTP_PASSWORD: str = os.getenv("SMTP_PASSWORD", "")
    SMTP_USE_TLS: bool = os.getenv("SMTP_USE_TLS", "true").lower() == "true"
    FROM_EMAIL: str = os.getenv("FROM_EMAIL", "<EMAIL>")
    FRONTEND_URL: str = os.getenv("FRONTEND_URL", "https://meubonsai.app")

    # CORS - Dynamic based on environment
    @property
    def BACKEND_CORS_ORIGINS(self) -> List[str]:
        base_origins = [
            "http://localhost:3000",
            "http://127.0.0.1:3000",
            "https://accounts.google.com",
            "https://www.googleapis.com",
        ]

        # Add environment-specific origins
        if self.ENVIRONMENT == "production":
            base_origins.extend([
                "https://meubonsai.app",
                "https://www.meubonsai.app",
                "https://api.meubonsai.app",
            ])
        else:  # development
            base_origins.extend([
                "https://dev.meubonsai.app",
                "https://api-dev.meubonsai.app",
                "https://meubonsai.app",  # Keep prod for testing
                "https://www.meubonsai.app",  # Keep prod for testing
                "https://api.meubonsai.app",  # Keep prod for testing
            ])

        # Always include both dev and prod origins for safety
        all_origins = list(set(base_origins + [
            "https://meubonsai.app",
            "https://www.meubonsai.app",
            "https://dev.meubonsai.app",
            "https://api-dev.meubonsai.app",
            "https://api.meubonsai.app",
        ]))

        return all_origins



    # Asaas Payment Gateway
    ASAAS_WALLET_ID_SANDBOX: str = os.getenv("ASAAS_WALLET_ID_SANDBOX", "")
    ASAAS_API_TOKEN_SANDBOX: str = os.getenv("ASAAS_API_TOKEN_SANDBOX", "")
    ASAAS_WALLET_ID_PRODUCTION: str = os.getenv("ASAAS_WALLET_ID_PRODUCTION", "")
    ASAAS_API_TOKEN_PRODUCTION: str = os.getenv("ASAAS_API_TOKEN_PRODUCTION", "")

    # Environment - Auto-detect based on container name or explicit setting
    @property
    def ENVIRONMENT(self) -> str:
        # Check explicit environment variable first
        env = os.getenv("ENVIRONMENT", "").lower()
        if env in ["production", "prod"]:
            return "production"
        elif env in ["development", "dev"]:
            return "development"

        # Auto-detect based on container name or hostname
        import socket
        hostname = socket.gethostname()
        if "prod" in hostname or "production" in hostname:
            return "production"
        elif "dev" in hostname or "development" in hostname:
            return "development"

        # Default to development for safety
        return "development"

    class Config:
        case_sensitive = True


settings = Settings()
