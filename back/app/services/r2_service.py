import boto3
import io
import uuid
from PIL import Image
from typing import <PERSON>ple, Optional
from botocore.exceptions import ClientError
from app.core.config import settings


class R2Service:
    def __init__(self):
        self.client = boto3.client(
            's3',
            endpoint_url=settings.R2_ENDPOINT_URL,
            aws_access_key_id=settings.R2_ACCESS_KEY_ID,
            aws_secret_access_key=settings.R2_SECRET_ACCESS_KEY,
            region_name='auto'  # R2 uses 'auto' as region
        )
        self.bucket_name = settings.R2_BUCKET_NAME
        self.public_url = settings.R2_PUBLIC_URL

    def _resize_image(self, image_data: bytes, max_size: Tuple[int, int], quality: int = 85, skip_auto_rotation: bool = False) -> bytes:
        """
        Resize image maintaining aspect ratio and optimize for web
        """
        try:
            # Open image and disable automatic EXIF orientation
            image = Image.open(io.BytesIO(image_data))

            # Remove EXIF data to prevent auto-rotation
            image = image.copy()

            # Check if <PERSON><PERSON> auto-rotated and fix it (only for new uploads, not manual rotations)
            if not skip_auto_rotation:
                # Frontend sends portrait photos as 3000x4000, but <PERSON><PERSON> might rotate to 4000x3000
                if image.width > image.height and image.width / image.height > 1.2:
                    # Likely auto-rotated, revert it
                    image = image.rotate(-90, expand=True)

            # Convert to RGB if necessary (handles RGBA, P, etc.)
            if image.mode in ('RGBA', 'LA', 'P'):
                # Create white background for transparency
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'P':
                    image = image.convert('RGBA')
                background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                image = background
            elif image.mode != 'RGB':
                image = image.convert('RGB')

            # Calculate new size maintaining aspect ratio
            image.thumbnail(max_size, Image.Resampling.LANCZOS)

            # Save optimized image WITHOUT EXIF to prevent orientation issues
            output = io.BytesIO()
            image.save(output, format='JPEG', quality=quality, optimize=True, exif=b'')
            output.seek(0)

            return output.getvalue()
        except Exception as e:
            print(f"Error resizing image: {e}")
            return image_data  # Return original if resize fails

    def upload_image(self, image_data: bytes, filename: str, plant_id: str, subfolder: str = None) -> dict:
        """
        Upload image in multiple sizes to R2
        Returns dict with URLs for different sizes
        """
        try:
            # Generate unique filename
            file_extension = filename.split('.')[-1].lower()
            if file_extension not in ['jpg', 'jpeg', 'png', 'webp']:
                file_extension = 'jpg'
            
            unique_filename = f"{uuid.uuid4().hex}.{file_extension}"
            
            # Create different sizes
            sizes = {
                'thumbnail': (150, 150),    # Small thumbnails
                'medium': (800, 600),       # Gallery view
                'large': (1200, 900),       # Detail view
                'original': None            # Keep original (but optimized)
            }
            
            uploaded_urls = {}
            
            for size_name, dimensions in sizes.items():
                if dimensions:
                    # Resize image
                    resized_data = self._resize_image(image_data, dimensions)
                else:
                    # Original size but optimized
                    resized_data = self._resize_image(image_data, (2000, 2000), quality=90)
                
                # Create S3 key with optional subfolder
                if subfolder:
                    s3_key = f"plants/{plant_id}/{subfolder}/{size_name}_{unique_filename}"
                else:
                    s3_key = f"plants/{plant_id}/{size_name}_{unique_filename}"
                
                # Upload to R2
                self.client.put_object(
                    Bucket=self.bucket_name,
                    Key=s3_key,
                    Body=resized_data,
                    ContentType='image/jpeg',
                    CacheControl='public, max-age=31536000',  # 1 year cache
                )
                
                # Store public URL
                uploaded_urls[size_name] = f"{self.public_url}/{s3_key}"
            
            return {
                'success': True,
                'urls': uploaded_urls,
                'filename': unique_filename
            }
            
        except ClientError as e:
            print(f"Error uploading to R2: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def upload_image_no_auto_rotation(self, image_data: bytes, filename: str, plant_id: str, subfolder: str = None) -> dict:
        """
        Upload image in multiple sizes to R2 WITHOUT automatic rotation correction
        Used for manual rotations to avoid double rotation
        """
        try:
            # Generate unique filename
            file_extension = filename.split('.')[-1].lower()
            if file_extension not in ['jpg', 'jpeg', 'png', 'webp']:
                file_extension = 'jpg'

            unique_filename = f"{uuid.uuid4().hex}.{file_extension}"

            # Create different sizes
            sizes = {
                'thumbnail': (150, 150),    # Small thumbnails
                'medium': (800, 600),       # Gallery view
                'large': (1200, 900),       # Detail view
                'original': None            # Keep original (but optimized)
            }

            uploaded_urls = {}

            for size_name, dimensions in sizes.items():
                if dimensions:
                    # Resize image WITHOUT auto-rotation
                    resized_data = self._resize_image(image_data, dimensions, skip_auto_rotation=True)
                else:
                    # Original size but optimized WITHOUT auto-rotation
                    resized_data = self._resize_image(image_data, (2000, 2000), quality=90, skip_auto_rotation=True)

                # Create S3 key with optional subfolder
                if subfolder:
                    s3_key = f"plants/{plant_id}/{subfolder}/{size_name}_{unique_filename}"
                else:
                    s3_key = f"plants/{plant_id}/{size_name}_{unique_filename}"

                # Upload to R2
                self.client.put_object(
                    Bucket=self.bucket_name,
                    Key=s3_key,
                    Body=resized_data,
                    ContentType='image/jpeg',
                    CacheControl='public, max-age=31536000',  # 1 year cache
                )

                # Store public URL
                uploaded_urls[size_name] = f"{self.public_url}/{s3_key}"

            return {
                'success': True,
                'urls': uploaded_urls,
                'filename': unique_filename
            }

        except ClientError as e:
            print(f"Error uploading to R2: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def upload_avatar(self, image_data: bytes, filename: str, user_id: str) -> dict:
        """
        Upload avatar in low resolution to save space
        Returns dict with URLs for different sizes
        """
        try:
            # Generate unique filename
            file_extension = filename.split('.')[-1].lower()
            if file_extension not in ['jpg', 'jpeg', 'png', 'webp']:
                file_extension = 'jpg'

            unique_filename = f"{uuid.uuid4().hex}.{file_extension}"

            # Create avatar sizes (smaller for profile pics)
            sizes = {
                'small': (150, 150),     # Profile display
                'medium': (300, 300),    # Larger profile view
            }

            uploaded_urls = {}

            for size_name, dimensions in sizes.items():
                # Resize image
                resized_data = self._resize_image(image_data, dimensions, quality=85)

                # Create S3 key
                s3_key = f"avatars/{user_id}/{size_name}_{unique_filename}"

                # Upload to R2
                self.client.put_object(
                    Bucket=self.bucket_name,
                    Key=s3_key,
                    Body=resized_data,
                    ContentType='image/jpeg',
                    CacheControl='public, max-age=31536000',  # 1 year cache
                )

                # Store public URL
                uploaded_urls[size_name] = f"{self.public_url}/{s3_key}"

            return {
                'success': True,
                'urls': uploaded_urls,
                'filename': unique_filename
            }

        except ClientError as e:
            print(f"Error uploading avatar to R2: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def delete_avatar(self, user_id: str, filename: str) -> bool:
        """
        Delete all sizes of an avatar from R2
        """
        try:
            sizes = ['small', 'medium']

            for size in sizes:
                s3_key = f"avatars/{user_id}/{size}_{filename}"
                try:
                    self.client.delete_object(
                        Bucket=self.bucket_name,
                        Key=s3_key
                    )
                except ClientError:
                    # Continue even if some files don't exist
                    pass

            return True

        except Exception as e:
            print(f"Error deleting avatar from R2: {e}")
            return False

    def delete_image(self, plant_id: str, filename: str, subfolder: str = None) -> bool:
        """
        Delete all sizes of an image from R2
        """
        try:
            sizes = ['thumbnail', 'medium', 'large', 'original']

            for size in sizes:
                if subfolder:
                    s3_key = f"plants/{plant_id}/{subfolder}/{size}_{filename}"
                else:
                    s3_key = f"plants/{plant_id}/{size}_{filename}"
                try:
                    self.client.delete_object(
                        Bucket=self.bucket_name,
                        Key=s3_key
                    )
                except ClientError:
                    # Continue even if some files don't exist
                    pass

            return True

        except Exception as e:
            print(f"Error deleting from R2: {e}")
            return False

    def delete_plant_images(self, plant_id: str) -> bool:
        """
        Delete all images and videos for a plant (including care images and videos)
        """
        try:
            # List all objects with the plant_id prefix
            response = self.client.list_objects_v2(
                Bucket=self.bucket_name,
                Prefix=f"plants/{plant_id}/"
            )

            if 'Contents' in response:
                # Delete all objects
                objects_to_delete = [{'Key': obj['Key']} for obj in response['Contents']]

                if objects_to_delete:
                    self.client.delete_objects(
                        Bucket=self.bucket_name,
                        Delete={'Objects': objects_to_delete}
                    )
                    print(f"Deleted {len(objects_to_delete)} images for plant {plant_id}")

            return True

        except Exception as e:
            print(f"Error deleting plant images from R2: {e}")
            return False

    def get_image_url(self, plant_id: str, filename: str, size: str = 'medium') -> Optional[str]:
        """
        Get public URL for an image
        """
        if not filename:
            return None
            
        s3_key = f"plants/{plant_id}/{size}_{filename}"
        return f"{self.public_url}/{s3_key}"

    def rotate_image(self, plant_id: str, filename: str, rotation_degrees: int, subfolder: str = None) -> Optional[dict]:
        """
        Rotate an existing image in R2 and re-upload
        """
        try:
            # Download original image from R2
            if subfolder:
                s3_key = f"plants/{plant_id}/{subfolder}/original_{filename}"
            else:
                s3_key = f"plants/{plant_id}/original_{filename}"

            response = self.client.get_object(
                Bucket=self.bucket_name,
                Key=s3_key
            )

            image_data = response['Body'].read()

            # Rotate image
            rotated_data = self._rotate_image_data(image_data, rotation_degrees)

            # Re-upload with new sizes WITHOUT auto-rotation (to avoid double rotation)
            result = self.upload_image_no_auto_rotation(rotated_data, filename, plant_id, subfolder)

            # Delete old image files if upload was successful and filename changed
            if result and result.get('success') and result.get('filename') != filename:
                self.delete_image(plant_id, filename, subfolder)

            return result

        except Exception as e:
            print(f"Error rotating image {filename}: {e}")
            import traceback
            traceback.print_exc()
            return {'success': False, 'error': str(e)}

    def _rotate_image_data(self, image_data: bytes, rotation_degrees: int) -> bytes:
        """
        Rotate image data by specified degrees
        """
        try:
            # Open image
            image = Image.open(io.BytesIO(image_data))

            # Convert to RGB if necessary
            if image.mode in ('RGBA', 'LA', 'P'):
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'P':
                    image = image.convert('RGBA')
                background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                image = background
            elif image.mode != 'RGB':
                image = image.convert('RGB')

            # Rotate image
            rotated_image = image.rotate(-rotation_degrees, expand=True)

            # Save to bytes
            output = io.BytesIO()
            rotated_image.save(output, format='JPEG', quality=95, optimize=True)
            output.seek(0)

            return output.getvalue()

        except Exception as e:
            print(f"Error rotating image data: {e}")
            return image_data

    def migrate_local_image(self, local_path: str, plant_id: str, original_filename: str) -> Optional[dict]:
        """
        Migrate an existing local image to R2
        """
        try:
            with open(local_path, 'rb') as f:
                image_data = f.read()

            return self.upload_image(image_data, original_filename, plant_id)

        except Exception as e:
            print(f"Error migrating image {local_path}: {e}")
            return None

    def upload_video(self, video_data: bytes, filename: str, plant_id: str, subfolder: str = None) -> dict:
        """
        Upload video to R2
        Returns dict with success status and filename
        """
        try:
            # Generate unique filename
            file_extension = filename.split('.')[-1].lower()
            if file_extension not in ['mp4', 'mov', 'avi', 'mkv', 'webm', 'm4v']:
                file_extension = 'mp4'

            unique_filename = f"{uuid.uuid4().hex}.{file_extension}"

            # Create S3 key with optional subfolder
            if subfolder:
                s3_key = f"plants/{plant_id}/{subfolder}/videos/{unique_filename}"
            else:
                s3_key = f"plants/{plant_id}/videos/{unique_filename}"

            # Determine content type
            content_type_map = {
                'mp4': 'video/mp4',
                'mov': 'video/quicktime',
                'avi': 'video/x-msvideo',
                'mkv': 'video/x-matroska',
                'webm': 'video/webm',
                'm4v': 'video/x-m4v'
            }
            content_type = content_type_map.get(file_extension, 'video/mp4')

            # Upload to R2
            self.client.put_object(
                Bucket=self.bucket_name,
                Key=s3_key,
                Body=video_data,
                ContentType=content_type,
                CacheControl='public, max-age=31536000',  # 1 year cache
            )

            # Store public URL
            video_url = f"{self.public_url}/{s3_key}"

            return {
                'success': True,
                'url': video_url,
                'filename': unique_filename
            }

        except ClientError as e:
            print(f"Error uploading video to R2: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def upload_thumbnail(self, thumbnail_data: bytes, filename: str, plant_id: str, subfolder: str = None) -> dict:
        """
        Upload video thumbnail to R2
        Returns dict with success status and filename
        """
        try:
            # Generate unique filename for thumbnail
            base_name = filename.rsplit('.', 1)[0]  # Remove extension
            unique_filename = f"{base_name}_thumb.jpg"

            # Create S3 key with optional subfolder
            if subfolder:
                s3_key = f"plants/{plant_id}/{subfolder}/thumbnails/{unique_filename}"
            else:
                s3_key = f"plants/{plant_id}/thumbnails/{unique_filename}"

            # Upload to R2
            self.client.put_object(
                Bucket=self.bucket_name,
                Key=s3_key,
                Body=thumbnail_data,
                ContentType='image/jpeg',
                CacheControl='public, max-age=31536000',  # 1 year cache
            )

            # Store public URL
            thumbnail_url = f"{self.public_url}/{s3_key}"

            return {
                'success': True,
                'url': thumbnail_url,
                'filename': unique_filename
            }

        except ClientError as e:
            print(f"Error uploading thumbnail to R2: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def delete_video(self, plant_id: str, filename: str, subfolder: str = None) -> bool:
        """
        Delete video from R2
        """
        try:
            if subfolder:
                s3_key = f"plants/{plant_id}/{subfolder}/videos/{filename}"
            else:
                s3_key = f"plants/{plant_id}/videos/{filename}"

            self.client.delete_object(
                Bucket=self.bucket_name,
                Key=s3_key
            )

            return True

        except Exception as e:
            print(f"Error deleting video from R2: {e}")
            return False

    def delete_thumbnail(self, plant_id: str, filename: str, subfolder: str = None) -> bool:
        """
        Delete thumbnail from R2
        """
        try:
            if subfolder:
                s3_key = f"plants/{plant_id}/{subfolder}/thumbnails/{filename}"
            else:
                s3_key = f"plants/{plant_id}/thumbnails/{filename}"

            self.client.delete_object(
                Bucket=self.bucket_name,
                Key=s3_key
            )

            return True

        except Exception as e:
            print(f"Error deleting thumbnail from R2: {e}")
            return False

    def get_video_url(self, plant_id: str, filename: str, subfolder: str = None) -> Optional[str]:
        """
        Get public URL for a video
        """
        if not filename:
            return None

        if subfolder:
            s3_key = f"plants/{plant_id}/{subfolder}/videos/{filename}"
        else:
            s3_key = f"plants/{plant_id}/videos/{filename}"

        return f"{self.public_url}/{s3_key}"


# Global instance
r2_service = R2Service()
