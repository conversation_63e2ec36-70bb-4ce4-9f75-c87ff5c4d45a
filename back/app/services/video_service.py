import os
import mimetypes
from typing import List, Optional, <PERSON><PERSON>
from fastapi import HTTPEx<PERSON>, UploadFile
import logging

logger = logging.getLogger(__name__)

# Configurações de vídeo
ALLOWED_VIDEO_EXTENSIONS = {'.mp4', '.mov', '.avi', '.mkv', '.webm', '.m4v'}
ALLOWED_VIDEO_MIMETYPES = {
    'video/mp4',
    'video/quicktime',
    'video/x-msvideo',
    'video/x-matroska',
    'video/webm',
    'video/x-m4v'
}

# Limites de tamanho (em bytes)
MAX_VIDEO_SIZE = 100 * 1024 * 1024  # 100MB
MAX_VIDEO_DURATION = 300  # 5 minutos em segundos


class VideoValidationService:
    """Serviço para validação de arquivos de vídeo"""
    
    @staticmethod
    def validate_video_file(file: UploadFile) -> Tuple[bool, Optional[str]]:
        """
        Valida se o arquivo é um vídeo válido
        
        Returns:
            Tuple[bool, Optional[str]]: (is_valid, error_message)
        """
        try:
            # 1. Verificar se o arquivo foi enviado
            if not file or not file.filename:
                return False, "Nenhum arquivo foi enviado"
            
            # 2. Verificar extensão do arquivo
            file_extension = os.path.splitext(file.filename.lower())[1]
            if file_extension not in ALLOWED_VIDEO_EXTENSIONS:
                allowed_exts = ', '.join(ALLOWED_VIDEO_EXTENSIONS)
                return False, f"Formato de vídeo não suportado. Formatos aceitos: {allowed_exts}"
            
            # 3. Verificar MIME type
            if file.content_type and file.content_type not in ALLOWED_VIDEO_MIMETYPES:
                return False, f"Tipo de arquivo não suportado: {file.content_type}"
            
            # 4. Verificar tamanho do arquivo
            if hasattr(file, 'size') and file.size:
                if file.size > MAX_VIDEO_SIZE:
                    size_mb = MAX_VIDEO_SIZE / (1024 * 1024)
                    return False, f"Arquivo muito grande. Tamanho máximo: {size_mb:.0f}MB"
            
            return True, None
            
        except Exception as e:
            logger.error(f"Erro na validação do vídeo: {str(e)}")
            return False, "Erro interno na validação do arquivo"
    
    @staticmethod
    def get_video_info(file_path: str) -> dict:
        """
        Extrai informações do vídeo (duração, tamanho, etc.)
        
        Args:
            file_path: Caminho para o arquivo de vídeo
            
        Returns:
            dict: Informações do vídeo
        """
        try:
            # Por enquanto, retorna informações básicas
            # Em uma implementação completa, poderia usar ffprobe ou similar
            file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
            
            return {
                'duration_seconds': None,  # Seria extraído com ffprobe
                'file_size_bytes': file_size,
                'width': None,  # Seria extraído com ffprobe
                'height': None,  # Seria extraído com ffprobe
                'format': None  # Seria extraído com ffprobe
            }
            
        except Exception as e:
            logger.error(f"Erro ao extrair informações do vídeo: {str(e)}")
            return {
                'duration_seconds': None,
                'file_size_bytes': 0,
                'width': None,
                'height': None,
                'format': None
            }
    
    @staticmethod
    def generate_video_filename(original_filename: str, plant_id: str, video_type: str = "plant") -> str:
        """
        Gera um nome único para o arquivo de vídeo
        
        Args:
            original_filename: Nome original do arquivo
            plant_id: ID da planta
            video_type: Tipo do vídeo ("plant" ou "care")
            
        Returns:
            str: Nome único do arquivo
        """
        import uuid
        from datetime import datetime
        
        # Extrair extensão
        file_extension = os.path.splitext(original_filename.lower())[1]
        
        # Gerar nome único
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        
        return f"{video_type}_{plant_id}_{timestamp}_{unique_id}{file_extension}"


class VideoLimitService:
    """Serviço para verificação de limites de vídeo por plano"""
    
    @staticmethod
    def check_video_limits(user_plan: dict, current_video_count: int, video_type: str = "plant") -> Tuple[bool, Optional[str]]:
        """
        Verifica se o usuário pode fazer upload de mais vídeos
        
        Args:
            user_plan: Informações do plano do usuário
            current_video_count: Quantidade atual de vídeos
            video_type: Tipo do vídeo ("plant" ou "care")
            
        Returns:
            Tuple[bool, Optional[str]]: (can_upload, error_message)
        """
        try:
            # 1. Verificar se o plano permite vídeos
            if not user_plan.get('allows_videos', False):
                return False, "Upload de vídeos é exclusivo do plano Premium. Faça upgrade para acessar esta funcionalidade."
            
            # 2. Verificar limites específicos
            if video_type == "plant":
                max_videos = user_plan.get('max_videos_per_plant')
                if max_videos is not None and current_video_count >= max_videos:
                    return False, f"Limite de {max_videos} vídeos por planta atingido."
            
            elif video_type == "care":
                max_videos = user_plan.get('max_videos_per_care', 0)
                if current_video_count >= max_videos:
                    return False, f"Limite de {max_videos} vídeos por cuidado atingido."
            
            return True, None
            
        except Exception as e:
            logger.error(f"Erro na verificação de limites: {str(e)}")
            return False, "Erro interno na verificação de limites"
    
    @staticmethod
    def check_care_media_limit(current_media_count: int, max_items_per_care: int = 10) -> Tuple[bool, Optional[str]]:
        """
        Verifica o limite total de mídia (fotos + vídeos) por cuidado
        
        Args:
            current_media_count: Quantidade atual de mídia (fotos + vídeos)
            max_items_per_care: Limite máximo de itens por cuidado
            
        Returns:
            Tuple[bool, Optional[str]]: (can_upload, error_message)
        """
        if current_media_count >= max_items_per_care:
            return False, f"Limite de {max_items_per_care} itens (fotos + vídeos) por cuidado atingido."
        
        return True, None
