import httpx
import jwt
from typing import Optional, Dict, Any
from google.auth.transport import requests
from google.oauth2 import id_token
from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from app.core.config import settings
from app.models.user import User
from app.models.social_auth import SocialAccount, SocialProvider
from app.schemas.social_auth import SocialUserInfo
from app.crud.crud_user import user as user_crud
from app.core.security import create_access_token
from app.schemas.user import UserCreate


class SocialAuthService:
    """Serviço para autenticação social com Google e Apple"""

    @staticmethod
    async def verify_google_token(id_token_str: str = None, access_token: str = None) -> SocialUserInfo:
        """Verifica token do Google e retorna informações do usuário"""
        try:
            if id_token_str:
                # Verifica o token ID do Google
                idinfo = id_token.verify_oauth2_token(
                    id_token_str,
                    requests.Request(),
                    settings.GOOGLE_CLIENT_ID
                )

                # Verifica se o token é válido
                if idinfo['iss'] not in ['accounts.google.com', 'https://accounts.google.com']:
                    raise ValueError('Wrong issuer.')

                return SocialUserInfo(
                    provider=SocialProvider.google,
                    provider_user_id=idinfo['sub'],
                    email=idinfo.get('email'),
                    first_name=idinfo.get('given_name'),
                    last_name=idinfo.get('family_name'),
                    name=idinfo.get('name'),
                    picture=idinfo.get('picture'),
                    verified_email=idinfo.get('email_verified', False)
                )

            elif access_token:
                # Usa access token para buscar informações do usuário
                async with httpx.AsyncClient() as client:
                    response = await client.get(
                        f"https://www.googleapis.com/oauth2/v2/userinfo?access_token={access_token}"
                    )

                    if response.status_code != 200:
                        raise ValueError(f"Invalid access token: {response.status_code}")

                    userinfo = response.json()

                    return SocialUserInfo(
                        provider=SocialProvider.google,
                        provider_user_id=userinfo['id'],
                        email=userinfo.get('email'),
                        first_name=userinfo.get('given_name'),
                        last_name=userinfo.get('family_name'),
                        name=userinfo.get('name'),
                        picture=userinfo.get('picture'),
                        verified_email=userinfo.get('verified_email', False)
                    )
            else:
                raise ValueError("Either id_token or access_token must be provided")

        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid Google token: {str(e)}"
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error verifying Google token: {str(e)}"
            )

    @staticmethod
    async def verify_facebook_token(access_token: str) -> SocialUserInfo:
        """Verifica token do Facebook e retorna informações do usuário"""
        try:
            # Verifica o token com o Facebook Graph API
            async with httpx.AsyncClient() as client:
                # Primeiro, verifica se o token é válido
                verify_response = await client.get(
                    f"https://graph.facebook.com/debug_token",
                    params={
                        "input_token": access_token,
                        "access_token": f"{settings.FACEBOOK_APP_ID}|{settings.FACEBOOK_APP_SECRET}"
                    }
                )

                if verify_response.status_code != 200:
                    raise ValueError(f"Invalid Facebook token: {verify_response.status_code}")

                verify_data = verify_response.json()
                if not verify_data.get("data", {}).get("is_valid"):
                    raise ValueError("Facebook token is not valid")

                # Busca informações do usuário
                user_response = await client.get(
                    f"https://graph.facebook.com/me",
                    params={
                        "access_token": access_token,
                        "fields": "id,email,first_name,last_name,name,picture.type(large)"
                    }
                )

                if user_response.status_code != 200:
                    raise ValueError(f"Failed to get Facebook user info: {user_response.status_code}")

                userinfo = user_response.json()

                return SocialUserInfo(
                    provider=SocialProvider.facebook,
                    provider_user_id=userinfo['id'],
                    email=userinfo.get('email'),
                    first_name=userinfo.get('first_name'),
                    last_name=userinfo.get('last_name'),
                    name=userinfo.get('name'),
                    picture=userinfo.get('picture', {}).get('data', {}).get('url'),
                    verified_email=True  # Facebook emails são considerados verificados
                )

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid Facebook token: {str(e)}"
            )

    @staticmethod
    async def verify_apple_token(id_token_str: str, user_info: Optional[Dict] = None) -> SocialUserInfo:
        """Verifica token da Apple e retorna informações do usuário"""
        try:
            # Decodifica o token sem verificação para obter o header
            unverified_header = jwt.get_unverified_header(id_token_str)
            
            # Busca as chaves públicas da Apple
            async with httpx.AsyncClient() as client:
                response = await client.get("https://appleid.apple.com/auth/keys")
                apple_keys = response.json()

            # Encontra a chave correta
            key = None
            for apple_key in apple_keys['keys']:
                if apple_key['kid'] == unverified_header['kid']:
                    key = jwt.algorithms.RSAAlgorithm.from_jwk(apple_key)
                    break

            if not key:
                raise ValueError("Unable to find appropriate key")

            # Verifica o token
            payload = jwt.decode(
                id_token_str,
                key,
                algorithms=['RS256'],
                audience=settings.APPLE_CLIENT_ID,
                issuer='https://appleid.apple.com'
            )

            # Extrai informações do usuário
            email = payload.get('email')
            name = None
            first_name = None
            last_name = None

            # Apple só envia informações do usuário na primeira vez
            if user_info:
                name_info = user_info.get('name', {})
                first_name = name_info.get('firstName')
                last_name = name_info.get('lastName')
                name = f"{first_name} {last_name}".strip() if first_name or last_name else None

            return SocialUserInfo(
                provider=SocialProvider.apple,
                provider_user_id=payload['sub'],
                email=email,
                first_name=first_name,
                last_name=last_name,
                name=name,
                verified_email=payload.get('email_verified', False) if email else False
            )

        except jwt.InvalidTokenError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid Apple token: {str(e)}"
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error verifying Apple token: {str(e)}"
            )

    @staticmethod
    def find_or_create_user(db: Session, social_info: SocialUserInfo) -> tuple[User, bool, SocialAccount]:
        """
        Encontra ou cria um usuário baseado nas informações sociais
        Retorna: (user, is_new_user, social_account)
        """
        # Primeiro, verifica se já existe uma conta social para este provedor
        social_account = db.query(SocialAccount).filter(
            SocialAccount.provider == social_info.provider.value,
            SocialAccount.provider_user_id == social_info.provider_user_id
        ).first()

        if social_account:
            # Usuário já existe, atualiza informações se necessário
            if social_info.email and social_account.email != social_info.email:
                social_account.email = social_info.email
            if social_info.name and social_account.name != social_info.name:
                social_account.name = social_info.name
            if social_info.picture and social_account.picture != social_info.picture:
                social_account.picture = social_info.picture
            
            db.commit()
            return social_account.user, False, social_account

        # Se não existe conta social, verifica se existe usuário com o mesmo email
        user = None
        if social_info.email:
            user = user_crud.get_by_email(db, email=social_info.email)

        # Se não existe usuário, cria um novo
        if not user:
            # Gerar username temporário único para usuário social
            # Será solicitado para definir um username real no primeiro acesso
            temp_username = SocialAuthService._generate_temp_username(db, social_info)

            user_create = UserCreate(
                email=social_info.email or f"{social_info.provider_user_id}@{social_info.provider.value}.local",
                first_name=social_info.first_name or social_info.name or "Usuário",
                last_name=social_info.last_name or "",
                password="social_login_placeholder",  # Senha placeholder para login social
                username=temp_username,  # Username temporário
                is_active=True  # Usuários sociais são ativados automaticamente
            )

            user = user_crud.create(db, obj_in=user_create)

            # Configurar plano gratuito para novos usuários sociais
            from app.models.subscription import SubscriptionPlan
            free_plan = db.query(SubscriptionPlan).filter(SubscriptionPlan.name == "free").first()
            if free_plan:
                user.current_plan_id = free_plan.id
                user.subscription_status = "active"  # Usuários gratuitos são ativos
                user.email_verified = True  # Login social já verifica email

            # Se tem foto, atualiza o avatar depois da criação
            if social_info.picture:
                user.avatar = social_info.picture

            db.commit()
            db.refresh(user)
            is_new_user = True
        else:
            is_new_user = False
            # Atualiza avatar se não tem e o provedor social tem
            if not user.avatar and social_info.picture:
                user.avatar = social_info.picture
                db.commit()

        # Cria a conta social
        social_account = SocialAccount(
            user_id=user.id,
            provider=social_info.provider.value,
            provider_user_id=social_info.provider_user_id,
            email=social_info.email,
            name=social_info.name,
            picture=social_info.picture
        )
        db.add(social_account)
        db.commit()
        db.refresh(social_account)

        return user, is_new_user, social_account

    @staticmethod
    def create_social_login_response(user: User, is_new_user: bool = False) -> dict:
        """Cria resposta de login social com token JWT"""
        access_token = create_access_token(subject=str(user.id))

        # Lista de provedores vinculados
        linked_accounts = [account.provider for account in user.social_accounts if account.is_active]

        # Verificar se username é temporário (precisa ser definido pelo usuário)
        needs_username = False
        if user.username:
            # Username temporário geralmente começa com padrões específicos
            temp_patterns = ['user_', 'googleuser', 'appleuser']
            needs_username = any(user.username.startswith(pattern) for pattern in temp_patterns)
        else:
            needs_username = True

        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": {
                "id": user.id,
                "email": user.email,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "avatar": user.avatar,
                "username": user.username,
                "is_active": user.is_active
            },
            "is_new_user": is_new_user,
            "needs_username": needs_username,
            "linked_accounts": linked_accounts
        }

    @staticmethod
    def link_social_account(db: Session, user: User, social_info: SocialUserInfo) -> SocialAccount:
        """Vincula uma conta social a um usuário existente"""
        # Verifica se já existe uma conta social para este provedor
        existing_account = db.query(SocialAccount).filter(
            SocialAccount.provider == social_info.provider.value,
            SocialAccount.provider_user_id == social_info.provider_user_id
        ).first()

        if existing_account:
            if existing_account.user_id == user.id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="This social account is already linked to your account"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="This social account is already linked to another user"
                )

        # Cria nova conta social
        social_account = SocialAccount(
            user_id=user.id,
            provider=social_info.provider.value,
            provider_user_id=social_info.provider_user_id,
            email=social_info.email,
            name=social_info.name,
            picture=social_info.picture
        )
        db.add(social_account)
        db.commit()
        db.refresh(social_account)

        return social_account

    @staticmethod
    def _generate_temp_username(db: Session, social_info: SocialUserInfo) -> str:
        """Gera um username temporário único para usuário de login social"""
        from app import models
        import random
        import string
        import re

        # Tentar usar nome e sobrenome primeiro
        base_username = ""
        if social_info.first_name and social_info.last_name:
            # Limpar nome e sobrenome
            first_clean = re.sub(r'[^a-z]', '', social_info.first_name.lower())
            last_clean = re.sub(r'[^a-z]', '', social_info.last_name.lower())

            if len(first_clean) >= 2 and len(last_clean) >= 2:
                base_username = f"{first_clean}{last_clean}"
                # Limitar a 25 caracteres para deixar espaço para números
                if len(base_username) > 25:
                    base_username = base_username[:25]

        # Se conseguiu nome válido, tentar com números sequenciais
        if base_username and len(base_username) >= 3:
            # Tentar sem número primeiro
            existing = db.query(models.User).filter(models.User.username == base_username).first()
            if not existing:
                return base_username

            # Tentar com números de 1 a 999
            for i in range(1, 1000):
                candidate = f"{base_username}{i}"
                if len(candidate) <= 30:
                    existing = db.query(models.User).filter(models.User.username == candidate).first()
                    if not existing:
                        return candidate

        # Se nome não disponível, gerar string completamente aleatória
        chars = string.ascii_lowercase + string.digits

        # Começar sempre com uma letra
        username = random.choice(string.ascii_lowercase)

        # Adicionar 7-11 caracteres aleatórios (total 8-12 caracteres)
        length = random.randint(7, 11)
        for _ in range(length):
            username += random.choice(chars)

        # Verificar se é único, se não, tentar mais algumas vezes
        for attempt in range(10):
            existing = db.query(models.User).filter(models.User.username == username).first()
            if not existing:
                return username

            # Gerar novo se já existe
            username = random.choice(string.ascii_lowercase)
            length = random.randint(7, 11)
            for _ in range(length):
                username += random.choice(chars)

        # Fallback final com timestamp
        import time
        timestamp = str(int(time.time()))[-6:]
        return f"u{timestamp}{random.choice(string.ascii_lowercase)}"

    @staticmethod
    def unlink_social_account(db: Session, user: User, provider: SocialProvider) -> bool:
        """Desvincula uma conta social de um usuário"""
        social_account = db.query(SocialAccount).filter(
            SocialAccount.user_id == user.id,
            SocialAccount.provider == provider.value
        ).first()

        if not social_account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Social account not found"
            )

        # Verifica se o usuário tem senha ou outras contas sociais
        other_accounts = db.query(SocialAccount).filter(
            SocialAccount.user_id == user.id,
            SocialAccount.provider != provider.value,
            SocialAccount.is_active == True
        ).count()

        has_password = user.hashed_password and user.hashed_password != "social_login"

        if not has_password and other_accounts == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot unlink the only authentication method. Please set a password first."
            )

        db.delete(social_account)
        db.commit()
        return True
