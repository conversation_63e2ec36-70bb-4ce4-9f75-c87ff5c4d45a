from datetime import datetime, date, timedelta
from typing import List, Dict, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func, desc

from app.models.plant import Plant, Care, Reminder, CareType
from app.schemas.plant import ReminderCreate
from app import crud
import logging

logger = logging.getLogger(__name__)


def calculate_care_interval(db: Session, plant_id: str, care_type: CareType) -> Optional[int]:
    """
    Calculate the average interval between cares of the same type for a plant
    Returns interval in days, or None if not enough data
    """
    try:
        # Get last 3 cares of this type for the plant
        recent_cares = db.query(Care).filter(
            Care.plant_id == plant_id,
            Care.care_types.contains([care_type])
        ).order_by(desc(Care.date)).limit(3).all()
        
        if len(recent_cares) < 2:
            # Not enough data, use default intervals
            default_intervals = {
                CareType.rega: 7,      # 1 week
                CareType.adubacao: 30, # 1 month
                CareType.poda: 90,     # 3 months
                CareType.transplante: 365, # 1 year
                CareType.outro: 30     # 1 month default
            }
            return default_intervals.get(care_type, 30)
        
        # Calculate intervals between consecutive cares
        intervals = []
        for i in range(len(recent_cares) - 1):
            current_care = recent_cares[i]
            previous_care = recent_cares[i + 1]
            
            current_date = current_care.date.date() if isinstance(current_care.date, datetime) else current_care.date
            previous_date = previous_care.date.date() if isinstance(previous_care.date, datetime) else previous_care.date
            
            interval = (current_date - previous_date).days
            if interval > 0:  # Only positive intervals
                intervals.append(interval)
        
        if not intervals:
            return None
        
        # Return average interval
        avg_interval = sum(intervals) / len(intervals)
        return int(avg_interval)
        
    except Exception as e:
        logger.error(f"Error calculating care interval: {e}")
        return None


def create_automatic_reminders(db: Session, plant_id: str, care_types: List[str]) -> List[Reminder]:
    """
    Create automatic reminders based on care history and patterns
    """
    created_reminders = []
    
    try:
        plant = crud.plant.get(db=db, id=plant_id)
        if not plant:
            return created_reminders
        
        for care_type_str in care_types:
            try:
                # Convert string to enum
                care_type = CareType(care_type_str)
                
                # Check if there's already a pending reminder for this care type
                existing_reminder = db.query(Reminder).filter(
                    Reminder.plant_id == plant_id,
                    Reminder.care_type == care_type,
                    Reminder.is_completed == False
                ).first()
                
                if existing_reminder:
                    logger.info(f"Reminder already exists for {care_type} on plant {plant.name}")
                    continue
                
                # Calculate interval based on history
                interval_days = calculate_care_interval(db, plant_id, care_type)
                
                if interval_days:
                    # Create next reminder date
                    next_date = date.today() + timedelta(days=interval_days)
                    
                    # Create reminder
                    reminder_data = ReminderCreate(
                        care_type=care_type,
                        scheduled_date=next_date,
                        description=f"Próximo {care_type.value} sugerido automaticamente (baseado no histórico)"
                    )
                    
                    reminder = crud.reminder.create_with_plant(
                        db=db, 
                        obj_in=reminder_data, 
                        plant_id=plant_id
                    )
                    
                    created_reminders.append(reminder)
                    logger.info(f"Auto-created reminder for {care_type} on {next_date} for plant {plant.name}")
                
            except ValueError:
                # Invalid care type, skip
                logger.warning(f"Invalid care type: {care_type_str}")
                continue
            except Exception as e:
                logger.error(f"Error creating reminder for {care_type_str}: {e}")
                continue
        
        db.commit()
        
    except Exception as e:
        logger.error(f"Error in create_automatic_reminders: {e}")
        db.rollback()
    
    return created_reminders


def suggest_next_care_dates(db: Session, plant_id: str) -> Dict[str, date]:
    """
    Suggest next care dates based on plant's care history
    Returns a dict with care_type -> suggested_date
    """
    suggestions = {}
    
    try:
        # Get all care types that have been used for this plant
        care_types_query = db.query(Care.care_types).filter(
            Care.plant_id == plant_id,
            Care.care_types.isnot(None)
        ).all()
        
        # Flatten the list of care types
        all_care_types = set()
        for care_types_list in care_types_query:
            if care_types_list[0]:  # care_types is an array
                all_care_types.update(care_types_list[0])
        
        for care_type_str in all_care_types:
            try:
                care_type = CareType(care_type_str)
                interval_days = calculate_care_interval(db, plant_id, care_type)
                
                if interval_days:
                    # Get last care of this type
                    last_care = db.query(Care).filter(
                        Care.plant_id == plant_id,
                        Care.care_types.contains([care_type])
                    ).order_by(desc(Care.date)).first()
                    
                    if last_care:
                        last_date = last_care.date.date() if isinstance(last_care.date, datetime) else last_care.date
                        suggested_date = last_date + timedelta(days=interval_days)
                        suggestions[care_type_str] = suggested_date
                
            except ValueError:
                continue
            except Exception as e:
                logger.error(f"Error suggesting date for {care_type_str}: {e}")
                continue
    
    except Exception as e:
        logger.error(f"Error in suggest_next_care_dates: {e}")
    
    return suggestions
