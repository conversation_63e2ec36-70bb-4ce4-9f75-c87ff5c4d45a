"""
Plant Limit Service

Handles validation of plant creation and management limits based on user subscription plans.
"""

import logging
from typing import Tuple, Optional, Dict, Any
from sqlalchemy.orm import Session
from app import models

logger = logging.getLogger(__name__)


class PlantLimitService:
    """Service for handling plant-related subscription limits"""
    
    @staticmethod
    def check_plant_creation_limit(user_plan: dict, current_plant_count: int) -> <PERSON>ple[bool, Optional[str]]:
        """
        Verifica se o usuário pode criar mais plantas
        
        Args:
            user_plan: Informações do plano do usuário
            current_plant_count: Quantidade atual de plantas do usuário
            
        Returns:
            Tuple[bool, Optional[str]]: (can_create, error_message)
        """
        try:
            max_plants = user_plan.get('max_plants', 10)
            
            if current_plant_count >= max_plants:
                return False, f"Limite de {max_plants} plantas atingido. Faça upgrade para o plano Premium para criar mais plantas."
            
            return True, None
            
        except Exception as e:
            logger.error(f"Erro na verificação de limite de plantas: {str(e)}")
            return False, "Erro interno na verificação de limites"
    
    @staticmethod
    def check_plant_photo_limit(user_plan: dict, current_photo_count: int) -> Tuple[bool, Optional[str]]:
        """
        Verifica se o usuário pode adicionar mais fotos a uma planta
        
        Args:
            user_plan: Informações do plano do usuário
            current_photo_count: Quantidade atual de fotos da planta
            
        Returns:
            Tuple[bool, Optional[str]]: (can_add, error_message)
        """
        try:
            max_photos = user_plan.get('max_photos_per_plant', 30)
            
            if current_photo_count >= max_photos:
                return False, f"Limite de {max_photos} fotos por planta atingido."
            
            return True, None
            
        except Exception as e:
            logger.error(f"Erro na verificação de limite de fotos: {str(e)}")
            return False, "Erro interno na verificação de limites"
    
    @staticmethod
    def get_user_plant_access_info(db: Session, user_id: str, user_plan: dict) -> Dict[str, Any]:
        """
        Obtém informações sobre o acesso do usuário às suas plantas
        
        Args:
            db: Sessão do banco de dados
            user_id: ID do usuário
            user_plan: Informações do plano do usuário
            
        Returns:
            Dict com informações sobre plantas editáveis e somente leitura
        """
        try:
            max_plants = user_plan.get('max_plants', 10)
            
            # Buscar todas as plantas do usuário ordenadas por data de criação (mais recentes primeiro)
            all_plants = db.query(models.Plant).filter(
                models.Plant.owner_id == user_id
            ).order_by(models.Plant.created_at.desc()).all()
            
            total_plants = len(all_plants)
            
            if total_plants <= max_plants:
                # Usuário está dentro do limite, todas as plantas são editáveis
                can_create_new = total_plants < max_plants  # Só pode criar se não atingiu o limite
                return {
                    "total_plants": total_plants,
                    "editable_plants": total_plants,
                    "readonly_plants": 0,
                    "can_create_new": can_create_new,
                    "editable_plant_ids": [str(plant.id) for plant in all_plants],
                    "readonly_plant_ids": [],
                    "message": f"Você tem {total_plants} de {max_plants} plantas permitidas."
                }
            else:
                # Usuário excedeu o limite
                editable_plants = all_plants[:max_plants]  # As mais recentes são editáveis
                readonly_plants = all_plants[max_plants:]  # As mais antigas ficam somente leitura
                
                return {
                    "total_plants": total_plants,
                    "editable_plants": len(editable_plants),
                    "readonly_plants": len(readonly_plants),
                    "can_create_new": False,
                    "editable_plant_ids": [str(plant.id) for plant in editable_plants],
                    "readonly_plant_ids": [str(plant.id) for plant in readonly_plants],
                    "message": f"Você tem {total_plants} plantas, mas seu plano permite apenas {max_plants}. "
                              f"As {len(readonly_plants)} plantas mais antigas estão em modo somente leitura. "
                              f"Delete algumas plantas ou faça upgrade para ter acesso completo."
                }
                
        except Exception as e:
            logger.error(f"Erro ao obter informações de acesso às plantas: {str(e)}")
            return {
                "total_plants": 0,
                "editable_plants": 0,
                "readonly_plants": 0,
                "can_create_new": False,
                "editable_plant_ids": [],
                "readonly_plant_ids": [],
                "message": "Erro interno ao verificar acesso às plantas"
            }
    
    @staticmethod
    def can_edit_plant(db: Session, user_id: str, plant_id: str, user_plan: dict) -> Tuple[bool, Optional[str]]:
        """
        Verifica se o usuário pode editar uma planta específica
        
        Args:
            db: Sessão do banco de dados
            user_id: ID do usuário
            plant_id: ID da planta
            user_plan: Informações do plano do usuário
            
        Returns:
            Tuple[bool, Optional[str]]: (can_edit, reason_if_not)
        """
        try:
            # Verificar se a planta pertence ao usuário
            plant = db.query(models.Plant).filter(
                models.Plant.id == plant_id,
                models.Plant.owner_id == user_id
            ).first()
            
            if not plant:
                return False, "Planta não encontrada ou não pertence ao usuário"
            
            # Obter informações de acesso
            access_info = PlantLimitService.get_user_plant_access_info(db, user_id, user_plan)
            
            if plant_id in access_info["editable_plant_ids"]:
                return True, None
            elif plant_id in access_info["readonly_plant_ids"]:
                return False, "Esta planta está em modo somente leitura devido aos limites do seu plano. Faça upgrade ou delete outras plantas para editá-la."
            else:
                return False, "Erro ao verificar permissões da planta"
                
        except Exception as e:
            logger.error(f"Erro ao verificar permissão de edição da planta: {str(e)}")
            return False, "Erro interno na verificação de permissões"
