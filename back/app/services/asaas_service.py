import requests
import logging

from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from app.core.config import settings

logger = logging.getLogger(__name__)



class AsaasService:
    def __init__(self):
        # Determinar ambiente baseado na configuração
        self.is_sandbox = settings.ENVIRONMENT == "development"

        logger.info(f"🔧 [ASAAS] Environment detected: {settings.ENVIRONMENT}")
        logger.info(f"🔧 [ASAAS] Is sandbox mode: {self.is_sandbox}")

        if self.is_sandbox:
            self.api_token = settings.ASAAS_API_TOKEN_SANDBOX
            self.wallet_id = settings.ASAAS_WALLET_ID_SANDBOX
            self.base_url = "https://sandbox.asaas.com/api/v3"
            logger.info(f"🔧 [ASAAS] Using SANDBOX credentials")
        else:
            self.api_token = settings.ASAAS_API_TOKEN_PRODUCTION
            self.wallet_id = settings.ASAAS_WALLET_ID_PRODUCTION
            self.base_url = "https://www.asaas.com/api/v3"
            logger.info(f"🔧 [ASAAS] Using PRODUCTION credentials")

        # Verificar se as credenciais foram carregadas
        if not self.api_token:
            logger.error(f"❌ [ASAAS] API Token not found for {'SANDBOX' if self.is_sandbox else 'PRODUCTION'} environment!")
        else:
            logger.info(f"✅ [ASAAS] API Token loaded: {self.api_token[:20]}...")

        if not self.wallet_id:
            logger.error(f"❌ [ASAAS] Wallet ID not found for {'SANDBOX' if self.is_sandbox else 'PRODUCTION'} environment!")
        else:
            logger.info(f"✅ [ASAAS] Wallet ID loaded: {self.wallet_id}")

        self.headers = {
            "access_token": self.api_token,
            "Content-Type": "application/json"
        }

        logger.info(f"🚀 [ASAAS] Service initialized in {'SANDBOX' if self.is_sandbox else 'PRODUCTION'} mode")
        logger.info(f"🌐 [ASAAS] Base URL: {self.base_url}")

    def create_customer(self, user_email: str, user_name: str, user_id: str, user_cpf_cnpj: str = None) -> Dict[str, Any]:
        """
        Criar ou buscar cliente no Asaas
        """
        try:
            logger.info(f"Creating/finding customer for email: {user_email}")

            # Primeiro, tentar buscar cliente existente pelo email
            search_url = f"{self.base_url}/customers"
            search_params = {"email": user_email}

            response = requests.get(search_url, headers=self.headers, params=search_params)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("data") and len(data["data"]) > 0:
                    # Cliente já existe
                    customer = data["data"][0]
                    logger.info(f"Customer found: {customer['id']}")
                    logger.info(f"Customer CPF: {customer.get('cpfCnpj', 'NOT SET')}")

                    # Se cliente não tem CPF e temos um CPF para adicionar, atualizar
                    if not customer.get("cpfCnpj") and user_cpf_cnpj:
                        logger.info(f"Updating customer {customer['id']} with CPF: {user_cpf_cnpj}")

                        update_url = f"{self.base_url}/customers/{customer['id']}"
                        update_data = {"cpfCnpj": user_cpf_cnpj}
                        update_response = requests.post(update_url, headers=self.headers, json=update_data)

                        if update_response.status_code == 200:
                            customer = update_response.json()
                            logger.info(f"Customer updated successfully with CPF")
                        else:
                            logger.error(f"Failed to update customer: {update_response.text}")

                    return {
                        "success": True,
                        "customer_id": customer["id"],
                        "customer": customer
                    }
            
            # Cliente não existe, criar novo
            # Usar CPF fornecido pelo usuário ou CPF de teste para sandbox
            cpf_cnpj = user_cpf_cnpj
            if not cpf_cnpj and self.is_sandbox:
                # CPF de teste válido para sandbox
                cpf_cnpj = "11144477735"
                logger.info(f"Using test CPF for sandbox: {cpf_cnpj}")
            elif cpf_cnpj:
                logger.info(f"Using user provided CPF/CNPJ")
            else:
                logger.error(f"No CPF/CNPJ provided for production environment")
                return {
                    "success": False,
                    "error": "CPF/CNPJ é obrigatório"
                }

            customer_data = {
                "name": user_name,
                "email": user_email,
                "cpfCnpj": cpf_cnpj,
                "externalReference": f"user_{user_id}",
                "notificationDisabled": False
            }

            logger.info(f"Creating customer with CPF: {cpf_cnpj}")
            
            create_url = f"{self.base_url}/customers"
            response = requests.post(create_url, headers=self.headers, json=customer_data)
            
            if response.status_code == 200:
                customer = response.json()
                logger.info(f"Customer created: {customer['id']}")
                return {
                    "success": True,
                    "customer_id": customer["id"],
                    "customer": customer
                }
            else:
                logger.error(f"Error creating customer: {response.status_code} - {response.text}")
                return {
                    "success": False,
                    "error": f"Erro ao criar cliente: {response.text}"
                }
                
        except Exception as e:
            logger.error(f"Exception creating customer: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def create_subscription(
        self,
        customer_id: str,
        plan_name: str,
        plan_price: float,
        plan_id: str,
        user_id: str,
        billing_cycle: str = "monthly",
        next_due_date: datetime = None,
        skip_first_payment: bool = False,
        discount_amount: float = 0,
        discount_description: str = None
    ) -> Dict[str, Any]:
        """
        Criar assinatura recorrente no Asaas com desconto opcional na primeira cobrança
        """
        try:
            # Calcular data de vencimento baseada no ciclo ou usar a fornecida
            if next_due_date:
                due_date = next_due_date.strftime("%Y-%m-%d")
            elif billing_cycle == "yearly":
                due_date = (datetime.now() + timedelta(days=7)).strftime("%Y-%m-%d")
            else:
                due_date = (datetime.now() + timedelta(days=7)).strftime("%Y-%m-%d")

            cycle = "YEARLY" if billing_cycle == "yearly" else "MONTHLY"

            subscription_data = {
                "customer": customer_id,
                "billingType": "UNDEFINED",
                "value": plan_price,
                "nextDueDate": due_date,
                "cycle": cycle,
                "description": f"Assinatura {plan_name} - MeuBonsai",
                "externalReference": f"subscription_{user_id}_{plan_id}"
            }

            # Adicionar desconto se fornecido
            if discount_amount > 0:
                subscription_data["discount"] = {
                    "value": discount_amount,
                    "dueDateLimitDays": 0,  # Desconto válido apenas até o vencimento
                    "type": "FIXED"  # 🎯 CORREÇÃO: Especificar que é valor fixo, não percentual
                }

                # Atualizar descrição se fornecida
                if discount_description:
                    subscription_data["description"] = f"Assinatura {plan_name} - {discount_description} - MeuBonsai"

                logger.info(f"🎯 Adding discount to subscription: R$ {discount_amount:.2f}")
            else:
                logger.info(f"🎯 Creating subscription without discount")

            logger.info(f"🔧 Creating {cycle} subscription for {plan_name}")
            logger.info(f"🔧 Subscription data being sent to ASAAS: {subscription_data}")

            url = f"{self.base_url}/subscriptions"
            response = requests.post(url, headers=self.headers, json=subscription_data)

            logger.info(f"🔧 ASAAS subscription response status: {response.status_code}")
            logger.info(f"🔧 ASAAS subscription response: {response.text}")

            if response.status_code == 200:
                subscription = response.json()
                logger.info(f"Subscription created: {subscription['id']}")
                logger.info(f"Full subscription response: {subscription}")

                # 🎯 CORREÇÃO: Usar apenas a assinatura (sem primeira cobrança separada)
                # O ASAAS já cria automaticamente a primeira cobrança com desconto
                payment_url = subscription.get("invoiceUrl") or subscription.get("bankSlipUrl")

                # Se não tiver URL na assinatura, buscar a primeira cobrança gerada automaticamente
                if not payment_url:
                    # Buscar pagamentos da assinatura
                    payments_result = self.get_subscription_invoices(subscription["id"])
                    if payments_result["success"] and payments_result["invoices"]:
                        first_invoice = payments_result["invoices"][0]
                        payment_url = first_invoice.get("invoice_url")
                        logger.info(f"Using first invoice URL: {payment_url}")

                return {
                    "success": True,
                    "subscription_id": subscription["id"],
                    "payment_id": subscription.get("id"),  # Usar ID da assinatura
                    "payment_link": payment_url,
                    "subscription": subscription
                }
            else:
                logger.error(f"Error creating subscription: {response.status_code} - {response.text}")
                return {
                    "success": False,
                    "error": f"Erro ao criar assinatura: {response.text}"
                }

        except Exception as e:
            logger.error(f"Exception creating subscription: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def create_first_payment_for_subscription(
        self,
        customer_id: str,
        subscription_id: str,
        plan_name: str,
        plan_price: float,
        user_id: str
    ) -> Dict[str, Any]:
        """
        Criar primeiro pagamento para uma assinatura
        """
        try:
            # Calcular data de vencimento (7 dias)
            due_date = (datetime.now() + timedelta(days=7)).strftime("%Y-%m-%d")

            payment_data = {
                "customer": customer_id,
                "billingType": "UNDEFINED",  # Permite PIX, cartão, boleto
                "value": plan_price,
                "dueDate": due_date,
                "description": f"Primeira cobrança - {plan_name} - MeuBonsai",
                "externalReference": f"subscription_{user_id}_{subscription_id}",
                "postalService": False  # Não solicitar endereço
            }

            logger.info(f"🔧 Creating first payment for subscription {subscription_id}")
            logger.info(f"🔧 Payment data being sent to ASAAS: {payment_data}")

            url = f"{self.base_url}/payments"
            response = requests.post(url, headers=self.headers, json=payment_data)

            logger.info(f"🔧 ASAAS payment response status: {response.status_code}")
            logger.info(f"🔧 ASAAS payment response: {response.text}")

            if response.status_code == 200:
                payment = response.json()
                logger.info(f"First payment created: {payment['id']}")

                payment_url = payment.get("invoiceUrl")

                return {
                    "success": True,
                    "payment_id": payment["id"],
                    "payment_link": payment_url,
                    "payment": payment
                }
            else:
                logger.error(f"Error creating first payment: {response.status_code} - {response.text}")
                return {
                    "success": False,
                    "error": f"Erro ao criar primeiro pagamento: {response.text}"
                }

        except Exception as e:
            logger.error(f"Exception creating first payment: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def create_payment_link(
        self,
        customer_id: str,
        plan_id: str,
        plan_name: str,
        plan_price: float,
        user_id: str
    ) -> Dict[str, Any]:
        """
        Criar link de pagamento único (não recorrente) no Asaas
        """
        try:

            # Criar cobrança única
            due_date = (datetime.now() + timedelta(days=7)).strftime("%Y-%m-%d")

            payment_data = {
                "customer": customer_id,
                "billingType": "UNDEFINED",  # Permite PIX, cartão, boleto
                "value": plan_price,
                "dueDate": due_date,
                "description": f"Plano {plan_name} - MeuBonsai",
                "externalReference": f"subscription_{user_id}_{plan_id}_first_payment",
                "installmentCount": 1,
                "installmentValue": plan_price,
                "discount": {
                    "value": 0,
                    "dueDateLimitDays": 0
                },
                "interest": {
                    "value": 0
                },
                "fine": {
                    "value": 0
                },
                "postalService": False
            }
            
            url = f"{self.base_url}/payments"
            response = requests.post(url, headers=self.headers, json=payment_data)
            
            if response.status_code == 200:
                payment = response.json()
                logger.info(f"Payment created: {payment['id']}")
                
                return {
                    "success": True,
                    "payment_id": payment["id"],
                    "payment_link": payment.get("invoiceUrl"),
                    "payment": payment
                }
            else:
                logger.error(f"Error creating payment: {response.status_code} - {response.text}")
                return {
                    "success": False,
                    "error": f"Erro ao criar pagamento: {response.text}"
                }
                
        except Exception as e:
            logger.error(f"Exception creating payment: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def get_payment_status(self, payment_id: str) -> Dict[str, Any]:
        """
        Verificar status de um pagamento
        """
        try:
            url = f"{self.base_url}/payments/{payment_id}"
            response = requests.get(url, headers=self.headers)
            
            if response.status_code == 200:
                payment = response.json()
                return {
                    "success": True,
                    "payment": payment,
                    "status": payment.get("status")
                }
            else:
                return {
                    "success": False,
                    "error": f"Erro ao buscar pagamento: {response.text}"
                }
                
        except Exception as e:
            logger.error(f"Exception getting payment status: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def cancel_subscription(self, subscription_id: str) -> Dict[str, Any]:
        """
        Cancelar assinatura no Asaas
        """
        try:
            url = f"{self.base_url}/subscriptions/{subscription_id}"

            response = requests.delete(url, headers=self.headers)

            if response.status_code == 200:
                logger.info(f"Subscription {subscription_id} cancelled successfully in Asaas")
                return {"success": True, "data": response.json()}
            else:
                logger.error(f"Error cancelling subscription {subscription_id}: {response.status_code} - {response.text}")
                return {"success": False, "error": f"HTTP {response.status_code}: {response.text}"}

        except Exception as e:
            logger.error(f"Error cancelling subscription {subscription_id}: {e}")
            return {"success": False, "error": str(e)}

    def create_discounted_first_payment(
        self,
        customer_id: str,
        subscription_id: str,
        plan_name: str,
        discounted_price: float,
        user_id: str
    ) -> Dict[str, Any]:
        """
        Criar primeira cobrança com desconto (substitui a cobrança automática da assinatura)
        """
        try:
            # Calcular data de vencimento (7 dias)
            due_date = (datetime.now() + timedelta(days=7)).strftime("%Y-%m-%d")

            payment_data = {
                "customer": customer_id,
                "billingType": "UNDEFINED",  # Permite PIX, cartão, boleto
                "value": discounted_price,
                "dueDate": due_date,
                "description": f"Primeira cobrança com desconto - {plan_name} - MeuBonsai",
                "externalReference": f"subscription_{user_id}_{subscription_id}_discounted",
                "postalService": False  # Não solicitar endereço
            }

            logger.info(f"Creating discounted first payment for subscription {subscription_id}: R$ {discounted_price:.2f}")

            url = f"{self.base_url}/payments"
            response = requests.post(url, headers=self.headers, json=payment_data)

            if response.status_code == 200:
                payment = response.json()
                logger.info(f"Discounted first payment created: {payment['id']}")

                payment_url = payment.get("invoiceUrl")

                return {
                    "success": True,
                    "payment_id": payment["id"],
                    "payment_link": payment_url,
                    "payment": payment
                }
            else:
                logger.error(f"Error creating discounted first payment: {response.status_code} - {response.text}")
                return {
                    "success": False,
                    "error": f"Erro ao criar primeira cobrança com desconto: {response.text}"
                }

        except Exception as e:
            logger.error(f"Exception creating discounted first payment: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def create_subscription_with_delayed_start(
        self,
        customer_id: str,
        plan_name: str,
        plan_price: float,
        plan_id: str,
        user_id: str,
        billing_cycle: str = "monthly"
    ) -> Dict[str, Any]:
        """
        Criar assinatura recorrente com primeira cobrança adiada (para permitir cobrança manual com desconto)
        """
        try:
            # Calcular data de vencimento para o PRÓXIMO CICLO (mesmo dia do mês)
            if billing_cycle == "yearly":
                # Para anual, próxima cobrança em 1 ano (mesmo dia)
                due_date = (datetime.now() + timedelta(days=365)).strftime("%Y-%m-%d")
                cycle = "YEARLY"
            else:
                # Para mensal, próxima cobrança no mesmo dia do próximo mês
                current_date = datetime.now()
                if current_date.month == 12:
                    next_month_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    next_month_date = current_date.replace(month=current_date.month + 1)

                # Ajustar se o dia não existe no próximo mês (ex: 31 de janeiro -> 28/29 de fevereiro)
                try:
                    due_date = next_month_date.strftime("%Y-%m-%d")
                except ValueError:
                    # Usar último dia do mês se o dia atual não existe
                    import calendar
                    last_day = calendar.monthrange(next_month_date.year, next_month_date.month)[1]
                    due_date = next_month_date.replace(day=last_day).strftime("%Y-%m-%d")

                cycle = "MONTHLY"

            subscription_data = {
                "customer": customer_id,
                "billingType": "UNDEFINED",  # Permite escolher na hora do pagamento
                "value": plan_price,
                "nextDueDate": due_date,  # ← DATA ADIADA
                "cycle": cycle,
                "description": f"Assinatura {plan_name} - MeuBonsai",
                "externalReference": f"subscription_{user_id}_{plan_id}",
                "endDate": None,  # Assinatura sem fim (renovação automática)
                "maxPayments": None,  # Ilimitado
                "enablePaymentLink": False,  # ← NÃO criar link automático
                "creditCard": {
                    "autoRenewal": True  # ← GARANTIR RENOVAÇÃO AUTOMÁTICA
                }
            }

            logger.info(f"Creating {cycle} subscription with delayed start for {plan_name}")

            url = f"{self.base_url}/subscriptions"
            response = requests.post(url, headers=self.headers, json=subscription_data)

            if response.status_code == 200:
                subscription = response.json()
                logger.info(f"Subscription with delayed start created: {subscription['id']}")

                return {
                    "success": True,
                    "subscription_id": subscription["id"],
                    "subscription": subscription
                }
            else:
                logger.error(f"Error creating subscription with delayed start: {response.status_code} - {response.text}")
                return {
                    "success": False,
                    "error": f"Erro ao criar assinatura: {response.text}"
                }

        except Exception as e:
            logger.error(f"Exception creating subscription with delayed start: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def update_subscription_auto_renewal(self, subscription_id: str) -> Dict[str, Any]:
        """
        Atualizar assinatura para garantir renovação automática no cartão
        """
        try:
            # Primeiro, vamos buscar a assinatura para ver o status atual
            get_url = f"{self.base_url}/subscriptions/{subscription_id}"
            get_response = requests.get(get_url, headers=self.headers)

            if get_response.status_code == 200:
                subscription_data = get_response.json()
                logger.info(f"Current subscription data: {subscription_data}")

                # Verificar se já tem cartão configurado
                billing_type = subscription_data.get("billingType")
                logger.info(f"Current billing type: {billing_type}")

                if billing_type == "CREDIT_CARD":
                    logger.info(f"Subscription {subscription_id} already configured for credit card auto-renewal")
                    return {
                        "success": True,
                        "message": "Subscription already configured for auto-renewal",
                        "subscription": subscription_data
                    }
                else:
                    # Tentar atualizar para cartão de crédito
                    update_data = {
                        "billingType": "CREDIT_CARD"
                    }

                    put_response = requests.put(get_url, headers=self.headers, json=update_data)

                    if put_response.status_code == 200:
                        updated_subscription = put_response.json()
                        logger.info(f"Subscription billing type updated to CREDIT_CARD: {subscription_id}")
                        return {
                            "success": True,
                            "subscription": updated_subscription
                        }
                    else:
                        logger.error(f"Error updating subscription billing type: {put_response.status_code} - {put_response.text}")
                        return {
                            "success": False,
                            "error": f"Erro ao atualizar tipo de cobrança: {put_response.text}"
                        }
            else:
                logger.error(f"Error getting subscription: {get_response.status_code} - {get_response.text}")
                return {
                    "success": False,
                    "error": f"Erro ao buscar assinatura: {get_response.text}"
                }

        except Exception as e:
            logger.error(f"Exception updating subscription auto-renewal: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def get_subscription_invoices(self, subscription_id: str) -> Dict[str, Any]:
        """
        Buscar faturas de uma assinatura no ASAAS
        """
        try:
            url = f"{self.base_url}/subscriptions/{subscription_id}/payments"

            logger.info(f"Fetching invoices for subscription: {subscription_id}")

            response = requests.get(url, headers=self.headers)

            if response.status_code == 200:
                data = response.json()
                payments = data.get("data", [])

                # Formatar faturas para o frontend
                invoices = []
                for payment in payments:
                    invoice = {
                        "id": payment.get("id"),
                        "value": payment.get("value"),
                        "status": payment.get("status"),
                        "due_date": payment.get("dueDate"),
                        "payment_date": payment.get("paymentDate"),
                        "invoice_url": payment.get("invoiceUrl"),
                        "bank_slip_url": payment.get("bankSlipUrl"),
                        "billing_type": payment.get("billingType"),
                        "description": payment.get("description", "Assinatura MeuBonsai")
                    }
                    invoices.append(invoice)

                logger.info(f"Found {len(invoices)} invoices for subscription {subscription_id}")

                return {
                    "success": True,
                    "invoices": invoices
                }
            else:
                logger.error(f"Error fetching invoices: {response.status_code} - {response.text}")
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}: {response.text}"
                }

        except Exception as e:
            logger.error(f"Error fetching subscription invoices: {e}")
            return {"success": False, "error": str(e)}

    def get_customer_by_email(self, email: str) -> Dict[str, Any]:
        """
        Buscar customer no ASAAS pelo email
        """
        try:
            url = f"{self.base_url}/customers"
            params = {"email": email}

            logger.info(f"Searching customer by email: {email}")

            response = requests.get(url, headers=self.headers, params=params)

            if response.status_code == 200:
                data = response.json()
                customers = data.get("data", [])

                if customers:
                    customer = customers[0]  # Pegar o primeiro (deve ser único)
                    logger.info(f"Customer found: {customer['id']}")
                    return {
                        "success": True,
                        "customer_id": customer["id"],
                        "customer": customer
                    }
                else:
                    logger.warning(f"No customer found for email: {email}")
                    return {
                        "success": False,
                        "error": "Customer not found"
                    }
            else:
                logger.error(f"Error searching customer: {response.status_code} - {response.text}")
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}: {response.text}"
                }

        except Exception as e:
            logger.error(f"Error searching customer by email: {e}")
            return {"success": False, "error": str(e)}

    def get_customer_payments(self, customer_id: str) -> Dict[str, Any]:
        """
        Buscar todas as cobranças de um customer no ASAAS
        """
        try:
            url = f"{self.base_url}/payments"
            params = {
                "customer": customer_id,
                "limit": 100  # Buscar até 100 cobranças
            }

            logger.info(f"Fetching payments for customer: {customer_id}")

            response = requests.get(url, headers=self.headers, params=params)

            if response.status_code == 200:
                data = response.json()
                payments = data.get("data", [])

                logger.info(f"Found {len(payments)} payments for customer {customer_id}")

                return {
                    "success": True,
                    "payments": payments
                }
            else:
                logger.error(f"Error fetching customer payments: {response.status_code} - {response.text}")
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}: {response.text}"
                }

        except Exception as e:
            logger.error(f"Error fetching customer payments: {e}")
            return {"success": False, "error": str(e)}

# Instância global do serviço
asaas_service = AsaasService()
