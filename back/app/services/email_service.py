import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import List, Optional
import logging
from app.core.config import settings

logger = logging.getLogger(__name__)

class EmailService:
    def __init__(self):
        self.smtp_server = settings.SMTP_SERVER
        self.smtp_port = settings.SMTP_PORT
        self.smtp_username = settings.SMTP_USERNAME
        self.smtp_password = settings.SMTP_PASSWORD
        self.from_email = settings.FROM_EMAIL
        self.use_tls = getattr(settings, 'SMTP_USE_TLS', True)

    def _create_connection(self):
        """Cria conexão SMTP"""
        try:
            logger.info(f"Conectando ao SMTP: {self.smtp_server}:{self.smtp_port}")

            if self.use_tls:
                server = smtplib.SMTP(self.smtp_server, self.smtp_port, timeout=30)
                server.ehlo()  # Identificar com o servidor
                logger.info("Iniciando STARTTLS...")
                server.starttls()
                server.ehlo()  # Re-identificar após TLS
            else:
                server = smtplib.SMTP_SSL(self.smtp_server, self.smtp_port, timeout=30)
                server.ehlo()

            logger.info(f"Fazendo login com usuário: {self.smtp_username}")
            server.login(self.smtp_username, self.smtp_password)
            logger.info("Conexão SMTP estabelecida com sucesso")
            return server
        except smtplib.SMTPAuthenticationError as e:
            logger.error(f"Erro de autenticação SMTP: {e}")
            logger.error("Verifique se as credenciais estão corretas e se a conta permite SMTP")
            raise
        except smtplib.SMTPConnectError as e:
            logger.error(f"Erro de conexão SMTP: {e}")
            raise
        except Exception as e:
            logger.error(f"Erro inesperado ao conectar com SMTP: {e}")
            raise

    def send_email(
        self,
        to_email: str,
        subject: str,
        html_content: str,
        text_content: Optional[str] = None
    ) -> bool:
        """Envia email"""
        try:
            logger.info(f"Tentando enviar email para: {to_email}")
            logger.info(f"Configurações SMTP: {self.smtp_server}:{self.smtp_port}, TLS: {self.use_tls}")
            logger.info(f"From email: {self.from_email}")

            # Verificar se as configurações estão definidas
            if not all([self.smtp_server, self.smtp_username, self.smtp_password]):
                logger.error("Configurações SMTP incompletas")
                logger.error(f"SMTP_SERVER: {self.smtp_server}")
                logger.error(f"SMTP_USERNAME: {self.smtp_username}")
                logger.error(f"SMTP_PASSWORD: {'***' if self.smtp_password else 'None'}")
                return False
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = self.from_email
            msg['To'] = to_email

            # Adiciona versão texto se fornecida
            if text_content:
                text_part = MIMEText(text_content, 'plain', 'utf-8')
                msg.attach(text_part)

            # Adiciona versão HTML
            html_part = MIMEText(html_content, 'html', 'utf-8')
            msg.attach(html_part)

            # Envia email
            with self._create_connection() as server:
                server.send_message(msg)
            
            logger.info(f"Email enviado com sucesso para {to_email}")
            return True

        except Exception as e:
            logger.error(f"Erro ao enviar email para {to_email}: {e}")
            return False

    def send_welcome_email(self, email: str, name: str, activation_token: str) -> bool:
        """Envia email de boas-vindas com ativação de conta"""
        activation_url = f"{settings.FRONTEND_URL}/activate-account?token={activation_token}"
        
        subject = "Bem-vindo ao MeuBonsai! Ative sua conta"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #2d5a27; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }}
                .content {{ background-color: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }}
                .button {{ display: inline-block; background-color: #2d5a27; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
                .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 14px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🌱 Bem-vindo ao MeuBonsai!</h1>
                </div>
                <div class="content">
                    <h2>Olá, {name}!</h2>
                    <p>Obrigado por se cadastrar no MeuBonsai, sua plataforma para cuidar dos seus bonsais com carinho e precisão.</p>
                    
                    <p>Para começar a usar sua conta, você precisa ativá-la clicando no botão abaixo:</p>
                    
                    <div style="text-align: center;">
                        <a href="{activation_url}" class="button">Ativar Minha Conta</a>
                    </div>
                    
                    <p>Ou copie e cole este link no seu navegador:</p>
                    <p style="word-break: break-all; background-color: #e9e9e9; padding: 10px; border-radius: 4px;">
                        {activation_url}
                    </p>
                    
                    <p><strong>Este link expira em 24 horas.</strong></p>
                    
                    <hr style="margin: 30px 0; border: none; border-top: 1px solid #ddd;">
                    
                    <h3>🌿 O que você pode fazer no MeuBonsai:</h3>
                    <ul>
                        <li>📸 Registrar seus bonsais com fotos</li>
                        <li>📅 Acompanhar cuidados e criar lembretes</li>
                        <li>📊 Visualizar o crescimento ao longo do tempo</li>
                        <li>🎯 Organizar sua coleção de forma profissional</li>
                    </ul>
                </div>
                <div class="footer">
                    <p>Se você não criou esta conta, pode ignorar este email.</p>
                    <p>© 2024 MeuBonsai - Cuidando dos seus bonsais com tecnologia</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        text_content = f"""
        Bem-vindo ao MeuBonsai!
        
        Olá, {name}!
        
        Obrigado por se cadastrar no MeuBonsai. Para ativar sua conta, acesse:
        {activation_url}
        
        Este link expira em 24 horas.
        
        Se você não criou esta conta, pode ignorar este email.
        """
        
        return self.send_email(email, subject, html_content, text_content)

    def send_password_reset_email(self, email: str, name: str, reset_token: str) -> bool:
        """Envia email de recuperação de senha"""
        reset_url = f"{settings.FRONTEND_URL}/reset-password?token={reset_token}"
        
        subject = "Redefinir sua senha - MeuBonsai"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #2d5a27; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }}
                .content {{ background-color: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }}
                .button {{ display: inline-block; background: linear-gradient(135deg, #2d5a27, #4a7c59); color: white !important; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 20px 0; }}
                .button:hover {{ background: linear-gradient(135deg, #1e3a1a, #2d5a27); color: white !important; }}
                .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 14px; }}
                .warning {{ background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🔐 Redefinir Senha</h1>
                </div>
                <div class="content">
                    <h2>Olá, {name}!</h2>
                    <p>Recebemos uma solicitação para redefinir a senha da sua conta no MeuBonsai.</p>
                    
                    <div class="warning">
                        <strong>⚠️ Importante:</strong> Se você não solicitou esta alteração, ignore este email. Sua senha permanecerá inalterada.
                    </div>
                    
                    <p>Para criar uma nova senha, clique no botão abaixo:</p>
                    
                    <div style="text-align: center;">
                        <a href="{reset_url}" class="button">Redefinir Minha Senha</a>
                    </div>
                    
                    <p>Ou copie e cole este link no seu navegador:</p>
                    <p style="word-break: break-all; background-color: #e9e9e9; padding: 10px; border-radius: 4px;">
                        {reset_url}
                    </p>
                    
                    <p><strong>Este link expira em 1 hora por segurança.</strong></p>
                    
                    <hr style="margin: 30px 0; border: none; border-top: 1px solid #ddd;">
                    
                    <h3>🔒 Dicas de Segurança:</h3>
                    <ul>
                        <li>Use uma senha forte com pelo menos 8 caracteres</li>
                        <li>Combine letras, números e símbolos</li>
                        <li>Não compartilhe sua senha com ninguém</li>
                        <li>Use senhas diferentes para cada serviço</li>
                    </ul>
                </div>
                <div class="footer">
                    <p>Se você não solicitou esta alteração, pode ignorar este email.</p>
                    <p>© 2024 MeuBonsai - Cuidando dos seus bonsais com tecnologia</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        text_content = f"""
        Redefinir Senha - MeuBonsai
        
        Olá, {name}!
        
        Recebemos uma solicitação para redefinir sua senha. Para criar uma nova senha, acesse:
        {reset_url}
        
        Este link expira em 1 hora.
        
        Se você não solicitou esta alteração, ignore este email.
        """

        return self.send_email(email, subject, html_content, text_content)

    def send_password_change_email(self, email: str, name: str, change_token: str) -> bool:
        """Envia email de confirmação de alteração de senha"""
        change_url = f"{settings.FRONTEND_URL}/confirm-password-change?token={change_token}"

        subject = "Confirmar alteração de senha - MeuBonsai"

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Confirmar Alteração de Senha</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }}
                .container {{ max-width: 600px; margin: 0 auto; background-color: white; }}
                .header {{ background: linear-gradient(135deg, #2d5a27, #4a7c59); color: white; padding: 30px; text-align: center; }}
                .header h1 {{ margin: 0; font-size: 28px; }}
                .content {{ padding: 30px; }}
                .content h2 {{ color: #2d5a27; margin-top: 0; }}
                .button {{ display: inline-block; background: linear-gradient(135deg, #2d5a27, #4a7c59); color: white !important; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 20px 0; }}
                .button:hover {{ background: linear-gradient(135deg, #1e3a1a, #2d5a27); color: white !important; }}
                .footer {{ background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px; }}
                .warning {{ background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; margin: 20px 0; }}
                .security {{ background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 4px; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🔐 Confirmar Alteração de Senha</h1>
                </div>
                <div class="content">
                    <h2>Olá, {name}!</h2>
                    <p>Você solicitou a alteração da senha da sua conta no MeuBonsai.</p>

                    <div class="security">
                        <strong>🔒 Por segurança:</strong> Para confirmar esta alteração, você precisa clicar no botão abaixo. Sua senha atual permanecerá ativa até que você confirme a alteração.
                    </div>

                    <div class="warning">
                        <strong>⚠️ Importante:</strong> Se você não solicitou esta alteração, ignore este email e sua senha permanecerá inalterada. Recomendamos que você faça login na sua conta para verificar se há atividade suspeita.
                    </div>

                    <p>Para confirmar a alteração da sua senha, clique no botão abaixo:</p>

                    <div style="text-align: center;">
                        <a href="{change_url}" class="button">Confirmar Alteração de Senha</a>
                    </div>

                    <p>Ou copie e cole este link no seu navegador:</p>
                    <p style="word-break: break-all; background-color: #e9e9e9; padding: 10px; border-radius: 4px;">
                        {change_url}
                    </p>

                    <p><strong>Este link expira em 2 horas por segurança.</strong></p>

                    <hr style="margin: 30px 0; border: none; border-top: 1px solid #ddd;">

                    <h3>🔒 Lembre-se:</h3>
                    <ul>
                        <li>Nunca compartilhe suas senhas com ninguém</li>
                        <li>Use senhas fortes e únicas para cada serviço</li>
                        <li>Se você não solicitou esta alteração, ignore este email</li>
                        <li>Em caso de dúvidas, acesse sua conta diretamente pelo site</li>
                    </ul>
                </div>
                <div class="footer">
                    <p>Se você não solicitou esta alteração, pode ignorar este email com segurança.</p>
                    <p>© 2024 MeuBonsai - Cuidando dos seus bonsais com tecnologia</p>
                </div>
            </div>
        </body>
        </html>
        """

        text_content = f"""
        Confirmar Alteração de Senha - MeuBonsai

        Olá, {name}!

        Você solicitou a alteração da senha da sua conta no MeuBonsai.

        Para confirmar esta alteração, acesse:
        {change_url}

        Este link expira em 2 horas.

        Se você não solicitou esta alteração, ignore este email.
        """

        return self.send_email(email, subject, html_content, text_content)

# Instância global do serviço
email_service = EmailService()
