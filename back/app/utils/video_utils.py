"""
Utilities for video processing and thumbnail generation
"""
import subprocess
import tempfile
import os
import logging
from typing import Op<PERSON>, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)

def generate_video_thumbnail(
    video_file_path: str,
    output_path: str,
    timestamp: str = "00:00:01",
    width: int = 320,
    height: int = 240
) -> bool:
    """
    Generate a thumbnail from a video file using FFmpeg
    
    Args:
        video_file_path: Path to the input video file
        output_path: Path where the thumbnail should be saved
        timestamp: Time position to extract frame from (format: HH:MM:SS)
        width: Thumbnail width in pixels
        height: Thumbnail height in pixels
    
    Returns:
        bool: True if thumbnail was generated successfully, False otherwise
    """
    try:
        # FFmpeg command to extract thumbnail
        cmd = [
            'ffmpeg',
            '-i', video_file_path,           # Input video
            '-ss', timestamp,                # Seek to timestamp
            '-vframes', '1',                 # Extract only 1 frame
            '-vf', f'scale={width}:{height}', # Resize to specified dimensions
            '-q:v', '2',                     # High quality (1-31, lower is better)
            '-y',                            # Overwrite output file if exists
            output_path                      # Output thumbnail path
        ]
        
        # Run FFmpeg command
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=30  # 30 second timeout
        )
        
        if result.returncode == 0:
            logger.info(f"Thumbnail generated successfully: {output_path}")
            return True
        else:
            logger.error(f"FFmpeg error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error(f"FFmpeg timeout while generating thumbnail for {video_file_path}")
        return False
    except FileNotFoundError:
        logger.error("FFmpeg not found. Please install FFmpeg to generate video thumbnails.")
        return False
    except Exception as e:
        logger.error(f"Error generating thumbnail: {str(e)}")
        return False

def get_video_info(video_file_path: str) -> Optional[dict]:
    """
    Get video information using FFprobe
    
    Args:
        video_file_path: Path to the video file
    
    Returns:
        dict: Video information (duration, width, height, etc.) or None if error
    """
    try:
        cmd = [
            'ffprobe',
            '-v', 'quiet',
            '-print_format', 'json',
            '-show_format',
            '-show_streams',
            video_file_path
        ]
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            import json
            info = json.loads(result.stdout)
            
            # Extract video stream info
            video_stream = None
            for stream in info.get('streams', []):
                if stream.get('codec_type') == 'video':
                    video_stream = stream
                    break
            
            if video_stream:
                duration = float(info.get('format', {}).get('duration', 0))
                width = int(video_stream.get('width', 0))
                height = int(video_stream.get('height', 0))
                
                return {
                    'duration_seconds': duration,
                    'width': width,
                    'height': height,
                    'codec': video_stream.get('codec_name'),
                    'fps': eval(video_stream.get('r_frame_rate', '0/1'))
                }
        
        return None
        
    except Exception as e:
        logger.error(f"Error getting video info: {str(e)}")
        return None

def create_thumbnail_from_upload(
    video_content: bytes,
    original_filename: str
) -> Tuple[Optional[bytes], Optional[str]]:
    """
    Create thumbnail from uploaded video content

    Args:
        video_content: Video file content as bytes
        original_filename: Original filename for extension detection

    Returns:
        Tuple[Optional[bytes], Optional[str]]: (thumbnail_content, thumbnail_filename)
    """
    try:
        # Get file extension
        file_ext = Path(original_filename).suffix.lower()

        # Create temporary files
        with tempfile.NamedTemporaryFile(suffix=file_ext, delete=False) as video_temp:
            video_temp.write(video_content)
            video_temp_path = video_temp.name

        # Create thumbnail filename
        base_name = Path(original_filename).stem
        thumbnail_filename = f"{base_name}_thumb.jpg"

        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as thumb_temp:
            thumb_temp_path = thumb_temp.name

        try:
            # Generate thumbnail - try multiple timestamps
            # Try different timestamps in case video is very short
            timestamps = ["00:00:01", "00:00:00.5", "00:00:00.1", "00:00:00"]
            success = False

            for timestamp in timestamps:
                success = generate_video_thumbnail(
                    video_file_path=video_temp_path,
                    output_path=thumb_temp_path,
                    timestamp=timestamp,
                    width=320,
                    height=240
                )

                if success and os.path.exists(thumb_temp_path) and os.path.getsize(thumb_temp_path) > 0:
                    break
                else:
                    # Remove failed thumbnail file if it exists
                    if os.path.exists(thumb_temp_path):
                        os.unlink(thumb_temp_path)
                        # Recreate the temp file for next attempt
                        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as new_thumb_temp:
                            thumb_temp_path = new_thumb_temp.name

            if success and os.path.exists(thumb_temp_path):
                # Check thumbnail file size
                thumb_size = os.path.getsize(thumb_temp_path)

                if thumb_size > 0:
                    # Read thumbnail content
                    with open(thumb_temp_path, 'rb') as f:
                        thumbnail_content = f.read()

                    return thumbnail_content, thumbnail_filename
                else:
                    logger.error("Generated thumbnail file is empty")
                    return None, None
            else:
                logger.error("Failed to generate thumbnail")
                return None, None

        finally:
            # Clean up temporary files
            try:
                if os.path.exists(video_temp_path):
                    os.unlink(video_temp_path)
                if os.path.exists(thumb_temp_path):
                    os.unlink(thumb_temp_path)
            except Exception as cleanup_error:
                logger.warning(f"Error during thumbnail cleanup: {cleanup_error}")

    except Exception as e:
        logger.error(f"Error creating thumbnail from upload: {str(e)}")
        return None, None

def is_ffmpeg_available() -> bool:
    """
    Check if FFmpeg is available in the system
    
    Returns:
        bool: True if FFmpeg is available, False otherwise
    """
    try:
        result = subprocess.run(
            ['ffmpeg', '-version'],
            capture_output=True,
            timeout=5
        )
        return result.returncode == 0
    except:
        return False
