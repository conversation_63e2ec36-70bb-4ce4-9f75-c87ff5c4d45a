#!/usr/bin/env python3
"""
Script para criar os planos de assinatura iniciais
"""

import sys
import os
sys.path.append('/app')

from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.subscription import SubscriptionPlan, BillingCycle

def create_plans():
    """
    Criar os planos de assinatura padrão
    """
    db: Session = SessionLocal()
    
    try:
        # Verificar se os planos já existem
        existing_plans = db.query(SubscriptionPlan).count()
        if existing_plans > 0:
            print("Planos já existem no banco de dados")
            return
        
        # Plano Gratuito
        free_plan = SubscriptionPlan(
            name="free",
            display_name="Gratuito",
            description="Perfeito para começar sua coleção",
            price_cents=0,
            currency="BRL",
            billing_cycle=BillingCycle.monthly,
            max_plants=10,  # Reduced from 20 to 10
            max_photos_per_plant=30,  # Reduced from 50 to 30
            max_photos_per_care=1,
            max_videos_per_plant=0,
            max_videos_per_care=0,
            allows_videos=False,
            is_active=True
        )

        # Plano Premium Mensal
        premium_monthly_plan = SubscriptionPlan(
            name="premium_monthly",
            display_name="Premium Mensal",
            description="Para colecionadores sérios e profissionais",
            price_cents=1800,  # R$ 18,00
            currency="BRL",
            billing_cycle=BillingCycle.monthly,
            max_plants=None,  # Ilimitado
            max_photos_per_plant=None,  # Ilimitado
            max_photos_per_care=10,
            max_videos_per_plant=None,  # Ilimitado
            max_videos_per_care=10,
            allows_videos=True,
            is_active=True
        )

        # Plano Premium Anual
        premium_annual_plan = SubscriptionPlan(
            name="premium_annual",
            display_name="Premium Anual",
            description="Premium anual - Economize com pagamento anual",
            price_cents=1500,  # R$ 15,00 por mês (equivalente mensal de R$ 180/ano)
            currency="BRL",
            billing_cycle=BillingCycle.yearly,
            max_plants=None,  # Ilimitado
            max_photos_per_plant=None,  # Ilimitado
            max_photos_per_care=10,
            max_videos_per_plant=None,  # Ilimitado
            max_videos_per_care=10,
            allows_videos=True,
            is_active=True
        )
        
        # Adicionar planos ao banco
        db.add(free_plan)
        db.add(premium_monthly_plan)
        db.add(premium_annual_plan)

        db.commit()

        print("✅ Planos criados com sucesso!")
        print(f"- {free_plan.display_name}: {free_plan.price_display}")
        print(f"- {premium_monthly_plan.display_name}: {premium_monthly_plan.price_display}")
        print(f"- {premium_annual_plan.display_name}: {premium_annual_plan.price_display}")
        
    except Exception as e:
        print(f"❌ Erro ao criar planos: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_plans()
