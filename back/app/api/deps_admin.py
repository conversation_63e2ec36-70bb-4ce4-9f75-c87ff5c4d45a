from fastapi import HTTPException, Depends, status
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.db.session import get_db


def get_admin_user(
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(get_db)
) -> User:
    """
    Dependency to ensure only admin users can access admin endpoints.
    <NAME_EMAIL> is considered admin.
    """
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required."
        )

    if current_user.email != "<EMAIL>":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied. Admin privileges required."
        )

    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin account is inactive."
        )

    return current_user


def verify_admin_access(email: str) -> bool:
    """
    Verify if user has admin access.
    Can be extended in the future to support multiple admins.
    """
    admin_emails = [
        "<EMAIL>"
    ]
    return email in admin_emails
