from typing import Any
from fastapi import APIRouter, HTTPException, status
import logging
from app.schemas.contact import ContactRequest, ContactResponse
from app.services.email_service import EmailService
from app.core.config import settings

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/", response_model=ContactResponse)
async def send_contact_message(
    *,
    contact_data: ContactRequest
) -> Any:
    """
    Enviar mensagem de contato
    """
    try:
        logger.info(f"Recebida mensagem de contato: tipo={contact_data.type}, assunto={contact_data.subject}")
        
        # Mapear tipos para descrições
        type_descriptions = {
            'bug': '🐛 Reportar Bug',
            'suggestion': '💡 Sugestão/Ideia', 
            'support': '💬 Suporte Geral'
        }
        
        type_description = type_descriptions.get(contact_data.type, contact_data.type)
        
        # Preparar conteúdo do email
        subject = f"[MeuBonsai] {type_description}: {contact_data.subject}"
        
        # Email do remetente (se fornecido) ou "anônimo"
        sender_email = contact_data.email or "Usuário anônimo"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Nova Mensagem de Contato - MeuBonsai</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0;">
                <h1 style="margin: 0; font-size: 24px;">📧 Nova Mensagem de Contato</h1>
                <p style="margin: 5px 0 0 0; opacity: 0.9;">MeuBonsai.App</p>
            </div>
            
            <div style="background: white; padding: 30px; border: 1px solid #ddd; border-top: none;">
                <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin-bottom: 20px;">
                    <h2 style="margin: 0 0 10px 0; color: #495057;">📋 Detalhes da Mensagem</h2>
                    <p style="margin: 5px 0;"><strong>Tipo:</strong> {type_description}</p>
                    <p style="margin: 5px 0;"><strong>Assunto:</strong> {contact_data.subject}</p>
                    <p style="margin: 5px 0;"><strong>Email:</strong> {sender_email}</p>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <h3 style="color: #495057; margin-bottom: 10px;">💬 Mensagem:</h3>
                    <div style="background: #f8f9fa; padding: 15px; border-left: 4px solid #667eea; border-radius: 0 6px 6px 0;">
                        <p style="margin: 0; white-space: pre-wrap;">{contact_data.message}</p>
                    </div>
                </div>
                
                <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
                
                <div style="font-size: 12px; color: #6c757d;">
                    <p style="margin: 0;">Esta mensagem foi enviada através do formulário de contato do MeuBonsai.App</p>
                    <p style="margin: 5px 0 0 0;">Data: {contact_data.__dict__.get('timestamp', 'Agora')}</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        text_content = f"""
        Nova Mensagem de Contato - MeuBonsai
        
        Tipo: {type_description}
        Assunto: {contact_data.subject}
        Email: {sender_email}
        
        Mensagem:
        {contact_data.message}
        
        ---
        Esta mensagem foi enviada através do formulário de contato do MeuBonsai.App
        """
        
        # Enviar email
        email_service = EmailService()
        success = email_service.send_email(
            to_email="<EMAIL>",
            subject=subject,
            html_content=html_content,
            text_content=text_content
        )

        if success:
            logger.info(f"✅ EMAIL ENVIADO COM <NAME_EMAIL>")
            logger.info(f"📧 Assunto: {subject}")
            logger.info(f"👤 Remetente: {sender_email}")
            return ContactResponse(
                success=True,
                message="Mensagem enviada com sucesso! Responderemos em breve."
            )
        else:
            # Log detalhado do erro mas não falha completamente em desenvolvimento
            logger.error("Falha ao enviar email de contato - possível problema de configuração SMTP")
            logger.error(f"Dados da mensagem: tipo={contact_data.type}, assunto={contact_data.subject}, email={sender_email}")
            logger.error(f"Mensagem: {contact_data.message[:100]}...")

            # Registrar erro mas não falhar completamente
            # As mensagens ficam registradas nos logs para auditoria
            logger.warning("⚠️ ATENÇÃO: Email não foi enviado, mas mensagem foi registrada nos logs")
            logger.warning("📧 Para receber a mensagem, verifique os logs do servidor")
            logger.warning("🔧 Verifique as configurações SMTP para corrigir o problema")

            return ContactResponse(
                success=True,
                message="Mensagem registrada com sucesso! Responderemos em breve."
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro inesperado ao processar mensagem de contato: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno do servidor. Tente novamente ou envie <NAME_EMAIL>"
        )
