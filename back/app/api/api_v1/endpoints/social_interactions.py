from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from uuid import UUID

from app import crud, models, schemas
from app.api import deps
from app.db.session import get_db
from app.models.social import FeedActivityType
from app.models.notification import NotificationType

router = APIRouter()


@router.get("/feed", response_model=schemas.FeedResponse)
def get_user_feed(
    *,
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get social feed for current user (activities from followed users)
    """
    activities, total = crud.feed_activity.get_user_feed(
        db=db, user_id=current_user.id, skip=skip, limit=limit
    )
    
    return {
        "activities": activities,
        "total": total,
        "page": (skip // limit) + 1,
        "per_page": limit,
        "has_next": skip + limit < total,
        "has_prev": skip > 0
    }


@router.post("/plants/{plant_id}/like", response_model=schemas.LikeActionResponse)
def like_plant(
    *,
    db: Session = Depends(get_db),
    plant_id: UUID,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Like a plant
    """
    # Check if plant exists and is accessible
    plant = crud.plant.get(db=db, id=plant_id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    
    # Check if plant is public (can only like public plants)
    if not plant.is_public:
        raise HTTPException(status_code=403, detail="Cannot like private plants")
    
    # Check if user is not the owner (can't like own plants)
    if plant.owner_id == current_user.id:
        raise HTTPException(status_code=400, detail="Cannot like your own plant")
    
    # Like the plant (idempotent)
    like = crud.plant_like.like_plant(
        db=db, user_id=current_user.id, plant_id=plant_id
    )
    
    # Get updated likes count
    likes_count = crud.plant_like.get_plant_likes_count(db=db, plant_id=plant_id)
    
    # Create notification for plant owner (only if it's a new like)
    if like:
        try:
            liker_name = f"{current_user.first_name} {current_user.last_name}"
            crud.notification.create_plant_like_notification(
                db=db,
                plant_owner_id=str(plant.owner_id),
                liker_id=str(current_user.id),
                liker_name=liker_name,
                plant_id=str(plant_id),
                plant_name=plant.name,
                like_id=str(like.id)
            )
        except Exception as e:
            # Don't fail the like if notification creation fails
            print(f"Failed to create like notification: {e}")
    
    return {
        "success": True,
        "is_liked": True,
        "likes_count": likes_count,
        "message": "Planta curtida com sucesso!"
    }


@router.delete("/plants/{plant_id}/like", response_model=schemas.LikeActionResponse)
def unlike_plant(
    *,
    db: Session = Depends(get_db),
    plant_id: UUID,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Unlike a plant
    """
    # Check if plant exists and is accessible
    plant = crud.plant.get(db=db, id=plant_id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")

    # Check if plant is public (can only unlike public plants)
    if not plant.is_public:
        raise HTTPException(status_code=403, detail="Cannot unlike private plants")

    # Check if user is not the owner (can't unlike own plants)
    if plant.owner_id == current_user.id:
        raise HTTPException(status_code=400, detail="Cannot unlike your own plant")

    # Unlike the plant
    success = crud.plant_like.unlike_plant(
        db=db, user_id=current_user.id, plant_id=plant_id
    )

    if not success:
        raise HTTPException(status_code=400, detail="Plant was not liked")

    # Get updated likes count
    likes_count = crud.plant_like.get_plant_likes_count(db=db, plant_id=plant_id)

    return {
        "success": True,
        "is_liked": False,
        "likes_count": likes_count,
        "message": "Curtida removida com sucesso!"
    }


@router.get("/plants/{plant_id}/likes")
def get_plant_likes(
    *,
    db: Session = Depends(get_db),
    plant_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get likes for a plant
    """
    # Check if plant exists and is accessible
    plant = crud.plant.get(db=db, id=plant_id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    
    # Check if plant is public or user is owner
    if not plant.is_public and plant.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Cannot view likes of private plants")
    
    likes, total = crud.plant_like.get_plant_likes(
        db=db, plant_id=plant_id, skip=skip, limit=limit
    )
    
    return {
        "likes": likes,
        "total": total,
        "page": (skip // limit) + 1,
        "per_page": limit,
        "has_next": skip + limit < total,
        "has_prev": skip > 0
    }


@router.post("/plants/{plant_id}/comments", response_model=schemas.PlantComment)
def create_plant_comment(
    *,
    db: Session = Depends(get_db),
    plant_id: UUID,
    comment_data: schemas.PlantCommentCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create a comment on a plant
    """
    # Check if plant exists and is accessible
    plant = crud.plant.get(db=db, id=plant_id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    
    # Check if plant is public (can only comment on public plants)
    if not plant.is_public:
        raise HTTPException(status_code=403, detail="Cannot comment on private plants")
    
    # Validate comment content
    if not comment_data.content.strip():
        raise HTTPException(status_code=400, detail="Comment cannot be empty")
    
    if len(comment_data.content) > 1000:
        raise HTTPException(status_code=400, detail="Comment too long (max 1000 characters)")
    
    # Create comment
    comment = crud.plant_comment.create_comment(
        db=db, 
        user_id=current_user.id, 
        plant_id=plant_id, 
        content=comment_data.content.strip()
    )
    
    # Create notification for plant owner (only if commenter is not the owner)
    if plant.owner_id != current_user.id:
        try:
            commenter_name = f"{current_user.first_name} {current_user.last_name}"
            crud.notification.create_plant_comment_notification(
                db=db,
                plant_owner_id=str(plant.owner_id),
                commenter_id=str(current_user.id),
                commenter_name=commenter_name,
                plant_id=str(plant_id),
                plant_name=plant.name,
                comment_id=str(comment.id),
                comment_preview=comment_data.content.strip()
            )
        except Exception as e:
            # Don't fail the comment if notification creation fails
            print(f"Failed to create comment notification: {e}")
    
    # Return comment with user info
    return {
        "id": comment.id,
        "user_id": comment.user_id,
        "plant_id": comment.plant_id,
        "content": comment.content,
        "created_at": comment.created_at,
        "updated_at": comment.updated_at,
        "user_first_name": current_user.first_name,
        "user_last_name": current_user.last_name,
        "user_username": current_user.username,
        "user_avatar": current_user.avatar
    }


@router.get("/plants/{plant_id}/comments", response_model=schemas.CommentListResponse)
def get_plant_comments(
    *,
    db: Session = Depends(get_db),
    plant_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get comments for a plant
    """
    # Check if plant exists and is accessible
    plant = crud.plant.get(db=db, id=plant_id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    
    # Check if plant is public or user is owner
    if not plant.is_public and plant.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Cannot view comments of private plants")
    
    comments, total = crud.plant_comment.get_plant_comments(
        db=db, plant_id=plant_id, skip=skip, limit=limit
    )
    
    return {
        "comments": comments,
        "total": total,
        "page": (skip // limit) + 1,
        "per_page": limit,
        "has_next": skip + limit < total,
        "has_prev": skip > 0
    }


@router.delete("/comments/{comment_id}")
def delete_plant_comment(
    *,
    db: Session = Depends(get_db),
    comment_id: UUID,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete a comment (only by comment owner or plant owner)
    """
    success = crud.plant_comment.delete_comment(
        db=db, comment_id=comment_id, user_id=current_user.id
    )
    
    if not success:
        raise HTTPException(
            status_code=404, 
            detail="Comment not found or you don't have permission to delete it"
        )
    
    return {"message": "Comment deleted successfully"}


@router.get("/plants/{plant_id}/social-info")
def get_plant_social_info(
    *,
    db: Session = Depends(get_db),
    plant_id: UUID,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get social information for a plant (likes count, comments count, user's like status)
    """
    # Check if plant exists and is accessible
    plant = crud.plant.get(db=db, id=plant_id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    
    # Check if plant is public or user is owner
    if not plant.is_public and plant.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Cannot view social info of private plants")
    
    likes_count = crud.plant_like.get_plant_likes_count(db=db, plant_id=plant_id)
    comments_count = crud.plant_comment.get_plant_comments_count(db=db, plant_id=plant_id)
    is_liked_by_user = crud.plant_like.is_plant_liked_by_user(
        db=db, plant_id=plant_id, user_id=current_user.id
    )
    
    return {
        "plant_id": plant_id,
        "likes_count": likes_count,
        "comments_count": comments_count,
        "is_liked_by_user": is_liked_by_user,
        "can_like": plant.owner_id != current_user.id and plant.is_public,
        "can_comment": plant.is_public
    }
