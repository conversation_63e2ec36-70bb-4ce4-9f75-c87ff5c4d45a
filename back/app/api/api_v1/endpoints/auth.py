from datetime import timedel<PERSON>
from typing import Any
from fastapi import APIRouter, Body, Depends, HTTPException
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
import logging

from app import crud, models, schemas
from app.api import deps
from app.core import security
from app.core.config import settings
from app.db.session import get_db
from app.services.email_service import email_service
from app.models.auth_token import TokenType
from app.models.temp_password_change import TempPasswordChange

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/login", response_model=schemas.Token)
def login_access_token(
    db: Session = Depends(get_db), form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    """
    OAuth2 compatible token login, get an access token for future requests
    """
    user = crud.user.authenticate(
        db, email=form_data.username, password=form_data.password
    )
    if not user:
        raise HTTPException(status_code=400, detail="Incorrect email or password")
    elif not crud.user.is_active(user):
        raise HTTPException(
            status_code=400,
            detail="Conta não ativada. Verifique seu email e clique no link de ativação."
        )
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    return {
        "access_token": security.create_access_token(
            user.id, expires_delta=access_token_expires
        ),
        "token_type": "bearer",
    }


@router.post("/register")
def register(
    *,
    db: Session = Depends(get_db),
    user_in: schemas.UserCreate,
) -> Any:
    """
    Create new user and send activation email.
    """
    user = crud.user.get_by_email(db, email=user_in.email)
    if user:
        raise HTTPException(
            status_code=400,
            detail="The user with this email already exists in the system.",
        )

    # Criar usuário (inativo por padrão)
    user = crud.user.create(db, obj_in=user_in)

    try:
        # Criar token de ativação
        activation_token = crud.auth_token.create_activation_token(db, user_id=user.id)

        # Enviar email de boas-vindas com ativação
        email_sent = email_service.send_welcome_email(
            email=user.email,
            name=user.first_name,
            activation_token=activation_token.token
        )

        if not email_sent:
            logger.warning(f"Failed to send welcome email to {user.email}")

        return {
            "message": "Usuário criado com sucesso! Verifique seu email para ativar a conta.",
            "email": user.email
        }

    except Exception as e:
        logger.error(f"Error during user registration: {e}")
        # Se falhar o envio do email, ainda retorna sucesso mas avisa
        return {
            "message": "Usuário criado, mas houve problema no envio do email. Entre em contato com o suporte.",
            "email": user.email
        }


@router.post("/activate-account")
def activate_account(
    *,
    db: Session = Depends(get_db),
    token: str = Body(..., embed=True),
) -> Any:
    """
    Activate user account with token.
    """
    from app.models.auth_token import TokenType

    # Buscar token válido
    auth_token = crud.auth_token.get_valid_token(
        db, token=token, token_type=TokenType.ACTIVATION
    )

    if not auth_token:
        raise HTTPException(
            status_code=400,
            detail="Token inválido ou expirado."
        )

    # Buscar usuário
    user = crud.user.get(db, id=auth_token.user_id)
    if not user:
        raise HTTPException(
            status_code=404,
            detail="Usuário não encontrado."
        )

    # Ativar usuário
    user.is_active = True
    user.email_verified = True
    db.commit()

    # Marcar token como usado
    crud.auth_token.use_token(db, token=auth_token)

    return {"message": "Conta ativada com sucesso! Você já pode fazer login."}


@router.post("/forgot-password")
def forgot_password(
    *,
    db: Session = Depends(get_db),
    email: str = Body(..., embed=True),
) -> Any:
    """
    Send password reset email.
    """
    user = crud.user.get_by_email(db, email=email)

    # Sempre retorna sucesso por segurança (não revela se email existe)
    if not user:
        return {"message": "Se o email existir em nossa base, você receberá instruções para redefinir sua senha."}

    try:
        # Criar token de reset
        reset_token = crud.auth_token.create_password_reset_token(db, user_id=user.id)

        # Tentar enviar email de reset
        try:
            email_sent = email_service.send_password_reset_email(
                email=user.email,
                name=user.first_name,
                reset_token=reset_token.token
            )

            if not email_sent:
                logger.warning(f"Failed to send password reset email to {user.email}")
            else:
                logger.info(f"Password reset email sent successfully to {user.email}")

        except Exception as email_error:
            logger.error(f"Error sending password reset email to {user.email}: {email_error}")

    except Exception as e:
        logger.error(f"Error during password reset request: {e}")

    return {"message": "Se o email existir em nossa base, você receberá instruções para redefinir sua senha."}


@router.post("/reset-password")
def reset_password(
    *,
    db: Session = Depends(get_db),
    token: str = Body(...),
    new_password: str = Body(...),
) -> Any:
    """
    Reset password with token.
    """

    # Buscar token válido
    auth_token = crud.auth_token.get_valid_token(
        db, token=token, token_type=TokenType.PASSWORD_RESET
    )

    if not auth_token:
        raise HTTPException(
            status_code=400,
            detail="Token inválido ou expirado."
        )

    # Buscar usuário
    user = crud.user.get(db, id=auth_token.user_id)
    if not user:
        raise HTTPException(
            status_code=404,
            detail="Usuário não encontrado."
        )

    # Atualizar senha
    hashed_password = security.get_password_hash(new_password)
    user.hashed_password = hashed_password
    db.commit()

    # Marcar token como usado
    crud.auth_token.use_token(db, token=auth_token)

    return {"message": "Senha redefinida com sucesso! Você já pode fazer login com a nova senha."}


@router.post("/request-password-change")
def request_password_change(
    *,
    db: Session = Depends(get_db),
    password_data: schemas.PasswordChangeRequest,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Request password change with email confirmation.
    """
    # Verificar senha atual
    if not security.verify_password(password_data.current_password, current_user.hashed_password):
        raise HTTPException(
            status_code=400,
            detail="Senha atual incorreta."
        )

    # Validar nova senha (mínimo 8 caracteres)
    if len(password_data.new_password) < 8:
        raise HTTPException(
            status_code=400,
            detail="A nova senha deve ter pelo menos 8 caracteres."
        )

    # Verificar se a nova senha é diferente da atual
    if security.verify_password(password_data.new_password, current_user.hashed_password):
        raise HTTPException(
            status_code=400,
            detail="A nova senha deve ser diferente da senha atual."
        )

    try:
        # Criar token de alteração de senha
        change_token = crud.auth_token.create_password_change_token(db, user_id=current_user.id)

        # Armazenar temporariamente a nova senha no token (hash)
        # Vamos usar um campo adicional ou criar uma tabela temporária
        # Por simplicidade, vamos armazenar no próprio token como metadata
        hashed_new_password = security.get_password_hash(password_data.new_password)

        # Atualizar o token com a nova senha hasheada (usando um campo personalizado)
        # Como não temos campo para isso, vamos criar uma entrada temporária
        temp_change = TempPasswordChange(
            token_id=change_token.id,
            new_password_hash=hashed_new_password
        )
        db.add(temp_change)
        db.commit()

        # Enviar email de confirmação
        email_sent = email_service.send_password_change_email(
            email=current_user.email,
            name=current_user.first_name,
            change_token=change_token.token
        )

        if not email_sent:
            logger.warning(f"Failed to send password change email to {current_user.email}")

        return {
            "message": "Solicitação de alteração de senha enviada! Verifique seu email para confirmar a alteração."
        }

    except Exception as e:
        logger.error(f"Error during password change request: {e}")
        raise HTTPException(
            status_code=500,
            detail="Erro interno do servidor. Tente novamente mais tarde."
        )


@router.post("/confirm-password-change")
def confirm_password_change(
    *,
    db: Session = Depends(get_db),
    token_data: schemas.PasswordChangeConfirm,
) -> Any:
    """
    Confirm password change with token.
    """

    # Buscar token válido
    auth_token = crud.auth_token.get_valid_token(
        db, token=token_data.token, token_type=TokenType.PASSWORD_CHANGE
    )

    if not auth_token:
        raise HTTPException(
            status_code=400,
            detail="Token inválido ou expirado."
        )

    # Buscar usuário
    user = crud.user.get(db, id=auth_token.user_id)
    if not user:
        raise HTTPException(
            status_code=404,
            detail="Usuário não encontrado."
        )

    # Buscar a nova senha temporária
    temp_change = db.query(TempPasswordChange).filter(
        TempPasswordChange.token_id == auth_token.id
    ).first()

    if not temp_change:
        raise HTTPException(
            status_code=400,
            detail="Dados de alteração não encontrados."
        )

    try:
        # Salvar a nova senha antes de fazer qualquer operação
        new_password_hash = temp_change.new_password_hash

        # Atualizar senha do usuário
        user.hashed_password = new_password_hash

        # Marcar token como usado (sem commit automático)
        auth_token.mark_as_used()

        # Remover entrada temporária
        db.delete(temp_change)

        # Fazer commit de todas as operações de uma vez
        db.commit()

        logger.info(f"Password changed successfully for user {user.email}")

        return {"message": "Senha alterada com sucesso! Você já pode fazer login com a nova senha."}

    except Exception as e:
        logger.error(f"Error confirming password change: {e}")
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail="Erro interno do servidor. Tente novamente mais tarde."
        )


@router.post("/test-token", response_model=schemas.User)
def test_token(current_user: models.User = Depends(deps.get_current_user)) -> Any:
    """
    Test access token
    """
    return current_user
