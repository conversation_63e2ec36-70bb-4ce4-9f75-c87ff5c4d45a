from typing import Any, List
from uuid import UUID
from datetime import date, timedelta
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.api import deps
from app.db.session import get_db

router = APIRouter()


@router.get("/", response_model=List[schemas.ReminderList])
def read_reminders(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    include_completed: bool = False,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Retrieve user's reminders.
    """
    # Get all plants for the user first
    user_plants = crud.plant.get_by_owner(db, owner_id=current_user.id)
    plant_ids = [plant.id for plant in user_plants]
    
    if not plant_ids:
        return []
    
    # Get reminders for user's plants
    reminders = []
    for plant in user_plants:
        plant_reminders = crud.reminder.get_by_plant(
            db, plant_id=plant.id, skip=skip, limit=limit, include_completed=include_completed
        )
        for reminder in plant_reminders:
            reminder_dict = reminder.__dict__.copy()
            reminder_dict["plant_name"] = plant.name
            reminder_dict["care_type_display"] = reminder.care_type_display
            reminder_dict["days_until_due"] = reminder.days_until_due
            reminder_dict["is_overdue"] = reminder.is_overdue
            reminders.append(schemas.ReminderList(**reminder_dict))
    
    # Sort by scheduled date
    reminders.sort(key=lambda x: x.scheduled_date)
    
    return reminders


@router.get("/upcoming", response_model=List[schemas.ReminderList])
def read_upcoming_reminders(
    db: Session = Depends(get_db),
    days_ahead: int = Query(7, ge=1, le=30),
    timezone_offset: int = Query(-3, description="User timezone offset in hours"),
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Get upcoming reminders for the next X days.
    """
    reminders = crud.reminder.get_upcoming_reminders(
        db, owner_id=current_user.id, days_ahead=days_ahead
    )

    result = []
    for reminder in reminders:
        reminder_dict = reminder.__dict__.copy()
        reminder_dict["plant_name"] = reminder.plant.name
        reminder_dict["care_type_display"] = reminder.care_type_display
        reminder_dict["days_until_due"] = reminder.get_days_until_due(timezone_offset)
        reminder_dict["is_overdue"] = reminder.get_is_overdue(timezone_offset)
        result.append(schemas.ReminderList(**reminder_dict))

    return result


@router.get("/overdue", response_model=List[schemas.ReminderList])
def read_overdue_reminders(
    db: Session = Depends(get_db),
    timezone_offset: int = Query(-3, description="User timezone offset in hours"),
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Get overdue reminders.
    """
    reminders = crud.reminder.get_overdue_reminders(db, owner_id=current_user.id)

    result = []
    for reminder in reminders:
        reminder_dict = reminder.__dict__.copy()
        reminder_dict["plant_name"] = reminder.plant.name
        reminder_dict["care_type_display"] = reminder.care_type_display
        reminder_dict["days_until_due"] = reminder.get_days_until_due(timezone_offset)
        reminder_dict["is_overdue"] = reminder.get_is_overdue(timezone_offset)
        result.append(schemas.ReminderList(**reminder_dict))

    return result


@router.post("/", response_model=schemas.Reminder)
def create_reminder(
    *,
    db: Session = Depends(get_db),
    reminder_in: schemas.ReminderCreate,
    plant_id: UUID,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Create new reminder.
    """
    plant = crud.plant.get(db=db, id=plant_id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    if plant.owner_id != current_user.id:
        raise HTTPException(status_code=400, detail="Not enough permissions")

    reminder = crud.reminder.create_with_plant(db=db, obj_in=reminder_in, plant_id=plant_id)

    # Add computed properties
    reminder_dict = reminder.__dict__.copy()
    reminder_dict["care_type_display"] = reminder.care_type_display
    reminder_dict["days_until_due"] = reminder.days_until_due
    reminder_dict["is_overdue"] = reminder.is_overdue

    return schemas.Reminder(**reminder_dict)


@router.post("/create", response_model=schemas.Reminder)
def create_reminder_with_plant_id_in_body(
    *,
    db: Session = Depends(get_db),
    reminder_data: dict,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Create new reminder with plant_id in body (alternative endpoint).
    """
    # Extract plant_id from body
    plant_id = reminder_data.get("plant_id")
    if not plant_id:
        raise HTTPException(status_code=400, detail="plant_id is required")

    try:
        plant_id = UUID(plant_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid plant_id format")

    plant = crud.plant.get(db=db, id=plant_id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    if plant.owner_id != current_user.id:
        raise HTTPException(status_code=400, detail="Not enough permissions")

    # Create reminder schema from data
    reminder_in = schemas.ReminderCreate(
        care_type=reminder_data.get("care_type"),
        scheduled_date=reminder_data.get("scheduled_date"),
        description=reminder_data.get("description")
    )

    reminder = crud.reminder.create_with_plant(db=db, obj_in=reminder_in, plant_id=plant_id)

    # Add computed properties
    reminder_dict = reminder.__dict__.copy()
    reminder_dict["care_type_display"] = reminder.care_type_display
    reminder_dict["days_until_due"] = reminder.days_until_due
    reminder_dict["is_overdue"] = reminder.is_overdue

    return schemas.Reminder(**reminder_dict)


@router.get("/{id}", response_model=schemas.Reminder)
def read_reminder(
    *,
    db: Session = Depends(get_db),
    id: UUID,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Get reminder by ID.
    """
    reminder = crud.reminder.get(db=db, id=id)
    if not reminder:
        raise HTTPException(status_code=404, detail="Reminder not found")
    
    # Check if user owns the plant
    plant = crud.plant.get(db=db, id=reminder.plant_id)
    if not plant or plant.owner_id != current_user.id:
        raise HTTPException(status_code=400, detail="Not enough permissions")

    # Add computed properties
    reminder_dict = reminder.__dict__.copy()
    reminder_dict["care_type_display"] = reminder.care_type_display
    reminder_dict["days_until_due"] = reminder.days_until_due
    reminder_dict["is_overdue"] = reminder.is_overdue
    
    return schemas.Reminder(**reminder_dict)


@router.put("/{id}", response_model=schemas.Reminder)
def update_reminder(
    *,
    db: Session = Depends(get_db),
    id: UUID,
    reminder_in: schemas.ReminderUpdate,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Update reminder.
    """
    reminder = crud.reminder.get(db=db, id=id)
    if not reminder:
        raise HTTPException(status_code=404, detail="Reminder not found")
    
    # Check if user owns the plant
    plant = crud.plant.get(db=db, id=reminder.plant_id)
    if not plant or plant.owner_id != current_user.id:
        raise HTTPException(status_code=400, detail="Not enough permissions")

    reminder = crud.reminder.update(db=db, db_obj=reminder, obj_in=reminder_in)
    
    # Add computed properties
    reminder_dict = reminder.__dict__.copy()
    reminder_dict["care_type_display"] = reminder.care_type_display
    reminder_dict["days_until_due"] = reminder.days_until_due
    reminder_dict["is_overdue"] = reminder.is_overdue
    
    return schemas.Reminder(**reminder_dict)


@router.post("/{id}/complete", response_model=schemas.Reminder)
def complete_reminder(
    *,
    db: Session = Depends(get_db),
    id: UUID,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Mark reminder as completed.
    """
    reminder = crud.reminder.get(db=db, id=id)
    if not reminder:
        raise HTTPException(status_code=404, detail="Reminder not found")
    
    # Check if user owns the plant
    plant = crud.plant.get(db=db, id=reminder.plant_id)
    if not plant or plant.owner_id != current_user.id:
        raise HTTPException(status_code=400, detail="Not enough permissions")

    reminder = crud.reminder.mark_as_completed(db=db, reminder_id=id)
    
    # Add computed properties
    reminder_dict = reminder.__dict__.copy()
    reminder_dict["care_type_display"] = reminder.care_type_display
    reminder_dict["days_until_due"] = reminder.days_until_due
    reminder_dict["is_overdue"] = reminder.is_overdue
    
    return schemas.Reminder(**reminder_dict)


@router.delete("/{id}")
def delete_reminder(
    *,
    db: Session = Depends(get_db),
    id: UUID,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Delete reminder and related notifications.
    """
    try:
        print(f"🗑️ [DELETE_REMINDER] Attempting to delete reminder: {id}")

        reminder = crud.reminder.get(db=db, id=id)
        if not reminder:
            print(f"❌ [DELETE_REMINDER] Reminder not found: {id}")
            raise HTTPException(status_code=404, detail="Reminder not found")

        # Check if user owns the plant
        plant = crud.plant.get(db=db, id=reminder.plant_id)
        if not plant or plant.owner_id != current_user.id:
            print(f"❌ [DELETE_REMINDER] Permission denied for user {current_user.id}")
            raise HTTPException(status_code=400, detail="Not enough permissions")

        print(f"🔗 [DELETE_REMINDER] Deleting related notifications...")
        # Delete related notifications first to avoid foreign key constraint
        from app.models.notification import Notification
        deleted_notifications = db.query(Notification).filter(Notification.reminder_id == id).delete()
        print(f"🗑️ [DELETE_REMINDER] Deleted {deleted_notifications} notifications")

        # Now delete the reminder
        print(f"🗑️ [DELETE_REMINDER] Deleting reminder...")
        crud.reminder.remove(db=db, id=id)

        print(f"✅ [DELETE_REMINDER] Successfully deleted reminder: {id}")
        return {"ok": True}

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ [DELETE_REMINDER] Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
