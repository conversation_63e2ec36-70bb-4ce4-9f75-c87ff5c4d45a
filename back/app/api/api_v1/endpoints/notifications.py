from typing import List
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.api import deps
from app.db.session import get_db

router = APIRouter()


@router.get("/", response_model=List[schemas.notification.Notification])
def get_notifications(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(deps.get_current_user),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    unread_only: bool = Query(False)
):
    """
    Get user notifications
    """
    notifications = crud.notification.get_user_notifications(
        db=db, 
        user_id=current_user.id, 
        skip=skip, 
        limit=limit,
        unread_only=unread_only
    )
    
    # Enriquecer com dados adicionais
    result = []
    for notification in notifications:
        notification_dict = notification.__dict__.copy()
        
        # Adicionar dados do seguidor se for notificação de seguidor
        if notification.type == models.NotificationType.new_follower and notification.follower_id:
            follower = db.query(models.User).filter(models.User.id == notification.follower_id).first()
            if follower:
                notification_dict["follower_name"] = follower.full_name
                notification_dict["follower_avatar"] = follower.avatar
        
        # Adicionar dados da planta se for notificação de lembrete
        if notification.type == models.NotificationType.reminder and notification.reminder_id:
            reminder = db.query(models.Reminder).filter(models.Reminder.id == notification.reminder_id).first()
            if reminder and reminder.plant:
                notification_dict["plant_name"] = reminder.plant.name
        
        result.append(schemas.notification.Notification(**notification_dict))
    
    return result


@router.get("/stats", response_model=schemas.notification.NotificationStats)
def get_notification_stats(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(deps.get_current_user)
):
    """
    Get notification statistics
    """
    total_notifications = crud.notification.get_user_notifications(
        db=db, user_id=current_user.id, limit=1000
    )
    unread_count = crud.notification.get_unread_count(db=db, user_id=current_user.id)
    unseen_count = crud.notification.get_unseen_count(db=db, user_id=current_user.id)

    return schemas.notification.NotificationStats(
        total_count=len(total_notifications),
        unread_count=unread_count,
        unseen_count=unseen_count
    )


@router.put("/{notification_id}/read")
def mark_notification_as_read(
    notification_id: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(deps.get_current_user)
):
    """
    Mark a notification as read
    """
    notification = crud.notification.mark_as_read(
        db=db, 
        notification_id=notification_id, 
        user_id=current_user.id
    )
    
    if not notification:
        raise HTTPException(status_code=404, detail="Notification not found")
    
    return {"message": "Notification marked as read"}


@router.put("/read-all")
def mark_all_notifications_as_read(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(deps.get_current_user)
):
    """
    Mark all notifications as read
    """
    count = crud.notification.mark_all_as_read(db=db, user_id=current_user.id)

    return {"message": f"{count} notifications marked as read"}


@router.put("/seen-all")
def mark_all_notifications_as_seen(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(deps.get_current_user)
):
    """
    Mark all notifications as seen (clears badge but keeps notifications)
    """
    count = crud.notification.mark_all_as_seen(db=db, user_id=current_user.id)

    return {"message": f"{count} notifications marked as seen"}


@router.delete("/cleanup")
def cleanup_old_notifications(
    days_old: int = Query(30, ge=1, le=365),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(deps.get_current_user)
):
    """
    Cleanup notifications older than specified days (user only - cleans their own notifications)
    """
    # Modificar para limpar apenas notificações do usuário atual
    count = crud.notification.cleanup_user_old_notifications(db=db, user_id=current_user.id, days_old=days_old)

    return {"message": f"{count} old notifications deleted"}
