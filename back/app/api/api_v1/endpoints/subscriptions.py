from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from sqlalchemy.sql import func
from typing import List, Optional
import logging

from app.api import deps
from app.core.config import settings
from app.db.session import get_db
from app.models.user import User
from app.models.subscription import SubscriptionPlan, UserSubscription, Payment, SubscriptionStatus
from app.models.coupon import Coupon, CouponUse
from app.schemas.subscription import (
    SubscriptionPlan as SubscriptionPlanSchema,
    CreateSubscriptionRequest,
    CreateSubscriptionResponse,
    PricingResponse,
    PricingPlan,
    WebhookNotification
)
from app.services.asaas_service import asaas_service
from datetime import datetime, timedelta
import math

router = APIRouter()


def get_user_plan_info(user: User, db: Session) -> dict:
    """
    Obter informações do plano atual do usuário
    """
    # Buscar assinatura ativa do usuário
    current_subscription = db.query(UserSubscription).filter(
        UserSubscription.user_id == user.id,
        UserSubscription.status == "active"
    ).first()

    if current_subscription:
        plan = current_subscription.plan
        return {
            "name": plan.name,
            "display_name": plan.display_name,
            "allows_videos": plan.allows_videos,
            "max_videos_per_plant": plan.max_videos_per_plant,
            "max_videos_per_care": plan.max_videos_per_care,
            "max_photos_per_care": plan.max_photos_per_care,
            "max_plants": plan.max_plants,
            "max_photos_per_plant": plan.max_photos_per_plant
        }
    else:
        # Usuário está no plano gratuito
        free_plan = db.query(SubscriptionPlan).filter(SubscriptionPlan.name == "free").first()
        if free_plan:
            return {
                "name": free_plan.name,
                "display_name": free_plan.display_name,
                "allows_videos": free_plan.allows_videos,
                "max_videos_per_plant": free_plan.max_videos_per_plant,
                "max_videos_per_care": free_plan.max_videos_per_care,
                "max_photos_per_care": free_plan.max_photos_per_care,
                "max_plants": free_plan.max_plants,
                "max_photos_per_plant": free_plan.max_photos_per_plant
            }
        else:
            # Fallback para plano gratuito básico
            return {
                "name": "free",
                "display_name": "Gratuito",
                "allows_videos": False,
                "max_videos_per_plant": 0,
                "max_videos_per_care": 0,
                "max_photos_per_care": 1,
                "max_plants": 10,  # Updated from 15 to 10
                "max_photos_per_plant": 30
            }
logger = logging.getLogger(__name__)

# Hierarquia de planos para validação de upgrade/downgrade
PLAN_HIERARCHY = {
    "free": 0,
    "premium_monthly": 1,
    "premium_annual": 1
}

def get_plan_tier(plan_name: str) -> int:
    """Retorna o tier do plano (0=free, 1=premium)"""
    return PLAN_HIERARCHY.get(plan_name, 0)

def is_valid_plan_change(current_plan_name: str, new_plan_name: str) -> dict:
    """Valida se a mudança de plano é permitida"""
    current_tier = get_plan_tier(current_plan_name)
    new_tier = get_plan_tier(new_plan_name)

    if new_tier < current_tier:
        return {
            "valid": False,
            "reason": "downgrade_not_allowed",
            "message": f"Não é possível fazer downgrade do plano {current_plan_name} para {new_plan_name}. Use a opção de cancelamento se desejar reduzir seu plano."
        }

    return {"valid": True, "reason": "upgrade_or_same_tier", "message": "Mudança de plano permitida"}

def calculate_proportional_credit(subscription: UserSubscription, db: Session = None) -> dict:
    """
    Calcula o crédito proporcional de uma assinatura ativa
    """
    if not subscription or subscription.status != "active":
        return {"credit_amount": 0, "days_remaining": 0, "explanation": "Nenhum crédito disponível"}

    plan = subscription.plan
    now = datetime.utcnow()

    # Calcular dias restantes
    if subscription.expires_at:
        expires_at = subscription.expires_at
        # Normalizar ambos para naive datetime (sem timezone)
        if expires_at.tzinfo is not None:
            expires_at = expires_at.replace(tzinfo=None)
        if now.tzinfo is not None:
            now = now.replace(tzinfo=None)

        days_remaining = max(0, (expires_at - now).days)
    else:
        # Se não tem data de expiração, assumir que acabou de começar o ciclo
        if subscription.billing_cycle == "monthly":
            days_remaining = 30
        else:  # yearly
            days_remaining = 365

    # 🎯 CORREÇÃO: Calcular valor proporcional baseado no valor REAL pago
    if subscription.billing_cycle == "monthly":
        # Para assinaturas mensais, usar o valor mensal do plano
        total_days = 30
        total_value_cents = plan.price_cents
        daily_value = total_value_cents / total_days
    else:  # yearly
        # Para assinaturas anuais, usar o valor anual REAL (com desconto de 17%)
        total_days = 365

        # 🎯 CORREÇÃO CRÍTICA: Para planos anuais, precisamos calcular baseado no valor mensal equivalente
        # Os planos anuais no banco têm valores mensais equivalentes, mas precisamos do valor mensal base
        if plan.name == "premium_annual":
            # Buscar o plano mensal correspondente para obter o valor base
            monthly_plan = db.query(SubscriptionPlan).filter(
                SubscriptionPlan.name == "premium_monthly",
                SubscriptionPlan.is_active == True
            ).first()
            base_monthly_cents = monthly_plan.price_cents if monthly_plan else 1800  # Fallback to new Premium price
        else:
            # Para outros planos anuais, usar o valor do banco como base
            base_monthly_cents = plan.price_cents

        # Usar o valor real do plano anual do banco (R$ 180,00 = 18000 centavos)
        annual_value_cents = plan.price_cents
        daily_value = annual_value_cents / total_days

        logger.info(f"🎯 [CREDIT] Annual subscription credit calculation:")
        logger.info(f"🎯 [CREDIT] - Plan: {plan.name}")
        logger.info(f"🎯 [CREDIT] - Base monthly price: R$ {base_monthly_cents / 100:.2f}")
        logger.info(f"🎯 [CREDIT] - Annual real value: R$ {annual_value_cents / 100:.2f}")
        logger.info(f"🎯 [CREDIT] - Daily value: R$ {daily_value / 100:.4f}")
        logger.info(f"🎯 [CREDIT] - Days remaining: {days_remaining}")

    credit_cents = int(daily_value * days_remaining)
    credit_amount = credit_cents / 100  # Converter para reais

    logger.info(f"🎯 [CREDIT] Final credit amount: R$ {credit_amount:.2f}")

    explanation = f"Restam {days_remaining} dias do seu plano {plan.display_name}"

    return {
        "credit_amount": credit_amount,
        "credit_cents": credit_cents,
        "days_remaining": days_remaining,
        "explanation": explanation,
        "current_plan": plan.display_name
    }

@router.get("/test")
def test_endpoint():
    """
    Endpoint de teste para verificar se as rotas estão funcionando
    """
    # Testar inicialização do Asaas
    logger.info(f"Asaas service test - is_sandbox: {asaas_service.is_sandbox}")
    logger.info(f"Asaas API token: {asaas_service.api_token[:20]}..." if asaas_service.api_token else "No token")

    return {
        "message": "Subscriptions endpoint is working",
        "status": "ok",
        "asaas_sandbox_mode": asaas_service.is_sandbox,
        "has_token": bool(asaas_service.api_token),
        "wallet_id": asaas_service.wallet_id,
        "environment": settings.ENVIRONMENT if hasattr(settings, 'ENVIRONMENT') else 'not_set'
    }

@router.post("/test/calculate-credit")
def test_calculate_credit(
    request: dict,
    db: Session = Depends(get_db)
):
    """
    Endpoint de teste para verificar cálculo de crédito proporcional
    """
    try:
        plan_name = request.get("plan_name")
        days_remaining = request.get("days_remaining", 365)

        # Buscar plano
        plan = db.query(SubscriptionPlan).filter(SubscriptionPlan.name == plan_name).first()
        if not plan:
            return {"error": "Plano não encontrado"}

        # Simular cálculo de crédito usando a mesma lógica da função principal
        if plan.billing_cycle == "monthly":
            total_days = 30
            total_value_cents = plan.price_cents
            daily_value = total_value_cents / total_days
            annual_value_cents = None
        else:  # yearly
            total_days = 365

            # Usar a mesma lógica da função principal
            if plan.name == "premium_annual":
                monthly_plan = db.query(SubscriptionPlan).filter(
                    SubscriptionPlan.name == "premium_monthly",
                    SubscriptionPlan.is_active == True
                ).first()
                base_monthly_cents = monthly_plan.price_cents if monthly_plan else 1800
            else:
                base_monthly_cents = plan.price_cents

            # Usar o valor real do plano anual do banco (R$ 180,00 = 18000 centavos)
            annual_value_cents = plan.price_cents
            daily_value = annual_value_cents / total_days

        credit_cents = int(daily_value * days_remaining)
        credit_amount = credit_cents / 100

        return {
            "plan_name": plan_name,
            "plan_display_name": plan.display_name,
            "plan_price_cents": plan.price_cents,
            "plan_price_display": f"R$ {plan.price_cents / 100:.2f}",
            "billing_cycle": plan.billing_cycle,
            "days_remaining": days_remaining,
            "calculations": {
                "total_days": total_days,
                "annual_value_cents": annual_value_cents if plan.billing_cycle == "yearly" else None,
                "annual_value_display": f"R$ {annual_value_cents / 100:.2f}" if plan.billing_cycle == "yearly" else None,
                "daily_value_cents": daily_value,
                "daily_value_display": f"R$ {daily_value / 100:.4f}",
                "credit_cents": credit_cents,
                "credit_amount": credit_amount,
                "credit_display": f"R$ {credit_amount:.2f}"
            }
        }

    except Exception as e:
        return {"error": str(e)}



@router.delete("/test/reset-user-subscriptions")
def reset_user_subscriptions(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    Endpoint de teste para resetar assinaturas do usuário atual
    """
    # Cancelar todas as assinaturas do usuário
    subscriptions = db.query(UserSubscription).filter(
        UserSubscription.user_id == current_user.id
    ).all()

    for subscription in subscriptions:
        subscription.status = "cancelled"
        subscription.cancelled_at = func.now()

    db.commit()

    return {
        "message": f"Reset {len(subscriptions)} subscriptions for user {current_user.email}",
        "cancelled_subscriptions": len(subscriptions)
    }

@router.post("/admin/update-plans")
def update_plans(
    db: Session = Depends(get_db)
):
    """
    Endpoint para atualizar os planos de assinatura
    """
    try:
        # Desativar planos existentes
        db.query(SubscriptionPlan).update({"is_active": False})

        # Criar novos planos
        plans_data = [
            {
                "name": "free",
                "display_name": "Gratuito",
                "description": "Plano gratuito para começar",
                "price_cents": 0,
                "billing_cycle": "monthly",
                "is_active": True,
                "allows_auto_renewal": False,
                "max_plants": 10,  # Updated from 15 to 10
                "max_photos_per_plant": 30,
                "max_photos_per_care": 1,
                "max_videos_per_plant": 0,
                "max_videos_per_care": 0,
                "allows_videos": False
            },
            {
                "name": "premium_monthly",
                "display_name": "Premium Mensal",
                "description": "Para colecionadores sérios e profissionais",
                "price_cents": 1800,  # Updated from 5990 to 1800 (R$ 18,00)
                "billing_cycle": "monthly",
                "is_active": True,
                "allows_auto_renewal": True,
                "max_plants": None,
                "max_photos_per_plant": None,
                "max_photos_per_care": 10,
                "max_videos_per_plant": None,
                "max_videos_per_care": 10,
                "allows_videos": True
            },
            {
                "name": "premium_annual",
                "display_name": "Premium Anual",
                "description": "Premium anual - Economize com pagamento anual",
                "price_cents": 1500,  # R$ 15,00 por mês (equivalente mensal de R$ 180/ano)
                "billing_cycle": "yearly",
                "is_active": True,
                "allows_auto_renewal": True,
                "max_plants": None,
                "max_photos_per_plant": None,
                "max_photos_per_care": 10,
                "max_videos_per_plant": None,
                "max_videos_per_care": 10,
                "allows_videos": True
            }
        ]

        for plan_data in plans_data:
            # Verificar se o plano já existe
            existing_plan = db.query(SubscriptionPlan).filter(
                SubscriptionPlan.name == plan_data["name"]
            ).first()

            if existing_plan:
                # Atualizar plano existente
                for key, value in plan_data.items():
                    if key != "name":  # Não atualizar o nome
                        setattr(existing_plan, key, value)
            else:
                # Criar novo plano
                plan = SubscriptionPlan(**plan_data)
                db.add(plan)

        db.commit()

        return {
            "message": "Plans updated successfully",
            "plans_created": len(plans_data)
        }

    except Exception as e:
        db.rollback()
        logger.error(f"Error updating plans: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/test/payment-data")
def test_payment_data(
    current_user: User = Depends(deps.get_current_user)
):
    """
    Endpoint para testar dados que serão enviados ao Mercado Pago
    """
    # Para Asaas, sempre usar dados reais do usuário
    return {
        "user_email": current_user.email,
        "user_name": current_user.full_name,
        "customer_email_to_asaas": current_user.email,
        "customer_name_to_asaas": current_user.full_name,
        "is_sandbox_mode": asaas_service.is_sandbox,
        "environment": settings.ENVIRONMENT,
        "wallet_id": asaas_service.wallet_id
    }

@router.post("/test/simple-payment")
def test_simple_payment(
    current_user: User = Depends(deps.get_current_user)
):
    """
    Teste simples de pagamento com dados mínimos
    """
    try:
        # Primeiro, criar cliente com CPF
        customer_result = asaas_service.create_customer(
            user_email=current_user.email,
            user_name=current_user.full_name,
            user_id=str(current_user.id),
            user_cpf_cnpj=current_user.cpf_cnpj
        )

        if not customer_result["success"]:
            return {
                "success": False,
                "error": customer_result["error"]
            }

        # Depois, criar pagamento
        result = asaas_service.create_payment_link(
            customer_id=customer_result["customer_id"],
            plan_id="test-plan",
            plan_name="Teste",
            plan_price=5.00,  # R$ 5,00 (valor mínimo do Asaas)
            user_id=str(current_user.id)
        )

        return {
            "success": result.get("success", False),
            "payment_link": result.get("payment_link"),
            "payment_id": result.get("payment_id"),
            "error": result.get("error")
        }

    except Exception as e:
        logger.error(f"Error in simple payment test: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/plans", response_model=List[SubscriptionPlanSchema])
def get_plans(
    db: Session = Depends(get_db)
):
    """
    Obter todos os planos disponíveis (público)
    """
    plans = db.query(SubscriptionPlan).filter(SubscriptionPlan.is_active == True).all()
    return plans

@router.get("/plan-options/{plan_base_name}")
@router.options("/plan-options/{plan_base_name}")
def get_plan_options(
    plan_base_name: str,  # "premium" only now
    db: Session = Depends(get_db)
):
    """
    Obter opções mensal e anual para um plano específico
    """
    monthly_plan = db.query(SubscriptionPlan).filter(
        SubscriptionPlan.name == f"{plan_base_name}_monthly",
        SubscriptionPlan.is_active == True
    ).first()

    annual_plan = db.query(SubscriptionPlan).filter(
        SubscriptionPlan.name == f"{plan_base_name}_annual",
        SubscriptionPlan.is_active == True
    ).first()

    if not monthly_plan or not annual_plan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Plano não encontrado"
        )

    # Calcular economia usando valores reais
    monthly_annual_cost = (monthly_plan.price_cents * 12) / 100  # R$ 18 * 12 = R$ 216
    annual_cost = annual_plan.price_cents / 100  # R$ 180 (valor real do banco)
    savings_percentage = round(((monthly_annual_cost - annual_cost) / monthly_annual_cost) * 100)
    savings_amount = monthly_annual_cost - annual_cost

    return {
        "plan_name": plan_base_name.title(),
        "monthly": {
            "id": str(monthly_plan.id),
            "name": monthly_plan.name,
            "display_name": monthly_plan.display_name,
            "price_cents": monthly_plan.price_cents,
            "price_display": monthly_plan.price_display,
            "billing_cycle": "monthly",
            "description": f"R$ {monthly_plan.price_cents / 100:.2f}/mês no plano mensal".replace('.', ',')
        },
        "annual": {
            "id": str(annual_plan.id),
            "name": annual_plan.name,
            "display_name": annual_plan.display_name,
            "price_cents": annual_plan.price_cents,
            "price_display": annual_plan.price_display,
            "billing_cycle": "yearly",
            "description": f"R$ {annual_plan.price_cents / 100:.2f}/ano no plano anual".replace('.', ',')
        },
        "savings": {
            "percentage": savings_percentage,
            "amount": savings_amount,
            "description": f"Economize {savings_percentage}% (R$ {savings_amount:.2f}) com o plano anual".replace('.', ',')
        }
    }

@router.get("/test-user-plan")
def test_user_plan(
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(get_db)
):
    """
    Endpoint de teste para verificar plano do usuário
    """
    current_subscription = db.query(UserSubscription).filter(
        UserSubscription.user_id == current_user.id,
        UserSubscription.status == SubscriptionStatus.active
    ).first()

    return {
        "user_email": current_user.email,
        "user_id": str(current_user.id),
        "has_active_subscription": current_subscription is not None,
        "current_plan": {
            "name": current_subscription.plan.name,
            "display_name": current_subscription.plan.display_name,
            "status": str(current_subscription.status)
        } if current_subscription else None
    }

@router.get("/pricing")
@router.options("/pricing")
def get_pricing(
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(deps.get_current_user_optional)
):
    """
    Obter informações de preços e planos para a página de pricing
    """
    logger.info(f"🔍 [PRICING] Getting pricing data for user: {current_user.email if current_user else 'anonymous'}")
    # Buscar apenas planos para página de pricing: free e premium_monthly
    plans = db.query(SubscriptionPlan).filter(
        SubscriptionPlan.is_active == True,
        SubscriptionPlan.name.in_(["free", "premium_monthly"])
    ).all()

    # Buscar assinatura atual do usuário (se logado)
    current_subscription = None
    current_plan_id = None

    if current_user:
        # Buscar assinatura ativa (incluindo canceladas mas ainda válidas)
        active_statuses = [
            SubscriptionStatus.active,
            SubscriptionStatus.cancelled_pending_expiration,
            SubscriptionStatus.expired_grace_period
        ]

        current_subscription = db.query(UserSubscription).filter(
            UserSubscription.user_id == current_user.id,
            UserSubscription.status.in_(active_statuses)
        ).order_by(
            # Priorizar assinaturas realmente ativas, depois por data de criação
            UserSubscription.created_at.desc()
        ).first()

        # Se encontrou múltiplas, priorizar a que está realmente ativa
        if current_subscription and current_subscription.status != SubscriptionStatus.active:
            active_subscription = db.query(UserSubscription).filter(
                UserSubscription.user_id == current_user.id,
                UserSubscription.status == SubscriptionStatus.active
            ).order_by(UserSubscription.created_at.desc()).first()

            if active_subscription:
                current_subscription = active_subscription

        if current_subscription:
            logger.info(f"📊 [PRICING] Active subscription found: {current_subscription.plan.name}")
            current_plan_id = str(current_subscription.plan_id)
        else:
            logger.info("⚠️ [PRICING] No active subscription found - defaulting to free plan")
            # Se não tem assinatura ativa, está no plano gratuito
            free_plan = db.query(SubscriptionPlan).filter(
                SubscriptionPlan.name == "free"
            ).first()
            if free_plan:
                current_plan_id = str(free_plan.id)

    # Determinar limites baseados no plano ativo do usuário
    current_plan = None
    if current_subscription:
        current_plan = current_subscription.plan
    else:
        # Usuário sem assinatura ativa = plano gratuito
        current_plan = db.query(SubscriptionPlan).filter(SubscriptionPlan.name == "free").first()

    # Calcular estatísticas de uso com limites do plano atual
    usage = {
        "plants_count": 0,
        "total_photos_count": 0,
        "max_photos_per_plant": 0,
        "plants_limit": current_plan.max_plants if current_plan else 10,  # Updated from 15 to 10
        "photos_per_plant_limit": current_plan.max_photos_per_plant if current_plan else 30,
        "photos_per_care_limit": current_plan.max_photos_per_care if current_plan else 1,
        "can_create_plant": True,
        "can_add_photos": True
    }

    if current_user:
        # Calcular uso real do usuário
        from app.models.plant import Plant, PlantImage

        plants_count = db.query(Plant).filter(Plant.owner_id == current_user.id).count()
        total_photos = db.query(PlantImage).join(Plant).filter(Plant.owner_id == current_user.id).count()

        usage.update({
            "plants_count": plants_count,
            "total_photos_count": total_photos
        })
    
    # Converter planos para formato de pricing
    pricing_plans = []
    for plan in plans:
        features = []

        # Plantas
        if plan.max_plants:
            features.append(f"Até {plan.max_plants} plantas")
        else:
            features.append("Sem limites de plantas")

        # Fotos
        if plan.max_photos_per_plant:
            features.append(f"Até {plan.max_photos_per_plant} fotos por planta")
        else:
            features.append("Sem limites de fotos")

        # Vídeos (apenas para Premium)
        if plan.allows_videos:
            features.append("Carregamento de vídeos")

        # Histórico de cuidados (todos os planos)
        features.append("Histórico de cuidados")

        # Fotos/vídeos por cuidado
        if plan.allows_videos:
            features.append(f"Até {plan.max_photos_per_care} fotos ou vídeos por cuidado")
        else:
            features.append(f"Até {plan.max_photos_per_care} foto(s) por cuidado")

        # Features comuns a todos os planos
        features.append("Lembretes e calendário de cuidados")
        features.append("Galeria pública")
        features.append("Conecte-se com outros bonsaístas")
        
        # Agora só temos free e premium_monthly, não precisa filtrar mais

        # Simplificar nomes dos planos
        display_name = plan.display_name
        if "Mensal" in display_name:
            display_name = display_name.replace(" Mensal", "")

        # Usar valor mensal normal
        price_display = plan.price_display

        # Texto de economia
        savings_text = "Economize 17% com o plano anual" if not plan.is_free else ""

        # Verificar se é o plano atual (comparar tipo base, não ciclo específico)
        is_current_plan = False
        if current_subscription:
            # Se o plano está cancelado, não considerar como atual (permitir nova compra)
            if current_subscription.status == "cancelled_pending_expiration":
                is_current_plan = False
            else:
                # Extrair tipo base do plano atual (premium_annual -> premium, premium_monthly -> premium)
                current_plan_base = current_subscription.plan.name.split('_')[0]  # premium_annual -> premium
                display_plan_base = plan.name.split('_')[0]  # premium_monthly -> premium
                is_current_plan = current_plan_base == display_plan_base

        pricing_plan = {
            "id": str(plan.id),
            "name": plan.name,
            "display_name": display_name,
            "description": savings_text or plan.description,
            "price_display": price_display,
            "is_free": plan.is_free,
            "billing_cycle": plan.billing_cycle,
            "features": features,
            "limits": {
                "max_plants": plan.max_plants,
                "max_photos_per_plant": plan.max_photos_per_plant,
                "max_photos_per_care": plan.max_photos_per_care,
                "max_videos_per_plant": plan.max_videos_per_plant,
                "max_videos_per_care": plan.max_videos_per_care,
                "allows_videos": plan.allows_videos
            },
            "is_popular": plan.name == "premium_monthly",  # Premium mensal é mais popular
            "is_current": is_current_plan,
            "current_plan_name": current_subscription.plan.display_name if current_subscription and is_current_plan else None
        }
        pricing_plans.append(pricing_plan)

    # Ordenar planos na ordem correta: Gratuito → Premium
    plan_order = {"free": 1, "premium_monthly": 2}
    pricing_plans.sort(key=lambda x: plan_order.get(x["name"], 999))

    # Informações detalhadas do plano atual
    current_plan_info = None
    if current_subscription:
        # Usar a data de expiração do usuário (que é a correta para admin)
        expires_at = current_user.subscription_expires_at if current_user else current_subscription.expires_at
        current_plan_info = {
            "id": str(current_subscription.plan.id),
            "name": current_subscription.plan.name,
            "display_name": current_subscription.plan.display_name,
            "billing_cycle": current_subscription.billing_cycle,  # Usar billing_cycle da assinatura
            "status": current_subscription.status,
            "activated_at": current_subscription.started_at.isoformat() if current_subscription.started_at else None,
            "expires_at": expires_at.isoformat() if expires_at else None
        }
    else:
        # Usuário está no plano gratuito
        free_plan = db.query(SubscriptionPlan).filter(SubscriptionPlan.name == "free").first()
        if free_plan:
            current_plan_info = {
                "id": str(free_plan.id),
                "name": free_plan.name,
                "display_name": free_plan.display_name,
                "billing_cycle": None,
                "status": "active",
                "activated_at": None
            }

    return {
        "plans": pricing_plans,
        "current_plan": current_subscription.plan if current_subscription else None,
        "current_plan_info": current_plan_info,
        "usage": usage
    }

@router.post("/create", response_model=CreateSubscriptionResponse)
def create_subscription(
    request: CreateSubscriptionRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    Criar uma nova assinatura
    """
    logger.info(f"Creating subscription for user {current_user.id}, plan {request.plan_id}")
    try:
        # Buscar o plano
        plan = db.query(SubscriptionPlan).filter(SubscriptionPlan.id == request.plan_id).first()
        if not plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Plano não encontrado"
            )
        
        # Verificar se o usuário já tem uma assinatura ativa
        existing_subscription = db.query(UserSubscription).filter(
            UserSubscription.user_id == current_user.id,
            UserSubscription.status == SubscriptionStatus.active
        ).first()

        # Validar se a mudança de plano é permitida (prevenir downgrades)
        if existing_subscription:
            current_plan_name = existing_subscription.plan.name
            new_plan_name = plan.name

            validation = is_valid_plan_change(current_plan_name, new_plan_name)
            if not validation["valid"]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={
                        "error": validation["reason"],
                        "message": validation["message"],
                        "current_plan": current_plan_name,
                        "attempted_plan": new_plan_name
                    }
                )

            logger.info(f"User {current_user.id} has existing active subscription {existing_subscription.id}. Will be cancelled only after payment confirmation.")
        
        # Se for plano gratuito, criar assinatura diretamente
        if plan.is_free:
            subscription = UserSubscription(
                user_id=current_user.id,
                plan_id=plan.id,
                status="active"
            )
            db.add(subscription)
            db.commit()
            db.refresh(subscription)
            
            return CreateSubscriptionResponse(
                success=True,
                subscription_id=subscription.id
            )
        
        # Usar preço customizado se fornecido (para planos anuais), senão usar preço do banco
        if request.custom_price_cents:
            original_price = request.custom_price_cents / 100
            logger.info(f"Using custom price: R$ {original_price:.2f} (billing_cycle: {request.custom_billing_cycle})")
        else:
            original_price = plan.price_cents / 100
            logger.info(f"Using plan price: R$ {original_price:.2f} (billing_cycle: {plan.billing_cycle})")

        # Calcular desconto (crédito proporcional ou cupom)
        discount_amount = 0
        discount_description = None
        credit_info = None
        coupon_info = None

        # 1. Verificar crédito proporcional (upgrade)
        if existing_subscription:
            credit_info = calculate_proportional_credit(existing_subscription, db)
            discount_amount = credit_info["credit_amount"]
            discount_description = f"Crédito proporcional R$ {discount_amount:.2f}"
            logger.info(f"Applying proportional credit: R$ {discount_amount:.2f}")

        # 2. Verificar cupom de desconto (apenas para planos anuais sem upgrade)
        elif request.coupon_code and request.custom_billing_cycle == "yearly":
            try:
                # Validar cupom
                coupon = db.query(Coupon).filter(Coupon.code == request.coupon_code.strip().upper()).first()
                if coupon and coupon.is_valid:
                    # Verificar se usuário já usou o cupom
                    existing_use = db.query(CouponUse).filter(
                        CouponUse.coupon_id == coupon.id,
                        CouponUse.user_id == current_user.id
                    ).first()

                    if not existing_use:
                        # Calcular desconto do cupom
                        if coupon.coupon_type == "percentage":
                            discount_amount = original_price * (coupon.discount_value / 100)
                        else:  # fixed
                            discount_amount = coupon.discount_value

                        # Garantir que o desconto não seja maior que o preço
                        discount_amount = min(discount_amount, original_price)
                        discount_description = f"Cupom {coupon.code} - {coupon.name}"
                        coupon_info = coupon
                        logger.info(f"Applying coupon {coupon.code}: R$ {discount_amount:.2f}")
                    else:
                        logger.warning(f"User {current_user.id} already used coupon {request.coupon_code}")
                else:
                    logger.warning(f"Invalid coupon: {request.coupon_code}")
            except Exception as e:
                logger.error(f"Error processing coupon {request.coupon_code}: {e}")
                # Continuar sem cupom se houver erro

        # Para planos pagos, criar assinatura recorrente no Asaas
        customer_result = asaas_service.create_customer(
            user_email=current_user.email,
            user_name=current_user.full_name,
            user_id=str(current_user.id),
            user_cpf_cnpj=current_user.cpf_cnpj
        )

        if not customer_result["success"]:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=customer_result["error"]
            )

        # 🎯 NOVA IMPLEMENTAÇÃO: Criar assinatura com desconto nativo do ASAAS
        plan_description = request.custom_description or plan.display_name
        billing_cycle = request.custom_billing_cycle or plan.billing_cycle

        logger.info(f"🎯 Creating subscription with native ASAAS discount: R$ {discount_amount:.2f}")
        result = asaas_service.create_subscription(
            customer_id=customer_result["customer_id"],
            plan_name=plan_description,
            plan_price=original_price,  # Preço normal da assinatura
            plan_id=str(plan.id),
            user_id=str(current_user.id),
            billing_cycle=billing_cycle,
            discount_amount=discount_amount,  # Desconto aplicado na primeira cobrança
            discount_description=discount_description
        )

        if not result["success"]:
            logger.error(f"Asaas error: {result}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result["error"]
            )

        # Criar assinatura pendente com ID do ASAAS
        subscription = UserSubscription(
            user_id=current_user.id,
            plan_id=plan.id,
            status="pending",
            billing_cycle=billing_cycle,  # Salvar ciclo correto (mensal ou anual)
            asaas_subscription_id=result.get("subscription_id")  # ID da assinatura ASAAS
        )
        db.add(subscription)
        db.commit()
        db.refresh(subscription)

        # 🎯 NOVO: Registrar uso de cupom se aplicável (será confirmado no webhook)
        if coupon_info and discount_amount > 0:
            try:
                coupon_use = CouponUse(
                    coupon_id=coupon_info.id,
                    user_id=current_user.id,
                    subscription_id=subscription.id,
                    discount_applied=discount_amount,
                    original_price=original_price,
                    final_price=original_price - discount_amount,
                    is_confirmed=False  # Será confirmado no webhook
                )
                db.add(coupon_use)

                # Incrementar contador de usos do cupom
                coupon_info.current_uses = (coupon_info.current_uses or 0) + 1

                db.commit()
                logger.info(f"Pre-registered coupon use: {coupon_info.code} for user {current_user.email}")
            except Exception as e:
                logger.error(f"Error pre-registering coupon use: {e}")
                # Continuar mesmo se falhar

        response = CreateSubscriptionResponse(
            success=True,
            subscription_id=subscription.id,
            preference_id=result.get("payment_id"),  # ID do primeiro pagamento
            init_point=result.get("payment_link"),
            sandbox_init_point=result.get("payment_link")
        )

        logger.info(f"Subscription created successfully: {response}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating subscription: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno do servidor"
        )

@router.get("/current")
def get_current_subscription(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    Obter assinatura atual do usuário
    """
    # Buscar assinatura ativa ou em período de graça
    subscription = db.query(UserSubscription).filter(
        UserSubscription.user_id == current_user.id,
        UserSubscription.status.in_([
            SubscriptionStatus.active,
            SubscriptionStatus.cancelled_pending_expiration,
            SubscriptionStatus.expired_grace_period
        ])
    ).order_by(UserSubscription.created_at.desc()).first()

    if not subscription:
        # Retornar plano gratuito padrão
        free_plan = db.query(SubscriptionPlan).filter(SubscriptionPlan.name == "free").first()
        if free_plan:
            return {
                "plan": free_plan,
                "subscription": None,
                "is_active": True,
                "grace_period_info": None
            }
        else:
            # Se não encontrar plano gratuito, retornar erro
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Nenhuma assinatura encontrada e plano gratuito não configurado"
            )

    # Informações do período de graça
    grace_period_info = None
    if subscription and subscription.status == SubscriptionStatus.expired_grace_period:
        grace_period_info = {
            "is_in_grace_period": subscription.is_in_grace_period,
            "days_remaining": subscription.grace_period_days_remaining,
            "expires_at": subscription.expires_at.isoformat() if subscription.expires_at else None,
            "grace_period_end": (subscription.expires_at + timedelta(days=15)).isoformat() if subscription.expires_at else None
        }

    return {
        "plan": subscription.plan,
        "subscription": subscription,
        "is_active": subscription.is_active,
        "grace_period_info": grace_period_info
    }

@router.post("/fix-auto-renewal")
def fix_auto_renewal_for_existing_subscriptions(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    Corrigir renovação automática para assinaturas existentes
    """
    try:
        # Buscar assinatura ativa do usuário
        subscription = db.query(UserSubscription).filter(
            UserSubscription.user_id == current_user.id,
            UserSubscription.status == "active"
        ).first()

        if not subscription or not subscription.asaas_subscription_id:
            return {
                "success": False,
                "message": "Nenhuma assinatura ativa encontrada"
            }

        # Atualizar renovação automática no ASAAS
        result = asaas_service.update_subscription_auto_renewal(subscription.asaas_subscription_id)

        if result["success"]:
            return {
                "success": True,
                "message": "Renovação automática habilitada com sucesso",
                "subscription_id": subscription.asaas_subscription_id
            }
        else:
            return {
                "success": False,
                "message": f"Erro ao habilitar renovação automática: {result['error']}"
            }

    except Exception as e:
        logger.error(f"Error fixing auto-renewal: {e}")
        return {
            "success": False,
            "message": f"Erro interno: {str(e)}"
        }

@router.post("/cancel/{subscription_id}")
@router.options("/cancel/{subscription_id}")
def cancel_subscription(
    subscription_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    Cancelar assinatura (mantém ativa até expiração)
    """
    subscription = db.query(UserSubscription).filter(
        UserSubscription.id == subscription_id,
        UserSubscription.user_id == current_user.id,
        UserSubscription.status == "active"
    ).first()

    if not subscription:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Assinatura ativa não encontrada"
        )

    # Cancelar no Asaas (auto-renovação)
    try:
        if subscription.asaas_subscription_id:
            logger.info(f"Cancelling subscription {subscription.asaas_subscription_id} in Asaas")
            result = asaas_service.cancel_subscription(subscription.asaas_subscription_id)
            if result["success"]:
                logger.info(f"Subscription cancelled successfully in Asaas")
            else:
                logger.error(f"Failed to cancel subscription in Asaas: {result['error']}")
        else:
            logger.warning(f"No Asaas subscription ID found for subscription {subscription.id}")
    except Exception as e:
        logger.error(f"Error cancelling subscription in Asaas: {e}")
        # Continuar mesmo se falhar no Asaas

    # Marcar como cancelada mas manter ativa até expiração
    subscription.status = "cancelled_pending_expiration"
    subscription.cancelled_at = datetime.utcnow()
    subscription.auto_renew = False

    db.commit()

    # Calcular dias restantes
    days_remaining = 0
    if subscription.expires_at:
        expires_at = subscription.expires_at
        if expires_at.tzinfo is not None:
            expires_at = expires_at.replace(tzinfo=None)
        now = datetime.utcnow()
        days_remaining = max(0, (expires_at - now).days)

    return {
        "message": "Assinatura cancelada com sucesso",
        "status": "cancelled_pending_expiration",
        "expires_at": subscription.expires_at.isoformat() if subscription.expires_at else None,
        "days_remaining": days_remaining,
        "plan_name": subscription.plan.display_name,
        "billing_cycle": subscription.billing_cycle,
        "note": f"Você continuará tendo acesso ao plano {subscription.plan.display_name} até {subscription.expires_at.strftime('%d/%m/%Y') if subscription.expires_at else 'o final do período pago'}."
    }

@router.get("/cancel-info/{subscription_id}")
@router.options("/cancel-info/{subscription_id}")
def get_cancellation_info(
    subscription_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    Obter informações para cancelamento de assinatura
    """
    subscription = db.query(UserSubscription).filter(
        UserSubscription.id == subscription_id,
        UserSubscription.user_id == current_user.id,
        UserSubscription.status == "active"
    ).first()

    if not subscription:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Assinatura ativa não encontrada"
        )

    # Calcular dias restantes
    days_remaining = 0
    if subscription.expires_at:
        expires_at = subscription.expires_at
        if expires_at.tzinfo is not None:
            expires_at = expires_at.replace(tzinfo=None)
        now = datetime.utcnow()
        days_remaining = max(0, (expires_at - now).days)

    # Calcular valor real do ciclo baseado no billing_cycle da assinatura
    plan_price_cents = subscription.plan.price_cents
    if subscription.billing_cycle == "yearly":
        # Para assinaturas anuais, usar o valor real do banco
        actual_price = plan_price_cents / 100
        price_display = f"R$ {actual_price:.2f}".replace('.', ',')
    else:
        # Para assinaturas mensais, usar o valor mensal
        actual_price = plan_price_cents / 100
        price_display = f"R$ {actual_price:.2f}".replace('.', ',')

    return {
        "subscription_id": str(subscription.id),
        "plan_name": subscription.plan.display_name,
        "billing_cycle": subscription.billing_cycle,
        "expires_at": subscription.expires_at.isoformat() if subscription.expires_at else None,
        "days_remaining": days_remaining,
        "price_display": price_display,
        "actual_price": actual_price,
        "cancellation_note": f"Seu plano {subscription.plan.display_name} permanecerá ativo até {subscription.expires_at.strftime('%d/%m/%Y') if subscription.expires_at else 'o final do período pago'} ({days_remaining} dias restantes). A renovação automática será desabilitada."
    }

@router.post("/calculate-upgrade-cost")
def calculate_upgrade_cost(
    request: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    Calcular o custo de upgrade considerando crédito proporcional
    """
    try:
        new_plan_id = request.get("plan_id")
        custom_price_cents = request.get("custom_price_cents")
        custom_billing_cycle = request.get("custom_billing_cycle")

        if not new_plan_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="plan_id é obrigatório"
            )

        # Buscar novo plano
        new_plan = db.query(SubscriptionPlan).filter(SubscriptionPlan.id == new_plan_id).first()
        if not new_plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Plano não encontrado"
            )

        # Buscar assinatura atual
        current_subscription = db.query(UserSubscription).filter(
            UserSubscription.user_id == current_user.id,
            UserSubscription.status == SubscriptionStatus.active
        ).first()

        # Calcular crédito proporcional
        credit_info = calculate_proportional_credit(current_subscription, db)

        # Calcular valor final usando preço correto (mensal ou anual)
        if custom_price_cents and custom_billing_cycle == "yearly":
            # Para planos anuais customizados, usar o preço anual
            new_plan_price = custom_price_cents / 100
            logger.info(f"Using custom annual price for upgrade calculation: R$ {new_plan_price:.2f}")
        else:
            # Para planos mensais, usar preço do banco
            new_plan_price = new_plan.price_cents / 100
            logger.info(f"Using monthly price for upgrade calculation: R$ {new_plan_price:.2f}")

        credit_amount = credit_info["credit_amount"]

        # Verificar se é downgrade (crédito maior que preço do novo plano)
        is_downgrade = credit_amount > new_plan_price

        if is_downgrade:
            # Para downgrades, não aplicar crédito - seria necessário reembolso
            final_price = new_plan_price
            applied_credit = 0
            discount_percentage = 0
            message = f"Downgrade detectado. Você tem R$ {credit_amount:.2f} de crédito, mas o novo plano custa apenas R$ {new_plan_price:.2f}. Entre em contato para reembolso."
        else:
            # Upgrade normal - aplicar crédito
            final_price = max(5.00, new_plan_price - credit_amount)  # Mínimo R$ 5,00
            applied_credit = credit_amount
            discount_percentage = round((applied_credit / new_plan_price) * 100, 1) if new_plan_price > 0 else 0
            message = None

        return {
            "success": True,
            "new_plan": {
                "id": str(new_plan.id),
                "name": new_plan.name,
                "display_name": new_plan.display_name,
                "original_price": new_plan_price,
                "price_display": new_plan.price_display
            },
            "credit_info": credit_info,
            "pricing": {
                "original_price": new_plan_price,
                "credit_amount": applied_credit,
                "final_price": final_price,
                "discount_percentage": discount_percentage
            },
            "has_active_subscription": current_subscription is not None,
            "is_downgrade": is_downgrade,
            "message": message
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error calculating upgrade cost: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno do servidor"
        )

@router.post("/validate-coupon")
def validate_coupon(
    request: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    Validar cupom de desconto
    """
    try:
        coupon_code = request.get("coupon_code", "").strip().upper()
        plan_id = request.get("plan_id")
        custom_price_cents = request.get("custom_price_cents")
        custom_billing_cycle = request.get("custom_billing_cycle")

        if not coupon_code:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Código do cupom é obrigatório"
            )

        if not plan_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="ID do plano é obrigatório"
            )

        # Restringir cupons apenas para planos anuais
        if not custom_billing_cycle or custom_billing_cycle != "yearly":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cupons são válidos apenas para planos anuais"
            )

        # Buscar cupom
        coupon = db.query(Coupon).filter(Coupon.code == coupon_code).first()
        if not coupon:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cupom não encontrado"
            )

        # Verificar se o cupom está válido
        if not coupon.is_valid:
            reasons = []
            if not coupon.is_active:
                reasons.append("cupom desativado")
            if coupon.valid_from and datetime.utcnow() < coupon.valid_from:
                reasons.append("cupom ainda não está válido")
            if coupon.valid_until and datetime.utcnow() > coupon.valid_until:
                reasons.append("cupom expirado")
            if coupon.max_uses and coupon.current_uses >= coupon.max_uses:
                reasons.append("cupom esgotado")

            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cupom inválido: {', '.join(reasons)}"
            )

        # Verificar se o usuário já usou este cupom
        existing_use = db.query(CouponUse).filter(
            CouponUse.coupon_id == coupon.id,
            CouponUse.user_id == current_user.id
        ).first()

        if existing_use and coupon.max_uses_per_user <= 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Você já utilizou este cupom"
            )

        user_uses = db.query(CouponUse).filter(
            CouponUse.coupon_id == coupon.id,
            CouponUse.user_id == current_user.id
        ).count()

        if user_uses >= coupon.max_uses_per_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Você já utilizou este cupom o máximo de vezes permitidas ({coupon.max_uses_per_user})"
            )

        # Verificar se o cupom se aplica ao plano
        if coupon.applicable_plans:
            import json
            try:
                applicable_plan_ids = json.loads(coupon.applicable_plans)
                if str(plan_id) not in applicable_plan_ids:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Este cupom não se aplica ao plano selecionado"
                    )
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON in coupon applicable_plans: {coupon.applicable_plans}")

        # Buscar informações do plano para calcular desconto
        plan = db.query(SubscriptionPlan).filter(SubscriptionPlan.id == plan_id).first()
        if not plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Plano não encontrado"
            )

        # Calcular preço correto baseado no ciclo (mensal ou anual)
        custom_price_cents = request.get("custom_price_cents")
        custom_billing_cycle = request.get("custom_billing_cycle")

        if custom_price_cents and custom_billing_cycle == "yearly":
            # Para planos anuais customizados, usar o preço anual
            original_price = custom_price_cents / 100
            logger.info(f"Using custom annual price for coupon calculation: R$ {original_price:.2f}")
        else:
            # Para planos mensais, usar preço do banco
            original_price = plan.price_cents / 100
            logger.info(f"Using monthly price for coupon calculation: R$ {original_price:.2f}")

        if coupon.coupon_type == "percentage":
            discount_amount = original_price * (coupon.discount_value / 100)
        else:  # fixed
            discount_amount = coupon.discount_value

        # Garantir que o desconto não seja maior que o preço
        discount_amount = min(discount_amount, original_price)
        final_price = max(0, original_price - discount_amount)

        return {
            "valid": True,
            "coupon": {
                "id": str(coupon.id),
                "code": coupon.code,
                "name": coupon.name,
                "description": coupon.description,
                "coupon_type": coupon.coupon_type,
                "discount_value": coupon.discount_value
            },
            "discount": {
                "original_price": original_price,
                "discount_amount": discount_amount,
                "final_price": final_price,
                "discount_percentage": round((discount_amount / original_price) * 100, 1) if original_price > 0 else 0
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating coupon: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno do servidor"
        )


@router.get("/grace-period-status/{user_id}")
def get_grace_period_status(
    user_id: str,
    db: Session = Depends(get_db)
):
    """
    Obter status do período de graça para um usuário específico
    """
    try:
        subscription = db.query(UserSubscription).filter(
            UserSubscription.user_id == user_id,
            UserSubscription.status == "expired_grace_period"
        ).first()

        if not subscription:
            return {"in_grace_period": False}

        return {
            "in_grace_period": subscription.is_in_grace_period,
            "days_remaining": subscription.grace_period_days_remaining,
            "expires_at": subscription.expires_at.isoformat() if subscription.expires_at else None,
            "grace_period_end": (subscription.expires_at + timedelta(days=15)).isoformat() if subscription.expires_at else None
        }

    except Exception as e:
        logger.error(f"Error getting grace period status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno do servidor"
        )


@router.get("/subscription-history")
def get_subscription_history(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    Obter histórico de assinaturas e faturas do usuário
    """
    try:
        # Buscar todas as assinaturas do usuário
        subscriptions = db.query(UserSubscription).filter(
            UserSubscription.user_id == current_user.id
        ).order_by(UserSubscription.created_at.desc()).all()

        # Buscar todas as cobranças avulsas do usuário no ASAAS
        standalone_payments = []
        try:
            logger.info(f"🔍 Searching for customer with email: {current_user.email}")
            # Buscar customer do usuário no ASAAS
            customer_result = asaas_service.get_customer_by_email(current_user.email)
            logger.info(f"🔍 Customer search result: {customer_result}")

            if customer_result.get("success") and customer_result.get("customer_id"):
                customer_id = customer_result["customer_id"]
                logger.info(f"🔍 Found customer ID: {customer_id}")

                payments_result = asaas_service.get_customer_payments(customer_id)
                logger.info(f"🔍 Payments search result: success={payments_result.get('success')}, count={len(payments_result.get('payments', []))}")

                if payments_result.get("success"):
                    standalone_payments = payments_result.get("payments", [])
                    logger.info(f"🔍 Found {len(standalone_payments)} total payments for customer")
            else:
                logger.warning(f"❌ Customer not found for email: {current_user.email}")
        except Exception as e:
            logger.error(f"Error fetching standalone payments: {e}")

        # 1. HISTÓRICO DE ASSINATURAS COM SUAS FATURAS
        subscription_history = []

        for subscription in subscriptions:
            # Buscar faturas desta assinatura específica
            subscription_invoices = []
            if subscription.asaas_subscription_id:
                try:
                    invoices_result = asaas_service.get_subscription_invoices(subscription.asaas_subscription_id)
                    if invoices_result.get("success"):
                        raw_invoices = invoices_result.get("invoices", [])

                        for invoice in raw_invoices:
                            subscription_invoices.append({
                                "id": invoice.get("id"),
                                "value": invoice.get("value"),
                                "status": invoice.get("status"),
                                "due_date": invoice.get("due_date"),
                                "payment_date": invoice.get("payment_date"),
                                "invoice_url": invoice.get("invoice_url"),
                                "bank_slip_url": invoice.get("bank_slip_url"),
                                "billing_type": invoice.get("billing_type"),
                                "description": f"🔄 {subscription.plan.display_name} ({subscription.billing_cycle.value})",
                                "subscription_id": str(subscription.id),
                                "subscription_name": subscription.plan.display_name
                            })

                        logger.info(f"🔍 Found {len(subscription_invoices)} invoices for subscription {subscription.id}")
                except Exception as e:
                    logger.error(f"Error fetching invoices for subscription {subscription.asaas_subscription_id}: {e}")

            sub_data = {
                "id": str(subscription.id),
                "plan_name": subscription.plan.display_name,
                "billing_cycle": subscription.billing_cycle.value,
                "status": subscription.status.value,
                "created_at": subscription.created_at.isoformat() if subscription.created_at else None,
                "expires_at": subscription.expires_at.isoformat() if subscription.expires_at else None,
                "cancelled_at": subscription.cancelled_at.isoformat() if subscription.cancelled_at else None,
                "asaas_subscription_id": subscription.asaas_subscription_id,
                "invoices": subscription_invoices  # Faturas desta assinatura
            }
            subscription_history.append(sub_data)

        # 2. COBRANÇAS AVULSAS (se houver)
        standalone_invoices = []

        # Identificar cobranças que NÃO são de assinaturas
        subscription_payment_ids = set()
        for subscription in subscriptions:
            if subscription.asaas_subscription_id:
                try:
                    invoices_result = asaas_service.get_subscription_invoices(subscription.asaas_subscription_id)
                    if invoices_result.get("success"):
                        for invoice in invoices_result.get("invoices", []):
                            subscription_payment_ids.add(invoice.get("id"))
                except Exception as e:
                    logger.error(f"Error collecting subscription payment IDs: {e}")

        # Adicionar apenas cobranças que NÃO são de assinaturas
        for payment in standalone_payments:
            payment_id = payment.get("id")
            if payment_id not in subscription_payment_ids:
                external_ref = payment.get("externalReference", "")
                payment_value = payment.get("value")

                standalone_invoices.append({
                    "id": payment.get("id"),
                    "value": payment.get("value"),
                    "status": payment.get("status"),
                    "due_date": payment.get("dueDate"),
                    "payment_date": payment.get("paymentDate"),
                    "invoice_url": payment.get("invoiceUrl"),
                    "bank_slip_url": payment.get("bankSlipUrl"),
                    "billing_type": payment.get("billingType"),
                    "description": f"💰 Cobrança Avulsa - R$ {payment_value}",
                    "external_reference": external_ref,
                    "is_standalone": True,
                    "source": "standalone"
                })

        # Ordenar cobranças avulsas por data de vencimento (mais recente primeiro)
        standalone_invoices.sort(key=lambda x: x.get("due_date", ""), reverse=True)

        # Adicionar seção de cobranças avulsas apenas se houver
        if standalone_invoices:
            subscription_history.append({
                "id": "standalone_payments",
                "plan_name": "💰 Cobranças Avulsas",
                "billing_cycle": "N/A",
                "status": "active",
                "created_at": None,
                "expires_at": None,
                "cancelled_at": None,
                "asaas_subscription_id": None,
                "invoices": standalone_invoices
            })
            logger.info(f"🔍 Added {len(standalone_invoices)} standalone payments to history")

        return {
            "success": True,
            "subscription_history": subscription_history,
            "total_subscriptions": len(subscription_history)
        }

    except Exception as e:
        logger.error(f"Error getting subscription history: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno do servidor"
        )


@router.post("/cleanup-expired-grace-periods")
def cleanup_expired_grace_periods(
    db: Session = Depends(get_db)
):
    """
    Cancelar assinaturas que passaram do período de graça de 15 dias
    Este endpoint deve ser chamado por um cron job diário
    """
    try:
        from datetime import datetime, timedelta

        # Buscar assinaturas em período de graça que já passaram dos 15 dias
        cutoff_date = datetime.utcnow() - timedelta(days=15)

        expired_grace_subscriptions = db.query(UserSubscription).filter(
            UserSubscription.status == "expired_grace_period",
            UserSubscription.expires_at < cutoff_date
        ).all()

        cancelled_count = 0

        for subscription in expired_grace_subscriptions:
            logger.info(f"Cancelling expired grace period subscription {subscription.id} for user {subscription.user.email}")

            # Cancelar no ASAAS se ainda existir
            if subscription.asaas_subscription_id:
                try:
                    result = asaas_service.cancel_subscription(subscription.asaas_subscription_id)
                    if result["success"]:
                        logger.info(f"ASAAS subscription {subscription.asaas_subscription_id} cancelled")
                    else:
                        logger.error(f"Failed to cancel ASAAS subscription: {result['error']}")
                except Exception as e:
                    logger.error(f"Error cancelling ASAAS subscription: {e}")

            # Marcar como cancelada no banco
            subscription.status = "cancelled"
            subscription.cancelled_at = datetime.utcnow()

            # Garantir que o usuário está no plano gratuito
            user = subscription.user
            free_plan = db.query(SubscriptionPlan).filter(SubscriptionPlan.name == "free").first()
            if free_plan:
                user.current_plan_id = free_plan.id
                user.subscription_status = "cancelled"
                user.subscription_expires_at = None

            cancelled_count += 1

        db.commit()

        logger.info(f"Cleanup completed: {cancelled_count} expired grace period subscriptions cancelled")

        return {
            "success": True,
            "cancelled_count": cancelled_count,
            "message": f"Successfully cancelled {cancelled_count} expired grace period subscriptions"
        }

    except Exception as e:
        logger.error(f"Error in cleanup expired grace periods: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro interno do servidor"
        )
