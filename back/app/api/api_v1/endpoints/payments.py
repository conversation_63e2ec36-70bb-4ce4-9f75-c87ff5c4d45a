from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from sqlalchemy.sql import func
import logging
import json
from datetime import datetime, timedelta

from app.db.session import get_db
from app.models.user import User
from app.models.subscription import SubscriptionPlan, UserSubscription, Payment, PaymentStatus
from app.schemas.subscription import WebhookNotification
from app.services.asaas_service import asaas_service

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/webhook")
async def asaas_webhook(
    request: Request,
    db: Session = Depends(get_db)
):
    """
    Webhook para receber notificações do Asaas
    """
    try:
        # Obter dados do webhook
        body = await request.body()
        webhook_data = json.loads(body.decode('utf-8'))

        # Log básico de segurança
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")

        logger.info(f"🔔 [WEBHOOK] Received from IP: {client_ip}")
        logger.info(f"🔔 [WEBHOOK] User-Agent: {user_agent}")
        logger.info(f"🔔 [WEBHOOK] Event data: {webhook_data}")

        # Validação básica da estrutura do webhook
        if not isinstance(webhook_data, dict):
            logger.error(f"❌ [WEBHOOK] Invalid webhook data format")
            return {"status": "error", "message": "Invalid data format"}

        event = webhook_data.get("event")
        if not event:
            logger.error(f"❌ [WEBHOOK] Missing event field")
            return {"status": "error", "message": "Missing event field"}

        logger.info(f"✅ [WEBHOOK] Processing event: {event}")

        # Processar diferentes tipos de eventos do Asaas
        if event == "PAYMENT_RECEIVED" or event == "PAYMENT_CONFIRMED":
            await handle_payment_received(webhook_data, db)
        elif event == "PAYMENT_OVERDUE":
            await handle_payment_overdue(webhook_data, db)
        elif event == "PAYMENT_DELETED":
            await handle_payment_deleted(webhook_data, db)
        elif event == "SUBSCRIPTION_CYCLE_CREATED":
            await handle_subscription_cycle_created(webhook_data, db)
        elif event == "SUBSCRIPTION_EXPIRED":
            await handle_subscription_expired(webhook_data, db)
        else:
            logger.info(f"⚠️ [WEBHOOK] Unhandled event type: {event}")

        logger.info(f"✅ [WEBHOOK] Event {event} processed successfully")
        return {"status": "ok"}

    except json.JSONDecodeError as e:
        logger.error(f"❌ [WEBHOOK] Invalid JSON: {e}")
        return {"status": "error", "message": "Invalid JSON"}
    except Exception as e:
        logger.error(f"❌ [WEBHOOK] Error processing webhook: {e}")
        return {"status": "error", "message": str(e)}

async def handle_payment_received(webhook_data: dict, db: Session):
    """
    Processar pagamento aprovado
    """
    try:
        payment = webhook_data.get("payment", {})
        external_reference = payment.get("externalReference", "")

        logger.info(f"🎯 Payment received: {payment.get('id')}")
        logger.info(f"🎯 External reference: {external_reference}")
        logger.info(f"🎯 Payment data: {payment}")

        # Extrair informações da referência externa
        if external_reference.startswith("subscription_"):
            parts = external_reference.split("_")
            if len(parts) >= 3:
                user_id = parts[1]
                # O terceiro parte pode ser plan_id ou subscription_id do Asaas

                # 🔧 NOVO: Detectar tipo de pagamento
                # Formatos possíveis:
                # - subscription_user_id_plan_id_first_payment (primeira cobrança)
                # - subscription_user_id_sub_subscription_id_discounted (upgrade com desconto)
                # - subscription_user_id_subscription_id (cobrança recorrente)

                is_first_payment = len(parts) >= 4 and parts[3] == "first_payment"
                is_discounted_payment = len(parts) >= 5 and parts[4] == "discounted"

                if is_first_payment:
                    plan_id = parts[2]
                    asaas_subscription_id = None
                    logger.info(f"Processing first payment for plan {plan_id}")
                elif is_discounted_payment:
                    # Para cobranças com desconto, o subscription_id é parts[2] + "_" + parts[3]
                    asaas_subscription_id = f"{parts[2]}_{parts[3]}"
                    logger.info(f"Processing discounted payment for subscription {asaas_subscription_id}")
                else:
                    asaas_subscription_id = None

                # Buscar usuário
                user = db.query(User).filter(User.id == user_id).first()

                if user:
                    logger.info(f"Processing payment for user: {user.email}")

                    # 🎯 SIMPLIFICADO: Buscar assinatura pendente mais recente
                    subscription = db.query(UserSubscription).filter(
                        UserSubscription.user_id == user.id,
                        UserSubscription.status == "pending"
                    ).order_by(UserSubscription.created_at.desc()).first()
                    logger.info(f"Looking for pending subscription for user: {user.id}")

                    if subscription:
                        # 🎯 CORREÇÃO: A assinatura já foi criada no ASAAS, apenas ativar
                        logger.info(f"Activating existing subscription {subscription.asaas_subscription_id} for user {user.email}")

                        # 🔧 CORREÇÃO: Cancelar assinaturas anteriores ANTES de ativar a nova
                        # Incluir assinaturas em período de graça
                        previous_subscriptions = db.query(UserSubscription).filter(
                            UserSubscription.user_id == user.id,
                            UserSubscription.status.in_(["active", "expired_grace_period"]),
                            UserSubscription.id != subscription.id
                        ).all()

                        for prev_sub in previous_subscriptions:
                            logger.info(f"Cancelling previous subscription {prev_sub.id} for user {user.email}")

                            # Cancelar no ASAAS também
                            if prev_sub.asaas_subscription_id:
                                try:
                                    logger.info(f"Cancelling subscription {prev_sub.asaas_subscription_id} in Asaas")
                                    result = asaas_service.cancel_subscription(prev_sub.asaas_subscription_id)
                                    if result["success"]:
                                        logger.info(f"Previous subscription cancelled successfully in Asaas")
                                    else:
                                        logger.error(f"Failed to cancel previous subscription in Asaas: {result['error']}")
                                except Exception as e:
                                    logger.error(f"Error cancelling previous subscription in Asaas: {e}")

                            prev_sub.status = "cancelled"
                            prev_sub.cancelled_at = datetime.utcnow()

                        # Ativar nova assinatura
                        subscription.status = "active"

                        # Atualizar data de expiração baseada no ciclo da assinatura
                        if subscription.billing_cycle == "monthly":
                            subscription.expires_at = datetime.utcnow() + timedelta(days=30)
                        elif subscription.billing_cycle == "yearly":
                            subscription.expires_at = datetime.utcnow() + timedelta(days=365)

                        subscription.started_at = datetime.utcnow()

                        # 🔧 CORREÇÃO: Atualizar também a tabela users
                        user.current_plan_id = subscription.plan_id
                        user.subscription_status = "active"
                        user.subscription_expires_at = subscription.expires_at

                        # 🎯 NOVO: Confirmar uso de cupom se aplicável
                        from app.models.coupon import CouponUse

                        # Buscar uso de cupom pendente para esta assinatura
                        pending_coupon_use = db.query(CouponUse).filter(
                            CouponUse.subscription_id == subscription.id,
                            CouponUse.is_confirmed == False
                        ).first()

                        if pending_coupon_use:
                            # Confirmar o uso do cupom
                            pending_coupon_use.is_confirmed = True
                            logger.info(f"Confirmed coupon use: {pending_coupon_use.coupon.code} for user {user.email}")

                        # 🔧 NOVO: Garantir renovação automática se pagamento foi com cartão
                        billing_type = payment.get("billingType")
                        if billing_type == "CREDIT_CARD" and subscription.asaas_subscription_id:
                            try:
                                logger.info(f"Updating subscription {subscription.asaas_subscription_id} for auto-renewal")
                                result = asaas_service.update_subscription_auto_renewal(subscription.asaas_subscription_id)
                                if result["success"]:
                                    logger.info(f"Auto-renewal enabled for subscription {subscription.asaas_subscription_id}")
                                else:
                                    logger.error(f"Failed to enable auto-renewal: {result['error']}")
                            except Exception as e:
                                logger.error(f"Error enabling auto-renewal: {e}")

                        # Create payment success notification
                        try:
                            from app import crud
                            from app.models.notification import NotificationType

                            plan_name = subscription.plan.display_name if subscription.plan else "Premium"
                            amount = payment.get("value", 0)

                            crud.notification.create_subscription_notification(
                                db=db,
                                user_id=str(user.id),
                                notification_type=NotificationType.payment_success,
                                subscription_id=str(subscription.id),
                                title="Pagamento Aprovado",
                                message=f"Seu pagamento de R$ {amount:.2f} foi aprovado. Plano {plan_name} ativado!",
                                data={
                                    "amount": amount,
                                    "plan_name": plan_name,
                                    "payment_id": payment.get("id")
                                }
                            )
                            logger.info(f"✅ Payment success notification created")
                        except Exception as e:
                            logger.error(f"Error creating payment notification: {e}")
                            # Don't fail payment processing if notification fails

                        db.commit()
                        logger.info(f"Subscription activated for user {user.email} - Plan: {subscription.plan.display_name}")
                    else:
                        logger.warning(f"No pending subscription found for user {user.email}")
                        logger.warning(f"External reference: {external_reference}")
                        logger.warning(f"Is discounted payment: {is_discounted_payment}")
                        logger.warning(f"ASAAS subscription ID: {asaas_subscription_id}")

                        # Listar todas as assinaturas do usuário para debug
                        all_subscriptions = db.query(UserSubscription).filter(
                            UserSubscription.user_id == user.id
                        ).all()
                        logger.warning(f"All user subscriptions: {[(s.id, s.status, s.asaas_subscription_id) for s in all_subscriptions]}")
                else:
                    logger.warning(f"User not found: {user_id}")

    except Exception as e:
        logger.error(f"Error processing payment received: {e}")
        db.rollback()
        raise

async def handle_payment_overdue(webhook_data: dict, db: Session):
    """
    Processar pagamento em atraso
    """
    try:
        payment = webhook_data.get("payment", {})
        external_reference = payment.get("externalReference", "")

        logger.info(f"Payment overdue: {payment.get('id')}")

        # Marcar assinatura como em atraso
        if external_reference.startswith("subscription_"):
            parts = external_reference.split("_")
            if len(parts) >= 3:
                user_id = parts[1]

                subscription = db.query(UserSubscription).filter(
                    UserSubscription.user_id == user_id,
                    UserSubscription.status == "active"
                ).first()

                if subscription:
                    subscription.status = "overdue"

                    # Create payment failed notification
                    try:
                        from app import crud
                        from app.models.notification import NotificationType

                        plan_name = subscription.plan.display_name if subscription.plan else "Premium"

                        crud.notification.create_subscription_notification(
                            db=db,
                            user_id=str(user_id),
                            notification_type=NotificationType.payment_failed,
                            subscription_id=str(subscription.id),
                            title="Falha no Pagamento",
                            message=f"Não foi possível processar o pagamento do seu plano {plan_name}. Verifique seus dados de pagamento.",
                            data={
                                "plan_name": plan_name,
                                "payment_id": payment.get("id")
                            }
                        )
                        logger.info(f"✅ Payment failed notification created")
                    except Exception as e:
                        logger.error(f"Error creating payment failed notification: {e}")

                    db.commit()
                    logger.info(f"Subscription marked as overdue for user {user_id}")

    except Exception as e:
        logger.error(f"Error processing payment overdue: {e}")
        db.rollback()
        raise


async def handle_payment_deleted(webhook_data: dict, db: Session):
    """
    Processar cancelamento de pagamento/assinatura
    """
    try:
        payment = webhook_data.get("payment", {})
        external_reference = payment.get("externalReference", "")

        logger.info(f"Payment deleted: {payment.get('id')}")

        # Cancelar assinatura
        if external_reference.startswith("subscription_"):
            parts = external_reference.split("_")
            if len(parts) >= 3:
                user_id = parts[1]

                subscription = db.query(UserSubscription).filter(
                    UserSubscription.user_id == user_id
                ).first()

                if subscription:
                    subscription.status = "cancelled"
                    subscription.cancelled_at = func.now()
                    db.commit()
                    logger.info(f"Subscription cancelled for user {user_id}")

    except Exception as e:
        logger.error(f"Error processing payment deleted: {e}")
        db.rollback()
        raise


async def handle_subscription_cycle_created(webhook_data: dict, db: Session):
    """
    Processar nova cobrança de assinatura gerada
    """
    try:
        subscription_data = webhook_data.get("subscription", {})
        logger.info(f"New subscription cycle created: {subscription_data.get('id')}")

        # Aqui podemos registrar a nova cobrança se necessário
        # Por enquanto, apenas loggar

    except Exception as e:
        logger.error(f"Error processing subscription cycle created: {e}")
        raise


async def handle_subscription_expired(webhook_data: dict, db: Session):
    """
    Processar expiração de assinatura - iniciar período de graça de 15 dias
    """
    try:
        subscription_data = webhook_data.get("subscription", {})
        asaas_subscription_id = subscription_data.get("id")

        logger.info(f"Subscription expired: {asaas_subscription_id}")

        # Buscar assinatura no banco pelo ID do ASAAS
        subscription = db.query(UserSubscription).filter(
            UserSubscription.asaas_subscription_id == asaas_subscription_id
        ).first()

        if subscription:
            logger.info(f"Starting grace period for user {subscription.user.email}")

            # Marcar como expirada em período de graça
            subscription.status = "expired_grace_period"

            # Atualizar tabela users para plano gratuito
            user = subscription.user
            free_plan = db.query(SubscriptionPlan).filter(SubscriptionPlan.name == "free").first()
            if free_plan:
                user.current_plan_id = free_plan.id
                user.subscription_status = "expired_grace_period"
                # Manter subscription_expires_at para calcular fim do período de graça

            # Create subscription expired notification
            try:
                from app import crud
                from app.models.notification import NotificationType

                plan_name = subscription.plan.display_name if subscription.plan else "Premium"

                crud.notification.create_subscription_notification(
                    db=db,
                    user_id=str(user.id),
                    notification_type=NotificationType.subscription_expired,
                    subscription_id=str(subscription.id),
                    title="Assinatura Expirada",
                    message=f"Seu plano {plan_name} expirou. Você tem 15 dias para renovar antes de perder o acesso.",
                    data={
                        "plan_name": plan_name,
                        "grace_period_days": 15
                    }
                )
                logger.info(f"✅ Subscription expired notification created")
            except Exception as e:
                logger.error(f"Error creating subscription expired notification: {e}")

            db.commit()
            logger.info(f"Subscription {subscription.id} marked as expired_grace_period for user {user.email}")
        else:
            logger.warning(f"Subscription not found for ASAAS ID: {asaas_subscription_id}")

    except Exception as e:
        logger.error(f"Error processing subscription expired: {e}")
        db.rollback()
        raise



@router.get("/test-webhook")
def test_webhook():
    """
    Endpoint para testar se o webhook está funcionando
    """
    return {
        "message": "Webhook endpoint is working",
        "timestamp": datetime.utcnow().isoformat(),
        "environment": "sandbox" if asaas_service.is_sandbox else "production"
    }
