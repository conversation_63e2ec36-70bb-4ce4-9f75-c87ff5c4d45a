from fastapi import APIRouter, Response
from datetime import datetime
import xml.etree.ElementTree as ET

router = APIRouter()

@router.get("/sitemap.xml")
def generate_sitemap():
    """
    Generate dynamic sitemap.xml for SEO
    """
    # Create root element
    urlset = ET.Element("urlset")
    urlset.set("xmlns", "http://www.sitemaps.org/schemas/sitemap/0.9")
    
    # Static pages
    static_pages = [
        {
            "loc": "https://meubonsai.app/",
            "lastmod": "2025-01-14",
            "changefreq": "weekly",
            "priority": "1.0"
        },
        {
            "loc": "https://meubonsai.app/login",
            "lastmod": "2025-01-14", 
            "changefreq": "monthly",
            "priority": "0.8"
        },
        {
            "loc": "https://meubonsai.app/register",
            "lastmod": "2025-01-14",
            "changefreq": "monthly", 
            "priority": "0.8"
        },
        {
            "loc": "https://meubonsai.app/pricing",
            "lastmod": "2025-01-14",
            "changefreq": "weekly",
            "priority": "0.9"
        },
        {
            "loc": "https://meubonsai.app/forgot-password",
            "lastmod": "2025-01-14",
            "changefreq": "monthly",
            "priority": "0.5"
        }
    ]
    
    # Add static pages to sitemap
    for page in static_pages:
        url = ET.SubElement(urlset, "url")
        
        loc = ET.SubElement(url, "loc")
        loc.text = page["loc"]
        
        lastmod = ET.SubElement(url, "lastmod")
        lastmod.text = page["lastmod"]
        
        changefreq = ET.SubElement(url, "changefreq")
        changefreq.text = page["changefreq"]
        
        priority = ET.SubElement(url, "priority")
        priority.text = page["priority"]
    
    # Convert to string
    xml_str = ET.tostring(urlset, encoding='unicode', method='xml')
    
    # Add XML declaration
    xml_content = '<?xml version="1.0" encoding="UTF-8"?>\n' + xml_str
    
    return Response(
        content=xml_content,
        media_type="application/xml",
        headers={"Cache-Control": "max-age=3600"}  # Cache for 1 hour
    )

@router.get("/robots.txt")
def generate_robots():
    """
    Generate dynamic robots.txt for SEO
    """
    robots_content = """User-agent: *
Allow: /
Allow: /login
Allow: /register
Allow: /forgot-password
Allow: /pricing

# Block private user areas
Disallow: /plants
Disallow: /profile
Disallow: /settings
Disallow: /admin
Disallow: /api/

# Block authentication pages that shouldn't be indexed
Disallow: /reset-password
Disallow: /activate-account
Disallow: /confirm-password-change

# Allow important static files
Allow: /static/
Allow: /logo_meubonsai.png
Allow: /favicon.png
Allow: /manifest.json

# Sitemap location
Sitemap: https://meubonsai.app/sitemap.xml

# Crawl delay (optional)
Crawl-delay: 1"""
    
    return Response(
        content=robots_content,
        media_type="text/plain",
        headers={"Cache-Control": "max-age=86400"}  # Cache for 24 hours
    )
