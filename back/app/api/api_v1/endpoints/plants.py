from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query, Request
from fastapi.responses import StreamingResponse, Response
from sqlalchemy.orm import Session
import requests
import os
import uuid
from uuid import UUID
from PIL import Image
from PIL.ExifTags import TAGS
from datetime import datetime, date, timedelta

from app import crud, models, schemas
from app.api import deps
from app.db.session import get_db
from app.services.r2_service import r2_service
from app.services.video_service import VideoValidationService, VideoLimitService

router = APIRouter()


def extract_photo_date(image_path: str) -> datetime:
    """Extract photo date from EXIF data"""
    try:
        with Image.open(image_path) as img:
            exif = img._getexif()
            if exif is not None:
                # Try different date tags
                date_tags = ['DateTime', 'DateTimeOriginal', 'DateTimeDigitized']
                for tag_name in date_tags:
                    for tag_id, value in exif.items():
                        tag = TAGS.get(tag_id, tag_id)
                        if tag == tag_name and value:
                            try:
                                # Parse EXIF date format: "YYYY:MM:DD HH:MM:SS"
                                return datetime.strptime(value, "%Y:%m:%d %H:%M:%S")
                            except ValueError:
                                continue
    except Exception as e:
        print(f"Error extracting EXIF date: {e}")

    # Return None if no date found
    return None


@router.get("/", response_model=List[schemas.PlantList])
def read_plants(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    archived: Optional[bool] = False,
    favorite: Optional[bool] = None,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Retrieve plants.
    """
    plants = crud.plant.get_by_owner(db, owner_id=current_user.id, skip=skip, limit=limit, archived=archived, favorite=favorite)
    # Add primary image and care count to each plant
    result = []
    for plant in plants:
        plant_dict = plant.__dict__.copy()
        # Get primary image
        primary_image = db.query(models.PlantImage).filter(
            models.PlantImage.plant_id == plant.id,
            models.PlantImage.is_primary == True
        ).first()
        # Construct R2 URL for primary image
        if primary_image and primary_image.image:
            filename = primary_image.image
            # Check if it's an R2 filename (UUID format without hyphens)
            filename_without_ext = filename.split('.')[0]
            if len(filename_without_ext) == 32:  # R2 filename
                from app.core.config import settings
                plant_dict["primary_image"] = f"{settings.R2_PUBLIC_URL}/plants/{plant.id}/medium_{filename}"
            else:
                # Legacy local file
                plant_dict["primary_image"] = f"/media/{filename}"
        else:
            plant_dict["primary_image"] = None
        # Get care count
        care_count = db.query(models.Care).filter(models.Care.plant_id == plant.id).count()
        plant_dict["care_count"] = care_count
        # Add plant_type_display
        plant_dict["plant_type_display"] = plant.plant_type_display
        result.append(schemas.PlantList(**plant_dict))
    return result


@router.post("/", response_model=schemas.Plant)
def create_plant(
    *,
    db: Session = Depends(get_db),
    plant_in: schemas.PlantCreate,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Create new plant.
    """
    # Get user's subscription plan
    from app.api.api_v1.endpoints.subscriptions import get_user_plan_info
    from app.services.plant_limit_service import PlantLimitService

    user_plan = get_user_plan_info(current_user, db)

    # Check current plant count
    current_plant_count = db.query(models.Plant).filter(
        models.Plant.owner_id == current_user.id
    ).count()

    # Validate plant creation limit
    can_create, error_message = PlantLimitService.check_plant_creation_limit(user_plan, current_plant_count)

    if not can_create:
        raise HTTPException(status_code=403, detail=error_message)

    plant = crud.plant.create_with_owner(db=db, obj_in=plant_in, owner_id=current_user.id)
    return plant


@router.put("/{id}", response_model=schemas.Plant)
def update_plant(
    *,
    db: Session = Depends(get_db),
    id: UUID,
    plant_in: schemas.PlantUpdate,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Update a plant.
    """
    plant = crud.plant.get(db=db, id=id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    if plant.owner_id != current_user.id:
        raise HTTPException(status_code=400, detail="Not enough permissions")



    plant = crud.plant.update(db=db, db_obj=plant, obj_in=plant_in)
    return plant


@router.get("/{id}", response_model=schemas.Plant)
def read_plant(
    *,
    db: Session = Depends(get_db),
    id: UUID,
    timezone_offset: int = Query(-3, description="User timezone offset in hours (e.g., -3 for Brazil)"),
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Get plant by ID.
    """
    plant = crud.plant.get(db=db, id=id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")

    # Allow access if user owns the plant OR if plant is public
    if plant.owner_id != current_user.id and not plant.is_public:
        raise HTTPException(status_code=400, detail="Not enough permissions")

    # Update reminder calculations with user timezone (only for owner)
    if plant.owner_id == current_user.id:
        for reminder in plant.reminders:
            reminder._days_until_due = reminder.get_days_until_due(timezone_offset)
            reminder._is_overdue = reminder.get_is_overdue(timezone_offset)
    else:
        # If viewing someone else's plant, include owner information
        owner = crud.user.get(db=db, id=plant.owner_id)
        if owner:
            # Add owner info to plant object
            plant.owner_name = owner.full_name or owner.username
            plant.owner_username = owner.username
            plant.owner_avatar = owner.avatar  # Use avatar field from user model

    return plant


@router.put("/{id}/archive", response_model=schemas.Plant)
def archive_plant(
    *,
    db: Session = Depends(get_db),
    id: UUID,
    archived: bool = True,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Archive or unarchive a plant.
    """
    plant = crud.plant.get(db=db, id=id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    if plant.owner_id != current_user.id:
        raise HTTPException(status_code=400, detail="Not enough permissions")

    plant_update = schemas.PlantUpdate(archived=archived)
    plant = crud.plant.update(db=db, db_obj=plant, obj_in=plant_update)
    return plant


@router.put("/{id}/favorite", response_model=schemas.Plant)
def favorite_plant(
    *,
    db: Session = Depends(get_db),
    id: UUID,
    favorite: bool = True,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Favorite or unfavorite a plant.
    """
    plant = crud.plant.get(db=db, id=id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    if plant.owner_id != current_user.id:
        raise HTTPException(status_code=400, detail="Not enough permissions")

    plant_update = schemas.PlantUpdate(favorite=favorite)
    plant = crud.plant.update(db=db, db_obj=plant, obj_in=plant_update)
    return plant


@router.put("/{id}/privacy", response_model=schemas.Plant)
def update_plant_privacy(
    *,
    db: Session = Depends(get_db),
    id: UUID,
    is_public: bool,
    allow_care_sharing: bool,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Update plant privacy settings.
    """
    plant = crud.plant.get(db=db, id=id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    if plant.owner_id != current_user.id:
        raise HTTPException(status_code=400, detail="Not enough permissions")



    plant_update = schemas.PlantUpdate(
        is_public=is_public,
        allow_care_sharing=allow_care_sharing
    )
    plant = crud.plant.update(db=db, db_obj=plant, obj_in=plant_update)
    return plant


@router.delete("/{id}", response_model=schemas.Plant)
def delete_plant(
    *,
    db: Session = Depends(get_db),
    id: UUID,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Delete a plant and all its images from R2.
    """
    plant = crud.plant.get(db=db, id=id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    if plant.owner_id != current_user.id:
        raise HTTPException(status_code=400, detail="Not enough permissions")

    # Delete all images from R2 before deleting plant
    try:
        r2_service.delete_plant_images(str(id))
        print(f"Deleted all R2 images for plant {id}")
    except Exception as e:
        print(f"Error deleting R2 images for plant {id}: {e}")
        # Continue with plant deletion even if R2 cleanup fails

    plant = crud.plant.remove(db=db, id=id)
    return plant


# Plant Images
@router.post("/{id}/images", response_model=schemas.PlantImage)
async def upload_plant_image(
    *,
    db: Session = Depends(get_db),
    id: UUID,
    file: UploadFile = File(...),
    caption: str = Form(None),
    is_primary: bool = Form(False),
    photo_date: str = Form(None),  # ISO format string or None for EXIF extraction
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Upload image for a plant.
    """
    plant = crud.plant.get(db=db, id=id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    if plant.owner_id != current_user.id:
        raise HTTPException(status_code=400, detail="Not enough permissions")

    # Verificar limite de fotos por planta
    from app.services.plant_limit_service import PlantLimitService
    from app.api.api_v1.endpoints.subscriptions import get_user_plan_info

    user_plan = get_user_plan_info(current_user, db)
    current_photo_count = len(plant.images)

    can_add_photo, error_message = PlantLimitService.check_plant_photo_limit(user_plan, current_photo_count)
    if not can_add_photo:
        raise HTTPException(status_code=403, detail=error_message)



    # Validate file type
    if not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")

    # Read file content
    content = await file.read()

    # Extract photo date from EXIF before processing
    extracted_photo_date = None
    if not photo_date:  # Only extract if not provided by user
        try:
            # Create temporary file for EXIF extraction
            import tempfile
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                temp_file.write(content)
                temp_file.flush()
                extracted_photo_date = extract_photo_date(temp_file.name)
                os.unlink(temp_file.name)
        except Exception:
            pass

    # Parse user-provided date if available
    parsed_photo_date = None
    if photo_date:
        try:
            if 'T' in photo_date:
                # Full datetime provided
                parsed_photo_date = datetime.fromisoformat(photo_date.replace('Z', '+00:00'))
            else:
                # Date-only provided (YYYY-MM-DD), use noon as default time
                date_part = datetime.strptime(photo_date, '%Y-%m-%d').date()
                default_time = datetime.strptime('12:00:00', '%H:%M:%S').time()
                parsed_photo_date = datetime.combine(date_part, default_time)
        except ValueError:
            try:
                # Try alternative format
                parsed_photo_date = datetime.strptime(photo_date, "%Y-%m-%d %H:%M:%S")
            except ValueError:
                pass  # Use extracted date or None

    # Use provided date, extracted date, or None
    final_photo_date = parsed_photo_date or extracted_photo_date

    # Upload to R2 with resizing
    upload_result = r2_service.upload_image(
        image_data=content,
        filename=file.filename or "image.jpg",
        plant_id=str(id)
    )

    if not upload_result['success']:
        raise HTTPException(status_code=500, detail=f"Failed to upload image: {upload_result.get('error', 'Unknown error')}")

    # Create database record
    image_in = schemas.PlantImageCreate(
        caption=caption,
        is_primary=is_primary,
        photo_date=final_photo_date
    )
    image = crud.plant_image.create_with_plant(
        db=db,
        obj_in=image_in,
        plant_id=id,
        image_path=upload_result['filename'],
        original_filename=file.filename
    )

    return image


@router.get("/{id}/images", response_model=List[schemas.PlantImage])
def read_plant_images(
    *,
    db: Session = Depends(get_db),
    id: UUID,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Get images for a plant.
    """
    plant = crud.plant.get(db=db, id=id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")

    # Allow access if user owns the plant OR if plant is public
    if plant.owner_id != current_user.id and not plant.is_public:
        raise HTTPException(status_code=400, detail="Not enough permissions")

    images = crud.plant_image.get_by_plant(db=db, plant_id=id)
    return images


@router.post("/{id}/cares/{care_id}/create-reminder", response_model=schemas.Reminder)
def create_reminder_from_care(
    *,
    db: Session = Depends(get_db),
    id: UUID,
    care_id: UUID,
    interval_amount: int = Form(...),
    interval_unit: str = Form(...),  # 'days', 'weeks', 'months', 'years'
    description: str = Form(None),
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Create reminder based on a care record.
    """
    plant = crud.plant.get(db=db, id=id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    if plant.owner_id != current_user.id:
        raise HTTPException(status_code=400, detail="Not enough permissions")



    care = crud.care.get(db=db, id=care_id)
    if not care or care.plant_id != id:
        raise HTTPException(status_code=404, detail="Care not found")

    # Calculate next care date
    base_date = care.date.date() if isinstance(care.date, datetime) else care.date

    if interval_unit == 'days':
        next_date = base_date + timedelta(days=interval_amount)
    elif interval_unit == 'weeks':
        next_date = base_date + timedelta(weeks=interval_amount)
    elif interval_unit == 'months':
        # Approximate months as 30 days
        next_date = base_date + timedelta(days=interval_amount * 30)
    elif interval_unit == 'years':
        # Approximate years as 365 days
        next_date = base_date + timedelta(days=interval_amount * 365)
    else:
        raise HTTPException(status_code=400, detail="Invalid interval unit")

    # Note: Reminders use date-only (scheduled_date is Date type)
    # The reminder time logic is handled when notifications are sent

    # Create reminder - use the care_type_display property
    care_type_display = care.care_type_display.lower()

    # Get the first care type for the reminder (reminders are single-type)
    if care.care_types:
        first_care_type = care.care_types[0]
    elif care.care_type:
        first_care_type = care.care_type
    else:
        # Fallback to a default
        from app.models.plant import CareType
        first_care_type = CareType.outro

    reminder_data = schemas.ReminderCreate(
        care_type=first_care_type,
        scheduled_date=next_date,
        description=description or f"Próximo {care_type_display} agendado automaticamente"
    )

    reminder = crud.reminder.create_with_plant(db=db, obj_in=reminder_data, plant_id=id)

    # Add computed properties
    reminder_dict = reminder.__dict__.copy()
    reminder_dict["care_type_display"] = reminder.care_type_display
    reminder_dict["days_until_due"] = reminder.days_until_due
    reminder_dict["is_overdue"] = reminder.is_overdue

    return schemas.Reminder(**reminder_dict)





@router.get("/{id}/all-images")
def read_all_plant_images(
    *,
    db: Session = Depends(get_db),
    id: UUID,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Get all images for a plant (including care images).
    """
    try:
        print(f"=== ALL-IMAGES DEBUG ===")
        print(f"Plant ID: {id}")
        print(f"User ID: {current_user.id}")

        plant = crud.plant.get(db=db, id=id)
        if not plant:
            raise HTTPException(status_code=404, detail="Plant not found")

        # Allow access if user owns the plant OR if plant is public
        if plant.owner_id != current_user.id and not plant.is_public:
            raise HTTPException(status_code=400, detail="Not enough permissions")

        print(f"Plant found: {plant.name}")
    except Exception as e:
        print(f"Error in all-images endpoint: {e}")
        raise

    # Get regular plant images
    plant_images = crud.plant_image.get_by_plant(db=db, plant_id=id)

    # Get plant videos
    plant_videos = crud.plant_video.get_by_plant(db=db, plant_id=id)

    # Get care images (both legacy and new care_images table)
    cares_with_images = db.query(models.Care).filter(
        models.Care.plant_id == id,
        models.Care.image.isnot(None)
    ).order_by(models.Care.date.desc()).all()

    # Get all care images from care_images table using ORM
    from sqlalchemy.orm import joinedload
    care_images_query = db.query(models.CareImage).join(models.Care).filter(
        models.Care.plant_id == id
    ).options(joinedload(models.CareImage.care)).order_by(
        models.Care.date.desc(), models.CareImage.is_primary.desc()
    ).all()

    # Get all care videos from care_videos table using ORM
    care_videos_query = db.query(models.CareVideo).join(models.Care).filter(
        models.Care.plant_id == id
    ).options(joinedload(models.CareVideo.care)).order_by(
        models.Care.date.desc(), models.CareVideo.is_primary.desc()
    ).all()

    # Convert to unified format
    all_images = []

    # Add plant images
    for img in plant_images:
        # Construct R2 URL for plant image
        image_url = img.image
        if img.image:
            filename = img.image
            # Check if it's an R2 filename (UUID format without hyphens)
            filename_without_ext = filename.split('.')[0]
            if len(filename_without_ext) == 32:  # R2 filename
                from app.core.config import settings
                image_url = f"{settings.R2_PUBLIC_URL}/plants/{id}/medium_{filename}"
            else:
                # Legacy local file
                image_url = f"/media/{filename}"

        all_images.append({
            "id": str(img.id),
            "image": image_url,
            "caption": img.caption,
            "photo_date": img.photo_date.isoformat() if img.photo_date else None,
            "created_at": img.created_at.isoformat(),
            "is_primary": img.is_primary,
            "type": "plant",
            "source_id": str(img.id),
            "is_video": False
        })

    # Add plant videos
    for video in plant_videos:
        # Construct R2 URL for plant video
        video_url = video.video
        if video.video:
            filename = video.video
            # Check if it's an R2 filename (UUID format without hyphens)
            filename_without_ext = filename.split('.')[0]
            if len(filename_without_ext) == 32:  # R2 filename
                from app.core.config import settings
                from app.core.security import create_access_token
                # Create a token for video access
                video_token = create_access_token(subject=str(current_user.id))
                # Use backend proxy URL to avoid CORS issues
                video_url = f"{settings.API_V1_STR}/plants/{id}/video-stream/{filename}?token={video_token}"
            else:
                # Legacy local file
                video_url = f"/media/{filename}"

        all_images.append({
            "id": f"video_{video.id}",
            "image": video_url,
            "thumbnail_url": video.thumbnail_url,  # Add thumbnail URL
            "caption": video.caption,
            "photo_date": video.video_date.isoformat() if video.video_date else None,
            "created_at": video.created_at.isoformat(),
            "is_primary": False,  # Videos can't be primary
            "type": "plant",
            "source_id": str(video.id),
            "is_video": True
        })

    # Add care images
    for care in cares_with_images:
        # Use the model's care_type_display property
        care_type_display = care.care_type_display

        caption = f"{care_type_display} - {care.date.strftime('%d/%m/%Y')}"
        if care.description:
            caption += f": {care.description}"

        # Construct R2 URL for care image
        image_url = care.image
        if care.image:
            filename = care.image
            # Check if it's an R2 filename (UUID format without hyphens)
            filename_without_ext = filename.split('.')[0]
            if len(filename_without_ext) == 32:  # R2 filename
                from app.core.config import settings
                image_url = f"{settings.R2_PUBLIC_URL}/plants/{id}/medium_{filename}"
            else:
                # Legacy local file
                image_url = f"/media/{filename}"

        all_images.append({
            "id": f"care_{care.id}",
            "image": image_url,
            "caption": caption,
            "photo_date": care.date.isoformat(),
            "created_at": care.created_at.isoformat(),
            "is_primary": False,
            "type": "care",
            "source_id": str(care.id),
            "care_type": care_type_display,
            "is_video": False
        })

    # Add images from care_images table
    for care_image in care_images_query:
        # Use the model's care_type_display property
        care_type_display = care_image.care.care_type_display

        caption = f"{care_type_display} - {care_image.care.date.strftime('%d/%m/%Y')}"
        if care_image.care.description:
            caption += f": {care_image.care.description}"
        if care_image.caption:
            caption = care_image.caption

        # Construct image URL
        image_url = care_image.image_path
        if care_image.image_path:
            filename = care_image.image_path
            # Check if it's an R2 filename (UUID format without hyphens)
            filename_without_ext = filename.split('.')[0]
            if len(filename_without_ext) == 32:  # R2 filename
                from app.core.config import settings
                image_url = f"{settings.R2_PUBLIC_URL}/plants/{id}/cares/medium_{filename}"
            else:
                # Legacy local file
                image_url = f"/media/{filename}"

        all_images.append({
            "id": f"care_image_{care_image.id}",
            "image": image_url,
            "caption": caption,
            "photo_date": care_image.care.date.isoformat(),
            "created_at": care_image.created_at.isoformat(),
            "is_primary": care_image.is_primary,
            "type": "care",
            "source_id": str(care_image.care_id),
            "care_type": care_type_display,
            "is_video": False
        })

    # Add videos from care_videos table
    for care_video in care_videos_query:
        # Use the model's care_type_display property
        care_type_display = care_video.care.care_type_display

        caption = f"{care_type_display} - {care_video.care.date.strftime('%d/%m/%Y')}"
        if care_video.care.description:
            caption += f": {care_video.care.description}"
        if care_video.caption:
            caption = care_video.caption

        # Construct video URL for care video
        video_url = care_video.video_path
        if care_video.video_path:
            filename = care_video.video_path
            # Check if it's an R2 filename (UUID format without hyphens)
            filename_without_ext = filename.split('.')[0]
            if len(filename_without_ext) == 32:  # R2 filename
                from app.core.config import settings
                from app.core.security import create_access_token
                # Create a token for video access
                video_token = create_access_token(subject=str(current_user.id))
                # Use backend proxy URL to avoid CORS issues (care videos are in cares subfolder)
                video_url = f"{settings.API_V1_STR}/plants/{id}/care-video-stream/{filename}?token={video_token}"
            else:
                # Legacy local file
                video_url = f"/media/{filename}"

        all_images.append({
            "id": f"care_video_{care_video.id}",
            "image": video_url,
            "thumbnail_url": care_video.thumbnail_url,  # Add thumbnail URL
            "caption": caption,
            "photo_date": care_video.care.date.isoformat(),
            "created_at": care_video.created_at.isoformat(),
            "is_primary": care_video.is_primary,
            "type": "care",
            "source_id": str(care_video.care_id),
            "care_type": care_type_display,
            "is_video": True
        })

    # Sort by photo_date (most recent first), then by created_at
    all_images.sort(key=lambda x: (
        x["photo_date"] or x["created_at"],
        x["created_at"]
    ), reverse=True)

    print(f"Returning {len(all_images)} images")
    print(f"========================")
    return all_images


@router.put("/{plant_id}/images/{image_id}", response_model=schemas.PlantImage)
async def update_plant_image(
    *,
    db: Session = Depends(get_db),
    plant_id: UUID,
    image_id: UUID,
    file: UploadFile = File(None),
    caption: str = Form(None),
    is_primary: bool = Form(False),
    photo_date: str = Form(None),
    rotation: int = Form(0),
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Update plant image metadata and optionally replace the image file.
    """
    plant = crud.plant.get(db=db, id=plant_id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    if plant.owner_id != current_user.id:
        raise HTTPException(status_code=400, detail="Not enough permissions")



    image = crud.plant_image.get(db=db, id=image_id)
    if not image or image.plant_id != plant_id:
        raise HTTPException(status_code=404, detail="Image not found")

    # If a new file is provided, replace the image
    if file:
        # Validate file type
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")

        # Delete old image from R2 if it exists
        if image.image:
            filename_without_ext = image.image.split('.')[0]
            if len(filename_without_ext) == 32:  # R2 filename
                r2_service.delete_image(str(plant_id), image.image)

        # Read file content
        content = await file.read()

        # Upload new image to R2 with resizing
        upload_result = r2_service.upload_image(
            image_data=content,
            filename=file.filename or "image.jpg",
            plant_id=str(plant_id)
        )

        if not upload_result['success']:
            raise HTTPException(status_code=500, detail=f"Failed to upload image: {upload_result.get('error', 'Unknown error')}")

        # Update image path in database
        update_data = {
            "image": upload_result['filename'],
            "original_filename": file.filename,
            "caption": caption,
            "is_primary": is_primary
        }
    else:
        # Only update metadata
        update_data = {"caption": caption, "is_primary": is_primary}

    # Parse and add photo_date if provided
    if photo_date:
        try:
            if 'T' in photo_date:
                # Full datetime provided
                parsed_photo_date = datetime.fromisoformat(photo_date.replace('Z', '+00:00'))
            else:
                # Date-only provided (YYYY-MM-DD), use noon as default time
                date_part = datetime.strptime(photo_date, '%Y-%m-%d').date()
                default_time = datetime.strptime('12:00:00', '%H:%M:%S').time()
                parsed_photo_date = datetime.combine(date_part, default_time)
            update_data["photo_date"] = parsed_photo_date
        except ValueError:
            try:
                # Try alternative format
                parsed_photo_date = datetime.strptime(photo_date, "%Y-%m-%d %H:%M:%S")
                update_data["photo_date"] = parsed_photo_date
            except ValueError:
                pass  # Keep existing date

    # If setting as primary, remove primary from other images
    if is_primary:
        crud.plant_image.set_primary(db=db, plant_id=plant_id, image_id=image_id)

    # Update image record
    image = crud.plant_image.update(db=db, db_obj=image, obj_in=update_data)

    return image


@router.post("/{plant_id}/images/{image_id}/rotate", response_model=schemas.PlantImage)
def rotate_plant_image(
    *,
    db: Session = Depends(get_db),
    plant_id: UUID,
    image_id: UUID,
    rotation: int = 90,  # Default 90 degrees clockwise
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Rotate plant image by specified degrees.
    """
    plant = crud.plant.get(db=db, id=plant_id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    if plant.owner_id != current_user.id:
        raise HTTPException(status_code=400, detail="Not enough permissions")

    image = crud.plant_image.get(db=db, id=image_id)
    if not image or image.plant_id != plant_id:
        raise HTTPException(status_code=404, detail="Image not found")

    # Check if it's an R2 image
    if not image.image:
        raise HTTPException(status_code=400, detail="No image file found")

    filename_without_ext = image.image.split('.')[0]
    if len(filename_without_ext) != 32:  # Not R2 filename
        raise HTTPException(status_code=400, detail="Can only rotate R2 images")

    # Rotate image in R2
    rotation_result = r2_service.rotate_image(
        plant_id=str(plant_id),
        filename=image.image,
        rotation_degrees=rotation
    )

    if not rotation_result or not rotation_result['success']:
        raise HTTPException(status_code=500, detail="Failed to rotate image")

    # Update database with new filename if it changed
    if rotation_result['filename'] != image.image:
        update_data = {"image": rotation_result['filename']}
        image = crud.plant_image.update(db=db, db_obj=image, obj_in=update_data)

    return image


@router.get("/{id}/reminders", response_model=List[schemas.ReminderList])
def read_plant_reminders(
    *,
    db: Session = Depends(get_db),
    id: UUID,
    include_completed: bool = False,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Get reminders for a specific plant.
    """
    plant = crud.plant.get(db=db, id=id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")

    # Only plant owners can see reminders (reminders are private)
    if plant.owner_id != current_user.id:
        raise HTTPException(status_code=400, detail="Not enough permissions")

    reminders = crud.reminder.get_by_plant(
        db, plant_id=id, include_completed=include_completed
    )

    result = []
    for reminder in reminders:
        reminder_dict = reminder.__dict__.copy()
        reminder_dict["plant_name"] = plant.name
        reminder_dict["care_type_display"] = reminder.care_type_display
        reminder_dict["days_until_due"] = reminder.days_until_due
        reminder_dict["is_overdue"] = reminder.is_overdue
        result.append(schemas.ReminderList(**reminder_dict))

    return result


@router.delete("/{plant_id}/images/{image_id}")
def delete_plant_image(
    *,
    db: Session = Depends(get_db),
    plant_id: UUID,
    image_id: UUID,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Delete plant image.
    """
    plant = crud.plant.get(db=db, id=plant_id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    if plant.owner_id != current_user.id:
        raise HTTPException(status_code=400, detail="Not enough permissions")

    image = crud.plant_image.get(db=db, id=image_id)
    if not image or image.plant_id != plant_id:
        raise HTTPException(status_code=404, detail="Image not found")

    # Delete file from R2 or local filesystem
    if image.image:
        filename_without_ext = image.image.split('.')[0]
        if len(filename_without_ext) == 32:  # R2 filename
            r2_service.delete_image(str(plant_id), image.image)
        else:
            # Legacy local file
            file_path = f"media/{image.image}"
            if os.path.exists(file_path):
                os.remove(file_path)

    # Delete from database
    crud.plant_image.remove(db=db, id=image_id)

    return {"message": "Image deleted successfully"}


# Plant Videos (Premium Feature)
@router.post("/{id}/videos", response_model=schemas.PlantVideo)
async def upload_plant_video(
    *,
    db: Session = Depends(get_db),
    id: UUID,
    file: UploadFile = File(...),
    caption: str = Form(None),
    video_date: str = Form(None),
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Upload video for a plant (Premium feature only).
    """
    plant = crud.plant.get(db=db, id=id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    if plant.owner_id != current_user.id:
        raise HTTPException(status_code=400, detail="Not enough permissions")

    # Get user's subscription plan
    from app.api.api_v1.endpoints.subscriptions import get_user_plan_info
    user_plan = get_user_plan_info(current_user, db)

    # Check if user can upload videos
    current_video_count = db.query(models.PlantVideo).filter(models.PlantVideo.plant_id == id).count()
    can_upload, error_message = VideoLimitService.check_video_limits(user_plan, current_video_count, "plant")

    if not can_upload:
        raise HTTPException(status_code=403, detail=error_message)

    # Validate video file
    is_valid, validation_error = VideoValidationService.validate_video_file(file)
    if not is_valid:
        raise HTTPException(status_code=400, detail=validation_error)

    # Read file content
    content = await file.read()

    # Generate unique filename
    video_filename = VideoValidationService.generate_video_filename(
        file.filename or "video.mp4",
        str(id),
        "plant"
    )

    # Upload to R2
    upload_result = r2_service.upload_video(
        video_data=content,
        filename=video_filename,
        plant_id=str(id)
    )

    if not upload_result['success']:
        raise HTTPException(status_code=500, detail=f"Failed to upload video: {upload_result.get('error', 'Unknown error')}")

    # Generate thumbnail
    from app.utils.video_utils import create_thumbnail_from_upload
    thumbnail_content, thumbnail_filename = create_thumbnail_from_upload(
        video_content=content,
        original_filename=file.filename or "video.mp4"
    )

    thumbnail_url = None
    if thumbnail_content and thumbnail_filename:
        # Upload thumbnail to R2
        thumbnail_result = r2_service.upload_thumbnail(
            thumbnail_data=thumbnail_content,
            filename=video_filename,  # Use video filename as base
            plant_id=str(id)
        )

        if thumbnail_result['success']:
            thumbnail_url = thumbnail_result['url']
        else:
            print(f"Warning: Failed to upload thumbnail: {thumbnail_result.get('error', 'Unknown error')}")
    else:
        print("Warning: Failed to generate thumbnail")

    # Parse video date if provided
    parsed_video_date = None
    if video_date:
        try:
            if 'T' in video_date:
                parsed_video_date = datetime.fromisoformat(video_date.replace('Z', '+00:00'))
            else:
                date_part = datetime.strptime(video_date, '%Y-%m-%d').date()
                default_time = datetime.strptime('12:00:00', '%H:%M:%S').time()
                parsed_video_date = datetime.combine(date_part, default_time)
        except ValueError:
            pass

    # Create database record
    video_in = schemas.PlantVideoCreate(
        caption=caption,
        video_date=parsed_video_date
    )

    video = crud.plant_video.create_with_plant(
        db=db,
        obj_in=video_in,
        plant_id=id,
        video_path=upload_result['filename'],
        thumbnail_url=thumbnail_url,
        original_filename=file.filename,
        file_size_bytes=len(content)
    )

    return video


@router.get("/{id}/videos", response_model=List[schemas.PlantVideo])
def read_plant_videos(
    *,
    db: Session = Depends(get_db),
    id: UUID,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Get videos for a plant.
    """
    plant = crud.plant.get(db=db, id=id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    if plant.owner_id != current_user.id:
        raise HTTPException(status_code=400, detail="Not enough permissions")

    videos = crud.plant_video.get_by_plant(db=db, plant_id=id)
    return videos


@router.delete("/{plant_id}/videos/{video_id}")
def delete_plant_video(
    *,
    db: Session = Depends(get_db),
    plant_id: UUID,
    video_id: UUID,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Delete plant video.
    """
    plant = crud.plant.get(db=db, id=plant_id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    if plant.owner_id != current_user.id:
        raise HTTPException(status_code=400, detail="Not enough permissions")

    video = crud.plant_video.get(db=db, id=video_id)
    if not video or video.plant_id != plant_id:
        raise HTTPException(status_code=404, detail="Video not found")

    # Delete file from R2
    if video.video:
        r2_service.delete_video(str(plant_id), video.video)

    # Delete thumbnail from R2 if exists
    if video.thumbnail_url:
        # Extract thumbnail filename from URL
        thumbnail_filename = video.thumbnail_url.split('/')[-1]
        r2_service.delete_thumbnail(str(plant_id), thumbnail_filename)

    # Delete from database
    crud.plant_video.remove(db=db, id=video_id)

    return {"message": "Video deleted successfully"}


@router.api_route("/{id}/video-stream/{filename}", methods=["GET", "HEAD"])
async def serve_plant_video(
    request: Request,
    *,
    db: Session = Depends(get_db),
    id: UUID,
    filename: str,
    token: str = Query(None),
    current_user: models.User = Depends(deps.get_current_user_optional),
) -> Any:
    """
    Serve plant video through backend as proxy to avoid CORS issues.
    Accepts authentication via header or query string token.
    """
    # If no user from header, try to get from token query parameter
    if not current_user and token:
        try:
            from app.core.config import settings
            from jose import jwt
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
            user_id = payload.get("sub")
            if user_id:
                current_user = crud.user.get(db, id=user_id)
        except Exception:
            pass  # Invalid token, will be handled below

    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")

    plant = crud.plant.get(db=db, id=id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")

    # Allow access if user owns the plant OR if plant is public
    if plant.owner_id != current_user.id and not plant.is_public:
        raise HTTPException(status_code=400, detail="Not enough permissions")

    # Construct R2 URL
    from app.core.config import settings
    video_url = f"{settings.R2_PUBLIC_URL}/plants/{id}/videos/{filename}"

    try:
        # For HEAD requests, just return headers
        if request.method == "HEAD":
            response = requests.head(video_url)
            response.raise_for_status()
            return Response(
                content="",
                media_type=response.headers.get('content-type', 'video/mp4'),
                headers={
                    "Accept-Ranges": "bytes",
                    "Content-Length": response.headers.get('content-length', ''),
                    "Cache-Control": "public, max-age=31536000",
                }
            )

        # For GET requests, stream video from R2
        response = requests.get(video_url, stream=True)
        response.raise_for_status()

        def generate():
            for chunk in response.iter_content(chunk_size=8192):
                yield chunk

        return StreamingResponse(
            generate(),
            media_type=response.headers.get('content-type', 'video/mp4'),
            headers={
                "Accept-Ranges": "bytes",
                "Content-Length": response.headers.get('content-length', ''),
                "Cache-Control": "public, max-age=31536000",
            }
        )
    except requests.RequestException as e:
        raise HTTPException(status_code=404, detail="Video not found")


@router.api_route("/{id}/care-video-stream/{filename}", methods=["GET", "HEAD"])
async def serve_care_video(
    request: Request,
    *,
    db: Session = Depends(get_db),
    id: UUID,
    filename: str,
    token: str = Query(None),
    current_user: models.User = Depends(deps.get_current_user_optional),
) -> Any:
    """
    Serve care video through backend as proxy to avoid CORS issues.
    Accepts authentication via header or query string token.
    """
    # If no user from header, try to get from token query parameter
    if not current_user and token:
        try:
            from app.core.config import settings
            from jose import jwt
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
            user_id = payload.get("sub")
            if user_id:
                current_user = crud.user.get(db, id=user_id)
        except Exception:
            pass  # Invalid token, will be handled below

    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")

    plant = crud.plant.get(db=db, id=id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")

    # Allow access if user owns the plant OR if plant allows care sharing
    if plant.owner_id != current_user.id:
        # Check if plant is public and allows care sharing
        if not (plant.is_public and plant.allow_care_sharing):
            raise HTTPException(status_code=400, detail="Not enough permissions")

        # Check if plant owner allows care sharing globally
        owner = crud.user.get(db=db, id=plant.owner_id)
        if not (owner and owner.allow_care_sharing):
            raise HTTPException(status_code=400, detail="Not enough permissions")

    # Construct R2 URL for care video (in cares subfolder)
    from app.core.config import settings
    video_url = f"{settings.R2_PUBLIC_URL}/plants/{id}/cares/videos/{filename}"

    try:
        # For HEAD requests, just return headers
        if request.method == "HEAD":
            response = requests.head(video_url)
            response.raise_for_status()
            return Response(
                content="",
                media_type=response.headers.get('content-type', 'video/mp4'),
                headers={
                    "Accept-Ranges": "bytes",
                    "Content-Length": response.headers.get('content-length', ''),
                    "Cache-Control": "public, max-age=31536000",
                }
            )

        # For GET requests, stream video from R2
        response = requests.get(video_url, stream=True)
        response.raise_for_status()

        def generate():
            for chunk in response.iter_content(chunk_size=8192):
                yield chunk

        return StreamingResponse(
            generate(),
            media_type=response.headers.get('content-type', 'video/mp4'),
            headers={
                "Accept-Ranges": "bytes",
                "Content-Length": response.headers.get('content-length', ''),
                "Cache-Control": "public, max-age=31536000",
            }
        )
    except requests.RequestException as e:
        raise HTTPException(status_code=404, detail="Care video not found")


# Plant Cares
@router.post("/{id}/cares", response_model=schemas.Care)
async def create_plant_care(
    *,
    db: Session = Depends(get_db),
    id: UUID,
    care_type: str = Form(None),  # Keep for backward compatibility
    care_types: List[str] = Form(None),  # New field for multiple types
    description: str = Form(None),
    date: str = Form(...),
    notes: str = Form(None),
    files: List[UploadFile] = File(None),
    videos: List[UploadFile] = File(None),
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Create new care record for a plant.
    """
    print(f"=== CREATE CARE DEBUG ===")
    print(f"Plant ID: {id}")
    print(f"Care type (single): {care_type}")
    print(f"Care types (multiple): {care_types}")
    print(f"Date: {date}")
    print(f"Files received: {len(files) if files else 0}")
    if files:
        for i, file in enumerate(files):
            print(f"File {i+1}: {file.filename if file.filename else 'No filename'}")
    print(f"Videos received: {len(videos) if videos else 0}")
    if videos:
        for i, video in enumerate(videos):
            print(f"Video {i+1}: {video.filename if video.filename else 'No filename'}")
    print(f"========================")

    # Determine which care types to use
    final_care_types = []
    if care_types:
        final_care_types = care_types
    elif care_type:
        final_care_types = [care_type]
    else:
        raise HTTPException(status_code=400, detail="At least one care type must be provided")

    print(f"Final care types: {final_care_types}")

    plant = crud.plant.get(db=db, id=id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    if plant.owner_id != current_user.id:
        raise HTTPException(status_code=400, detail="Not enough permissions")

    # Verificar limite de fotos por cuidado
    if files and files[0].filename:
        from app.services.plant_limit_service import PlantLimitService
        from app.api.api_v1.endpoints.subscriptions import get_user_plan_info

        user_plan = get_user_plan_info(current_user, db)
        photo_count = len([f for f in files if f.filename and f.content_type.startswith('image/')])

        # Para um novo cuidado, o número atual de fotos é 0, então verificamos se pode adicionar as novas fotos
        can_add_photos, error_message = PlantLimitService.check_care_photo_limit(user_plan, photo_count)
        if not can_add_photos:
            raise HTTPException(status_code=403, detail=error_message)

    # Parse date - support both date-only and datetime formats
    try:
        if 'T' in date:
            # Full datetime provided
            parsed_date = datetime.fromisoformat(date.replace('Z', '+00:00'))
        else:
            # Date-only provided (YYYY-MM-DD), add current time
            date_part = datetime.strptime(date, '%Y-%m-%d').date()
            current_time = datetime.now().time()
            parsed_date = datetime.combine(date_part, current_time)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid date format. Use YYYY-MM-DD or full datetime.")

    # Get user's subscription plan for validation
    from app.api.api_v1.endpoints.subscriptions import get_user_plan_info
    user_plan = get_user_plan_info(current_user, db)

    # Count total media items (files + videos)
    total_files = (len(files) if files and files[0].filename else 0)
    total_videos = (len(videos) if videos and videos[0].filename else 0)
    total_media = total_files + total_videos

    # Check total media limit (10 items max per care)
    can_upload_media, media_error = VideoLimitService.check_care_media_limit(total_media, 10)
    if not can_upload_media:
        raise HTTPException(status_code=400, detail=media_error)

    # Check video limits if videos are being uploaded
    if total_videos > 0:
        can_upload_videos, video_error = VideoLimitService.check_video_limits(user_plan, total_videos, "care")
        if not can_upload_videos:
            raise HTTPException(status_code=403, detail=video_error)

    # Handle multiple file uploads if provided
    # We'll save all images to care_images table only (no more legacy care.image)
    if files and files[0].filename:  # Check if files were actually uploaded
        # Create directory if it doesn't exist
        os.makedirs("media/cares", exist_ok=True)

    # Create care record with multiple types
    care_in = schemas.CareCreate(
        care_type=final_care_types[0] if len(final_care_types) == 1 else None,  # Backward compatibility
        care_types=final_care_types,  # New multiple types field
        description=description,
        date=parsed_date,
        notes=notes,
        image=None  # No longer using legacy image field
    )

    care = crud.care.create_with_plant(db=db, obj_in=care_in, plant_id=id)

    # Save all images to care_images table using R2
    if files and files[0].filename:
        for i, file in enumerate(files):
            # Validate file type
            if not file.content_type.startswith('image/'):
                continue  # Skip invalid files

            # Read file content
            content = await file.read()

            # Upload to R2 with resizing
            upload_result = r2_service.upload_image(
                image_data=content,
                filename=file.filename or "care_image.jpg",
                plant_id=str(id),
                subfolder="cares"  # Store care images in cares subfolder
            )

            if not upload_result['success']:
                print(f"Failed to upload care image: {upload_result.get('error', 'Unknown error')}")
                continue  # Skip this file and continue with others

            # Create care image record
            from sqlalchemy import text
            db.execute(text("""
                INSERT INTO care_images (id, care_id, image_path, is_primary)
                VALUES (gen_random_uuid(), :care_id, :image_path, :is_primary)
            """), {
                'care_id': str(care.id),
                'image_path': upload_result['filename'],  # R2 filename
                'is_primary': False  # Care images are never primary for the plant
            })

        db.commit()
        print(f"Saved {len(files)} images for care {care.id}")

    # Save all videos to care_videos table using R2
    if videos and videos[0].filename:
        for i, video in enumerate(videos):
            # Validate video file
            is_valid, validation_error = VideoValidationService.validate_video_file(video)
            if not is_valid:
                print(f"Invalid video file {video.filename}: {validation_error}")
                continue  # Skip invalid videos

            # Read video content
            content = await video.read()

            # Generate unique filename
            video_filename = VideoValidationService.generate_video_filename(
                video.filename or "care_video.mp4",
                str(id),
                "care"
            )

            # Upload to R2
            upload_result = r2_service.upload_video(
                video_data=content,
                filename=video_filename,
                plant_id=str(id),
                subfolder="cares"
            )

            if not upload_result['success']:
                print(f"Failed to upload care video: {upload_result.get('error', 'Unknown error')}")
                continue  # Skip this video and continue with others

            # Generate thumbnail
            from app.utils.video_utils import create_thumbnail_from_upload
            thumbnail_content, thumbnail_filename = create_thumbnail_from_upload(
                video_content=content,
                original_filename=video.filename or "care_video.mp4"
            )

            thumbnail_url = None
            if thumbnail_content and thumbnail_filename:
                # Upload thumbnail to R2
                thumbnail_result = r2_service.upload_thumbnail(
                    thumbnail_data=thumbnail_content,
                    filename=video_filename,  # Use video filename as base
                    plant_id=str(id),
                    subfolder="cares"
                )

                if thumbnail_result['success']:
                    thumbnail_url = thumbnail_result['url']
                else:
                    print(f"Warning: Failed to upload care video thumbnail: {thumbnail_result.get('error', 'Unknown error')}")
            else:
                print("Warning: Failed to generate care video thumbnail")

            # Create care video record
            from sqlalchemy import text
            db.execute(text("""
                INSERT INTO care_videos (id, care_id, video_path, thumbnail_url, original_filename, file_size_bytes, is_primary)
                VALUES (gen_random_uuid(), :care_id, :video_path, :thumbnail_url, :original_filename, :file_size_bytes, :is_primary)
            """), {
                'care_id': str(care.id),
                'video_path': upload_result['filename'],  # R2 filename
                'thumbnail_url': thumbnail_url,
                'original_filename': video.filename,
                'file_size_bytes': len(content),
                'is_primary': False  # Care videos are never primary for the plant
            })

        db.commit()
        print(f"Saved {len(videos)} videos for care {care.id}")

    # Refresh care to include images and videos
    db.refresh(care)
    print(f"Care images count: {len(care.images) if hasattr(care, 'images') else 'No images attr'}")
    print(f"Care videos count: {len(care.videos) if hasattr(care, 'videos') else 'No videos attr'}")

    return care


@router.get("/{id}/cares", response_model=List[schemas.Care])
def read_plant_cares(
    *,
    db: Session = Depends(get_db),
    id: UUID,
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Get care records for a plant.
    """
    plant = crud.plant.get(db=db, id=id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")

    # Allow access if user owns the plant OR if plant allows care sharing
    if plant.owner_id != current_user.id:
        # Check if plant is public and allows care sharing
        if not (plant.is_public and plant.allow_care_sharing):
            raise HTTPException(status_code=400, detail="Not enough permissions")

        # Check if plant owner allows care sharing globally
        owner = crud.user.get(db=db, id=plant.owner_id)
        if not (owner and owner.allow_care_sharing):
            raise HTTPException(status_code=400, detail="Not enough permissions")

    cares = crud.care.get_by_plant(db=db, plant_id=id, skip=skip, limit=limit)
    return cares


@router.put("/{plant_id}/cares/{care_id}", response_model=schemas.Care)
async def update_plant_care(
    *,
    db: Session = Depends(get_db),
    plant_id: UUID,
    care_id: UUID,
    care_type: str = Form(None),  # Keep for backward compatibility
    care_types: List[str] = Form(None),  # New field for multiple types
    description: str = Form(None),
    date: str = Form(None),
    notes: str = Form(None),
    file: UploadFile = File(None),
    files: List[UploadFile] = File(None),
    images_to_delete: str = Form(None),
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Update a care record.
    """
    plant = crud.plant.get(db=db, id=plant_id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    if plant.owner_id != current_user.id:
        raise HTTPException(status_code=400, detail="Not enough permissions")



    care = crud.care.get(db=db, id=care_id)
    if not care or care.plant_id != plant_id:
        raise HTTPException(status_code=404, detail="Care record not found")

    # Prepare update data
    update_data = {}

    print(f"=== UPDATE CARE DEBUG ===")
    print(f"Care ID: {care_id}")
    print(f"Plant ID: {plant_id}")
    print(f"Received data:")
    print(f"  care_type (single): {care_type}")
    print(f"  care_types (multiple): {care_types}")
    print(f"  description: {description}")
    print(f"  notes: {notes}")
    print(f"  date: {date}")
    print(f"  file: {file.filename if file else None}")
    print(f"  files: {[f.filename for f in files] if files else None}")
    print(f"  images_to_delete: {images_to_delete}")

    # Determine which care types to use
    if care_types:
        update_data['care_types'] = care_types
        update_data['care_type'] = None  # Clear single type when using multiple
        print(f"Using multiple care types: {care_types}")
    elif care_type:
        update_data['care_types'] = [care_type]
        update_data['care_type'] = care_type  # Keep for backward compatibility
        print(f"Using single care type: {care_type}")

    # Process images to delete
    if images_to_delete:
        try:
            import json
            image_ids = json.loads(images_to_delete)
            print(f"Images to delete: {image_ids}")

            for image_id in image_ids:
                if image_id == 'legacy':
                    # Delete legacy image
                    if care.image:
                        old_file_path = f"media/{care.image}"
                        if os.path.exists(old_file_path):
                            os.remove(old_file_path)
                            print(f"Deleted legacy image: {old_file_path}")
                        update_data["image"] = None
                else:
                    # Delete care image from care_images table
                    from sqlalchemy import text
                    result = db.execute(text("""
                        SELECT image_path FROM care_images WHERE id = :image_id AND care_id = :care_id
                    """), {'image_id': image_id, 'care_id': str(care_id)})

                    image_record = result.fetchone()
                    if image_record:
                        # Delete file
                        file_path = f"media/{image_record.image_path}"
                        if os.path.exists(file_path):
                            os.remove(file_path)
                            print(f"Deleted care image file: {file_path}")

                        # Delete database record
                        db.execute(text("""
                            DELETE FROM care_images WHERE id = :image_id AND care_id = :care_id
                        """), {'image_id': image_id, 'care_id': str(care_id)})
                        print(f"Deleted care image record: {image_id}")

            db.commit()
        except Exception as e:
            print(f"Error deleting images: {e}")

    if description is not None:
        update_data["description"] = description
    if notes is not None:
        update_data["notes"] = notes
    if date is not None:
        try:
            if 'T' in date:
                # Full datetime provided
                parsed_date = datetime.fromisoformat(date.replace('Z', '+00:00'))
            else:
                # Date-only provided (YYYY-MM-DD), preserve original time if editing, or use current time
                date_part = datetime.strptime(date, '%Y-%m-%d').date()
                if care.date:
                    # Preserve original time when editing
                    original_time = care.date.time()
                    parsed_date = datetime.combine(date_part, original_time)
                else:
                    # Use current time for new records
                    current_time = datetime.now().time()
                    parsed_date = datetime.combine(date_part, current_time)
            update_data["date"] = parsed_date
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid date format. Use YYYY-MM-DD or full datetime.")

    print(f"Update data prepared: {update_data}")

    # Update the care record
    care = crud.care.update(db=db, db_obj=care, obj_in=update_data)
    print(f"Care updated in database:")
    print(f"  ID: {care.id}")
    print(f"  care_type: {care.care_type}")
    print(f"  care_types: {care.care_types}")

    # Handle file upload if provided
    if file:
        # Validate file type
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")

        # Delete old image if exists
        if care.image:
            old_file_path = f"media/{care.image}"
            if os.path.exists(old_file_path):
                os.remove(old_file_path)

        # Generate unique filename
        file_extension = file.filename.split('.')[-1] if file.filename else 'jpg'
        filename = f"care_{uuid.uuid4()}.{file_extension}"
        file_path = f"media/cares/{filename}"

        # Create directory if it doesn't exist
        os.makedirs("media/cares", exist_ok=True)

        # Save file
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        # Process image (resize if needed)
        try:
            with Image.open(file_path) as img:
                # Automatic EXIF rotation disabled - frontend controls orientation

                # Resize if needed
                if img.width > 800 or img.height > 800:
                    img.thumbnail((800, 800), Image.Resampling.LANCZOS)

                # Save without EXIF to avoid orientation issues
                img.save(file_path, optimize=True, quality=85, exif=b'')
        except Exception as e:
            print(f"Error processing care image: {e}")
            pass  # If image processing fails, keep original

        update_data["image"] = f"cares/{filename}"

    # Process multiple new files using R2
    if files and files[0].filename:
        # Verificar limite de fotos por cuidado para novas fotos
        from app.services.plant_limit_service import PlantLimitService
        from app.api.api_v1.endpoints.subscriptions import get_user_plan_info

        user_plan = get_user_plan_info(current_user, db)
        new_photo_count = len([f for f in files if f.filename and f.content_type.startswith('image/')])
        current_photo_count = len(care.images)
        total_photos_after_upload = current_photo_count + new_photo_count

        can_add_photos, error_message = PlantLimitService.check_care_photo_limit(user_plan, total_photos_after_upload)
        if not can_add_photos:
            raise HTTPException(status_code=403, detail=error_message)

        print(f"Processing {len(files)} new files...")
        for i, new_file in enumerate(files):
            # Validate file type
            if not new_file.content_type.startswith('image/'):
                continue  # Skip invalid files

            # Read file content
            content = await new_file.read()

            # Upload to R2 with resizing
            upload_result = r2_service.upload_image(
                image_data=content,
                filename=new_file.filename or "care_image.jpg",
                plant_id=str(plant_id),
                subfolder="cares"
            )

            if not upload_result['success']:
                print(f"Failed to upload care image: {upload_result.get('error', 'Unknown error')}")
                continue  # Skip this file and continue with others

            # Create care image record
            from sqlalchemy import text
            db.execute(text("""
                INSERT INTO care_images (id, care_id, image_path, is_primary)
                VALUES (gen_random_uuid(), :care_id, :image_path, :is_primary)
            """), {
                'care_id': str(care_id),
                'image_path': upload_result['filename'],  # R2 filename
                'is_primary': False  # New images are not primary by default
            })
            print(f"Added new care image: {upload_result['filename']}")

        db.commit()

    print(f"Final care state:")
    print(f"  ID: {care.id}")
    print(f"  care_type: {care.care_type}")
    print(f"  care_types: {care.care_types}")
    print(f"  Description: {care.description}")
    print(f"  Notes: {care.notes}")
    print(f"  Date: {care.date}")
    print(f"========================")

    return care


@router.delete("/{plant_id}/cares/{care_id}")
def delete_plant_care(
    *,
    db: Session = Depends(get_db),
    plant_id: UUID,
    care_id: UUID,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Delete a care record.
    """
    plant = crud.plant.get(db=db, id=plant_id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    if plant.owner_id != current_user.id:
        raise HTTPException(status_code=400, detail="Not enough permissions")



    care = crud.care.get(db=db, id=care_id)
    if not care or care.plant_id != plant_id:
        raise HTTPException(status_code=404, detail="Care record not found")

    crud.care.remove(db=db, id=care_id)
    return {"message": "Care record deleted successfully"}


@router.put("/{plant_id}/cares/{care_id}/images/{image_id}")
async def update_care_image(
    *,
    db: Session = Depends(get_db),
    plant_id: UUID,
    care_id: UUID,
    image_id: UUID,
    caption: str = Form(None),
    is_primary: bool = Form(None),
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """Update care image metadata"""

    # Verify plant ownership
    plant = crud.plant.get(db=db, id=plant_id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    if plant.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not enough permissions")

    # Verify care exists and belongs to plant
    care = crud.care.get(db=db, id=care_id)
    if not care or care.plant_id != plant_id:
        raise HTTPException(status_code=404, detail="Care not found")

    # Update care image
    from sqlalchemy import text

    # Check if image exists
    result = db.execute(text("""
        SELECT id FROM care_images WHERE id = :image_id AND care_id = :care_id
    """), {'image_id': str(image_id), 'care_id': str(care_id)})

    if not result.fetchone():
        raise HTTPException(status_code=404, detail="Care image not found")

    # Update fields
    update_fields = []
    params = {'image_id': str(image_id), 'care_id': str(care_id)}

    if caption is not None:
        update_fields.append("caption = :caption")
        params['caption'] = caption

    if is_primary is not None:
        if is_primary:
            # First, set all other images for this care to not primary
            db.execute(text("""
                UPDATE care_images SET is_primary = false WHERE care_id = :care_id
            """), {'care_id': str(care_id)})

        update_fields.append("is_primary = :is_primary")
        params['is_primary'] = is_primary

    if update_fields:
        query = f"""
            UPDATE care_images
            SET {', '.join(update_fields)}
            WHERE id = :image_id AND care_id = :care_id
        """
        db.execute(text(query), params)
        db.commit()

    return {"message": "Care image updated successfully"}


@router.post("/{plant_id}/cares/{care_id}/images/{image_id}/rotate")
async def rotate_care_image(
    *,
    db: Session = Depends(get_db),
    plant_id: UUID,
    care_id: UUID,
    image_id: UUID,
    rotation: int = Query(90, description="Rotation angle in degrees"),
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """Rotate care image"""

    # Verify plant ownership
    plant = crud.plant.get(db=db, id=plant_id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    if plant.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not enough permissions")

    # Verify care exists and belongs to plant
    care = crud.care.get(db=db, id=care_id)
    if not care or care.plant_id != plant_id:
        raise HTTPException(status_code=404, detail="Care not found")

    # Get image path
    from sqlalchemy import text
    result = db.execute(text("""
        SELECT image_path FROM care_images WHERE id = :image_id AND care_id = :care_id
    """), {'image_id': str(image_id), 'care_id': str(care_id)})

    image_record = result.fetchone()
    if not image_record:
        raise HTTPException(status_code=404, detail="Care image not found")

    # Rotate image file (R2 or local)
    if image_record.image_path:
        filename_without_ext = image_record.image_path.split('.')[0]

        if len(filename_without_ext) == 32:  # R2 filename
            # Rotate image in R2
            rotation_result = r2_service.rotate_image(
                plant_id=str(plant_id),
                filename=image_record.image_path,
                rotation_degrees=rotation,
                subfolder="cares"
            )

            if not rotation_result or not rotation_result.get('success'):
                raise HTTPException(status_code=500, detail="Failed to rotate image in R2")

            # Update database with new filename if it changed
            new_filename = rotation_result.get('filename')
            if new_filename and new_filename != image_record.image_path:
                db.execute(text("""
                    UPDATE care_images
                    SET image_path = :new_filename
                    WHERE id = :image_id AND care_id = :care_id
                """), {
                    'new_filename': new_filename,
                    'image_id': str(image_id),
                    'care_id': str(care_id)
                })
                db.commit()

            return {"message": "Care image rotated successfully"}
        else:
            # Legacy local file
            file_path = f"media/{image_record.image_path}"
            if not os.path.exists(file_path):
                raise HTTPException(status_code=404, detail="Image file not found")

            try:
                with Image.open(file_path) as img:
                    # Rotate image
                    rotated_img = img.rotate(-rotation, expand=True)

                    # Save rotated image
                    rotated_img.save(file_path, optimize=True, quality=85)

                return {"message": "Care image rotated successfully"}
            except Exception as e:
                print(f"Error rotating care image: {e}")
                raise HTTPException(status_code=500, detail="Error rotating image")
    else:
        raise HTTPException(status_code=404, detail="No image path found")


@router.delete("/{plant_id}/cares/{care_id}/images/{image_id}")
async def delete_care_image(
    *,
    db: Session = Depends(get_db),
    plant_id: UUID,
    care_id: UUID,
    image_id: UUID,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """Delete care image"""

    # Verify plant ownership
    plant = crud.plant.get(db=db, id=plant_id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    if plant.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not enough permissions")



    # Verify care exists and belongs to plant
    care = crud.care.get(db=db, id=care_id)
    if not care or care.plant_id != plant_id:
        raise HTTPException(status_code=404, detail="Care not found")

    # Get and delete image
    from sqlalchemy import text
    result = db.execute(text("""
        SELECT image_path FROM care_images WHERE id = :image_id AND care_id = :care_id
    """), {'image_id': str(image_id), 'care_id': str(care_id)})

    image_record = result.fetchone()
    if not image_record:
        raise HTTPException(status_code=404, detail="Care image not found")

    # Delete file from R2 or local filesystem
    if image_record.image_path:
        filename_without_ext = image_record.image_path.split('.')[0]
        if len(filename_without_ext) == 32:  # R2 filename
            r2_service.delete_image(str(plant_id), image_record.image_path, subfolder="cares")
        else:
            # Legacy local file
            file_path = f"media/{image_record.image_path}"
            if os.path.exists(file_path):
                os.remove(file_path)

    # Delete database record
    db.execute(text("""
        DELETE FROM care_images WHERE id = :image_id AND care_id = :care_id
    """), {'image_id': str(image_id), 'care_id': str(care_id)})

    db.commit()

    return {"message": "Care image deleted successfully"}


@router.delete("/{plant_id}/cares/{care_id}/videos/{video_id}")
async def delete_care_video(
    *,
    db: Session = Depends(get_db),
    plant_id: UUID,
    care_id: UUID,
    video_id: UUID,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """Delete care video"""

    # Verify plant ownership
    plant = crud.plant.get(db=db, id=plant_id)
    if not plant:
        raise HTTPException(status_code=404, detail="Plant not found")
    if plant.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not enough permissions")



    # Verify care exists and belongs to plant
    care = crud.care.get(db=db, id=care_id)
    if not care or care.plant_id != plant_id:
        raise HTTPException(status_code=404, detail="Care not found")

    # Get and delete video
    from sqlalchemy import text
    result = db.execute(text("""
        SELECT video_path, thumbnail_url FROM care_videos WHERE id = :video_id AND care_id = :care_id
    """), {'video_id': str(video_id), 'care_id': str(care_id)})

    video_record = result.fetchone()
    if not video_record:
        raise HTTPException(status_code=404, detail="Care video not found")

    # Delete video file from R2 or local filesystem
    if video_record.video_path:
        filename_without_ext = video_record.video_path.split('.')[0]
        if len(filename_without_ext) == 32:  # R2 filename
            r2_service.delete_video(str(plant_id), video_record.video_path, subfolder="cares")
        else:
            # Legacy local file
            file_path = f"media/{video_record.video_path}"
            if os.path.exists(file_path):
                os.remove(file_path)

    # Delete thumbnail from R2 if exists
    if video_record.thumbnail_url:
        # Extract thumbnail filename from URL
        thumbnail_filename = video_record.thumbnail_url.split('/')[-1]
        r2_service.delete_thumbnail(str(plant_id), thumbnail_filename, subfolder="cares")

    # Delete database record
    db.execute(text("""
        DELETE FROM care_videos WHERE id = :video_id AND care_id = :care_id
    """), {'video_id': str(video_id), 'care_id': str(care_id)})

    db.commit()

    return {"message": "Care video deleted successfully"}
