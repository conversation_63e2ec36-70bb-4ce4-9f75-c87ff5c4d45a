from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from uuid import UUID
import logging

from app import crud, models, schemas
from app.api import deps
from app.core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/search", response_model=schemas.social.UserSearchResponse)
def search_users(
    q: str = Query(..., min_length=2, description="Search query"),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
):
    """
    Search for users by name, username or email
    """
    users, total = crud.social.search_users(
        db=db, query=q, current_user_id=current_user.id, skip=skip, limit=limit
    )
    
    # Check which users the current user is following
    user_results = []
    for user in users:
        is_following = crud.user_follow.is_following(
            db=db, follower_id=current_user.id, followed_id=user.id
        )
        
        user_result = schemas.social.UserSearchResult(
            id=user.id,
            username=user.username,
            first_name=user.first_name,
            last_name=user.last_name,
            bio=user.bio,
            avatar=user.avatar,
            followers_count=user.followers_count,
            following_count=user.following_count,
            is_following=is_following
        )
        user_results.append(user_result)
    
    return schemas.social.UserSearchResponse(
        users=user_results,
        total=total,
        page=(skip // limit) + 1,
        per_page=limit,
        has_next=(skip + limit) < total,
        has_prev=skip > 0
    )


@router.post("/follow/{user_id}", response_model=schemas.social.FollowActionResponse)
def follow_user(
    user_id: UUID,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
):
    """
    Follow a user
    """
    if user_id == current_user.id:
        raise HTTPException(status_code=400, detail="Cannot follow yourself")
    
    # Check if target user exists and is public
    target_user = crud.user.get(db=db, id=user_id)
    if not target_user:
        raise HTTPException(status_code=404, detail="User not found")
    
    if not target_user.is_public:
        raise HTTPException(status_code=403, detail="User profile is private")
    
    # Follow the user
    crud.user_follow.follow_user(
        db=db, follower_id=current_user.id, followed_id=user_id
    )

    # Create notification for the followed user
    try:
        crud.notification.create_follower_notification(
            db=db,
            followed_user_id=str(user_id),
            follower_id=str(current_user.id),
            follower_name=current_user.full_name
        )
    except Exception as e:
        print(f"Error creating follower notification: {e}")
        # Don't fail the follow operation if notification fails

    # Get updated follower count
    updated_user = crud.user.get(db=db, id=user_id)

    return schemas.social.FollowActionResponse(
        success=True,
        is_following=True,
        followers_count=updated_user.followers_count,
        message="User followed successfully"
    )


@router.delete("/follow/{user_id}", response_model=schemas.social.FollowActionResponse)
def unfollow_user(
    user_id: UUID,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
):
    """
    Unfollow a user
    """
    if user_id == current_user.id:
        raise HTTPException(status_code=400, detail="Cannot unfollow yourself")
    
    # Check if target user exists
    target_user = crud.user.get(db=db, id=user_id)
    if not target_user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Unfollow the user
    success = crud.user_follow.unfollow_user(
        db=db, follower_id=current_user.id, followed_id=user_id
    )
    
    if not success:
        raise HTTPException(status_code=400, detail="You are not following this user")
    
    # Get updated follower count
    updated_user = crud.user.get(db=db, id=user_id)
    
    return schemas.social.FollowActionResponse(
        success=True,
        is_following=False,
        followers_count=updated_user.followers_count,
        message="User unfollowed successfully"
    )


@router.get("/profile/{user_id}", response_model=schemas.social.PublicUserProfile)
def get_public_profile(
    user_id: UUID,
    db: Session = Depends(deps.get_db),
    current_user: Optional[models.User] = Depends(deps.get_current_user_optional),
):
    """
    Get public profile of a user by ID
    """
    current_user_id = current_user.id if current_user else None
    user = crud.social.get_public_profile(
        db=db, user_id=user_id, current_user_id=current_user_id
    )
    
    if not user:
        raise HTTPException(status_code=404, detail="User not found or profile is private")
    
    # Check if current user is following this profile
    is_following = False
    if current_user and current_user.id != user_id:
        is_following = crud.user_follow.is_following(
            db=db, follower_id=current_user.id, followed_id=user_id
        )

    return schemas.social.PublicUserProfile(
        id=user.id,
        username=user.username,
        first_name=user.first_name,
        last_name=user.last_name,
        bio=user.bio,
        avatar=user.avatar,
        city=user.city,
        state=user.state,
        country=user.country,
        followers_count=user.followers_count,
        following_count=user.following_count,
        is_public=user.is_public,
        is_following=is_following,
        created_at=user.created_at
    )


@router.get("/profile/username/{username}", response_model=schemas.social.PublicUserProfile)
def get_public_profile_by_username(
    username: str,
    db: Session = Depends(deps.get_db),
    current_user: Optional[models.User] = Depends(deps.get_current_user_optional),
):
    """
    Get public profile of a user by username
    """
    current_user_id = current_user.id if current_user else None
    user = crud.social.get_public_profile_by_username(
        db=db, username=username, current_user_id=current_user_id
    )

    if not user:
        raise HTTPException(status_code=404, detail="User not found or profile is private")

    # Check if current user is following this profile
    is_following = False
    if current_user and current_user.id != user.id:
        is_following = crud.user_follow.is_following(
            db=db, follower_id=current_user.id, followed_id=user.id
        )

    return schemas.social.PublicUserProfile(
        id=user.id,
        username=user.username,
        first_name=user.first_name,
        last_name=user.last_name,
        bio=user.bio,
        avatar=user.avatar,
        city=user.city,
        state=user.state,
        country=user.country,
        followers_count=user.followers_count,
        following_count=user.following_count,
        is_public=user.is_public,
        is_following=is_following,
        created_at=user.created_at
    )


@router.get("/profile/{user_id}/plants")
def get_public_plants(
    user_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(deps.get_db),
    current_user: Optional[models.User] = Depends(deps.get_current_user_optional),
):
    """
    Get public plants of a user
    """
    current_user_id = current_user.id if current_user else None
    plants, total = crud.social.get_public_plants(
        db=db, user_id=user_id, current_user_id=current_user_id, skip=skip, limit=limit
    )

    # Format plants with primary images
    result = []
    for plant in plants:
        plant_dict = plant.__dict__.copy()

        # Get primary image
        primary_image = db.query(models.PlantImage).filter(
            models.PlantImage.plant_id == plant.id,
            models.PlantImage.is_primary == True
        ).first()

        # Construct R2 URL for primary image
        if primary_image and primary_image.image:
            filename_without_ext = primary_image.image.split('.')[0]
            if len(filename_without_ext) == 32:  # R2 filename
                from app.core.config import settings
                plant_dict["primary_image"] = f"{settings.R2_PUBLIC_URL}/plants/{plant.id}/medium_{primary_image.image}"
            else:
                # Legacy local file
                plant_dict["primary_image"] = f"/media/{primary_image.image}"
        else:
            plant_dict["primary_image"] = None

        # Get care count
        care_count = db.query(models.Care).filter(models.Care.plant_id == plant.id).count()
        plant_dict["care_count"] = care_count

        # Add plant_type_display
        plant_dict["plant_type_display"] = plant.plant_type_display

        result.append(plant_dict)

    return {
        "plants": result,
        "total": total,
        "skip": skip,
        "limit": limit
    }


@router.get("/profile/username/{username}/plants")
def get_public_plants_by_username(
    username: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(deps.get_db),
    current_user: Optional[models.User] = Depends(deps.get_current_user_optional),
):
    """
    Get public plants of a user by username
    """
    # First get the user by username
    user = crud.social.get_public_profile_by_username(
        db=db, username=username, current_user_id=current_user.id if current_user else None
    )

    if not user:
        raise HTTPException(status_code=404, detail="User not found or profile is private")

    # Then get their plants
    current_user_id = current_user.id if current_user else None
    plants, total = crud.social.get_public_plants(
        db=db, user_id=user.id, current_user_id=current_user_id, skip=skip, limit=limit
    )

    # Format plants with primary images
    result = []
    for plant in plants:
        plant_dict = plant.__dict__.copy()

        # Get primary image
        primary_image = db.query(models.PlantImage).filter(
            models.PlantImage.plant_id == plant.id,
            models.PlantImage.is_primary == True
        ).first()

        # Construct R2 URL for primary image
        if primary_image and primary_image.image:
            filename_without_ext = primary_image.image.split('.')[0]
            if len(filename_without_ext) == 32:  # R2 filename
                from app.core.config import settings
                plant_dict["primary_image"] = f"{settings.R2_PUBLIC_URL}/plants/{plant.id}/medium_{primary_image.image}"
            else:
                # Legacy local file
                plant_dict["primary_image"] = f"/media/{primary_image.image}"
        else:
            plant_dict["primary_image"] = None

        # Get care count
        care_count = db.query(models.Care).filter(models.Care.plant_id == plant.id).count()
        plant_dict["care_count"] = care_count

        # Add plant_type_display
        plant_dict["plant_type_display"] = plant.plant_type_display

        result.append(plant_dict)

    return {
        "plants": result,
        "total": total,
        "skip": skip,
        "limit": limit
    }


@router.get("/{user_id}/followers", response_model=schemas.social.FollowersResponse)
def get_user_followers(
    user_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(deps.get_db),
    current_user: Optional[models.User] = Depends(deps.get_current_user_optional),
):
    """
    Get followers of a user
    """
    # Check if user exists
    target_user = db.query(models.User).filter(models.User.id == user_id).first()
    if not target_user:
        raise HTTPException(status_code=404, detail="User not found")

    # Allow access if:
    # 1. User is viewing their own followers
    # 2. Target user has public profile
    if current_user and current_user.id == user_id:
        # User can always see their own followers
        pass
    elif not target_user.is_public:
        raise HTTPException(status_code=404, detail="User not found or profile is private")
    
    followers, total = crud.user_follow.get_followers(
        db=db, user_id=user_id, skip=skip, limit=limit
    )
    
    # Check which followers the current user is following
    current_user_id = current_user.id if current_user else None
    follower_results = []
    
    for follower in followers:
        is_following = False
        if current_user_id and current_user_id != follower.id:
            is_following = crud.user_follow.is_following(
                db=db, follower_id=current_user_id, followed_id=follower.id
            )
        
        # Get follow date
        follow_relationship = db.query(models.UserFollow).filter(
            models.UserFollow.follower_id == follower.id,
            models.UserFollow.followed_id == user_id
        ).first()
        followed_at = follow_relationship.created_at if follow_relationship else None
        
        follower_result = schemas.social.FollowerUser(
            id=follower.id,
            username=follower.username,
            first_name=follower.first_name,
            last_name=follower.last_name,
            avatar=follower.avatar,
            bio=follower.bio,
            followers_count=follower.followers_count,
            is_following=is_following,
            followed_at=followed_at
        )
        follower_results.append(follower_result)
    
    return schemas.social.FollowersResponse(
        followers=follower_results,
        total=total,
        page=(skip // limit) + 1,
        per_page=limit,
        has_next=(skip + limit) < total,
        has_prev=skip > 0
    )


@router.get("/{user_id}/following", response_model=schemas.social.FollowingResponse)
def get_user_following(
    user_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(deps.get_db),
    current_user: Optional[models.User] = Depends(deps.get_current_user_optional),
):
    """
    Get users that a user is following
    """
    # Check if user exists
    target_user = db.query(models.User).filter(models.User.id == user_id).first()
    if not target_user:
        raise HTTPException(status_code=404, detail="User not found")

    # Allow access if:
    # 1. User is viewing their own following list
    # 2. Target user has public profile
    if current_user and current_user.id == user_id:
        # User can always see who they are following
        pass
    elif not target_user.is_public:
        raise HTTPException(status_code=404, detail="User not found or profile is private")
    
    following, total = crud.user_follow.get_following(
        db=db, user_id=user_id, skip=skip, limit=limit
    )
    
    # Check which users the current user is following
    current_user_id = current_user.id if current_user else None
    following_results = []
    
    for followed_user in following:
        is_following = False
        if current_user_id and current_user_id != followed_user.id:
            is_following = crud.user_follow.is_following(
                db=db, follower_id=current_user_id, followed_id=followed_user.id
            )
        
        # Get follow date
        follow_relationship = db.query(models.UserFollow).filter(
            models.UserFollow.follower_id == user_id,
            models.UserFollow.followed_id == followed_user.id
        ).first()
        followed_at = follow_relationship.created_at if follow_relationship else None
        
        following_result = schemas.social.FollowingUser(
            id=followed_user.id,
            username=followed_user.username,
            first_name=followed_user.first_name,
            last_name=followed_user.last_name,
            avatar=followed_user.avatar,
            bio=followed_user.bio,
            followers_count=followed_user.followers_count,
            is_following=is_following,
            followed_at=followed_at
        )
        following_results.append(following_result)
    
    return schemas.social.FollowingResponse(
        following=following_results,
        total=total,
        page=(skip // limit) + 1,
        per_page=limit,
        has_next=(skip + limit) < total,
        has_prev=skip > 0
    )


@router.put("/privacy", response_model=schemas.social.UserPrivacySettings)
def update_privacy_settings(
    privacy_settings: schemas.social.UserPrivacySettings,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
):
    """
    Update user privacy settings
    """
    update_data = {
        "is_public": privacy_settings.is_public,
        "allow_care_sharing": privacy_settings.allow_care_sharing,
        "allow_search": privacy_settings.allow_search
    }

    updated_user = crud.user.update(db=db, db_obj=current_user, obj_in=update_data)

    return schemas.social.UserPrivacySettings(
        is_public=updated_user.is_public,
        allow_care_sharing=updated_user.allow_care_sharing,
        allow_search=updated_user.allow_search
    )


@router.get("/privacy", response_model=schemas.social.UserPrivacySettings)
def get_privacy_settings(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
):
    """
    Get current user privacy settings
    """
    return schemas.social.UserPrivacySettings(
        is_public=current_user.is_public,
        allow_care_sharing=current_user.allow_care_sharing,
        allow_search=current_user.allow_search
    )


@router.get("/username/check/{username}", response_model=dict)
def check_username_availability(
    username: str,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user)
):
    """Check if username is available"""
    try:
        logger.info(f"Checking username availability for: {username} by user: {current_user.id}")

        # Basic validation
        if len(username) < 3 or len(username) > 30:
            logger.info(f"Username {username} failed length validation")
            return {"available": False, "reason": "invalid_length"}

        # Check pattern
        import re
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            logger.info(f"Username {username} failed character validation")
            return {"available": False, "reason": "invalid_characters"}

        # Check if starts/ends with underscore
        if username.startswith('_') or username.endswith('_'):
            logger.info(f"Username {username} failed underscore validation")
            return {"available": False, "reason": "invalid_format"}

        # Check consecutive underscores
        if '__' in username:
            logger.info(f"Username {username} failed consecutive underscore validation")
            return {"available": False, "reason": "invalid_format"}

        # Check if username is already taken (but allow current user's username)
        logger.info(f"Checking database for existing username: {username}")
        existing_user = db.query(models.User).filter(models.User.username == username.lower()).first()

        if existing_user:
            logger.info(f"Found existing user with username {username}: {existing_user.id}")
            if existing_user.id != current_user.id:
                logger.info(f"Username {username} is taken by different user")
                return {"available": False, "reason": "already_taken"}
            else:
                logger.info(f"Username {username} belongs to current user")
                return {"available": True, "reason": "current_user"}

        logger.info(f"Username {username} is available")
        return {"available": True, "reason": "available"}
    except Exception as e:
        logger.error(f"Error checking username availability: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/username/check-public/{username}", response_model=dict)
def check_username_availability_public(
    username: str,
    db: Session = Depends(deps.get_db)
):
    """Check if username is available (public endpoint for registration)"""
    try:
        logger.info(f"Public username check for: {username}")

        # Basic validation
        if len(username) < 3 or len(username) > 30:
            logger.info(f"Username {username} failed length validation")
            return {"available": False, "reason": "invalid_length"}

        # Check pattern
        import re
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            logger.info(f"Username {username} failed character validation")
            return {"available": False, "reason": "invalid_characters"}

        # Check if starts/ends with underscore
        if username.startswith('_') or username.endswith('_'):
            logger.info(f"Username {username} failed underscore validation")
            return {"available": False, "reason": "invalid_format"}

        # Check consecutive underscores
        if '__' in username:
            logger.info(f"Username {username} failed consecutive underscore validation")
            return {"available": False, "reason": "invalid_format"}

        # Check if username is already taken
        logger.info(f"Checking database for existing username: {username}")
        existing_user = db.query(models.User).filter(models.User.username == username.lower()).first()

        if existing_user:
            logger.info(f"Username {username} is already taken")
            return {"available": False, "reason": "already_taken"}

        logger.info(f"Username {username} is available")
        return {"available": True, "reason": "available"}
    except Exception as e:
        logger.error(f"Error checking username availability: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/username", response_model=dict)
def update_username(
    username_data: schemas.social.UsernameUpdate,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
):
    """
    Update user's username
    """
    success = crud.social.update_username(
        db=db, user_id=current_user.id, username=username_data.username
    )

    if not success:
        raise HTTPException(status_code=400, detail="Username already taken")

    return {"success": True, "message": "Username updated successfully"}


@router.get("/stats", response_model=schemas.social.SocialStats)
def get_social_stats(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
):
    """
    Get social statistics for current user
    """
    # Count public plants
    public_plants_count = db.query(models.Plant).filter(
        models.Plant.owner_id == current_user.id,
        models.Plant.is_public == True,
        models.Plant.archived == False
    ).count()

    # Count total plants
    total_plants_count = db.query(models.Plant).filter(
        models.Plant.owner_id == current_user.id,
        models.Plant.archived == False
    ).count()

    return schemas.social.SocialStats(
        followers_count=current_user.followers_count,
        following_count=current_user.following_count,
        public_plants_count=public_plants_count,
        total_plants_count=total_plants_count
    )


@router.post("/fix-counters")
def fix_follow_counters(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
):
    """
    Fix follow counters for all users (temporary endpoint for debugging).
    """
    try:
        from app.crud.crud_social_interactions import fix_all_counters
        fix_all_counters(db)
        return {"message": "All follow counters fixed successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fixing counters: {str(e)}")
