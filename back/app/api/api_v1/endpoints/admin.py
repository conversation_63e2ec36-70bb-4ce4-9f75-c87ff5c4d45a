from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from uuid import UUID
from datetime import datetime

from app import models, schemas
from app.crud import subscription_plan
from app.api import deps_admin, deps
from app.db.session import get_db
from app.schemas.admin import (
    AdminUserList, AdminUserUpdate, AdminDashboardStats,
    CouponCreate, CouponUpdate, Coupon, PlanChangeRequest,
    AdminPaymentList
)
from app.crud.crud_admin import admin, coupon

router = APIRouter()


# Test endpoint
@router.get("/test-access")
def test_admin_access(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Test endpoint to check admin access
    """
    return {
        "user_id": str(current_user.id),
        "email": current_user.email,
        "is_active": current_user.is_active,
        "is_admin": current_user.email == "<EMAIL>"
    }


# Dashboard
@router.get("/dashboard", response_model=AdminDashboardStats)
def get_admin_dashboard(
    db: Session = Depends(get_db),
    current_admin: models.User = Depends(deps_admin.get_admin_user),
) -> Any:
    """
    Get admin dashboard statistics
    """
    return admin.get_dashboard_stats(db)


# User Management
@router.get("/users", response_model=List[AdminUserList])
def get_users_list(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_admin: models.User = Depends(deps_admin.get_admin_user),
) -> Any:
    """
    Get paginated list of users with subscription info
    """
    return admin.get_users_list(db, skip=skip, limit=limit)


@router.put("/users/{user_id}")
def update_user(
    user_id: UUID,
    user_update: AdminUserUpdate,
    db: Session = Depends(get_db),
    current_admin: models.User = Depends(deps_admin.get_admin_user),
) -> Any:
    """
    Update user data (admin only)
    """
    user = admin.update_user(db, user_id=user_id, user_update=user_update)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    return {"message": "User updated successfully", "user_id": str(user_id)}


@router.post("/users/{user_id}/change-plan")
def change_user_plan(
    user_id: UUID,
    plan_change: PlanChangeRequest,
    db: Session = Depends(get_db),
    current_admin: models.User = Depends(deps_admin.get_admin_user),
) -> Any:
    """
    Change user's subscription plan
    """
    success = admin.change_user_plan(
        db, 
        user_id=user_id, 
        new_plan_id=plan_change.new_plan_id,
        expires_at=plan_change.expires_at
    )
    
    if not success:
        raise HTTPException(status_code=400, detail="Failed to change user plan")
    
    return {"message": "User plan changed successfully", "user_id": str(user_id)}


@router.post("/users/{user_id}/deactivate-plan")
def deactivate_user_plan(
    user_id: UUID,
    db: Session = Depends(get_db),
    current_admin: models.User = Depends(deps_admin.get_admin_user),
) -> Any:
    """
    Deactivate user's subscription plan (move to free)
    """
    success = admin.deactivate_user_plan(db, user_id=user_id)
    
    if not success:
        raise HTTPException(status_code=400, detail="Failed to deactivate user plan")
    
    return {"message": "User plan deactivated successfully", "user_id": str(user_id)}


# Subscription Plans
@router.get("/plans")
def get_subscription_plans(
    db: Session = Depends(get_db),
    current_admin: models.User = Depends(deps_admin.get_admin_user),
) -> Any:
    """
    Get all active subscription plans for admin (simplified two-tier structure)
    """
    # Get only active plans
    plans = db.query(models.SubscriptionPlan).filter(
        models.SubscriptionPlan.is_active == True
    ).order_by(models.SubscriptionPlan.price_cents).all()

    # Convert to dict format with proper ordering
    result = []
    plan_order = {"free": 1, "premium_monthly": 2, "premium_annual": 3}

    for plan in plans:
        # Add price display for admin interface
        if plan.price_cents == 0:
            price_display = "Gratuito"
        else:
            price_display = f"R$ {(plan.price_cents / 100):.2f}"
            if plan.billing_cycle == "yearly":
                price_display += "/ano"
            else:
                price_display += "/mês"

        result.append({
            "id": str(plan.id),
            "name": plan.name,
            "display_name": plan.display_name,
            "description": plan.description,
            "price_cents": plan.price_cents,
            "price_display": price_display,
            "currency": plan.currency,
            "billing_cycle": plan.billing_cycle,
            "max_plants": plan.max_plants,
            "max_photos_per_plant": plan.max_photos_per_plant,
            "max_photos_per_care": plan.max_photos_per_care,
            "max_videos_per_plant": getattr(plan, 'max_videos_per_plant', 0),
            "max_videos_per_care": getattr(plan, 'max_videos_per_care', 0),
            "allows_videos": getattr(plan, 'allows_videos', False),
            "is_active": plan.is_active,
            "allows_auto_renewal": plan.allows_auto_renewal,
            "sort_order": plan_order.get(plan.name, 999)
        })

    # Sort by the defined order
    result.sort(key=lambda x: x["sort_order"])

    return result


# Coupon Management
@router.get("/coupons", response_model=List[Coupon])
def get_coupons(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    active_only: bool = Query(False),
    db: Session = Depends(get_db),
    current_admin: models.User = Depends(deps_admin.get_admin_user),
) -> Any:
    """
    Get coupons list
    """
    if active_only:
        return coupon.get_active_coupons(db, skip=skip, limit=limit)
    else:
        return coupon.get_multi(db, skip=skip, limit=limit)


@router.post("/coupons", response_model=Coupon)
def create_coupon(
    coupon_in: CouponCreate,
    db: Session = Depends(get_db),
    current_admin: models.User = Depends(deps_admin.get_admin_user),
) -> Any:
    """
    Create new coupon
    """
    # Check if code already exists
    existing_coupon = coupon.get_by_code(db, code=coupon_in.code)
    if existing_coupon:
        raise HTTPException(status_code=400, detail="Coupon code already exists")
    
    return coupon.create_with_creator(db, obj_in=coupon_in, creator_id=current_admin.id)


@router.put("/coupons/{coupon_id}", response_model=Coupon)
def update_coupon(
    coupon_id: UUID,
    coupon_update: CouponUpdate,
    db: Session = Depends(get_db),
    current_admin: models.User = Depends(deps_admin.get_admin_user),
) -> Any:
    """
    Update coupon
    """
    db_coupon = coupon.get(db, id=coupon_id)
    if not db_coupon:
        raise HTTPException(status_code=404, detail="Coupon not found")
    
    return coupon.update(db, db_obj=db_coupon, obj_in=coupon_update)


@router.delete("/coupons/{coupon_id}")
def delete_coupon(
    coupon_id: UUID,
    db: Session = Depends(get_db),
    current_admin: models.User = Depends(deps_admin.get_admin_user),
) -> Any:
    """
    Delete coupon
    """
    db_coupon = coupon.get(db, id=coupon_id)
    if not db_coupon:
        raise HTTPException(status_code=404, detail="Coupon not found")
    
    coupon.remove(db, id=coupon_id)
    return {"message": "Coupon deleted successfully"}


@router.get("/coupons/{coupon_id}/uses")
def get_coupon_uses(
    coupon_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_admin: models.User = Depends(deps_admin.get_admin_user),
) -> Any:
    """
    Get coupon usage history
    """
    from app.models.coupon import CouponUse
    
    uses = db.query(CouponUse).filter(
        CouponUse.coupon_id == coupon_id
    ).offset(skip).limit(limit).all()
    
    result = []
    for use in uses:
        result.append({
            "id": str(use.id),
            "user_email": use.user.email,
            "user_name": use.user.full_name,
            "discount_applied": use.discount_applied,
            "original_price": use.original_price,
            "final_price": use.final_price,
            "used_at": use.used_at.isoformat()
        })
    
    return result


@router.post("/fix-free-users-status")
def fix_free_users_status(
    *,
    db: Session = Depends(get_db),
    current_admin: models.User = Depends(deps_admin.get_admin_user),
) -> Any:
    """
    Fix status of free plan users to 'active' (Admin only).
    """
    try:
        # Buscar plano gratuito
        from app.models.subscription import SubscriptionPlan
        free_plan = db.query(SubscriptionPlan).filter(SubscriptionPlan.name == "free").first()

        if not free_plan:
            raise HTTPException(status_code=404, detail="Free plan not found")

        # Atualizar usuários do plano gratuito para status ativo
        updated_count = db.query(models.User).filter(
            models.User.current_plan_id == free_plan.id,
            models.User.subscription_status != "active"
        ).update({
            "subscription_status": "active"
        })

        db.commit()

        return {
            "message": f"Updated {updated_count} users to active status",
            "updated_count": updated_count
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=400,
            detail=f"Error fixing user status: {str(e)}"
        )


# Payment/Invoice Management
@router.get("/payments", response_model=List[AdminPaymentList])
def get_payments_list(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_admin: models.User = Depends(deps_admin.get_admin_user),
) -> Any:
    """
    Get paginated list of payments/invoices with user and subscription info
    """
    return admin.get_payments_list(db, skip=skip, limit=limit)


@router.get("/users/{user_id}/history")
def get_user_history(
    user_id: UUID,
    db: Session = Depends(get_db),
    current_admin: models.User = Depends(deps_admin.get_admin_user),
) -> Any:
    """
    Get complete history of plans and payments for a specific user
    """
    return admin.get_user_history(db, user_id=user_id)
