from typing import Any
from fastapi import <PERSON>Router, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app import models
from app.api import deps
from app.services.social_auth_service import SocialAuthService
from app.schemas.social_auth import (
    GoogleLoginRequest,
    AppleLoginRequest,
    FacebookLoginRequest,
    SocialLoginRequest,
    SocialLoginResponse,
    LinkSocialAccountRequest,
    UnlinkSocialAccountRequest,
    SocialAccount
)
from app.models.social_auth import SocialProvider

router = APIRouter()


@router.post("/google", response_model=SocialLoginResponse)
async def google_login(
    *,
    db: Session = Depends(deps.get_db),
    login_data: GoogleLoginRequest
) -> Any:
    """
    Login com Google usando ID Token
    """
    try:
        print(f"Google login request: id_token={bool(login_data.id_token)}, access_token={bool(login_data.access_token)}")

        # Verifica o token do Google (ID token ou access token)
        social_info = await SocialAuthService.verify_google_token(
            id_token_str=login_data.id_token,
            access_token=login_data.access_token
        )

        print(f"Social info: {social_info}")

        # Encontra ou cria o usuário
        user, is_new_user, social_account = SocialAuthService.find_or_create_user(db, social_info)

        print(f"User created/found: {user.email}, new_user={is_new_user}")

        # Cria resposta com token JWT
        print("Creating social login response...")
        try:
            response = SocialAuthService.create_social_login_response(user, is_new_user)
            print(f"Response created: {type(response)}")
            return response
        except Exception as jwt_error:
            print(f"Error creating JWT response: {jwt_error}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error creating authentication response: {str(jwt_error)}"
            )

    except HTTPException:
        raise
    except Exception as e:
        print(f"General error in Google login: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error during Google authentication: {str(e)}"
        )


@router.post("/facebook", response_model=SocialLoginResponse)
async def facebook_login(
    *,
    db: Session = Depends(deps.get_db),
    login_data: FacebookLoginRequest
) -> Any:
    """
    Login com Facebook usando Access Token
    """
    try:
        print(f"Facebook login request: access_token={bool(login_data.access_token)}")

        # Verifica o token do Facebook
        social_info = await SocialAuthService.verify_facebook_token(login_data.access_token)

        print(f"Facebook social info: {social_info}")

        # Encontra ou cria o usuário
        user, is_new_user, social_account = SocialAuthService.find_or_create_user(db, social_info)

        print(f"User created/found: {user.email}, new_user={is_new_user}")

        # Cria resposta com token JWT
        response = SocialAuthService.create_social_login_response(user, is_new_user)

        return response

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error during Facebook authentication: {str(e)}"
        )


@router.post("/apple", response_model=SocialLoginResponse)
async def apple_login(
    *,
    db: Session = Depends(deps.get_db),
    login_data: AppleLoginRequest
) -> Any:
    """
    Login com Apple usando ID Token
    """
    try:
        # Verifica o token da Apple
        social_info = await SocialAuthService.verify_apple_token(
            login_data.id_token,
            login_data.user_info
        )

        # Encontra ou cria o usuário
        user, is_new_user, social_account = SocialAuthService.find_or_create_user(db, social_info)

        # Cria resposta com token JWT
        response = SocialAuthService.create_social_login_response(user, is_new_user)

        return response

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error during Apple authentication: {str(e)}"
        )


@router.post("/login", response_model=SocialLoginResponse)
async def social_login(
    *,
    db: Session = Depends(deps.get_db),
    login_data: SocialLoginRequest
) -> Any:
    """
    Login social genérico (suporta múltiplos provedores)
    """
    try:
        social_info = None
        
        if login_data.provider == SocialProvider.google:
            social_info = await SocialAuthService.verify_google_token(login_data.id_token)
        elif login_data.provider == SocialProvider.facebook:
            social_info = await SocialAuthService.verify_facebook_token(login_data.access_token)
        elif login_data.provider == SocialProvider.apple:
            social_info = await SocialAuthService.verify_apple_token(
                login_data.id_token,
                login_data.user_info
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Provider {login_data.provider} not supported"
            )
        
        # Encontra ou cria o usuário
        user, is_new_user, social_account = SocialAuthService.find_or_create_user(db, social_info)
        
        # Cria resposta com token JWT
        response = SocialAuthService.create_social_login_response(user, is_new_user)
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error during social authentication: {str(e)}"
        )


@router.post("/link")
async def link_social_account(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    link_data: LinkSocialAccountRequest
) -> Any:
    """
    Vincula uma conta social a um usuário já autenticado
    """
    try:
        social_info = None
        
        if link_data.provider == SocialProvider.google:
            social_info = await SocialAuthService.verify_google_token(link_data.id_token)
        elif link_data.provider == SocialProvider.apple:
            social_info = await SocialAuthService.verify_apple_token(link_data.id_token)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Provider {link_data.provider} not supported"
            )
        
        # Vincula a conta social
        social_account = SocialAuthService.link_social_account(db, current_user, social_info)
        
        return {
            "message": f"Successfully linked {link_data.provider} account",
            "provider": link_data.provider,
            "linked_email": social_account.email
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error linking social account: {str(e)}"
        )


@router.post("/unlink")
async def unlink_social_account(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    unlink_data: UnlinkSocialAccountRequest
) -> Any:
    """
    Desvincula uma conta social de um usuário
    """
    try:
        success = SocialAuthService.unlink_social_account(db, current_user, unlink_data.provider)
        
        if success:
            return {
                "message": f"Successfully unlinked {unlink_data.provider} account",
                "provider": unlink_data.provider
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to unlink social account"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error unlinking social account: {str(e)}"
        )


@router.get("/accounts")
async def get_linked_accounts(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user)
) -> Any:
    """
    Lista todas as contas sociais vinculadas ao usuário
    """
    social_accounts = db.query(models.SocialAccount).filter(
        models.SocialAccount.user_id == current_user.id,
        models.SocialAccount.is_active == True
    ).all()
    
    return {
        "linked_accounts": [
            {
                "provider": account.provider,
                "email": account.email,
                "name": account.name,
                "linked_at": account.created_at
            }
            for account in social_accounts
        ]
    }


@router.get("/config")
async def get_social_auth_config() -> Any:
    """
    Retorna configurações públicas para autenticação social
    """
    from app.core.config import settings
    
    return {
        "google": {
            "client_id": settings.GOOGLE_CLIENT_ID,
            "enabled": bool(settings.GOOGLE_CLIENT_ID)
        },
        "facebook": {
            "app_id": settings.FACEBOOK_APP_ID,
            "enabled": bool(settings.FACEBOOK_APP_ID)
        },
        "apple": {
            "client_id": settings.APPLE_CLIENT_ID,
            "enabled": bool(settings.APPLE_CLIENT_ID)
        }
    }
