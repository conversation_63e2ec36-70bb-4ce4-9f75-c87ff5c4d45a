from typing import Any, List
from fastapi import APIRouter, Body, Depends, HTTPException, UploadFile, File, Form
from fastapi.encoders import jsonable_encoder
from pydantic.networks import EmailStr
from sqlalchemy.orm import Session
from sqlalchemy.sql import func
from uuid import UUID
from datetime import datetime

from app import crud, models, schemas
from app.api import deps
from app.db.session import get_db
from app.services.r2_service import r2_service
from app.services.asaas_service import asaas_service
from app.models.subscription import UserSubscription
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/me/debug")
def debug_user_me(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Debug current user data.
    """
    return {
        "id": str(current_user.id),
        "email": current_user.email,
        "username": current_user.username,
        "first_name": current_user.first_name,
        "last_name": current_user.last_name,
        "username_is_none": current_user.username is None,
        "username_length": len(current_user.username) if current_user.username else 0
    }

@router.get("/me", response_model=schemas.User)
def read_user_me(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Get current user.
    """
    return current_user


@router.put("/me", response_model=schemas.User)
async def update_user_me(
    *,
    db: Session = Depends(get_db),
    password: str = Form(None),
    first_name: str = Form(None),
    last_name: str = Form(None),
    bio: str = Form(None),
    city: str = Form(None),
    state: str = Form(None),
    country: str = Form(None),
    cpf_cnpj: str = Form(None),
    document_type: str = Form(None),
    avatar: UploadFile = File(None),
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Update own user.
    """
    current_user_data = jsonable_encoder(current_user)
    user_in = schemas.UserUpdate(**current_user_data)

    if password is not None:
        user_in.password = password
    if first_name is not None:
        user_in.first_name = first_name
    if last_name is not None:
        user_in.last_name = last_name
    if bio is not None:
        user_in.bio = bio
    if city is not None:
        user_in.city = city
    if state is not None:
        user_in.state = state
    if country is not None:
        user_in.country = country
    if cpf_cnpj is not None:
        user_in.cpf_cnpj = cpf_cnpj
    if document_type is not None:
        user_in.document_type = document_type

    # Handle avatar upload
    if avatar:
        # Validate file type
        if not avatar.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")

        # Delete old avatar if exists
        if current_user.avatar:
            old_filename = current_user.avatar.split('/')[-1]
            r2_service.delete_avatar(str(current_user.id), old_filename)

        # Read file content
        content = await avatar.read()

        # Upload new avatar to R2 (low resolution for profile)
        upload_result = r2_service.upload_avatar(
            image_data=content,
            filename=avatar.filename or "avatar.jpg",
            user_id=str(current_user.id)
        )

        if not upload_result['success']:
            raise HTTPException(status_code=500, detail=f"Failed to upload avatar: {upload_result.get('error', 'Unknown error')}")

        # Use the small size for avatar
        user_in.avatar = upload_result['urls']['small']

    user = crud.user.update(db, db_obj=current_user, obj_in=user_in)
    return user


@router.put("/me/profile", response_model=schemas.User)
def update_user_profile(
    *,
    db: Session = Depends(get_db),
    user_update: schemas.UserUpdate,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Update user profile with JSON data.
    """
    user = crud.user.update(db, db_obj=current_user, obj_in=user_update)
    return user


@router.get("/{user_id}", response_model=schemas.User)
def read_user_by_id(
    user_id: UUID,
    current_user: models.User = Depends(deps.get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Get a specific user by id.
    """
    user = crud.user.get(db, id=user_id)
    if user == current_user:
        return user
    if not user:
        raise HTTPException(
            status_code=404,
            detail="The user with this id does not exist in the system",
        )
    return user


@router.delete("/me")
def delete_user_account(
    *,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Delete current user account and all associated data.
    This action is irreversible and will:
    - Cancel all active subscriptions in ASAAS
    - Delete all user's plants and their images from R2
    - Delete all care records and images
    - Delete all reminders
    - Delete all subscriptions and payments
    - Delete all social accounts
    - Delete user profile and avatar from R2
    """
    try:
        logger.info(f"Starting account deletion for user {current_user.email} (ID: {current_user.id})")

        # 1. Cancel all active subscriptions in ASAAS first
        active_subscriptions = db.query(UserSubscription).filter(
            UserSubscription.user_id == current_user.id,
            UserSubscription.status.in_(["active", "pending"])
        ).all()

        for subscription in active_subscriptions:
            if subscription.asaas_subscription_id:
                try:
                    logger.info(f"Cancelling ASAAS subscription {subscription.asaas_subscription_id}")
                    result = asaas_service.cancel_subscription(subscription.asaas_subscription_id)
                    if result["success"]:
                        logger.info(f"Successfully cancelled ASAAS subscription {subscription.asaas_subscription_id}")
                    else:
                        logger.error(f"Failed to cancel ASAAS subscription {subscription.asaas_subscription_id}: {result['error']}")
                except Exception as e:
                    logger.error(f"Error cancelling ASAAS subscription {subscription.asaas_subscription_id}: {e}")
                    # Continue with deletion even if ASAAS cancellation fails

            # Mark subscription as cancelled in our database
            subscription.status = "cancelled"
            subscription.cancelled_at = func.now()

        # Commit subscription cancellations
        db.commit()
        logger.info(f"Cancelled {len(active_subscriptions)} subscriptions for user {current_user.email}")

        # 2. Get all user's plants to delete their R2 images
        user_plants = crud.plant.get_by_owner(db, owner_id=current_user.id)

        # Delete all plant images from R2
        for plant in user_plants:
            try:
                r2_service.delete_plant_images(str(plant.id))
                print(f"Deleted R2 images for plant {plant.id}")
            except Exception as e:
                print(f"Error deleting R2 images for plant {plant.id}: {e}")
                # Continue with deletion even if R2 cleanup fails

        # Delete user avatar from R2
        if current_user.avatar:
            try:
                old_filename = current_user.avatar.split('/')[-1]
                r2_service.delete_avatar(str(current_user.id), old_filename)
                print(f"Deleted avatar for user {current_user.id}")
            except Exception as e:
                print(f"Error deleting avatar for user {current_user.id}: {e}")

        # 3. Delete user from database (manually handle foreign key constraints)
        # Delete coupon uses first to avoid foreign key constraint
        db.query(models.CouponUse).filter(models.CouponUse.user_id == current_user.id).delete()

        # Delete notifications where user is the follower (follower_id references)
        db.query(models.Notification).filter(models.Notification.follower_id == current_user.id).delete()

        # Delete notifications where user is the recipient (user_id references)
        db.query(models.Notification).filter(models.Notification.user_id == current_user.id).delete()

        # Delete social follows where user is the follower or followed
        db.query(models.UserFollow).filter(
            (models.UserFollow.follower_id == current_user.id) |
            (models.UserFollow.followed_id == current_user.id)
        ).delete()

        # Delete plant likes by this user
        db.query(models.PlantLike).filter(models.PlantLike.user_id == current_user.id).delete()

        # Delete plant comments by this user
        db.query(models.PlantComment).filter(models.PlantComment.user_id == current_user.id).delete()

        db.commit()

        # Now delete user (cascade will handle other related records)
        crud.user.remove(db=db, id=current_user.id)

        logger.info(f"Successfully deleted account for user {current_user.email}")
        return {
            "message": "Account deleted successfully",
            "cancelled_subscriptions": len(active_subscriptions),
            "deleted_plants": len(user_plants)
        }

    except Exception as e:
        print(f"Error deleting user account {current_user.id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="Error deleting account. Please try again or contact support."
        )


@router.get("/me/data-export")
def export_user_data(
    *,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """
    Export all user data for LGPD compliance (data portability).
    Returns a comprehensive JSON with all user's personal data.
    """
    try:
        logger.info(f"Starting data export for user {current_user.email} (ID: {current_user.id})")

        # 1. User basic data
        user_data = {
            "user_info": {
                "id": str(current_user.id),
                "email": current_user.email,
                "first_name": current_user.first_name,
                "last_name": current_user.last_name,
                "bio": current_user.bio,
                "city": current_user.city,
                "state": current_user.state,
                "country": current_user.country,
                "username": current_user.username,
                "is_public": current_user.is_public,
                "allow_care_sharing": current_user.allow_care_sharing,
                "allow_search": current_user.allow_search,
                "followers_count": current_user.followers_count,
                "following_count": current_user.following_count,
                "created_at": current_user.created_at.isoformat() if current_user.created_at else None,
                "updated_at": current_user.updated_at.isoformat() if current_user.updated_at else None
            }
        }

        # 2. Plants data
        user_plants = crud.plant.get_by_owner(db, owner_id=current_user.id)
        plants_data = []

        for plant in user_plants:
            plant_dict = {
                "id": str(plant.id),
                "name": plant.name,
                "scientific_name": plant.scientific_name,
                "plant_type": plant.plant_type.value if plant.plant_type else None,
                "plant_category": plant.plant_category.value if plant.plant_category else None,
                "description": plant.description,
                "style": plant.style,
                "estimated_age": plant.estimated_age,
                "acquisition_date": plant.acquisition_date.isoformat() if plant.acquisition_date else None,
                "location": plant.location,
                "archived": plant.archived,
                "favorite": plant.favorite,
                "is_public": plant.is_public,
                "allow_care_sharing": plant.allow_care_sharing,
                "created_at": plant.created_at.isoformat() if plant.created_at else None,
                "updated_at": plant.updated_at.isoformat() if plant.updated_at else None
            }

            # Plant images
            plant_images = crud.plant_image.get_by_plant(db, plant_id=plant.id)
            plant_dict["images"] = [
                {
                    "id": str(img.id),
                    "image_path": img.image,
                    "caption": img.caption,
                    "is_primary": img.is_primary,
                    "photo_date": img.photo_date.isoformat() if img.photo_date else None,
                    "created_at": img.created_at.isoformat() if img.created_at else None
                }
                for img in plant_images
            ]

            # Care records
            care_records = db.query(models.Care).filter(models.Care.plant_id == plant.id).all()
            plant_dict["care_records"] = []

            for care in care_records:
                care_dict = {
                    "id": str(care.id),
                    "care_type": care.care_type.value if care.care_type else None,
                    "care_types": [ct.value for ct in care.care_types] if care.care_types else [],
                    "description": care.description,
                    "notes": care.notes,
                    "date": care.date.isoformat() if care.date else None,
                    "created_at": care.created_at.isoformat() if care.created_at else None
                }

                # Care images
                care_images = db.query(models.CareImage).filter(models.CareImage.care_id == care.id).all()
                care_dict["images"] = [
                    {
                        "id": str(img.id),
                        "image_path": img.image_path,
                        "caption": img.caption,
                        "is_primary": img.is_primary,
                        "created_at": img.created_at.isoformat() if img.created_at else None
                    }
                    for img in care_images
                ]

                plant_dict["care_records"].append(care_dict)

            # Reminders
            reminders = crud.reminder.get_by_plant(db, plant_id=plant.id, include_completed=True)
            plant_dict["reminders"] = [
                {
                    "id": str(reminder.id),
                    "care_type": reminder.care_type.value if reminder.care_type else None,
                    "scheduled_date": reminder.scheduled_date.isoformat() if reminder.scheduled_date else None,
                    "description": reminder.description,
                    "is_completed": reminder.is_completed,
                    "completed_at": reminder.completed_at.isoformat() if reminder.completed_at else None,
                    "created_at": reminder.created_at.isoformat() if reminder.created_at else None
                }
                for reminder in reminders
            ]

            plants_data.append(plant_dict)

        user_data["plants"] = plants_data

        # 3. Subscription data
        subscriptions = db.query(models.UserSubscription).filter(
            models.UserSubscription.user_id == current_user.id
        ).all()

        user_data["subscriptions"] = [
            {
                "id": str(sub.id),
                "plan_name": sub.plan.name if sub.plan else None,
                "plan_display_name": sub.plan.display_name if sub.plan else None,
                "status": sub.status.value if sub.status else None,
                "billing_cycle": sub.billing_cycle.value if sub.billing_cycle else None,
                "started_at": sub.started_at.isoformat() if sub.started_at else None,
                "expires_at": sub.expires_at.isoformat() if sub.expires_at else None,
                "cancelled_at": sub.cancelled_at.isoformat() if sub.cancelled_at else None,
                "auto_renew": sub.auto_renew,
                "is_active": sub.is_active,
                "days_until_expiry": sub.days_until_expiry,
                "created_at": sub.created_at.isoformat() if sub.created_at else None,
                "updated_at": sub.updated_at.isoformat() if sub.updated_at else None
            }
            for sub in subscriptions
        ]

        # 4. Payment data (without sensitive info)
        payments = db.query(models.Payment).filter(
            models.Payment.user_id == current_user.id
        ).all()

        user_data["payments"] = [
            {
                "id": str(payment.id),
                "amount_cents": payment.amount_cents,
                "amount_display": payment.amount_display,
                "currency": payment.currency,
                "status": payment.status.value if payment.status else None,
                "payment_method": payment.payment_method.value if payment.payment_method else None,
                "subscription_id": str(payment.subscription_id) if payment.subscription_id else None,
                "external_payment_id": payment.external_payment_id,
                "processed_at": payment.processed_at.isoformat() if payment.processed_at else None,
                "created_at": payment.created_at.isoformat() if payment.created_at else None,
                "updated_at": payment.updated_at.isoformat() if payment.updated_at else None
            }
            for payment in payments
        ]

        # 5. Social data
        following = db.query(models.UserFollow).filter(
            models.UserFollow.follower_id == current_user.id
        ).all()

        followers = db.query(models.UserFollow).filter(
            models.UserFollow.followed_id == current_user.id
        ).all()

        user_data["social"] = {
            "following": [
                {
                    "user_id": str(follow.followed_id),
                    "followed_at": follow.created_at.isoformat() if follow.created_at else None
                }
                for follow in following
            ],
            "followers": [
                {
                    "user_id": str(follow.follower_id),
                    "followed_at": follow.created_at.isoformat() if follow.created_at else None
                }
                for follow in followers
            ]
        }

        # 6. Notifications
        notifications = db.query(models.Notification).filter(
            models.Notification.user_id == current_user.id
        ).all()

        user_data["notifications"] = [
            {
                "id": str(notif.id),
                "type": notif.type.value if notif.type else None,
                "title": notif.title,
                "message": notif.message,
                "is_read": notif.is_read,
                "created_at": notif.created_at.isoformat() if notif.created_at else None
            }
            for notif in notifications
        ]

        # 7. Export metadata
        user_data["export_info"] = {
            "exported_at": datetime.utcnow().isoformat(),
            "export_version": "1.0",
            "total_plants": len(plants_data),
            "total_subscriptions": len(subscriptions),
            "total_payments": len(payments),
            "lgpd_compliance": True
        }

        logger.info(f"Data export completed for user {current_user.email}")

        return user_data

    except Exception as e:
        logger.error(f"Error exporting data for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="Error exporting data. Please try again or contact support."
        )
