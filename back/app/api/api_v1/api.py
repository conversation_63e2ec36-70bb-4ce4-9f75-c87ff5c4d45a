from fastapi import APIRouter
from app.api.api_v1.endpoints import auth, users, plants, subscriptions, reminders, social_auth, payments, admin, seo, social, notifications, social_interactions, config, contact

api_router = APIRouter()
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(social_auth.router, prefix="/auth/social", tags=["social-auth"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(plants.router, prefix="/plants", tags=["plants"])
api_router.include_router(reminders.router, prefix="/reminders", tags=["reminders"])
api_router.include_router(subscriptions.router, prefix="/subscriptions", tags=["subscriptions"])
api_router.include_router(payments.router, prefix="/payments", tags=["payments"])
api_router.include_router(admin.router, prefix="/admin", tags=["admin"])
api_router.include_router(social.router, prefix="/social", tags=["social"])
api_router.include_router(social_interactions.router, prefix="/social", tags=["social-interactions"])
api_router.include_router(notifications.router, prefix="/notifications", tags=["notifications"])
api_router.include_router(config.router, prefix="/config", tags=["config"])
api_router.include_router(contact.router, prefix="/contact", tags=["contact"])
api_router.include_router(seo.router, prefix="", tags=["seo"])
