#!/usr/bin/env python3
"""
Script to generate thumbnails for existing videos that don't have thumbnails yet.
"""
import sys
import os
import requests
import tempfile
from pathlib import Path

# Add the parent directory to the path so we can import from app
sys.path.append(str(Path(__file__).parent.parent))

from app.db.session import SessionLocal
from app.models.plant import PlantVideo, CareVideo
from app.services.r2_service import r2_service
from app.utils.video_utils import create_thumbnail_from_upload
from sqlalchemy import text

def download_video_from_r2(video_url: str) -> bytes:
    """Download video content from R2 URL"""
    try:
        response = requests.get(video_url, timeout=30)
        response.raise_for_status()
        return response.content
    except Exception as e:
        print(f"Error downloading video from {video_url}: {e}")
        return None

def generate_thumbnails_for_plant_videos():
    """Generate thumbnails for plant videos that don't have them"""
    db = SessionLocal()
    try:
        # Get plant videos without thumbnails
        videos = db.query(PlantVideo).filter(
            PlantVideo.thumbnail_url.is_(None)
        ).all()
        
        print(f"Found {len(videos)} plant videos without thumbnails")
        
        for video in videos:
            try:
                print(f"Processing plant video {video.id}...")
                
                # Get video URL
                video_url = r2_service.get_video_url(
                    plant_id=str(video.plant_id),
                    filename=video.video
                )
                
                if not video_url:
                    print(f"Could not get URL for video {video.id}")
                    continue
                
                # Download video content
                video_content = download_video_from_r2(video_url)
                if not video_content:
                    print(f"Could not download video {video.id}")
                    continue
                
                # Generate thumbnail
                thumbnail_content, thumbnail_filename = create_thumbnail_from_upload(
                    video_content=video_content,
                    original_filename=video.original_filename or "video.mp4"
                )
                
                if not thumbnail_content:
                    print(f"Could not generate thumbnail for video {video.id}")
                    continue
                
                # Upload thumbnail to R2
                thumbnail_result = r2_service.upload_thumbnail(
                    thumbnail_data=thumbnail_content,
                    filename=video.video,  # Use video filename as base
                    plant_id=str(video.plant_id)
                )
                
                if thumbnail_result['success']:
                    # Update database
                    video.thumbnail_url = thumbnail_result['url']
                    db.commit()
                    print(f"✅ Generated thumbnail for plant video {video.id}")
                else:
                    print(f"❌ Failed to upload thumbnail for video {video.id}: {thumbnail_result.get('error')}")
                    
            except Exception as e:
                print(f"❌ Error processing plant video {video.id}: {e}")
                db.rollback()
                continue
                
    finally:
        db.close()

def generate_thumbnails_for_care_videos():
    """Generate thumbnails for care videos that don't have them"""
    db = SessionLocal()
    try:
        # Get care videos without thumbnails
        videos = db.query(CareVideo).filter(
            CareVideo.thumbnail_url.is_(None)
        ).all()
        
        print(f"Found {len(videos)} care videos without thumbnails")
        
        for video in videos:
            try:
                print(f"Processing care video {video.id}...")
                
                # Get care and plant info
                care = db.execute(text("""
                    SELECT c.plant_id 
                    FROM cares c 
                    WHERE c.id = :care_id
                """), {'care_id': str(video.care_id)}).fetchone()
                
                if not care:
                    print(f"Could not find care for video {video.id}")
                    continue
                
                # Get video URL
                video_url = r2_service.get_video_url(
                    plant_id=str(care.plant_id),
                    filename=video.video_path,
                    subfolder="cares"
                )
                
                if not video_url:
                    print(f"Could not get URL for care video {video.id}")
                    continue
                
                # Download video content
                video_content = download_video_from_r2(video_url)
                if not video_content:
                    print(f"Could not download care video {video.id}")
                    continue
                
                # Generate thumbnail
                thumbnail_content, thumbnail_filename = create_thumbnail_from_upload(
                    video_content=video_content,
                    original_filename=video.original_filename or "care_video.mp4"
                )
                
                if not thumbnail_content:
                    print(f"Could not generate thumbnail for care video {video.id}")
                    continue
                
                # Upload thumbnail to R2
                thumbnail_result = r2_service.upload_thumbnail(
                    thumbnail_data=thumbnail_content,
                    filename=video.video_path,  # Use video filename as base
                    plant_id=str(care.plant_id),
                    subfolder="cares"
                )
                
                if thumbnail_result['success']:
                    # Update database
                    video.thumbnail_url = thumbnail_result['url']
                    db.commit()
                    print(f"✅ Generated thumbnail for care video {video.id}")
                else:
                    print(f"❌ Failed to upload thumbnail for care video {video.id}: {thumbnail_result.get('error')}")
                    
            except Exception as e:
                print(f"❌ Error processing care video {video.id}: {e}")
                db.rollback()
                continue
                
    finally:
        db.close()

def main():
    """Main function"""
    print("🎥 Starting thumbnail generation for existing videos...")
    
    # Generate thumbnails for plant videos
    print("\n📱 Processing plant videos...")
    generate_thumbnails_for_plant_videos()
    
    # Generate thumbnails for care videos
    print("\n🌱 Processing care videos...")
    generate_thumbnails_for_care_videos()
    
    print("\n✅ Thumbnail generation completed!")

if __name__ == "__main__":
    main()
