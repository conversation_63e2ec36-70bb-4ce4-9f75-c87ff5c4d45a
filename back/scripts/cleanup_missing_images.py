#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to clean up missing images from the database.
This script will:
1. Check all plant_images records
2. Verify if the physical files exist (R2 or local)
3. Remove database records for missing files
4. Update plants.primary_image_r2 if needed
"""

import sys
import os
import requests
from pathlib import Path

# Add the app directory to Python path
sys.path.append('/app')

from app.db.session import SessionLocal
from app.models.plant import Plant, PlantImage
from app.core.config import settings

def check_r2_image_exists(plant_id: str, filename: str) -> bool:
    """Check if image exists in R2 storage"""
    r2_url = f"https://pub-4192feb134c1436bb306ee0bcbec4d2f.r2.dev/plants/{plant_id}/medium_{filename}"
    try:
        response = requests.head(r2_url, timeout=10)
        return response.status_code == 200
    except:
        return False

def check_local_image_exists(filename: str) -> bool:
    """Check if image exists in local storage"""
    local_path = Path(f"/app/media/plants/{filename}")
    return local_path.exists()

def cleanup_missing_images():
    """Main cleanup function"""
    db = SessionLocal()
    try:
        print("🔍 Starting cleanup of missing images...")
        
        # Get all plant images
        plant_images = db.query(PlantImage).all()
        print(f"📊 Found {len(plant_images)} plant image records")
        
        missing_images = []
        
        for plant_image in plant_images:
            print(f"🔍 Checking image: {plant_image.image} (Plant: {plant_image.plant_id})")
            
            # Check if image exists in R2 or locally
            r2_exists = check_r2_image_exists(str(plant_image.plant_id), plant_image.image)
            local_exists = check_local_image_exists(plant_image.image)
            
            if not r2_exists and not local_exists:
                print(f"❌ Missing image: {plant_image.image}")
                missing_images.append(plant_image)
            else:
                print(f"✅ Image exists: {plant_image.image}")
        
        print(f"\n📊 Summary:")
        print(f"   Total images checked: {len(plant_images)}")
        print(f"   Missing images: {len(missing_images)}")
        
        if missing_images:
            print(f"\n🗑️ Removing {len(missing_images)} missing image records...")
            
            for missing_image in missing_images:
                print(f"   Removing: {missing_image.image} (ID: {missing_image.id})")
                
                # If this was a primary image, clear the plant's primary_image_r2
                if missing_image.is_primary:
                    plant = db.query(Plant).filter(Plant.id == missing_image.plant_id).first()
                    if plant and plant.primary_image_r2 == missing_image.image:
                        plant.primary_image_r2 = None
                        print(f"   Cleared primary_image_r2 for plant {plant.id}")
                
                # Remove the image record
                db.delete(missing_image)
            
            db.commit()
            print("✅ Cleanup completed successfully!")
        else:
            print("✅ No missing images found!")
            
    except Exception as e:
        print(f"❌ Error during cleanup: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    cleanup_missing_images()
