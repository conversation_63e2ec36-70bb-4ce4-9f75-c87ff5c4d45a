#!/usr/bin/env python3
"""
Script to send reminder notifications
This script should be run daily via cron job
"""

import sys
import os

# Add the parent directory to the path so we can import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.tasks.reminder_tasks import send_reminder_notifications
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

if __name__ == "__main__":
    print("🔔 Starting reminder notifications...")
    send_reminder_notifications()
    print("✅ Reminder notifications completed!")
