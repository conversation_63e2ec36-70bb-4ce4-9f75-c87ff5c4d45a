#!/usr/bin/env python3
"""
Script para testar diretamente o PlantLimitService
"""
import sys
import os

# Adicionar o diretório atual ao path
sys.path.insert(0, '/app')
sys.path.insert(0, '/app/app')

from app.db.session import get_db
from app.services.plant_limit_service import PlantLimitService
from app.services.subscription_service import SubscriptionService
from sqlalchemy.orm import Session

def test_plant_access():
    """Testa o acesso às plantas do usuário <EMAIL>"""
    
    # Obter sessão do banco
    db_gen = get_db()
    db: Session = next(db_gen)
    
    try:
        # Email do usuário de teste
        user_email = "<EMAIL>"
        
        # Buscar o usuário
        from app import models
        user = db.query(models.User).filter(models.User.email == user_email).first()
        if not user:
            print(f"❌ Usuário {user_email} não encontrado")
            return
            
        print(f"✅ Usuário encontrado: {user.email} (ID: {user.id})")
        
        # Obter informações da assinatura
        subscription_service = SubscriptionService()
        user_plan = subscription_service.get_user_plan_info(db, user.id)
        print(f"📋 Plano do usuário: {user_plan}")
        
        # Contar plantas do usuário
        total_plants = db.query(models.Plant).filter(models.Plant.owner_id == user.id).count()
        print(f"🌱 Total de plantas: {total_plants}")
        
        # Testar o serviço de limite de plantas
        access_info = PlantLimitService.get_user_plant_access_info(db, user.id, user_plan)
        print(f"\n📊 Informações de acesso:")
        print(f"   - Total de plantas: {access_info['total_plants']}")
        print(f"   - Limite do plano: {access_info['plan_limit']}")
        print(f"   - Plantas editáveis: {len(access_info['editable_plant_ids'])}")
        print(f"   - Plantas read-only: {len(access_info['readonly_plant_ids'])}")
        print(f"   - Pode criar nova: {access_info['can_create_new']}")
        
        # Listar plantas editáveis
        if access_info['editable_plant_ids']:
            print(f"\n✅ Plantas editáveis ({len(access_info['editable_plant_ids'])}):")
            for plant_id in access_info['editable_plant_ids']:
                plant = db.query(models.Plant).filter(models.Plant.id == plant_id).first()
                if plant:
                    print(f"   - {plant.name} (ID: {plant_id})")
        
        # Listar plantas read-only
        if access_info['readonly_plant_ids']:
            print(f"\n🔒 Plantas read-only ({len(access_info['readonly_plant_ids'])}):")
            for plant_id in access_info['readonly_plant_ids']:
                plant = db.query(models.Plant).filter(models.Plant.id == plant_id).first()
                if plant:
                    print(f"   - {plant.name} (ID: {plant_id})")
        
        # Testar edição de uma planta específica (Loropetalum - deve ser read-only)
        loropetalum_id = "7097b69b-5f81-4508-a306-b8beb9e839d3"
        can_edit, reason = PlantLimitService.can_edit_plant(db, user.id, loropetalum_id, user_plan)
        print(f"\n🧪 Teste de edição da Loropetalum:")
        print(f"   - Pode editar: {can_edit}")
        if reason:
            print(f"   - Motivo: {reason}")
            
        # Testar edição de uma planta recente (deve ser editável)
        recent_plant_id = "2e8708be-eaa3-4066-8c8e-3a428c7b1d9e"  # Planta "29"
        can_edit, reason = PlantLimitService.can_edit_plant(db, user.id, recent_plant_id, user_plan)
        print(f"\n🧪 Teste de edição da planta '29':")
        print(f"   - Pode editar: {can_edit}")
        if reason:
            print(f"   - Motivo: {reason}")
            
    except Exception as e:
        print(f"❌ Erro durante o teste: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    test_plant_access()
