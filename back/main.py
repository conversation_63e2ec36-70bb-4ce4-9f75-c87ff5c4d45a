from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import os

from app.core.config import settings
from app.api.api_v1.api import api_router
from app.db.init_db import init_db

app = FastAPI(
    title="MeuBonsai.App API",
    description="API para gerenciamento de plantas e bonsais",
    version="1.0.0",
    openapi_url=f"{settings.API_V1_STR}/openapi.json"
)

# Set up CORS
cors_origins = settings.BACKEND_CORS_ORIGINS
print(f"🌐 [CORS] Environment: {settings.ENVIRONMENT}")
print(f"🌐 [CORS] Allowed origins: {cors_origins}")
print(f"🌐 [CORS] Frontend URL: {settings.FRONTEND_URL}")

# Ensure all necessary origins are included for both dev and prod
base_cors_origins = [
    # Production origins
    "https://meubonsai.app",
    "https://www.meubonsai.app",
    "https://api.meubonsai.app",
    # Development origins
    "https://dev.meubonsai.app",
    "https://api-dev.meubonsai.app",
    # OAuth and external services
    "https://accounts.google.com",
    "https://www.googleapis.com",
    # Local development
    "http://localhost:3000",
    "http://127.0.0.1:3000",
]

# Combine with settings origins and remove duplicates
cors_origins_final = list(set(cors_origins + base_cors_origins))

print(f"🌐 [CORS] Final origins: {cors_origins_final}")

app.add_middleware(
    CORSMiddleware,
    allow_origins=cors_origins_final,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=[
        "Accept",
        "Accept-Language",
        "Content-Language",
        "Content-Type",
        "Authorization",
        "X-Requested-With",
        "X-CSRFToken",
        "Cache-Control",
        "Pragma",
        "Expires",
    ],
    expose_headers=["*"],
    max_age=600,
)

# Create media directory if it doesn't exist
os.makedirs("media", exist_ok=True)
os.makedirs("media/avatars", exist_ok=True)
os.makedirs("media/plants", exist_ok=True)
os.makedirs("media/cares", exist_ok=True)

# Mount static files
app.mount("/media", StaticFiles(directory="media"), name="media")

# Include API router
app.include_router(api_router, prefix=settings.API_V1_STR)

@app.on_event("startup")
async def startup_event():
    init_db()

@app.get("/")
async def root():
    return {"message": "MeuBonsai.App API", "version": "1.0.0", "docs": "/docs"}

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "environment": settings.ENVIRONMENT,
        "cors_origins": settings.BACKEND_CORS_ORIGINS,
        "frontend_url": settings.FRONTEND_URL
    }
