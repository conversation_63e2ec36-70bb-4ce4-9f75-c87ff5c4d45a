-- Fix feed activity trigger to handle cares table correctly
-- The trigger was trying to access NEW.is_public on cares table, but that field doesn't exist there

CREATE OR REPLACE FUNCTION create_feed_activity()
RETURNS TRIGGER AS $$
BEGIN
    -- Only handle plants table for plant insertions
    IF TG_TABLE_NAME = 'plants' AND TG_OP = 'INSERT' THEN
        IF NEW.is_public = true THEN
            INSERT INTO feed_activities (user_id, activity_type, plant_id)
            VALUES (NEW.owner_id, 'new_plant', NEW.id);
        END IF;
        RETURN NEW;
    END IF;

    -- Only handle cares table for care insertions
    IF TG_TABLE_NAME = 'cares' AND TG_OP = 'INSERT' THEN
        -- Check if plant is public and allows care sharing
        IF EXISTS (
            SELECT 1 FROM plants
            WHERE id = NEW.plant_id
            AND is_public = true
            AND allow_care_sharing = true
        ) THEN
            INSERT INTO feed_activities (user_id, activity_type, plant_id, care_id)
            SELECT owner_id, 'plant_care', NEW.plant_id, NEW.id
            FROM plants WHERE id = NEW.plant_id;
        END IF;
        RETURN NEW;
    END IF;

    -- Default return for other cases
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Recreate triggers to ensure they use the updated function
DROP TRIGGER IF EXISTS trigger_create_care_feed_activity ON cares;
CREATE TRIGGER trigger_create_care_feed_activity
    AFTER INSERT ON cares
    FOR EACH ROW
    EXECUTE FUNCTION create_feed_activity();

DROP TRIGGER IF EXISTS trigger_create_plant_feed_activity ON plants;
CREATE TRIGGER trigger_create_plant_feed_activity
    AFTER INSERT ON plants
    FOR EACH ROW
    EXECUTE FUNCTION create_feed_activity();
