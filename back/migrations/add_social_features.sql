-- Add social features to <PERSON><PERSON><PERSON>ons<PERSON>.App
-- Migration: Add social features

-- Add social fields to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS username VARCHAR(50) UNIQUE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_public BOOLEAN NOT NULL DEFAULT true;
ALTER TABLE users ADD COLUMN IF NOT EXISTS allow_care_sharing BOOLEAN NOT NULL DEFAULT true;
ALTER TABLE users ADD COLUMN IF NOT EXISTS allow_search BOOLEAN NOT NULL DEFAULT true;
ALTER TABLE users ADD COLUMN IF NOT EXISTS followers_count INTEGER NOT NULL DEFAULT 0;
ALTER TABLE users ADD COLUMN IF NOT EXISTS following_count INTEGER NOT NULL DEFAULT 0;

-- Add privacy fields to plants table
ALTER TABLE plants ADD COLUMN IF NOT EXISTS is_public BOOLEAN NOT NULL DEFAULT true;
ALTER TABLE plants ADD COLUMN IF NOT EXISTS allow_care_sharing BOOLEAN NOT NULL DEFAULT true;

-- Create user_follows table
CREATE TABLE IF NOT EXISTS user_follows (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    follower_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    followed_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE(follower_id, followed_id)
);

-- Create user_blocks table (for future use)
CREATE TABLE IF NOT EXISTS user_blocks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    blocker_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    blocked_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE(blocker_id, blocked_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_is_public ON users(is_public);
CREATE INDEX IF NOT EXISTS idx_users_allow_search ON users(allow_search);
CREATE INDEX IF NOT EXISTS idx_plants_is_public ON plants(is_public);
CREATE INDEX IF NOT EXISTS idx_plants_user_public ON plants(owner_id, is_public);
CREATE INDEX IF NOT EXISTS idx_follows_follower ON user_follows(follower_id);
CREATE INDEX IF NOT EXISTS idx_follows_followed ON user_follows(followed_id);
CREATE INDEX IF NOT EXISTS idx_follows_created ON user_follows(created_at);
CREATE INDEX IF NOT EXISTS idx_blocks_blocker ON user_blocks(blocker_id);
CREATE INDEX IF NOT EXISTS idx_blocks_blocked ON user_blocks(blocked_id);

-- Create trigger to update updated_at on user_follows
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER IF NOT EXISTS update_user_follows_updated_at 
    BEFORE UPDATE ON user_follows 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to update follow counters
CREATE OR REPLACE FUNCTION update_follow_counters()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Increment counters
        UPDATE users SET following_count = following_count + 1 WHERE id = NEW.follower_id;
        UPDATE users SET followers_count = followers_count + 1 WHERE id = NEW.followed_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- Decrement counters
        UPDATE users SET following_count = following_count - 1 WHERE id = OLD.follower_id;
        UPDATE users SET followers_count = followers_count - 1 WHERE id = OLD.followed_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ language 'plpgsql';

-- Create triggers for follow counters
DROP TRIGGER IF EXISTS trigger_update_follow_counters_insert ON user_follows;
CREATE TRIGGER trigger_update_follow_counters_insert
    AFTER INSERT ON user_follows
    FOR EACH ROW EXECUTE FUNCTION update_follow_counters();

DROP TRIGGER IF EXISTS trigger_update_follow_counters_delete ON user_follows;
CREATE TRIGGER trigger_update_follow_counters_delete
    AFTER DELETE ON user_follows
    FOR EACH ROW EXECUTE FUNCTION update_follow_counters();
