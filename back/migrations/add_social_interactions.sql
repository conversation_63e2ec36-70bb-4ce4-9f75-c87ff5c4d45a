-- Migration: Add social interactions (likes, comments, feed)
-- Date: 2024-12-18

-- Create enum for feed activity types
CREATE TYPE feedactivitytype AS ENUM ('new_plant', 'plant_care');

-- Create plant_likes table
CREATE TABLE plant_likes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    plant_id UUID NOT NULL REFERENCES plants(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    CONSTRAINT unique_plant_like UNIQUE (user_id, plant_id)
);

-- Create indexes for plant_likes
CREATE INDEX idx_plant_likes_user_id ON plant_likes(user_id);
CREATE INDEX idx_plant_likes_plant_id ON plant_likes(plant_id);
CREATE INDEX idx_plant_likes_created_at ON plant_likes(created_at);

-- Create plant_comments table
CREATE TABLE plant_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    plant_id UUID NOT NULL REFERENCES plants(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create indexes for plant_comments
CREATE INDEX idx_plant_comments_user_id ON plant_comments(user_id);
CREATE INDEX idx_plant_comments_plant_id ON plant_comments(plant_id);
CREATE INDEX idx_plant_comments_created_at ON plant_comments(created_at);

-- Create feed_activities table
CREATE TABLE feed_activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    activity_type feedactivitytype NOT NULL,
    plant_id UUID REFERENCES plants(id) ON DELETE CASCADE,
    care_id UUID REFERENCES cares(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create indexes for feed_activities
CREATE INDEX idx_feed_activities_user_id ON feed_activities(user_id);
CREATE INDEX idx_feed_activities_created_at ON feed_activities(created_at);
CREATE INDEX idx_feed_activities_activity_type ON feed_activities(activity_type);
CREATE INDEX idx_feed_activities_plant_id ON feed_activities(plant_id);

-- Add new notification types to existing enum
ALTER TYPE notificationtype ADD VALUE 'plant_like';
ALTER TYPE notificationtype ADD VALUE 'plant_comment';

-- Add new columns to notifications table for social interactions
ALTER TABLE notifications ADD COLUMN plant_id UUID REFERENCES plants(id);
ALTER TABLE notifications ADD COLUMN like_id UUID REFERENCES plant_likes(id);
ALTER TABLE notifications ADD COLUMN comment_id UUID REFERENCES plant_comments(id);

-- Create indexes for new notification columns
CREATE INDEX idx_notifications_plant_id ON notifications(plant_id);
CREATE INDEX idx_notifications_like_id ON notifications(like_id);
CREATE INDEX idx_notifications_comment_id ON notifications(comment_id);

-- Create function to automatically create feed activities
CREATE OR REPLACE FUNCTION create_feed_activity()
RETURNS TRIGGER AS $$
BEGIN
    -- Create feed activity for new plants (only if public)
    IF TG_TABLE_NAME = 'plants' AND TG_OP = 'INSERT' AND NEW.is_public = true THEN
        INSERT INTO feed_activities (user_id, activity_type, plant_id)
        VALUES (NEW.owner_id, 'new_plant', NEW.id);
    END IF;
    
    -- Create feed activity for new care records (only if plant is public and allows care sharing)
    IF TG_TABLE_NAME = 'cares' AND TG_OP = 'INSERT' THEN
        -- Check if plant is public and allows care sharing
        IF EXISTS (
            SELECT 1 FROM plants 
            WHERE id = NEW.plant_id 
            AND is_public = true 
            AND allow_care_sharing = true
        ) THEN
            INSERT INTO feed_activities (user_id, activity_type, plant_id, care_id)
            SELECT owner_id, 'plant_care', NEW.plant_id, NEW.id
            FROM plants WHERE id = NEW.plant_id;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic feed activity creation
CREATE TRIGGER trigger_create_plant_feed_activity
    AFTER INSERT ON plants
    FOR EACH ROW
    EXECUTE FUNCTION create_feed_activity();

CREATE TRIGGER trigger_create_care_feed_activity
    AFTER INSERT ON cares
    FOR EACH ROW
    EXECUTE FUNCTION create_feed_activity();

-- Create function to clean up old feed activities (optional, for maintenance)
CREATE OR REPLACE FUNCTION cleanup_old_feed_activities(days_old INTEGER DEFAULT 30)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM feed_activities 
    WHERE created_at < NOW() - INTERVAL '1 day' * days_old;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;
