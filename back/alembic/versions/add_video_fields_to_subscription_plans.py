"""Add video fields to subscription_plans table

Revision ID: add_video_fields_to_subscription_plans
Revises: add_video_thumbnails
Create Date: 2025-06-17 21:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'add_video_fields_to_subscription_plans'
down_revision = 'add_video_thumbnails'
branch_labels = None
depends_on = None

def upgrade():
    # Add video-related columns to subscription_plans table
    op.add_column('subscription_plans', sa.Column('max_videos_per_plant', sa.Integer(), nullable=True))
    op.add_column('subscription_plans', sa.Column('max_videos_per_care', sa.Integer(), nullable=False, server_default='0'))
    op.add_column('subscription_plans', sa.Column('allows_videos', sa.<PERSON>(), nullable=False, server_default='false'))

def downgrade():
    # Remove video-related columns from subscription_plans table
    op.drop_column('subscription_plans', 'allows_videos')
    op.drop_column('subscription_plans', 'max_videos_per_care')
    op.drop_column('subscription_plans', 'max_videos_per_plant')
