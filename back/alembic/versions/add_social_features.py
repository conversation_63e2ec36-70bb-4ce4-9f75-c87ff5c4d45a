"""Add social features

Revision ID: add_social_features
Revises: previous_revision
Create Date: 2025-01-14 11:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = 'add_social_features'
down_revision = None  # Will be updated
branch_labels = None
depends_on = None


def upgrade():
    # Add social fields to users table
    op.add_column('users', sa.Column('username', sa.String(50), nullable=True, unique=True))
    op.add_column('users', sa.Column('is_public', sa.<PERSON>(), nullable=False, server_default='true'))
    op.add_column('users', sa.Column('allow_care_sharing', sa.<PERSON>(), nullable=False, server_default='true'))
    op.add_column('users', sa.Column('allow_search', sa.<PERSON>olean(), nullable=False, server_default='true'))
    op.add_column('users', sa.Column('followers_count', sa.Integer(), nullable=False, server_default='0'))
    op.add_column('users', sa.Column('following_count', sa.Integer(), nullable=False, server_default='0'))
    
    # Add privacy fields to plants table
    op.add_column('plants', sa.Column('is_public', sa.Boolean(), nullable=False, server_default='true'))
    op.add_column('plants', sa.Column('allow_care_sharing', sa.Boolean(), nullable=False, server_default='true'))
    
    # Create user_follows table
    op.create_table('user_follows',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('follower_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('followed_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['follower_id'], ['users.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['followed_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('follower_id', 'followed_id', name='unique_follow_relationship')
    )
    
    # Create user_blocks table (for future blocking functionality)
    op.create_table('user_blocks',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('blocker_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('blocked_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['blocker_id'], ['users.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['blocked_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('blocker_id', 'blocked_id', name='unique_block_relationship')
    )
    
    # Create indexes for performance
    op.create_index('idx_users_username', 'users', ['username'])
    op.create_index('idx_users_is_public', 'users', ['is_public'])
    op.create_index('idx_users_allow_search', 'users', ['allow_search'])
    op.create_index('idx_plants_is_public', 'plants', ['is_public'])
    op.create_index('idx_plants_user_public', 'plants', ['user_id', 'is_public'])
    op.create_index('idx_follows_follower', 'user_follows', ['follower_id'])
    op.create_index('idx_follows_followed', 'user_follows', ['followed_id'])
    op.create_index('idx_follows_created', 'user_follows', ['created_at'])
    op.create_index('idx_blocks_blocker', 'user_blocks', ['blocker_id'])
    op.create_index('idx_blocks_blocked', 'user_blocks', ['blocked_id'])


def downgrade():
    # Drop indexes
    op.drop_index('idx_blocks_blocked')
    op.drop_index('idx_blocks_blocker')
    op.drop_index('idx_follows_created')
    op.drop_index('idx_follows_followed')
    op.drop_index('idx_follows_follower')
    op.drop_index('idx_plants_user_public')
    op.drop_index('idx_plants_is_public')
    op.drop_index('idx_users_allow_search')
    op.drop_index('idx_users_is_public')
    op.drop_index('idx_users_username')
    
    # Drop tables
    op.drop_table('user_blocks')
    op.drop_table('user_follows')
    
    # Remove columns from plants
    op.drop_column('plants', 'allow_care_sharing')
    op.drop_column('plants', 'is_public')
    
    # Remove columns from users
    op.drop_column('users', 'following_count')
    op.drop_column('users', 'followers_count')
    op.drop_column('users', 'allow_search')
    op.drop_column('users', 'allow_care_sharing')
    op.drop_column('users', 'is_public')
    op.drop_column('users', 'username')
