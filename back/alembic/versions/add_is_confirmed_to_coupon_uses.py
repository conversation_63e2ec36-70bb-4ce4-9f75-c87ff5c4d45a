"""Add is_confirmed field to coupon_uses

Revision ID: add_is_confirmed_to_coupon_uses
Revises: add_coupons_tables
Create Date: 2025-01-13 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'add_is_confirmed_to_coupon_uses'
down_revision = 'add_coupons_tables'
branch_labels = None
depends_on = None

def upgrade():
    # Add is_confirmed column to coupon_uses table
    op.add_column('coupon_uses', sa.Column('is_confirmed', sa.Boolean(), nullable=False, server_default='true'))

def downgrade():
    # Remove is_confirmed column from coupon_uses table
    op.drop_column('coupon_uses', 'is_confirmed')
