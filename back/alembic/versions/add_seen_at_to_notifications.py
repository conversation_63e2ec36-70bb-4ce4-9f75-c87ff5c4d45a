"""Add seen_at field to notifications

Revision ID: add_seen_at_notifications
Revises: 
Create Date: 2025-06-18 16:50:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_seen_at_notifications'
down_revision = None
head = None
branch_labels = None
depends_on = None


def upgrade():
    # Add seen_at column to notifications table
    op.add_column('notifications', sa.Column('seen_at', sa.DateTime(timezone=True), nullable=True))


def downgrade():
    # Remove seen_at column from notifications table
    op.drop_column('notifications', 'seen_at')
