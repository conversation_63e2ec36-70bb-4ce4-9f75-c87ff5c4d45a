"""Add CPF/CNPJ fields to users table

Revision ID: add_user_cpf_cnpj
Revises: 
Create Date: 2024-01-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_user_cpf_cnpj'
down_revision = None
head = None
branch_labels = None
depends_on = None


def upgrade():
    # Add CPF/CNPJ fields to users table
    op.add_column('users', sa.Column('cpf_cnpj', sa.String(20), nullable=True))
    op.add_column('users', sa.Column('document_type', sa.String(10), nullable=True))  # 'CPF' or 'CNPJ'
    
    # Add indexes for better performance
    op.create_index('ix_users_cpf_cnpj', 'users', ['cpf_cnpj'])


def downgrade():
    # Remove indexes
    op.drop_index('ix_users_cpf_cnpj', 'users')
    
    # Remove columns
    op.drop_column('users', 'document_type')
    op.drop_column('users', 'cpf_cnpj')
