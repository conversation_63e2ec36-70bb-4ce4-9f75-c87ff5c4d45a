"""Add thumbnail_url to video tables

Revision ID: add_video_thumbnails
Revises: add_social_features
Create Date: 2025-06-18 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'add_video_thumbnails'
down_revision = 'add_social_features'
branch_labels = None
depends_on = None

def upgrade():
    # Add thumbnail_url to plant_videos table
    op.add_column('plant_videos', sa.Column('thumbnail_url', sa.String(500), nullable=True))
    
    # Add thumbnail_url to care_videos table
    op.add_column('care_videos', sa.Column('thumbnail_url', sa.String(500), nullable=True))

def downgrade():
    # Remove thumbnail_url from care_videos table
    op.drop_column('care_videos', 'thumbnail_url')
    
    # Remove thumbnail_url from plant_videos table
    op.drop_column('plant_videos', 'thumbnail_url')
