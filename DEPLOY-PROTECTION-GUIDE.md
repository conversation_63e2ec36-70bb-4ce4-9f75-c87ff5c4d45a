# 🔒 Guia de Proteção de Deploy - MeuBonsai.App

## 📋 Visão Geral

Este guia explica como garantir que as configurações de SEO e produção não sejam sobrescritas durante o processo de deploy/merge do git.

## 🎯 Problema Resolvido

**Antes:** Quando você fazia merge do desenvolvimento para produção, os arquivos de SEO (robots.txt, index.html, etc.) eram sobrescritos com as configurações de desenvolvimento (noindex), quebrando a indexação do Google.

**Agora:** Sistema automatizado de proteção que preserva as configurações de produção durante deploys.

## 🛠️ Ferramentas Implementadas

### 1. 🔒 Script de Proteção (`protect-production-config.sh`)

**Localização:** `/root/meubonsai-app/protect-production-config.sh`

**Comandos disponíveis:**
```bash
# Fazer backup dos arquivos protegidos
./protect-production-config.sh backup

# Restaurar configurações de produção
./protect-production-config.sh restore

# Validar configurações de produção
./protect-production-config.sh validate

# Proteção completa pós-deploy
./protect-production-config.sh post-deploy
```

### 2. 🚀 Script de Deploy Automatizado (`deploy-to-production.sh`)

**Localização:** `/root/meubonsai-app/deploy-to-production.sh`

**Uso:**
```bash
# Deploy completo e seguro
./deploy-to-production.sh

# Ver ajuda
./deploy-to-production.sh help
```

## 📁 Arquivos Protegidos

Os seguintes arquivos são automaticamente protegidos em produção:

### 🌐 Configurações de SEO
- `front/public/index.html` - Meta tags de produção
- `front/public/robots.txt` - Permite indexação
- `front/public/sitemap.xml` - Sitemap completo
- `front/public/manifest.json` - Manifest PWA

### 🎨 Ícones e Favicons
- `front/public/favicon.ico`
- `front/public/favicon-16x16.png`
- `front/public/favicon-32x32.png`
- `front/public/apple-touch-icon.png`
- `front/public/icon-192x192.png`
- `front/public/icon-512x512.png`

## 🔄 Fluxo de Deploy Recomendado

### 1. Desenvolvimento
```bash
# No ambiente de desenvolvimento (/root/meubonsai-app-dev)
# Fazer suas alterações normalmente
git add .
git commit -m "Suas alterações"
git push origin develop
```

### 2. Merge para Main
```bash
# No GitHub/GitLab
# Criar Pull Request: develop → main
# Fazer merge
```

### 3. Deploy em Produção
```bash
# No ambiente de produção (/root/meubonsai-app)
./deploy-to-production.sh
```

**O script automaticamente:**
1. ✅ Verifica status do Git
2. 📦 Faz backup das configurações atuais
3. ⬇️ Puxa as mudanças do Git
4. 🔄 Restaura configurações de produção
5. 🔨 Reconstrói containers
6. 🔍 Valida o deploy
7. 📋 Mostra resumo

## ⚠️ Configurações por Ambiente

### 🔧 Desenvolvimento (dev.meubonsai.app)
- **robots.txt:** Bloqueia todos os crawlers
- **Meta robots:** `noindex, nofollow`
- **Sitemap:** Removido
- **URLs canônicas:** Apontam para produção

### 🌐 Produção (meubonsai.app)
- **robots.txt:** Permite indexação
- **Meta robots:** `index, follow`
- **Sitemap:** Completo e atualizado
- **URLs canônicas:** Corretas para produção

## 🚨 Situações de Emergência

### Rollback Automático
Se algo der errado durante o deploy, o script automaticamente:
1. Detecta o erro
2. Restaura o último backup
3. Reinicia os containers
4. Notifica sobre o rollback

### Rollback Manual
```bash
# Listar backups disponíveis
ls -la .production-backup-*

# Restaurar backup específico
cp .production-backup-YYYYMMDD-HHMMSS/* front/public/

# Reiniciar containers
./start.sh prod restart
```

## 🔍 Validação e Monitoramento

### Verificar Configurações
```bash
# Validar configurações de produção
./protect-production-config.sh validate

# Verificar status dos containers
./start.sh prod status

# Ver logs
./start.sh prod logs
```

### Testes de Conectividade
```bash
# Testar frontend
curl -I https://meubonsai.app/

# Testar backend
curl -I https://api.meubonsai.app/health

# Verificar robots.txt
curl https://meubonsai.app/robots.txt

# Verificar sitemap
curl https://meubonsai.app/sitemap.xml
```

## 📝 Checklist de Deploy

### ✅ Antes do Deploy
- [ ] Código testado em desenvolvimento
- [ ] Pull Request aprovado e merged
- [ ] Backup atual funcionando

### ✅ Durante o Deploy
- [ ] Executar `./deploy-to-production.sh`
- [ ] Aguardar conclusão sem erros
- [ ] Verificar validações automáticas

### ✅ Após o Deploy
- [ ] Testar funcionalidades críticas
- [ ] Verificar SEO (robots.txt, meta tags)
- [ ] Monitorar logs por alguns minutos
- [ ] Confirmar que dev.meubonsai.app não aparece no Google

## 🆘 Contatos e Suporte

Em caso de problemas:
1. Verificar logs: `./start.sh prod logs`
2. Executar validação: `./protect-production-config.sh validate`
3. Se necessário, fazer rollback manual
4. Documentar o problema para análise

## 📚 Arquivos de Referência

- `index-production.html` - Template do index.html de produção
- `robots-production.txt` - Template do robots.txt de produção
- `sitemap-production.xml` - Template do sitemap de produção
- `manifest-production.json` - Template do manifest de produção

---

**🎯 Objetivo:** Garantir que o MeuBonsai.App mantenha suas configurações de SEO corretas em produção, independentemente das mudanças feitas em desenvolvimento.
