#!/bin/bash

# Script para limpeza de logs antigos do Docker
# Uso: ./cleanup-logs.sh

echo "🧹 Iniciando limpeza de logs do Docker..."

# Verificar espaço em disco antes
echo "📊 Espaço em disco ANTES da limpeza:"
df -h / | grep -E "Filesystem|/dev/sda1"

# Limpar logs de containers antigos (mais de 7 dias)
echo "🗑️ Limpando logs de containers antigos..."
find /var/lib/docker/containers/ -name "*-json.log" -mtime +7 -exec truncate -s 0 {} \;

# Limpar imagens não utilizadas
echo "🖼️ Removendo imagens Docker não utilizadas..."
docker image prune -f

# Limpar containers parados
echo "📦 Removendo containers parados..."
docker container prune -f

# Limpar volumes não utilizados
echo "💾 Removendo volumes não utilizados..."
docker volume prune -f

# Limpar redes não utilizadas
echo "🌐 Removendo redes não utilizadas..."
docker network prune -f

# Limpar cache de build
echo "🔨 Limpando cache de build..."
docker builder prune -f

# Limpar logs do sistema (mais de 7 dias)
echo "📋 Limpando logs do sistema..."
journalctl --vacuum-time=7d

# Limpar cache do APT
echo "📦 Limpando cache do APT..."
apt-get clean
apt-get autoclean

# Limpar arquivos temporários
echo "🗂️ Limpando arquivos temporários..."
rm -rf /tmp/* /var/tmp/* 2>/dev/null || true

# Verificar espaço em disco depois
echo "📊 Espaço em disco DEPOIS da limpeza:"
df -h / | grep -E "Filesystem|/dev/sda1"

echo "✅ Limpeza concluída!"
