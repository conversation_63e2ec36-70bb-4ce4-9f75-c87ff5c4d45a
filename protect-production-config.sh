#!/bin/bash

# 🔒 Script de Proteção das Configurações de Produção
# Este script garante que as configurações de SEO e produção não sejam sobrescritas
# durante o processo de deploy/merge do git

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Diretório de produção
PROD_DIR="/root/meubonsai-app"

# Arquivos críticos que devem ser protegidos em produção
PROTECTED_FILES=(
    "front/public/index.html"
    "front/public/robots.txt"
    "front/public/sitemap.xml"
    "front/public/manifest.json"
    "front/public/favicon.ico"
    "front/public/favicon-16x16.png"
    "front/public/favicon-32x32.png"
    "front/public/apple-touch-icon.png"
    "front/public/icon-192x192.png"
    "front/public/icon-512x512.png"
)

# Arquivos de template de produção (fontes da verdade)
PRODUCTION_TEMPLATES=(
    "index-production.html"
    "robots-production.txt"
    "sitemap-production.xml"
    "manifest-production.json"
)

echo -e "${BLUE}🔒 Proteção de Configurações de Produção${NC}"
echo "=================================================="

# Função para fazer backup dos arquivos protegidos
backup_protected_files() {
    echo -e "${YELLOW}📦 Fazendo backup dos arquivos protegidos...${NC}"
    
    BACKUP_DIR="$PROD_DIR/.production-backup-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    for file in "${PROTECTED_FILES[@]}"; do
        if [ -f "$PROD_DIR/$file" ]; then
            cp "$PROD_DIR/$file" "$BACKUP_DIR/"
            echo "  ✅ Backup: $file"
        fi
    done
    
    echo -e "${GREEN}✅ Backup criado em: $BACKUP_DIR${NC}"
}

# Função para restaurar configurações de produção
restore_production_config() {
    echo -e "${YELLOW}🔄 Restaurando configurações de produção...${NC}"
    
    cd "$PROD_DIR"
    
    # Restaurar arquivos de template
    if [ -f "index-production.html" ]; then
        cp "index-production.html" "front/public/index.html"
        echo "  ✅ Restaurado: index.html"
    fi
    
    if [ -f "robots-production.txt" ]; then
        cp "robots-production.txt" "front/public/robots.txt"
        echo "  ✅ Restaurado: robots.txt"
    fi
    
    if [ -f "sitemap-production.xml" ]; then
        cp "sitemap-production.xml" "front/public/sitemap.xml"
        echo "  ✅ Restaurado: sitemap.xml"
    fi
    
    if [ -f "manifest-production.json" ]; then
        cp "manifest-production.json" "front/public/manifest.json"
        echo "  ✅ Restaurado: manifest.json"
    fi
    
    echo -e "${GREEN}✅ Configurações de produção restauradas!${NC}"
}

# Função para verificar se estamos em produção
check_production_environment() {
    if [ ! -d "$PROD_DIR" ]; then
        echo -e "${RED}❌ Diretório de produção não encontrado: $PROD_DIR${NC}"
        exit 1
    fi
    
    if [ "$(pwd)" != "$PROD_DIR" ]; then
        echo -e "${YELLOW}⚠️  Mudando para diretório de produção...${NC}"
        cd "$PROD_DIR"
    fi
}

# Função para validar configurações de produção
validate_production_config() {
    echo -e "${YELLOW}🔍 Validando configurações de produção...${NC}"

    local errors=0

    # Verificar robots.txt (deve permitir indexação geral)
    if grep -q "Allow: /" "front/public/robots.txt" 2>/dev/null; then
        echo "  ✅ robots.txt: Permite indexação"
    else
        echo -e "${RED}❌ ERRO: robots.txt não permite indexação!${NC}"
        errors=$((errors + 1))
    fi
    
    # Verificar index.html
    if grep -q "noindex" "front/public/index.html" 2>/dev/null; then
        echo -e "${RED}❌ ERRO: index.html contém meta noindex!${NC}"
        errors=$((errors + 1))
    else
        echo "  ✅ index.html: Permite indexação"
    fi
    
    # Verificar sitemap.xml
    if [ ! -f "front/public/sitemap.xml" ]; then
        echo -e "${RED}❌ ERRO: sitemap.xml não encontrado!${NC}"
        errors=$((errors + 1))
    else
        echo "  ✅ sitemap.xml: Presente"
    fi
    
    # Verificar favicon
    if [ ! -f "front/public/favicon.ico" ]; then
        echo -e "${RED}❌ ERRO: favicon.ico não encontrado!${NC}"
        errors=$((errors + 1))
    else
        echo "  ✅ favicon.ico: Presente"
    fi
    
    if [ $errors -gt 0 ]; then
        echo -e "${RED}❌ Encontrados $errors erros na configuração de produção!${NC}"
        return 1
    else
        echo -e "${GREEN}✅ Todas as configurações de produção estão corretas!${NC}"
        return 0
    fi
}

# Função principal
main() {
    case "${1:-help}" in
        "backup")
            check_production_environment
            backup_protected_files
            ;;
        "restore")
            check_production_environment
            backup_protected_files
            restore_production_config
            validate_production_config
            ;;
        "validate")
            check_production_environment
            validate_production_config
            ;;
        "post-deploy")
            echo -e "${BLUE}🚀 Executando proteção pós-deploy...${NC}"
            check_production_environment
            backup_protected_files
            restore_production_config
            validate_production_config
            ;;
        "help"|*)
            echo -e "${BLUE}🔒 Script de Proteção de Configurações de Produção${NC}"
            echo ""
            echo "Uso: ./protect-production-config.sh [comando]"
            echo ""
            echo "Comandos disponíveis:"
            echo -e "  ${GREEN}backup${NC}      - Faz backup dos arquivos protegidos"
            echo -e "  ${GREEN}restore${NC}     - Restaura configurações de produção"
            echo -e "  ${GREEN}validate${NC}    - Valida configurações de produção"
            echo -e "  ${GREEN}post-deploy${NC} - Executa proteção completa pós-deploy"
            echo -e "  ${GREEN}help${NC}        - Exibe esta ajuda"
            echo ""
            echo "Arquivos protegidos:"
            for file in "${PROTECTED_FILES[@]}"; do
                echo "  - $file"
            done
            ;;
    esac
}

main "$@"
