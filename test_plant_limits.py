#!/usr/bin/env python3
"""
Test script to verify plant limit enforcement
"""
import requests
import json
import sys

# Test configuration
BASE_URL = "https://api-dev.meubonsai.app"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "123456"  # Assuming this is the password

def login():
    """Login and get access token"""
    login_data = {
        "username": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    response = requests.post(
        f"{BASE_URL}/api/v1/auth/login",
        data=login_data,
        headers={"Content-Type": "application/x-www-form-urlencoded"}
    )
    
    if response.status_code == 200:
        token_data = response.json()
        return token_data.get("access_token")
    else:
        print(f"Login failed: {response.status_code} - {response.text}")
        return None

def test_plant_access_info(token):
    """Test the plant access info endpoint"""
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(
        f"{BASE_URL}/api/v1/subscriptions/plant-access-info",
        headers=headers
    )
    
    print(f"Plant Access Info Response ({response.status_code}):")
    print(json.dumps(response.json(), indent=2))
    return response.json()

def test_plant_creation_limit(token):
    """Test plant creation limit"""
    headers = {"Authorization": f"Bearer {token}"}
    
    plant_data = {
        "name": "Test Plant Limit",
        "species": "Test Species",
        "plant_type": "Conífera"
    }
    
    response = requests.post(
        f"{BASE_URL}/api/v1/plants/",
        json=plant_data,
        headers=headers
    )
    
    print(f"\nPlant Creation Response ({response.status_code}):")
    print(json.dumps(response.json(), indent=2))
    return response.status_code == 403

def test_check_limits_endpoint(token):
    """Test the check limits endpoint"""
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(
        f"{BASE_URL}/api/v1/subscriptions/check-limits/create_plant",
        headers=headers
    )
    
    print(f"\nCheck Limits Response ({response.status_code}):")
    print(json.dumps(response.json(), indent=2))
    return response.json()

def main():
    print("🧪 Testing Plant Limit Enforcement")
    print("=" * 50)
    
    # Step 1: Login
    print("1. Logging in...")
    token = login()
    if not token:
        print("❌ Failed to login")
        sys.exit(1)
    print("✅ Login successful")
    
    # Step 2: Check plant access info
    print("\n2. Checking plant access info...")
    access_info = test_plant_access_info(token)
    
    # Step 3: Test check limits endpoint
    print("\n3. Testing check limits endpoint...")
    limits_check = test_check_limits_endpoint(token)
    
    # Step 4: Test plant creation (should fail)
    print("\n4. Testing plant creation (should be blocked)...")
    creation_blocked = test_plant_creation_limit(token)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    if access_info:
        print(f"Total plants: {access_info.get('total_plants', 'N/A')}")
        print(f"Editable plants: {access_info.get('editable_plants', 'N/A')}")
        print(f"Read-only plants: {access_info.get('readonly_plants', 'N/A')}")
        print(f"Can create new: {access_info.get('can_create_new', 'N/A')}")
    
    if limits_check:
        print(f"Limits check - Can perform: {limits_check.get('can_perform', 'N/A')}")
        print(f"Limits check - Message: {limits_check.get('message', 'N/A')}")
    
    print(f"Plant creation blocked: {'✅ YES' if creation_blocked else '❌ NO'}")
    
    if creation_blocked and not limits_check.get('can_perform', True):
        print("\n🎉 Plant limit enforcement is working correctly!")
    else:
        print("\n⚠️  Plant limit enforcement may not be working as expected")

if __name__ == "__main__":
    main()
