# Configuração SMTP - MeuBonsai.App

## 🚨 Problema Identificado

O formulário de contato não está enviando emails devido a erro de autenticação SMTP:
```
Error: authentication failed: (535, b'5.7.8 Error: authentication failed: (reason unavailable)')
```

## 📧 Configurações Atuais (.env)

```bash
SMTP_SERVER=smtp.hostinger.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=sbz*P6XU^dVU6LVpxY!
SMTP_USE_TLS=true
FROM_EMAIL=<EMAIL>
```

## 🔧 Possíveis Soluções

### 1. Verificar Credenciais no Hostinger
- Acessar o painel do Hostinger
- Verificar se a conta `<EMAIL>` existe
- Confirmar se a senha está correta
- Verificar se SMTP está habilitado para a conta

### 2. Configurações Alternativas do Hostinger
Algumas configurações que podem funcionar:

```bash
# Opção 1: SMTP SSL
SMTP_SERVER=smtp.hostinger.com
SMTP_PORT=465
SMTP_USE_TLS=false  # Usar SSL direto

# Opção 2: Servidor alternativo
SMTP_SERVER=mail.meubonsai.app
SMTP_PORT=587
SMTP_USE_TLS=true
```

### 3. Verificar Configurações de Segurança
- Verificar se há autenticação de dois fatores
- Verificar se há restrições de IP
- Verificar se há senhas de aplicativo necessárias

## 🛠️ Solução Temporária Implementada

Para o ambiente de **desenvolvimento**, o sistema agora:
- ✅ Registra todas as mensagens nos logs
- ✅ Retorna sucesso para o usuário
- ⚠️ Não envia email (mas funciona para testes)

Para **produção**, o sistema:
- ❌ Falha se não conseguir enviar email
- 🔒 Garante que mensagens importantes sejam entregues

## 📋 Logs das Mensagens

As mensagens de contato são registradas nos logs do backend:
```bash
docker logs meubonsai_backend_dev --tail 50 | grep -A 5 "contato"
```

## 🚀 Para Corrigir em Produção

1. **Verificar credenciais no Hostinger**
2. **Testar conexão SMTP manualmente**
3. **Atualizar .env com credenciais corretas**
4. **Reiniciar containers**

## 📞 Contato de Emergência

Se o problema persistir, as mensagens podem ser enviadas diretamente para:
**<EMAIL>**

## 🧪 Teste Manual

Para testar SMTP manualmente:
```bash
python3 -c "
import smtplib
server = smtplib.SMTP('smtp.hostinger.com', 587)
server.starttls()
server.login('<EMAIL>', 'SENHA_AQUI')
print('✅ SMTP funcionando!')
server.quit()
"
```
