#!/usr/bin/env python3
"""
Test script for plant read-only enforcement system
Tests the plan downgrade enforcement with user bruno<PERSON>@gmail.com
"""

import requests
import json
import sys

# Configuration
BASE_URL = "https://api-dev.meubonsai.app"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "Bru@2024"

def login():
    """Login and get access token"""
    print("🔐 Fazendo login...")
    
    login_data = {
        "username": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/auth/login", data=login_data)
    
    if response.status_code == 200:
        token = response.json()["access_token"]
        print(f"✅ Login realizado com sucesso!")
        return token
    else:
        print(f"❌ Erro no login: {response.status_code} - {response.text}")
        return None

def get_headers(token):
    """Get authorization headers"""
    return {"Authorization": f"Bearer {token}"}

def test_plant_access_info(token):
    """Test the plant access info endpoint"""
    print("\n📊 Testando informações de acesso às plantas...")
    
    headers = get_headers(token)
    response = requests.get(f"{BASE_URL}/api/v1/subscriptions/plant-access-info", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Informações obtidas com sucesso:")
        print(f"   Total de plantas: {data.get('total_plants', 0)}")
        print(f"   Plantas editáveis: {data.get('editable_plants', 0)}")
        print(f"   Plantas somente leitura: {data.get('readonly_plants', 0)}")
        print(f"   Pode criar nova: {data.get('can_create_new', False)}")
        print(f"   Mensagem: {data.get('message', 'N/A')}")
        
        if data.get('readonly_plant_ids'):
            print(f"   IDs das plantas somente leitura: {data.get('readonly_plant_ids')}")
        if data.get('editable_plant_ids'):
            print(f"   IDs das plantas editáveis: {len(data.get('editable_plant_ids', []))} plantas")
            
        return data
    else:
        print(f"❌ Erro ao obter informações: {response.status_code} - {response.text}")
        return None

def get_user_plants(token):
    """Get user's plants"""
    print("\n🌱 Obtendo plantas do usuário...")
    
    headers = get_headers(token)
    response = requests.get(f"{BASE_URL}/api/v1/plants/", headers=headers)
    
    if response.status_code == 200:
        plants = response.json()
        print(f"✅ {len(plants)} plantas encontradas")
        
        # Show plant details
        for i, plant in enumerate(plants[:5]):  # Show first 5 plants
            print(f"   {i+1}. {plant.get('name', 'Sem nome')} (ID: {plant.get('id')}) - Atualizada: {plant.get('last_updated', 'N/A')}")
            
        return plants
    else:
        print(f"❌ Erro ao obter plantas: {response.status_code} - {response.text}")
        return None

def test_plant_edit(token, plant_id, plant_name):
    """Test editing a specific plant"""
    print(f"\n✏️ Testando edição da planta '{plant_name}' (ID: {plant_id})...")
    
    headers = get_headers(token)
    headers["Content-Type"] = "application/json"
    
    # Try to update plant name
    update_data = {
        "name": f"{plant_name} - TESTE EDIÇÃO",
        "description": "Teste de edição para verificar limitações do plano"
    }
    
    response = requests.put(f"{BASE_URL}/api/v1/plants/{plant_id}", 
                          headers=headers, 
                          json=update_data)
    
    if response.status_code == 200:
        print(f"✅ Planta editada com sucesso!")
        return True
    elif response.status_code == 403:
        print(f"🔒 Planta bloqueada para edição: {response.json().get('detail', 'Erro desconhecido')}")
        return False
    else:
        print(f"❌ Erro inesperado: {response.status_code} - {response.text}")
        return None

def test_care_creation(token, plant_id, plant_name):
    """Test creating a care record for a plant"""
    print(f"\n🌿 Testando criação de cuidado para '{plant_name}' (ID: {plant_id})...")
    
    headers = get_headers(token)
    
    # Try to create a care record
    care_data = {
        "care_types": ["rega"],
        "description": "Teste de rega para verificar limitações",
        "date": "2024-01-15",
        "notes": "Teste de criação de cuidado"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/plants/{plant_id}/cares", 
                           headers=headers, 
                           data=care_data)
    
    if response.status_code == 200:
        print(f"✅ Cuidado criado com sucesso!")
        return True
    elif response.status_code == 403:
        print(f"🔒 Criação de cuidado bloqueada: {response.json().get('detail', 'Erro desconhecido')}")
        return False
    else:
        print(f"❌ Erro inesperado: {response.status_code} - {response.text}")
        return None

def main():
    """Main test function"""
    print("🧪 TESTE DO SISTEMA DE LIMITAÇÃO DE PLANTAS")
    print("=" * 50)
    
    # Login
    token = login()
    if not token:
        sys.exit(1)
    
    # Get plant access info
    access_info = test_plant_access_info(token)
    if not access_info:
        sys.exit(1)
    
    # Get user plants
    plants = get_user_plants(token)
    if not plants:
        sys.exit(1)
    
    # Test editing plants
    print("\n🔧 TESTANDO EDIÇÃO DE PLANTAS")
    print("-" * 30)
    
    readonly_plant_ids = access_info.get('readonly_plant_ids', [])
    editable_plant_ids = access_info.get('editable_plant_ids', [])
    
    # Test editing a read-only plant (should fail)
    if readonly_plant_ids:
        readonly_plant = next((p for p in plants if str(p['id']) in readonly_plant_ids), None)
        if readonly_plant:
            test_plant_edit(token, readonly_plant['id'], readonly_plant.get('name', 'Sem nome'))
            test_care_creation(token, readonly_plant['id'], readonly_plant.get('name', 'Sem nome'))
    
    # Test editing an editable plant (should succeed)
    if editable_plant_ids:
        editable_plant = next((p for p in plants if str(p['id']) in editable_plant_ids), None)
        if editable_plant:
            test_plant_edit(token, editable_plant['id'], editable_plant.get('name', 'Sem nome'))
            test_care_creation(token, editable_plant['id'], editable_plant.get('name', 'Sem nome'))
    
    print("\n✅ TESTE CONCLUÍDO!")
    print("=" * 50)

if __name__ == "__main__":
    main()
