#!/bin/bash

# Script para gerenciar o MeuBonsai.App
# Uso: ./start.sh [ambiente] [comando]
# Ambiente: dev ou prod (padrão: dev)

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Determinar ambiente e comando
if [[ "$1" == "dev" || "$1" == "prod" ]]; then
    ENVIRONMENT="$1"
    COMMAND="${2:-help}"
else
    ENVIRONMENT="dev"  # padrão
    COMMAND="${1:-help}"
fi

# Definir arquivo docker-compose baseado no ambiente
if [ "$ENVIRONMENT" == "prod" ]; then
    COMPOSE_FILE="docker-compose-prod.yml"
    ENV_NAME="PRODUÇÃO"
else
    COMPOSE_FILE="docker-compose-dev.yml"
    ENV_NAME="DESENVOLVIMENTO"
fi

# Função para exibir ajuda
show_help() {
    echo -e "${BLUE}🌱 MeuBonsai.App - Gerenciador Docker${NC}"
    echo ""
    echo "Uso: ./start.sh [ambiente] [comando]"
    echo ""
    echo "Ambientes disponíveis:"
    echo -e "  ${GREEN}dev${NC}      - Desenvolvimento (padrão)"
    echo -e "  ${GREEN}prod${NC}     - Produção"
    echo ""
    echo "Comandos disponíveis:"
    echo -e "  ${GREEN}up${NC}       - Inicia todos os containers"
    echo -e "  ${GREEN}down${NC}     - Para todos os containers"
    echo -e "  ${GREEN}restart${NC}  - Reinicia todos os containers"
    echo -e "  ${GREEN}build${NC}    - Reconstrói as imagens e inicia"
    echo -e "  ${GREEN}logs${NC}     - Exibe logs de todos os containers"
    echo -e "  ${GREEN}status${NC}   - Mostra status dos containers"
    echo -e "  ${GREEN}clean${NC}    - Para containers e remove volumes (CUIDADO!)"
    echo -e "  ${GREEN}help${NC}     - Exibe esta ajuda"
    echo ""
    echo "Exemplos:"
    echo "  ./start.sh up           # Inicia em desenvolvimento"
    echo "  ./start.sh dev up       # Inicia em desenvolvimento"
    echo "  ./start.sh prod up      # Inicia em produção"
    echo "  ./start.sh dev logs     # Visualiza logs do ambiente de desenvolvimento"
    echo "  ./start.sh prod build   # Reconstrói e inicia em produção"
}

# Função para verificar se a rede existe
check_network() {
    if ! docker network ls | grep -q "my-network"; then
        echo -e "${YELLOW}⚠️  Criando rede 'my-network'...${NC}"
        docker network create my-network
    fi
}

# Função para iniciar containers
start_containers() {
    echo -e "${GREEN}🚀 Iniciando MeuBonsai.App (${ENV_NAME})...${NC}"
    check_network
    docker compose -f "$COMPOSE_FILE" up -d
    echo -e "${GREEN}✅ Aplicação iniciada em ambiente ${ENV_NAME}!${NC}"
    echo ""
    echo -e "${BLUE}🌐 URLs disponíveis:${NC}"

    if [ "$ENVIRONMENT" == "prod" ]; then
        echo -e "  Frontend: ${GREEN}https://meubonsai.app${NC}"
        echo -e "  Backend:  ${GREEN}https://api.meubonsai.app${NC}"
        echo -e "  PgAdmin:  ${GREEN}https://pg.meubonsai.app${NC}"
    else
        echo -e "  Frontend: ${GREEN}https://dev.meubonsai.app${NC}"
        echo -e "  Backend:  ${GREEN}https://api-dev.meubonsai.app${NC}"
        echo -e "  PgAdmin:  ${GREEN}https://pg-dev.meubonsai.app${NC}"
    fi
}

# Função para parar containers
stop_containers() {
    echo -e "${YELLOW}🛑 Parando containers (${ENV_NAME})...${NC}"
    docker compose -f "$COMPOSE_FILE" down
    echo -e "${GREEN}✅ Containers parados!${NC}"
}

# Função para reiniciar
restart_containers() {
    echo -e "${YELLOW}🔄 Reiniciando aplicação (${ENV_NAME})...${NC}"
    stop_containers
    start_containers
}

# Função para build e start
build_and_start() {
    echo -e "${BLUE}🔨 Reconstruindo imagens (${ENV_NAME})...${NC}"
    check_network
    docker compose -f "$COMPOSE_FILE" down
    docker compose -f "$COMPOSE_FILE" build --no-cache
    docker compose -f "$COMPOSE_FILE" up -d
    echo -e "${GREEN}✅ Aplicação reconstruída e iniciada em ambiente ${ENV_NAME}!${NC}"
}

# Função para exibir logs
show_logs() {
    echo -e "${BLUE}📋 Exibindo logs (${ENV_NAME})...${NC}"
    docker compose -f "$COMPOSE_FILE" logs -f
}

# Função para mostrar status
show_status() {
    echo -e "${BLUE}📊 Status dos containers (${ENV_NAME}):${NC}"
    docker compose -f "$COMPOSE_FILE" ps
}

# Função para limpeza completa
clean_all() {
    echo -e "${RED}⚠️  ATENÇÃO: Isso irá remover todos os dados do banco (${ENV_NAME})!${NC}"
    read -p "Tem certeza? (digite 'sim' para confirmar): " confirm
    if [ "$confirm" = "sim" ]; then
        echo -e "${YELLOW}🧹 Limpando tudo (${ENV_NAME})...${NC}"
        docker compose -f "$COMPOSE_FILE" down -v
        docker system prune -f
        echo -e "${GREEN}✅ Limpeza concluída!${NC}"
    else
        echo -e "${BLUE}ℹ️  Operação cancelada.${NC}"
    fi
}

# Processar comando
case "$COMMAND" in
    "up")
        start_containers
        ;;
    "down")
        stop_containers
        ;;
    "restart")
        restart_containers
        ;;
    "build")
        build_and_start
        ;;
    "logs")
        show_logs
        ;;
    "status")
        show_status
        ;;
    "clean")
        clean_all
        ;;
    "help"|*)
        show_help
        ;;
esac
