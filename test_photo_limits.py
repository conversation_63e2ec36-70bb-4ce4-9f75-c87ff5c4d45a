#!/usr/bin/env python3
"""
Script para testar os limites de fotos implementados
"""

import requests
import json
import sys
import os
from io import BytesIO
from PIL import Image

# Configurações
BASE_URL = "https://api-dev.meubonsai.app"
TEST_EMAIL = "<EMAIL>"

def create_test_image():
    """Cria uma imagem de teste simples"""
    img = Image.new('RGB', (100, 100), color='red')
    img_bytes = BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    return img_bytes

def test_plant_photo_limit():
    """Testa o limite de 30 fotos por planta no plano gratuito"""
    print("🧪 Testando limite de fotos por planta...")
    
    # Fazer login (assumindo que o usuário já está logado via Google)
    # Para este teste, vamos usar um token válido se disponível
    
    # Buscar plantas do usuário
    try:
        response = requests.get(f"{BASE_URL}/api/v1/plants/", 
                              headers={"Authorization": "Bearer YOUR_TOKEN_HERE"})
        
        if response.status_code == 401:
            print("❌ Token de autenticação necessário. Este teste requer login.")
            return False
            
        plants = response.json()
        if not plants:
            print("❌ Nenhuma planta encontrada para teste")
            return False
            
        plant_id = plants[0]["id"]
        print(f"✅ Usando planta: {plants[0]['name']} (ID: {plant_id})")
        
        # Contar fotos atuais
        current_photos = len(plants[0].get("images", []))
        print(f"📸 Fotos atuais: {current_photos}")
        
        # Tentar adicionar uma foto
        test_image = create_test_image()
        files = {"file": ("test.jpg", test_image, "image/jpeg")}
        
        response = requests.post(
            f"{BASE_URL}/api/v1/plants/{plant_id}/images",
            files=files,
            headers={"Authorization": "Bearer YOUR_TOKEN_HERE"}
        )
        
        if current_photos >= 30:
            if response.status_code == 403:
                print("✅ Limite de 30 fotos por planta funcionando corretamente!")
                print(f"   Mensagem: {response.json().get('detail', 'N/A')}")
                return True
            else:
                print(f"❌ Esperado erro 403, mas recebeu: {response.status_code}")
                return False
        else:
            if response.status_code == 200:
                print(f"✅ Foto adicionada com sucesso (ainda dentro do limite)")
                return True
            else:
                print(f"❌ Erro inesperado ao adicionar foto: {response.status_code}")
                print(f"   Resposta: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Erro durante teste: {e}")
        return False

def test_care_photo_limit():
    """Testa o limite de 1 foto por cuidado no plano gratuito"""
    print("\n🧪 Testando limite de fotos por cuidado...")
    
    try:
        # Buscar plantas do usuário
        response = requests.get(f"{BASE_URL}/api/v1/plants/", 
                              headers={"Authorization": "Bearer YOUR_TOKEN_HERE"})
        
        if response.status_code == 401:
            print("❌ Token de autenticação necessário. Este teste requer login.")
            return False
            
        plants = response.json()
        if not plants:
            print("❌ Nenhuma planta encontrada para teste")
            return False
            
        plant_id = plants[0]["id"]
        print(f"✅ Usando planta: {plants[0]['name']} (ID: {plant_id})")
        
        # Tentar criar cuidado com 2 fotos (deve falhar no plano gratuito)
        test_image1 = create_test_image()
        test_image2 = create_test_image()
        
        files = [
            ("files", ("test1.jpg", test_image1, "image/jpeg")),
            ("files", ("test2.jpg", test_image2, "image/jpeg"))
        ]
        
        data = {
            "care_types": ["watering"],
            "description": "Teste de limite de fotos",
            "date": "2024-01-15",
            "notes": "Teste"
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/plants/{plant_id}/cares",
            files=files,
            data=data,
            headers={"Authorization": "Bearer YOUR_TOKEN_HERE"}
        )
        
        if response.status_code == 403:
            print("✅ Limite de 1 foto por cuidado funcionando corretamente!")
            print(f"   Mensagem: {response.json().get('detail', 'N/A')}")
            return True
        else:
            print(f"❌ Esperado erro 403, mas recebeu: {response.status_code}")
            print(f"   Resposta: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Erro durante teste: {e}")
        return False

def main():
    """Executa todos os testes"""
    print("🚀 Iniciando testes de limites de fotos...")
    print("=" * 50)
    
    # Nota: Este script requer um token de autenticação válido
    print("⚠️  NOTA: Este script requer autenticação via Google OAuth.")
    print("   Para testar manualmente:")
    print("   1. Faça login em https://dev.meubonsai.app")
    print("   2. Tente adicionar mais de 30 fotos a uma planta")
    print("   3. Tente criar um cuidado com mais de 1 foto")
    print("=" * 50)
    
    # Os testes reais precisariam de um token válido
    # test_plant_photo_limit()
    # test_care_photo_limit()
    
    print("\n📋 Resumo dos limites implementados:")
    print("   • Plano Gratuito:")
    print("     - Máximo 30 fotos por planta")
    print("     - Máximo 1 foto por cuidado")
    print("   • Plano Premium:")
    print("     - Fotos ilimitadas por planta")
    print("     - Máximo 10 fotos por cuidado")

if __name__ == "__main__":
    main()
