services:
  # Database PostgreSQL
  database:
    container_name: meubonsai_postgres
    image: ankane/pgvector:latest
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    ports:
      - "5432:5432"
    volumes:
      - meubonsai_postgres_data:/var/lib/postgresql/data
      - ./db/init:/docker-entrypoint-initdb.d
    networks:
      - my-network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # PgAdmin
  pgadmin:
    container_name: meubonsai_pgadmin
    image: dpage/pgadmin4
    environment:
      - PGADMIN_DEFAULT_EMAIL=${PGADMIN_DEFAULT_EMAIL}
      - PGADMIN_DEFAULT_PASSWORD=${PGADMIN_DEFAULT_PASSWORD}
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.pgadmin.rule=Host(`pg.meubonsai.app`)"
      - "traefik.http.routers.pgadmin.entrypoints=websecure"
      - "traefik.http.routers.pgadmin.tls=true"
      - "traefik.http.services.pgadmin.loadbalancer.server.port=80"
      - "traefik.docker.network=my-network"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - my-network
    depends_on:
      - database
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Backend FastAPI
  backend:
    build: ./back
    container_name: meubonsai_backend
    volumes:
      - ./back:/app
      - ./back/media:/app/media
    environment:
      - ENVIRONMENT=production
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_PORT=${POSTGRES_PORT}
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - APPLE_CLIENT_ID=${APPLE_CLIENT_ID:-}
      - APPLE_TEAM_ID=${APPLE_TEAM_ID:-}
      - APPLE_KEY_ID=${APPLE_KEY_ID:-}
      - APPLE_PRIVATE_KEY=${APPLE_PRIVATE_KEY:-}
      - R2_ACCESS_KEY_ID=${R2_ACCESS_KEY_ID}
      - R2_SECRET_ACCESS_KEY=${R2_SECRET_ACCESS_KEY}
      - R2_ENDPOINT_URL=${R2_ENDPOINT_URL}
      - R2_BUCKET_NAME=${R2_BUCKET_NAME}
      - R2_PUBLIC_URL=${R2_PUBLIC_URL}
      - SMTP_SERVER=${SMTP_SERVER}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USERNAME=${SMTP_USERNAME}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - SMTP_USE_TLS=${SMTP_USE_TLS}
      - FROM_EMAIL=${FROM_EMAIL}
      - FRONTEND_URL=https://meubonsai.app
      - ASAAS_WALLET_ID_SANDBOX=${ASAAS_WALLET_ID_SANDBOX}
      - ASAAS_API_TOKEN_SANDBOX=${ASAAS_API_TOKEN_SANDBOX}
      - ASAAS_WALLET_ID_PRODUCTION=${ASAAS_WALLET_ID_PRODUCTION}
      - ASAAS_API_TOKEN_PRODUCTION=${ASAAS_API_TOKEN_PRODUCTION}
    networks:
      - my-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.meubonsai-backend.rule=Host(`api.meubonsai.app`)"
      - "traefik.http.routers.meubonsai-backend.entrypoints=websecure"
      - "traefik.http.routers.meubonsai-backend.tls=true"
      - "traefik.http.services.meubonsai-backend.loadbalancer.server.port=8000"
      - "traefik.docker.network=my-network"
    depends_on:
      - database
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Frontend React
  frontend:
    build: ./front
    container_name: meubonsai_frontend
    volumes:
      - ./front:/app
      - /app/node_modules
    environment:
      - REACT_APP_API_URL=https://api.meubonsai.app
      - CHOKIDAR_USEPOLLING=true
      - DANGEROUSLY_DISABLE_HOST_CHECK=true
      - WDS_SOCKET_HOST=0.0.0.0
      - WDS_SOCKET_PORT=0
    networks:
      - my-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.meubonsai.rule=Host(`meubonsai.app`)"
      - "traefik.http.routers.meubonsai.entrypoints=websecure"
      - "traefik.http.routers.meubonsai.tls=true"
      - "traefik.http.services.meubonsai.loadbalancer.server.port=3000"
      - "traefik.docker.network=my-network"
    depends_on:
      - backend
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

volumes:
  meubonsai_postgres_data:
  pgadmin_data:

networks:
  my-network:
    external: true
    name: my-network
