services:
  # Database PostgreSQL
  database_dev:
    container_name: meubonsai_postgres_dev
    image: ankane/pgvector:latest
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    ports:
      - "5433:5432"
    volumes:
      - meubonsai_postgres_data_dev:/var/lib/postgresql/data
      - ./db/init:/docker-entrypoint-initdb.d
    networks:
      - my-network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Backend FastAPI
  backend_dev:
    build: ./back
    container_name: meubonsai_backend_dev
    volumes:
      - ./back:/app
      - ./back/media:/app/media
    environment:
      - ENVIRONMENT=development
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_PORT=${POSTGRES_PORT}
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - APPLE_CLIENT_ID=${APPLE_CLIENT_ID:-}
      - APPLE_TEAM_ID=${APPLE_TEAM_ID:-}
      - APPLE_KEY_ID=${APPLE_KEY_ID:-}
      - APPLE_PRIVATE_KEY=${APPLE_PRIVATE_KEY:-}
      - R2_ACCESS_KEY_ID=${R2_ACCESS_KEY_ID}
      - R2_SECRET_ACCESS_KEY=${R2_SECRET_ACCESS_KEY}
      - R2_ENDPOINT_URL=${R2_ENDPOINT_URL}
      - R2_BUCKET_NAME=${R2_BUCKET_NAME}
      - R2_PUBLIC_URL=${R2_PUBLIC_URL}
      - SMTP_SERVER=${SMTP_SERVER}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USERNAME=${SMTP_USERNAME}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - SMTP_USE_TLS=${SMTP_USE_TLS}
      - FROM_EMAIL=${FROM_EMAIL}
      - FRONTEND_URL=https://dev.meubonsai.app
      - ASAAS_WALLET_ID_SANDBOX=${ASAAS_WALLET_ID_SANDBOX}
      - ASAAS_API_TOKEN_SANDBOX=${ASAAS_API_TOKEN_SANDBOX}
      - ASAAS_WALLET_ID_PRODUCTION=${ASAAS_WALLET_ID_PRODUCTION}
      - ASAAS_API_TOKEN_PRODUCTION=${ASAAS_API_TOKEN_PRODUCTION}
    networks:
      - my-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.meubonsai-backend-dev.rule=Host(`api-dev.meubonsai.app`)"
      - "traefik.http.routers.meubonsai-backend-dev.entrypoints=websecure"
      - "traefik.http.routers.meubonsai-backend-dev.tls=true"
      - "traefik.http.services.meubonsai-backend-dev.loadbalancer.server.port=8000"
      - "traefik.docker.network=my-network"
    depends_on:
      - database_dev
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Frontend React
  frontend_dev:
    build: ./front
    container_name: meubonsai_frontend_dev
    volumes:
      - ./front:/app
      - /app/node_modules
    environment:
      - REACT_APP_API_URL=https://api-dev.meubonsai.app
      - CHOKIDAR_USEPOLLING=true
      - DANGEROUSLY_DISABLE_HOST_CHECK=true
      - WDS_SOCKET_HOST=0.0.0.0
      - WDS_SOCKET_PORT=0
    networks:
      - my-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.meubonsai-dev.rule=Host(`dev.meubonsai.app`)"
      - "traefik.http.routers.meubonsai-dev.entrypoints=websecure"
      - "traefik.http.routers.meubonsai-dev.tls=true"
      - "traefik.http.services.meubonsai-dev.loadbalancer.server.port=3000"
      - "traefik.docker.network=my-network"
    depends_on:
      - backend_dev
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

volumes:
  meubonsai_postgres_data_dev:

networks:
  my-network:
    external: true
    name: my-network
